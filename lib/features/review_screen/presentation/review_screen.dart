import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

import '../../../utils/res/app_assets.dart';
import '../../../utils/res/constants.dart';

class ReviewScreen extends StatelessWidget {
  ReviewScreen({super.key, this.tiktok = false, required this.snapChat});

  bool tiktok;
  bool snapChat;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: const ValueKey('review_screen'),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Lottie.asset(AppAssets.reviewLottie, width: 120.h, height: 120.h),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                text: snapChat == false
                    ? tiktok == false
                        ? "Under Meta"
                        : "Under Tiktok"
                    : "Under SnapChat",
                color: Constants.primaryTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 16.sp,
              ),
            ],
          ),
          20.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                text: "Reviewing...",
                color: Constants.primaryTextColor,
                fontWeight: FontWeight.w600,
                fontSize: 20.sp,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

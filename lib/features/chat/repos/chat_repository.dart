
import 'package:dartz/dartz.dart';

import '../../../../utils/network/connection/network_info.dart';
import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/network/failure_helper.dart';
import '../data/chat_data_source.dart';
import '../models/chat_bot_model.dart';

class ChatRepo {
  NetworkInfo networkInfo;
  ChatDataSource chatDataSource;

  ChatRepo({required this.networkInfo, required this.chatDataSource});

  Future<Either<Failure, String>> getChannelName() {
    return FailureHelper.instance(
      method: () async {
        return await chatDataSource.getChannelName();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> sendMessage(Map<String, dynamic> message) {
    return FailureHelper.instance(
      method: () async {
        return await chatDataSource.sendMessage(message);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, ChatBotMesgsModel>> getBotMessages(
      {List<int>? ecxept}) {
    return FailureHelper.instance(
      method: () async {
        return await chatDataSource.getBotMessages(ecxept: ecxept);
      },
      networkInfo: networkInfo,
    );
  }
}

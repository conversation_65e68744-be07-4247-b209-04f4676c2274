
import 'package:dio/dio.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/network/dio/enum.dart';
import '../../../../utils/network/dio/network_call.dart';
import '../../../../utils/network/urls/end_points.dart';
import '../models/chat_bot_model.dart';

class ChatDataSource {
  Future<String> getChannelName() async {
    try {
      var response = await instance<NetworkCall>().request(
          EndPoints.getChannelName,
          // params: isImagesValid.every((element) => element) ? body : null,
          options: Options(
            method: Method.post.name,
            headers: {
              "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
            },
          ));
      return response['result']['channel'];
      // return response['result'];
    } catch (error) {
      rethrow;
    }
  }

  Future<String> sendMessage(Map<String, dynamic> message) async {
    try {
      var response =
          await instance<NetworkCall>().request(EndPoints.sendMessage,
              params: {"message": message['message']},
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return response['status'];
      // return response['result'];
    } catch (error) {
      rethrow;
    }
  }

  Future<ChatBotMesgsModel> getBotMessages({List<int>? ecxept}) async {
    try {
      var response =
          await instance<NetworkCall>().request(EndPoints.getBotMessages,
              params: {"except": ecxept},
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      ChatBotMesgsModel chatBotMsgs = ChatBotMesgsModel.fromJson(response);
      return chatBotMsgs;
      // return response['result'];
    } catch (error) {
      rethrow;
    }
  }
}

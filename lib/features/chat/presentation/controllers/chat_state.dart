part of 'chat_cubit.dart';

@immutable
abstract class ChatState {}

class ChatInitial extends ChatState {}

class ChatMessagesState extends ChatState {
  final List<Map<String, String>> messages;

  ChatMessagesState(this.messages);

  @override
  List<Object?> get props => [messages];

  ChatMessagesState copyWith({
    List<Map<String, String>>? messages,
  }) {
    return ChatMessagesState(
      messages ?? this.messages,
    );
  }
}

class ChatBotMessagesState extends ChatState {
  final List<Result> botMessages;

  ChatBotMessagesState(this.botMessages);
}

class ChatNewMessageState extends ChatState {
  final String message;

  ChatNewMessageState(this.message);
}

class ChatChannelNameState extends ChatState {
  final String channelName;

  ChatChannelNameState(this.channelName);
}

class ChatToggleBotState extends ChatState {
  final bool isBot;

  ChatToggleBotState(this.isBot);
}

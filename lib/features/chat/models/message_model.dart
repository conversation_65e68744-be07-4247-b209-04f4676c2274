class MessageModel {
  Message? message;
  String? channel;

  MessageModel({this.message, this.channel});

  MessageModel.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? Message.fromJson(json['message']) : null;
    channel = json['channel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (message != null) {
      data['message'] = message!.toJson();
    }
    data['channel'] = channel;
    return data;
  }
}

class Message {
  String? message;
  String? sender;
  int? chatId;
  String? updatedAt;
  String? createdAt;
  int? id;

  Message(
      {this.message,
      this.sender,
      this.chatId,
      this.updatedAt,
      this.createdAt,
      this.id});

  Message.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    sender = json['sender'];
    chatId = json['chat_id'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['sender'] = sender;
    data['chat_id'] = chatId;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}

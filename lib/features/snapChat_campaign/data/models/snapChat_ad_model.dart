import 'dart:io';

import 'package:ads_dv/features/create_campaigns/data/models/adset_location.dart';
import 'package:ads_dv/features/snapChat_campaign/data/models/snapChat_interests_response.dart';

class SnapChatAdModel {
  String? campaignName;
  String? adAccountId;
  String? existingCampaignId;
  String? status;
  String? startTime;
  String? endTime;
  String? adSquadName;
  String? adName;
  String? adSquadStatus;
  String? adText;
  String? type;
  String? minAge;
  String? maxAge;
  String? gender;
  String? countryCode;
  String? targetingReachStatusBillingAccount;
  String? billingEvent;
  String? bidMicro;
  String? autoBid;
  String? bidStrategy;
  String? optimizationGoal;
  String? lifeTimeBudgetMicro;
  String? pacingType;
  String? creativeName;
  String? creativeType;
  String? shareable;
  String? forcedViewEligibility;
  String? callToAction;
  String? ctaColorDisplayMode;
  String? renderType;
  String? adProduct;
  String? profileId;
  String? phoneId;
  String? link;
  String? selectedBudgetModes;
  File? file;
  String? mediaType;
  String? objective;
  List<AdSetGeoLocations>? geoLocations;
  List<CustomLocations>? customLocations;
  List<SnapChatInterestsModel>? interests;
  String? formName;
  String? formPrivacyPolicy;
  String? formDescription;
  List<String>? formFieldsType;

  // String? dailyBudgetMode;
  // List<String>? selectedTiktokInterests = [];
  // List<String>? palcementsPositions = [];

  SnapChatAdModel({
    this.campaignName,
    this.existingCampaignId,
    this.mediaType,
    this.file,
    this.profileId,
    this.adProduct,
    this.renderType,
    this.ctaColorDisplayMode,
    this.callToAction,
    this.shareable,
    this.creativeType,
    this.creativeName,
    this.pacingType,
    this.optimizationGoal,
    this.bidStrategy,
    this.autoBid,
    this.bidMicro,
    this.billingEvent,
    this.countryCode,
    this.gender,
    this.maxAge,
    this.minAge,
    this.phoneId,
    this.link,
    this.selectedBudgetModes,
    this.type,
    this.endTime,
    this.startTime,
    this.status,
    this.adAccountId,
    this.forcedViewEligibility,
    this.adText,
    this.adSquadName,
    this.adName,
    this.adSquadStatus,
    this.lifeTimeBudgetMicro,
    this.targetingReachStatusBillingAccount,
    this.objective,
    this.customLocations,
    this.interests,
    this.formName,
    this.formPrivacyPolicy,
    this.formDescription,
    this.formFieldsType,
    this.geoLocations,
  });

  SnapChatAdModel copyWith({
    String? campaignName,
    String? adAccountId,
    String? existingCampaignId,
    String? status,
    String? selectedBudgetModes,
    String? startTime,
    String? endTime,
    String? adSquadName,
    String? adName,
    String? adSquadStatus,
    String? adText,
    String? type,
    String? minAge,
    String? maxAge,
    String? gender,
    String? phoneId,
    String? countryCode,
    String? targetingReachStatusBillingAccount,
    String? billingEvent,
    String? bidMicro,
    String? autoBid,
    String? bidStrategy,
    String? optimizationGoal,
    String? lifeTimeBudgetMicro,
    String? pacingType,
    String? creativeName,
    String? creativeType,
    String? shareable,
    String? forcedViewEligibility,
    String? callToAction,
    String? ctaColorDisplayMode,
    String? renderType,
    String? adProduct,
    String? profileId,
    String? link,
    File? file,
    String? mediaType,
    String? objective,
    List<CustomLocations>? customLocations,
    List<SnapChatInterestsModel>? interests,
    String? formName,
    String? formPrivacyPolicy,
    String? formDescription,
    List<String>? formFieldsType,
    List<AdSetGeoLocations>? geoLocations,
  }) {
    return SnapChatAdModel(
      campaignName: campaignName ?? this.campaignName,
      adAccountId: adAccountId ?? this.adAccountId,
      existingCampaignId: existingCampaignId ?? this.existingCampaignId,
      status: status ?? this.status,
      optimizationGoal: optimizationGoal ?? this.optimizationGoal,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      adSquadName: adSquadName ?? this.adSquadName,
      adName: adName ?? this.adName,
      selectedBudgetModes: selectedBudgetModes ?? this.selectedBudgetModes,
      // adText: adText ?? this.adText,
      adSquadStatus: adSquadStatus ?? this.adSquadStatus,
      adText: adText ?? this.adText,
      type: type ?? this.type,
      minAge: minAge ?? this.minAge,
      maxAge: maxAge ?? this.maxAge,
      gender: gender ?? this.gender,
      phoneId: phoneId ?? this.phoneId,
      countryCode: countryCode ?? this.countryCode,
      targetingReachStatusBillingAccount: targetingReachStatusBillingAccount ??
          this.targetingReachStatusBillingAccount,
      billingEvent: billingEvent ?? this.billingEvent,
      bidMicro: bidMicro ?? this.bidMicro,
      autoBid: autoBid ?? this.autoBid,
      bidStrategy: bidStrategy ?? this.bidStrategy,
      lifeTimeBudgetMicro: lifeTimeBudgetMicro ?? this.lifeTimeBudgetMicro,
      pacingType: pacingType ?? this.pacingType,
      creativeName: creativeName ?? this.creativeName,
      creativeType: creativeType ?? this.creativeType,
      shareable: shareable ?? this.shareable,
      forcedViewEligibility:
          forcedViewEligibility ?? this.forcedViewEligibility,
      callToAction: callToAction ?? this.callToAction,
      ctaColorDisplayMode: ctaColorDisplayMode ?? this.ctaColorDisplayMode,
      renderType: renderType ?? this.renderType,
      adProduct: adProduct ?? this.adProduct,
      profileId: profileId ?? this.profileId,
      link: link ?? this.link,
      file: file ?? this.file,
      mediaType: mediaType ?? this.mediaType,
      objective: objective ?? this.objective,
      customLocations: customLocations ?? this.customLocations,
      interests: interests ?? this.interests,
      formName: formName ?? this.formName,
      formPrivacyPolicy: formPrivacyPolicy ?? this.formPrivacyPolicy,
      formDescription: formDescription ?? this.formDescription,
      formFieldsType: formFieldsType ?? this.formFieldsType,
      geoLocations: geoLocations ?? this.geoLocations,
    );
  }
}

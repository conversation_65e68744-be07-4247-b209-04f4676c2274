class GetSnapChatInterestsResponse {
  bool? success;
  String? message;
  List<GetSnapChatInterestsResult>? result;

  GetSnapChatInterestsResponse({this.success, this.message, this.result});

  GetSnapChatInterestsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <GetSnapChatInterestsResult>[];
      json['result'].forEach((v) {
        result!.add(GetSnapChatInterestsResult.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class GetSnapChatInterestsResult {
  String? subRequestStatus;
  SnapChatInterestsModel? scls;

  GetSnapChatInterestsResult({this.subRequestStatus, this.scls});

  GetSnapChatInterestsResult.fromJson(Map<String, dynamic> json) {
    subRequestStatus = json['sub_request_status'];
    scls = json['scls'] != null
        ? SnapChatInterestsModel.fromJson(json['scls'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sub_request_status'] = subRequestStatus;
    if (scls != null) {
      data['scls'] = scls!.toJson();
    }
    return data;
  }
}

class SnapChatInterestsModel {
  String? id;
  String? name;
  String? path;
  String? parentId;
  String? source;

  SnapChatInterestsModel(
      {this.id, this.name, this.path, this.parentId, this.source});

  SnapChatInterestsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    path = json['path'];
    parentId = json['parentId'];
    source = json['source'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['path'] = path;
    data['parentId'] = parentId;
    data['source'] = source;
    return data;
  }
}

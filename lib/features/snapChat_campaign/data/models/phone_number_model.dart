class PhoneNumberModel {
  String? id;
  String? updatedAt;
  String? createdAt;
  String? name;
  String? countryCode;
  String? numericalCountryCode;
  String? phoneNumber;
  String? verificationStatus;

  PhoneNumberModel(
      {this.id,
      this.updatedAt,
      this.createdAt,
      this.name,
      this.countryCode,
      this.numericalCountryCode,
      this.phoneNumber,
      this.verificationStatus});

  PhoneNumberModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    name = json['name'];
    countryCode = json['country_code'];
    numericalCountryCode = json['numerical_country_code'];
    phoneNumber = json['phone_number'];
    verificationStatus = json['verification_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['name'] = name;
    data['country_code'] = countryCode;
    data['numerical_country_code'] = numericalCountryCode;
    data['phone_number'] = phoneNumber;
    data['verification_status'] = verificationStatus;
    return data;
  }
}

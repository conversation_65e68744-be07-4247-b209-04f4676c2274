class SnapChatObjectivesResponse {
  int? status;
  List<SnapChatObjective>? data;
  String? message;

  SnapChatObjectivesResponse({this.status, this.data, this.message});

  SnapChatObjectivesResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <SnapChatObjective>[];
      json['data'].forEach((v) {
        data!.add(SnapChatObjective.fromJson(v));
      });
    }
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['message'] = message;
    return data;
  }
}

class SnapChatObjective {
  int? id;
  String? showName;
  String? actualName;

  SnapChatObjective({this.id, this.showName, this.actualName});

  SnapChatObjective.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    showName = json['show_name'];
    actualName = json['actual_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['show_name'] = showName;
    data['actual_name'] = actualName;
    return data;
  }
}

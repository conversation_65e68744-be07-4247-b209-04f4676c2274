class GetSnapChatCampaignsResponse {
  bool? success;
  String? message;
  List<ExistingCampaignsResult>? result;

  GetSnapChatCampaignsResponse({this.success, this.message, this.result});

  GetSnapChatCampaignsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <ExistingCampaignsResult>[];
      json['result'].forEach((v) {
        result!.add(ExistingCampaignsResult.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ExistingCampaignsResult {
  String? subRequestStatus;
  ExisitingSnapChatCampaign? campaign;

  ExistingCampaignsResult({this.subRequestStatus, this.campaign});

  ExistingCampaignsResult.fromJson(Map<String, dynamic> json) {
    subRequestStatus = json['sub_request_status'];
    campaign = json['campaign'] != null
        ? ExisitingSnapChatCampaign.fromJson(json['campaign'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sub_request_status'] = subRequestStatus;
    if (campaign != null) {
      data['campaign'] = campaign!.toJson();
    }
    return data;
  }
}

class ExisitingSnapChatCampaign {
  String? id;
  String? updatedAt;
  String? createdAt;
  String? name;
  String? adAccountId;
  String? status;
  String? objective;
  String? startTime;
  String? endTime;
  List<String>? deliveryStatus;
  String? creationState;
  int? pacingPropertiesVersion;

  ExisitingSnapChatCampaign(
      {this.id,
      this.updatedAt,
      this.createdAt,
      this.name,
      this.adAccountId,
      this.status,
      this.objective,
      this.startTime,
      this.endTime,
      this.deliveryStatus,
      this.creationState,
      this.pacingPropertiesVersion});

  ExisitingSnapChatCampaign.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    name = json['name'];
    adAccountId = json['ad_account_id'];
    status = json['status'];
    objective = json['objective'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    deliveryStatus = json['delivery_status'].cast<String>();
    creationState = json['creation_state'];
    pacingPropertiesVersion = json['pacing_properties_version'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['name'] = name;
    data['ad_account_id'] = adAccountId;
    data['status'] = status;
    data['objective'] = objective;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['delivery_status'] = deliveryStatus;
    data['creation_state'] = creationState;
    data['pacing_properties_version'] = pacingPropertiesVersion;
    return data;
  }
}

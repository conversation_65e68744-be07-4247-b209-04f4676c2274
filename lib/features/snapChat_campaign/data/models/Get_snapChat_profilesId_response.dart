class GetSnapChatProfilesIdResponse {
  bool? success;
  String? message;
  List<ProfilesIdModel>? result;

  GetSnapChatProfilesIdResponse({this.success, this.message, this.result});

  GetSnapChatProfilesIdResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <ProfilesIdModel>[];
      json['result'].forEach((v) {
        result!.add(ProfilesIdModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProfilesIdModel {
  String? subRequestStatus;
  PublicProfile? publicProfile;

  ProfilesIdModel({this.subRequestStatus, this.publicProfile});

  ProfilesIdModel.fromJson(Map<String, dynamic> json) {
    subRequestStatus = json['sub_request_status'];
    publicProfile = json['public_profile'] != null
        ? PublicProfile.fromJson(json['public_profile'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sub_request_status'] = subRequestStatus;
    if (publicProfile != null) {
      data['public_profile'] = publicProfile!.toJson();
    }
    return data;
  }
}

class PublicProfile {
  String? id;
  String? organizationId;
  String? displayName;
  LogoUrls? logoUrls;
  String? snapUserName;
  String? profileType;
  String? profileTier;
  String? internalProfileCategory;
  String? subscriberCount;
  String? profileIconPrimaryColorHex;

  PublicProfile(
      {this.id,
      this.organizationId,
      this.displayName,
      this.logoUrls,
      this.snapUserName,
      this.profileType,
      this.profileTier,
      this.internalProfileCategory,
      this.subscriberCount,
      this.profileIconPrimaryColorHex});

  PublicProfile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    organizationId = json['organization_id'];
    displayName = json['display_name'];
    logoUrls = json['logo_urls'] != null
        ? LogoUrls.fromJson(json['logo_urls'])
        : null;
    snapUserName = json['snap_user_name'];
    profileType = json['profile_type'];
    profileTier = json['profile_tier'];
    internalProfileCategory = json['internal_profile_category'];
    subscriberCount = json['subscriber_count'];
    profileIconPrimaryColorHex = json['profile_icon_primary_color_hex'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['organization_id'] = organizationId;
    data['display_name'] = displayName;
    if (logoUrls != null) {
      data['logo_urls'] = logoUrls!.toJson();
    }
    data['snap_user_name'] = snapUserName;
    data['profile_type'] = profileType;
    data['profile_tier'] = profileTier;
    data['internal_profile_category'] = internalProfileCategory;
    data['subscriber_count'] = subscriberCount;
    data['profile_icon_primary_color_hex'] = profileIconPrimaryColorHex;
    return data;
  }
}

class LogoUrls {
  String? originalLogoUrl;
  String? discoverFeedLogoUrl;
  String? megaProfileLogoUrl;
  String? manageProfileLogoUrl;

  LogoUrls(
      {this.originalLogoUrl,
      this.discoverFeedLogoUrl,
      this.megaProfileLogoUrl,
      this.manageProfileLogoUrl});

  LogoUrls.fromJson(Map<String, dynamic> json) {
    originalLogoUrl = json['original_logo_url'];
    discoverFeedLogoUrl = json['discover_feed_logo_url'];
    megaProfileLogoUrl = json['mega_profile_logo_url'];
    manageProfileLogoUrl = json['manage_profile_logo_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['original_logo_url'] = originalLogoUrl;
    data['discover_feed_logo_url'] = discoverFeedLogoUrl;
    data['mega_profile_logo_url'] = megaProfileLogoUrl;
    data['manage_profile_logo_url'] = manageProfileLogoUrl;
    return data;
  }
}

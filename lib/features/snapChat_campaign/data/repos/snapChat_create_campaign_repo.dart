import 'dart:io';

import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../data_source/craete_snapChat_campaign_data_source.dart';
import '../models/Get_snapChat_profilesId_response.dart';
import '../models/get_snapChat_campaigns_response.dart';
import '../models/phone_number_model.dart';
import '../models/snapChat_ad_model.dart';
import '../models/snapChat_interests_response.dart';
import '../models/snapChat_objectives_response.dart';

class CreateSnapChatCampaignRepo {
  NetworkInfo networkInfo;
  CreateSnapChatCampaignDataSource createSnapChatCampaignDataSource;

  CreateSnapChatCampaignRepo(
      {required this.networkInfo,
      required this.createSnapChatCampaignDataSource});

  Future<Either<Failure, SnapChatObjectivesResponse>> getSnapChatObjectives() {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.getSnapChatObjectives();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, GetSnapChatCampaignsResponse>> getCampaigns(
      {required String accountId}) {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.getCampaigns(accountId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<SnapChatObjective>>> getSnapChatOptimizations(
      {required String objectiveActualName}) {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.getSnapChatOptimizations(
            objectiveActualName: objectiveActualName);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<SnapChatInterestsModel>>> getSnapChatInterests() {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.getSnapChatInterests();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<PublicProfile>>> getSnapChatProfilesId() {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.getSnapChatProfilesId();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<PhoneNumberModel>>> getSnapChatPhoneNumbers() {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.getSnapChatPhoneNumbers();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> createAD({
    required List<File> imagesFiles,
    required List<File> videosFiles,
    SnapChatAdModel? snapChatAdModel,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await createSnapChatCampaignDataSource.createAD(
            imagesFiles: imagesFiles,
            videosFiles: videosFiles,
            snapChatAdModel: snapChatAdModel
            // thumbFiles: thumbFiles
            );
      },
      networkInfo: networkInfo,
    );
  }
}

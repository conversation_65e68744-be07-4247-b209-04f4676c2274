import 'dart:io';

import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../models/Get_snapChat_profilesId_response.dart';
import '../models/get_snapChat_campaigns_response.dart';
import '../models/phone_number_model.dart';
import '../models/snapChat_ad_model.dart';
import '../models/snapChat_interests_response.dart';
import '../models/snapChat_objectives_response.dart';

class CreateSnapChatCampaignDataSource {
  Future<SnapChatObjectivesResponse> getSnapChatObjectives() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getSnapChatObjectives,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return SnapChatObjectivesResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<GetSnapChatCampaignsResponse> getCampaigns(String accountId) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllSnapChatCampaigns,
        queryParameters: {
          "adAccount_id": accountId,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      GetSnapChatCampaignsResponse cam =
          GetSnapChatCampaignsResponse.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<SnapChatObjective>> getSnapChatOptimizations(
      {required String objectiveActualName}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getSnapChatOptimizations,
        params: {
          "objective_id": objectiveActualName,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<SnapChatObjective> optimizations = data
          .map((optimizations) => SnapChatObjective.fromJson(optimizations))
          .toList();
      return optimizations;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<SnapChatInterestsModel>> getSnapChatInterests() async {
    try {
      // print(
      //     'countryCodeasdewrwerasd ${instance.get<HiveHelper>().getMyCountryCode()}');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllSnapChatInterests,
        params: {
          "country_code": instance.get<HiveHelper>().getMyCountryCode(),
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<SnapChatInterestsModel> interests = data
          .map(
              (interests) => SnapChatInterestsModel.fromJson(interests['scls']))
          .toList();
      return interests;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<PublicProfile>> getSnapChatProfilesId() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllSnapChatProfilesId,
        // params: {
        //   "objective_id": objectiveActualName,
        // },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<PublicProfile> profilesId = data
          .map((interests) =>
              PublicProfile.fromJson(interests['public_profile']))
          .toList();
      return profilesId;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<PhoneNumberModel>> getSnapChatPhoneNumbers() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.snapChatGetPhoneNumbers,
        params: {
          "ad_account_id": instance.get<HiveHelper>().getSnapAdAccountId(),
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<PhoneNumberModel> phoneNumbers = data
          .map((interests) =>
              PhoneNumberModel.fromJson(interests['phone_number']))
          .toList();
      return phoneNumbers;
    } catch (error) {
      rethrow;
    }
  }

  Future<String> createAD({
    required List<File> imagesFiles,
    required List<File> videosFiles,
    SnapChatAdModel? snapChatAdModel,
  }) async {
    try {
      // List<bool> isImagesValid =
      //     imagesFiles.map((e) => e.existsSync()).toList();

      // List<bool> isImagesValid =
      //     imagesFiles.map((e) => e.existsSync()).toList();
      // List<bool> isVideosValid =
      //     videosFiles.map((e) => e.existsSync()).toList();

      print(
          'testIdentityType ${snapChatAdModel?.geoLocations} ${snapChatAdModel?.file?.path}');

      var body = FormData.fromMap({
        "campaign_name": snapChatAdModel?.campaignName,
        "ad_account_id": snapChatAdModel?.adAccountId,

        "status": "PAUSED",
        "start_time": snapChatAdModel?.startTime,
        "end_time": snapChatAdModel?.endTime,
        "adsquad_name": snapChatAdModel?.adSquadName,
        "ad_name": snapChatAdModel?.adName,
        "adsquad_status": "PAUSED",
        "type": "SNAP_ADS",
        "min_age": snapChatAdModel?.minAge,
        "max_age": snapChatAdModel?.maxAge,
        "gender":
            snapChatAdModel?.gender == "" ? null : snapChatAdModel?.gender,
        "country_code": snapChatAdModel?.countryCode?.toLowerCase(),
        "targeting_reach_status": "VALID",
        "billing_event": "IMPRESSION",
        "bid_micro": "20000",
        "auto_bid": "false",
        "bid_strategy": "LOWEST_COST_WITH_MAX_BID",
        "optimization_goal": snapChatAdModel?.optimizationGoal,
        "lifetime_budget_micro":
            snapChatAdModel?.selectedBudgetModes == 'BUDGET_MODE_TOTAL'
                ? snapChatAdModel?.lifeTimeBudgetMicro
                : null,
        "daily_budget_micro":
            snapChatAdModel?.selectedBudgetModes == 'BUDGET_MODE_DAY'
                ? snapChatAdModel?.lifeTimeBudgetMicro
                : null,
        "pacing_type": "STANDARD",
        // "creative_name": snapChatAdModel?.creativeName,
        "creative_type": creativeType(snapChatAdModel),
        "shareable": "true",
        "forced_view_eligibility": "FULL_DURATION",
        "call_to_action": snapChatAdModel?.callToAction,
        "cta_color_display_mode": "AUTO_COLOR_DETECTION",
        "render_type": "STATIC",
        "ad_product": "SNAP_AD",
        "profile_id": snapChatAdModel?.profileId,
        "objective": snapChatAdModel?.objective,
        "file": await MultipartFile.fromFile(snapChatAdModel?.file?.path ?? ""),
        "media_type": snapChatAdModel?.mediaType,
        "ad_type": adType(snapChatAdModel),
        "campaign_id": snapChatAdModel?.existingCampaignId,
        for (int i = 0;
            i <
                (snapChatAdModel?.geoLocations?[0].customLocations?.length ??
                    0);
            i++)
          "circles[$i][latitude]":
              snapChatAdModel?.geoLocations?[0].customLocations?[i].latitude,
        for (int i = 0;
            i <
                (snapChatAdModel?.geoLocations?[0].customLocations?.length ??
                    0);
            i++)
          "circles[$i][longitude]":
              snapChatAdModel?.geoLocations?[0].customLocations?[i].longitude,
        for (int i = 0;
            i <
                (snapChatAdModel?.geoLocations?[0].customLocations?.length ??
                    0);
            i++)
          "circles[$i][radius]":
              snapChatAdModel?.geoLocations?[0].customLocations?[i].radius,
        for (int i = 0;
            i <
                (snapChatAdModel?.geoLocations?[0].customLocations?.length ??
                    0);
            i++)
          "circles[$i][unit]": "KILOMETERS",
        for (int i = 0; i < (snapChatAdModel?.interests?.length ?? 0); i++)
          "interests[$i][category_id][]": snapChatAdModel?.interests?[i].id,
        for (int i = 0; i < (snapChatAdModel?.interests?.length ?? 0); i++)
          "interests[$i][operation]": "INCLUDE",
        'privacy_policy_url': snapChatAdModel?.formPrivacyPolicy,
        'form_name': snapChatAdModel?.formName,
        'form_description': snapChatAdModel?.formDescription,
        for (int i = 0; i < (snapChatAdModel?.formFieldsType?.length ?? 0); i++)
          "form_fields[$i][type]": snapChatAdModel?.formFieldsType?[i],
        "phone_number_id": snapChatAdModel?.phoneId,
        "link": snapChatAdModel?.link,
        // for (int i = 0; i < (adModel.questions?.length ?? 0); i++)
        //   "questions[$i][type]": adModel.questions?[i],
      });
      // if (videosFiles.isNotEmpty || imagesFiles.isNotEmpty) {
      // for (int i = 0; i < videosFiles.length; i++) {
      // if (videosFiles[i].existsSync() || imagesFiles[i].existsSync()) {
      // body.files.add(MapEntry(
      //   "file",
      //   await MultipartFile.fromFile(snapChatAdModel?.file?.path ?? ""),
      // "video[$i]",
      // await MultipartFile.fromFile(videosFiles[i].path),
      // ));
      // print(
      //     '✅ Does body.files contain a video? ${body.files.any((entry) => entry.key.startsWith("video"))}');
      // } else {
      //   print("❌ Video file does not exist: ${videosFiles[i].path}");
      // }
      // }
      // }

      // for (int i = 0; i < imagesFiles.length; i++) {
      //   if (imagesFiles[i].existsSync()) {
      //     body.files.add(MapEntry(
      //       "image_file[$i]",
      //       await MultipartFile.fromFile(imagesFiles[i].path),
      //     ));
      //     // print(
      //     //     '✅ Does body.files contain an image? ${body.files.any((entry) => entry.key.startsWith("image_file"))}');
      //   } else {
      //     print("❌ Image file does not exist: ${imagesFiles[i].path}");
      //   }
      // }

      // for (var entry in body.files) {
      //   print(
      //       "📂 Key: ${entry.key}, File Path: ${(entry.value as MultipartFile).filename}");
      // }
      //
      print('jasdoasfdklf ${body.fields}');

      var response =
          await instance<NetworkCall>().request(EndPoints.snapChatFullCycle,
              params:
                  // isImagesValid.every((element) => element) ?
                  body
              //     :
              // null
              ,
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return response['message'];
    } catch (error) {
      rethrow;
    }
  }

  String creativeType(SnapChatAdModel? snapChatAdModel) {
    if (snapChatAdModel?.optimizationGoal == "LEAD_FORM_SUBMISSIONS") {
      return 'LEAD_GENERATION';
    } else if (snapChatAdModel?.link != '') {
      print('whatisthelink ${snapChatAdModel?.link}');
      return 'WEB_VIEW';
    } else if (snapChatAdModel?.phoneId != null) {
      print('whatisthelink ${snapChatAdModel?.link}');
      return 'AD_TO_CALL';
    } else {
      return 'SNAP_AD';
    }
  }

  String adType(SnapChatAdModel? snapChatAdModel) {
    if (snapChatAdModel?.optimizationGoal == "LEAD_FORM_SUBMISSIONS") {
      return 'LEAD_GENERATION';
    } else if (snapChatAdModel?.link != '') {
      print('whatisthelink ${snapChatAdModel?.link.runtimeType}');
      return 'REMOTE_WEBPAGE';
    } else if (snapChatAdModel?.phoneId != null) {
      return 'AD_TO_CALL';
    } else {
      return 'SNAP_AD';
    }
  }
}

import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_adSet_widget/create_snapChat_adSet_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_ad_widget/snapChat_ad_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_campaign_widget/snapChat_campaign_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/snapChat_review_widget.dart';
import 'package:ads_dv/widgets/stepper/bottom_nav.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../widgets/appbar.dart';
import '../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';

class CreateSnapChatCampaignScreen extends StatelessWidget {
  const CreateSnapChatCampaignScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: const CustomBottomNavBar(
        isReview: true,
        isTiktok: false,
        isSnapChat: true,
      ),
      appBar: const CustomAppBar(
        title: "SnapChat Campaign",
        showBackButton: true,
        hasDrawer: true,
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              if (instance<HiveHelper>().getAdvertiserId() != null)
                Column(
                  children: [
                    5.verticalSpace,
                    AccountHintText(
                      isDefaultHint: true,
                      hint:
                          "${instance<HiveHelper>().getTiktokPageName() ?? ""}'s Ad Account",
                    ),
                    20.verticalSpace,
                  ],
                )
              else
                const SizedBox(),
              const SnapChatCampaignWidget(),
              SizedBox(height: 20.h),
              const CreateSnapChatAdSetWidget(),
              SizedBox(height: 20.h),
              const SnapChatAdCreativeWidget(),
              SizedBox(height: 20.h),
              const SnapChatAdReviewExpandedWidget(),
            ],
          ),
        ),
      ),
    );
  }
}

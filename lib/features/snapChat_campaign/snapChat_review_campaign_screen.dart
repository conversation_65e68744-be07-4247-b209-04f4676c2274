// import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/snapChat_review_widget.dart';
// import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_ad_review_widget.dart';
// import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_review_ad_widget.dart';
// import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
// import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_review/tiktok_adGroup_review_widget.dart';
// import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_review/tiktok_ad_review_widget.dart';
// import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_review/tiktok_ad_widget.dart';
// import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_review/tiktok_campaign_review_widget.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
//
// import '../../../../widgets/appbar.dart';
// import '../../../../widgets/stepper/bottom_nav.dart';
// import '../review_screen/presentation/review_screen.dart';
// import '../sidebar/ad_accounts/presentation/controllers/tiktok_accounts/tiktok_accounts_cubit.dart';
// import '../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
//
// class TiktokReviewCampaignScreen extends StatelessWidget {
//   const TiktokReviewCampaignScreen({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<TiktokAdCubit, TiktokAdState>(
//       builder: (context, state) {
//         return state is ADCreatedStateLoading
//             ? ReviewScreen(
//                 tiktok: true,
//               )
//             : Scaffold(
//                 bottomNavigationBar: CustomBottomNavBar(
//                   isReview: false,
//                   isTiktok: true,
//                 ),
//                 appBar: const CustomAppBar(
//                   title: "Campaign Summary",
//                   showBackButton: true,
//                   hasDrawer: true,
//                 ),
//                 body:
//                     // CreateAdCubit.get(context).isAddCreated
//                     //     ? const SizedBox()
//                     //     :
//                     SingleChildScrollView(
//                   physics: const BouncingScrollPhysics(),
//                   child: Padding(
//                     padding: const EdgeInsets.all(24.0),
//                     child: Column(
//                       children: [
//                         if (TiktokAccountsCubit.get(context)
//                                 .addedAdAccount
//                                 ?.advertiserName !=
//                             null)
//                           Column(
//                             children: [
//                               5.verticalSpace,
//                               AccountHintText(
//                                 isDefaultHint: true,
//                                 hint:
//                                     "${TiktokAccountsCubit.get(context).addedAdAccount?.advertiserName ?? ""}'s Ad Account",
//                               ),
//                               20.verticalSpace,
//                             ],
//                           )
//                         else
//                           const SizedBox(),
//                         const TiktokCampaignReviewWidget(),
//                         20.verticalSpace,
//                         const TiktokAdGroupReviewWidget(),
//                         20.verticalSpace,
//                         const SnapChatAdReviewWidget(),
//                         20.verticalSpace,
//                         const TiktokReviewAdWidget(),
//                       ],
//                     ),
//                   ),
//                 ),
//               );
//       },
//     );
//   }
// }

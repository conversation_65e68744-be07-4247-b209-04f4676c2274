import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/custom_widgets.dart';
import '../../../../../utils/res/media_query_config.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/custom_text_field.dart';
import '../../controllers/snapChat_objectives/get_snap_chat_objectives_cubit.dart';

class SnapChatPageViewDialog extends StatefulWidget {
  GetSnapChatObjectivesCubit getSnapChatObjectivesCubit;

  SnapChatPageViewDialog({super.key, required this.getSnapChatObjectivesCubit});

  @override
  _SnapChatPageViewDialogState createState() => _SnapChatPageViewDialogState();
}

class _SnapChatPageViewDialogState extends State<SnapChatPageViewDialog> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  void _goToNextPage(GlobalKey<FormState> key) {
    // if(key.currentState!.validate()){
    //
    // }
    if (_currentPage < 3) {
      _controller.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
      setState(() {
        _currentPage++;
      });
    }
  }

  void _goToPreviousPage() {
    if (_currentPage > 0) {
      _controller.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
      setState(() {
        _currentPage--;
      });
    }
  }

  @override
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: SizeConfig.screenHeight(context) * 0.5, // Adjust height as needed
      child: Form(
        key: GetSnapChatObjectivesCubit.get(context).leadsFormKey,
        child: Column(
          children: [
            // Padding(
            //   padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 30.sp),
            //   child: Container(
            //     decoration: ShapeDecoration(
            //       color: Colors.white,
            //       shape: RoundedRectangleBorder(
            //         borderRadius: BorderRadius.circular(40),
            //       ),
            //       shadows: const [
            //         BoxShadow(
            //           color: Color(0x3D000000),
            //           blurRadius: 13.93,
            //           offset: Offset(0, 0),
            //           spreadRadius: -3.80,
            //         )
            //       ],
            //     ),
            //     child: TabsWidget(
            //       newObject: 'New Form',
            //       existObject: 'Existing Form',
            //       selectedTab:
            //           GetSnapChatObjectivesCubit.get(context).selectFormTab,
            //       onTabChanged: (tab) {
            //         if (GetSnapChatObjectivesCubit.get(context).selectFormTab !=
            //             tab) {
            //           setState(() {
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .changeFormTabIndex(tab);
            //             GetSnapChatObjectivesCubit.get(context).leadDesc.text =
            //                 "";
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .leadMessage
            //                 .text = "";
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .ctaController
            //                 .text = "";
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .websiteLinkController
            //                 .text = "";
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .linkTextController
            //                 .text = "";
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .linkController
            //                 .text = "";
            //             GetSnapChatObjectivesCubit.get(context)
            //                 .leadHeadlineController
            //                 .text = "";
            //             GetSnapChatObjectivesCubit.get(context).questions = [
            //               // Question(
            //               //     name: 'What is your city?',
            //               //     value: 'CITY',
            //               //     isChecked: false),
            //               Question(
            //                   name: 'What is your company name?',
            //                   value: 'COMPANY_NAME',
            //                   isChecked: false),
            //               // Question(
            //               //     name: 'What is your country name?',
            //               //     value: 'COUNTRY',
            //               //     isChecked: false),
            //               // Question(
            //               //     name: 'What is your gender?',
            //               //     value: 'GENDER',
            //               //     isChecked: false),
            //               Question(
            //                   name: 'What is your first name?',
            //                   value: 'FIRST_NAME',
            //                   isChecked: false),
            //               // Question(
            //               //     name: 'What is your full name?',
            //               //     value: 'FULL_NAME',
            //               //     isChecked: false),
            //               Question(
            //                   name: 'What is your job title?',
            //                   value: 'JOB_TITLE',
            //                   isChecked: false),
            //               Question(
            //                   name: 'What is your date of birth?',
            //                   value: 'BIRTHDAY_DATE',
            //                   isChecked: false),
            //               Question(
            //                   name: 'What is your email?',
            //                   value: 'EMAIL',
            //                   isChecked: false),
            //               Question(
            //                   name: 'What is your last name?',
            //                   value: 'LAST_NAME',
            //                   isChecked: false),
            //               Question(
            //                   name: 'What is your postal code?',
            //                   value: 'POSTAL_CODE',
            //                   isChecked: false),
            //               Question(
            //                   name: 'What is your phone?',
            //                   value: 'PHONE_NUMBER',
            //                   isChecked: false),
            //               // Question(
            //               //     name: 'What is your state?',
            //               //     value: 'STATE',
            //               //     isChecked: false),
            //               Question(
            //                   name: 'What is your address?',
            //                   value: 'ADDRESS',
            //                   isChecked: false),
            //             ];
            //             // CUSTOM, CITY, COMPANY_NAME, COUNTRY, DOB, EMAIL,
            //             // GENDER, FIRST_NAME, FULL_NAME, JOB_TITLE, LAST_NAME, MARITIAL_STATUS, PHONE, PHONE_OTP, POST_CODE, PROVINCE,
            //             // RELATIONSHIP_STATUS, STATE, STREET_ADDRESS, ZIP, WORK_EMAIL, MILITARY_STATUS, WORK_PHONE_NUMBER, SLIDER, STORE_LOOKUP,
            //             // STORE_LOOKUP_WITH_TYPEAHEAD, DATE_TIME, ID_CPF, ID_AR_DNI, ID_CL_RUT, ID_CO_CC, ID_EC_CI, ID_PE_DNI, ID_MX_RFC,
            //             // JOIN_CODE, USER_PROVIDED_PHONE_NUMBER, FACEBOOK_LEAD_ID, EMAIL_ALIAS, MESSENGER
            //
            //             GetSnapChatObjectivesCubit.get(context).lang = [
            //               Language(name: 'Arabic', value: 'AR_AR'),
            //               Language(name: 'English', value: 'EN_US'),
            //             ];
            //
            //             GetSnapChatObjectivesCubit.get(context).addedQuestions =
            //                 [];
            //             GetSnapChatObjectivesCubit.get(context).langValue =
            //                 null;
            //             GetSnapChatObjectivesCubit.get(context).langIndex =
            //                 null;
            //
            //             GetSnapChatObjectivesCubit.get(context).formId = null;
            //             GetSnapChatObjectivesCubit.get(context).formIndex =
            //                 null;
            //           });
            //         }
            //       },
            //     ),
            //   ),
            // ),
            // (GetSnapChatObjectivesCubit.get(context).selectFormTab == 0)
            //     ?
            SizedBox(
              height: 20.0.h,
            ),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: PageView(
                      controller: _controller,
                      onPageChanged: (index) {
                        setState(() {
                          _currentPage = index;
                        });
                      },
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20.sp, vertical: 16.sp),
                          child: Column(
                            children: [
                              CustomTextFormField(
                                label: "Headline",
                                showIsReqiredFlag: true,
                                textFontSize: 12,
                                key: const ValueKey('headline'),
                                hintText: "Headline",
                                textInputAction: TextInputAction.next,
                                keyboardType: TextInputType.text,
                                controller:
                                    GetSnapChatObjectivesCubit.get(context)
                                        .leadHeadlineController,
                                validator: (value) =>
                                    AppValidator.validateIdentity(
                                        value, context),
                              ),
                              25.verticalSpace,
                              ListView.builder(
                                padding: EdgeInsets.zero,
                                itemBuilder: (item, index) {
                                  return Row(
                                    children: [
                                      Radio<int>(
                                        value: index,
                                        groupValue:
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .langIndex,
                                        onChanged: (int? newValue) {
                                          setState(() {
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .setSelectedLang(
                                                    GetSnapChatObjectivesCubit
                                                            .get(context)
                                                        .lang[index]
                                                        .value,
                                                    index);
                                          });
                                          print("agdsgdsfs${GetSnapChatObjectivesCubit.get(
                                                      context)
                                                  .langValue}");
                                        },
                                        activeColor: AppColors.mainColor,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0),
                                        child: SizedBox(
                                          width: 150.h,
                                          child: CustomText(
                                            text:
                                                GetSnapChatObjectivesCubit.get(
                                                            context)
                                                        .lang[index]
                                                        .name ??
                                                    "",
                                            maxLines: 10,
                                            textAlign: TextAlign.right,
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                                itemCount:
                                    GetSnapChatObjectivesCubit.get(context)
                                        .lang
                                        .length,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                              ),
                            ],
                          ),
                        ),
                        Center(
                          child: ListView.builder(
                            shrinkWrap: true,
                            // physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (item, index) {
                              return Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Checkbox(
                                    onChanged: (value) {
                                      setState(() {
                                        GetSnapChatObjectivesCubit.get(context)
                                                .questions[index]
                                                .isChecked =
                                            value!; // Toggle the checkbox value
                                        GetSnapChatObjectivesCubit.get(context)
                                            .setSelectedQuestion();
                                        print(
                                            "igPositions${GetSnapChatObjectivesCubit.get(context).addedQuestions}");
                                      });
                                    },
                                    value:
                                        GetSnapChatObjectivesCubit.get(context)
                                            .questions[index]
                                            .isChecked,
                                    activeColor: Constants.primaryTextColor,
                                    checkColor: Colors.white,
                                    // Color of the checkmark
                                    focusColor: Constants.primaryTextColor,
                                    // Color of the border when focused
                                    side: const BorderSide(
                                      color: Constants.primaryTextColor,
                                      width: 2,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: CustomText(
                                      text: GetSnapChatObjectivesCubit.get(
                                                  context)
                                              .questions[index]
                                              .name ??
                                          "",
                                      color: Constants.primaryTextColor,
                                      alignment: AlignmentDirectional.center,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              );
                            },
                            itemCount: GetSnapChatObjectivesCubit.get(context)
                                .questions
                                .length,
                          ),
                        ),
                        Center(
                            child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20.sp, vertical: 16.sp),
                          child: Column(
                            children: [
                              CustomTextFormField(
                                label: "Privacy Policy Link",
                                showIsReqiredFlag: true,
                                textFontSize: 12,
                                key: const ValueKey('link'),
                                hintText: "Privacy Policy Link",
                                textInputAction: TextInputAction.next,
                                keyboardType: TextInputType.text,
                                controller:
                                    GetSnapChatObjectivesCubit.get(context)
                                        .linkController,
                                validator: (value) =>
                                    AppValidator.validateIdentity(
                                        value, context),
                              ),
                              const SizedBox(height: 20),
                              CustomTextFormField(
                                label: "Privacy Policy Text",
                                showIsReqiredFlag: true,
                                textFontSize: 12,
                                key: const ValueKey('lint_text'),
                                hintText: "Privacy Policy Text",
                                textInputAction: TextInputAction.next,
                                keyboardType: TextInputType.text,
                                controller:
                                    GetSnapChatObjectivesCubit.get(context)
                                        .linkTextController,
                                validator: (value) =>
                                    AppValidator.validateIdentity(
                                        value, context),
                              ),
                            ],
                          ),
                        )),
                        Center(
                            child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20.sp, vertical: 16.sp),
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                CustomTextFormField(
                                  label: "Lead Message",
                                  showIsReqiredFlag: true,
                                  textFontSize: 12,
                                  key: const ValueKey('message'),
                                  hintText: "Lead Message",
                                  textInputAction: TextInputAction.next,
                                  keyboardType: TextInputType.text,
                                  controller:
                                      GetSnapChatObjectivesCubit.get(context)
                                          .leadMessage,
                                  validator: (value) =>
                                      AppValidator.validateIdentity(
                                          value, context),
                                ),
                                const SizedBox(height: 20),
                                CustomTextFormField(
                                  label: "Description",
                                  showIsReqiredFlag: true,
                                  textFontSize: 12,
                                  key: const ValueKey('description'),
                                  hintText: "Description",
                                  textInputAction: TextInputAction.next,
                                  keyboardType: TextInputType.text,
                                  controller:
                                      GetSnapChatObjectivesCubit.get(context)
                                          .leadDesc,
                                  validator: (value) =>
                                      AppValidator.validateIdentity(
                                          value, context),
                                ),
                                const SizedBox(height: 20),
                                CustomTextFormField(
                                  label: "Website Link",
                                  showIsReqiredFlag: true,
                                  textFontSize: 12,
                                  key: const ValueKey('web_link'),
                                  hintText: "Website Link",
                                  textInputAction: TextInputAction.next,
                                  keyboardType: TextInputType.text,
                                  controller:
                                      GetSnapChatObjectivesCubit.get(context)
                                          .websiteLinkController,
                                  validator: (value) =>
                                      AppValidator.validateIdentity(
                                          value, context),
                                ),
                                const SizedBox(height: 20),
                                CustomTextFormField(
                                  label: "Call to action",
                                  showIsReqiredFlag: true,
                                  textFontSize: 12,
                                  key: const ValueKey('cta'),
                                  hintText: "Call to action",
                                  textInputAction: TextInputAction.next,
                                  keyboardType: TextInputType.text,
                                  controller:
                                      GetSnapChatObjectivesCubit.get(context)
                                          .ctaController,
                                  validator: (value) =>
                                      AppValidator.validateIdentity(
                                          value, context),
                                ),
                                const SizedBox(height: 20),
                                // CustomText(
                                //   text: 'Additional Action',
                                //   fontSize: 12.sp,
                                //   color: Constants.primaryTextColor,
                                //   fontWeight: FontWeight.w400,
                                //   alignment: AlignmentDirectional.centerStart,
                                // ),
                                // 10.verticalSpace,
                                // ListView.builder(
                                //   padding: EdgeInsets.zero,
                                //   itemBuilder: (item, index) {
                                //     return Row(
                                //       children: [
                                //         Radio<int>(
                                //           value: index,
                                //           groupValue:
                                //           CreateAdCubit.get(context)
                                //               .actionIndex,
                                //           onChanged: (int? newValue) {
                                //             setState(() {
                                //               CreateAdCubit.get(context)
                                //                   .setSelectedAction(
                                //                   CreateAdCubit.get(
                                //                       context)
                                //                       .actions[index]
                                //                       .value,
                                //                   index);
                                //             });
                                //             print("agdsgdsfs" +
                                //                 CreateAdCubit.get(context)
                                //                     .actionValue
                                //                     .toString());
                                //           },
                                //           activeColor: AppColors.mainColor,
                                //         ),
                                //         Padding(
                                //           padding:
                                //           const EdgeInsets.symmetric(
                                //               horizontal: 8.0),
                                //           child: SizedBox(
                                //             width: 150.h,
                                //             child: CustomText(
                                //               text:
                                //               CreateAdCubit.get(context)
                                //                   .actions[index]
                                //                   .name ??
                                //                   "",
                                //               maxLines: 10,
                                //               textAlign: TextAlign.right,
                                //             ),
                                //           ),
                                //         ),
                                //       ],
                                //     );
                                //   },
                                //   itemCount: CreateAdCubit.get(context)
                                //       .actions
                                //       .length,
                                //   shrinkWrap: true,
                                //   physics:
                                //   const NeverScrollableScrollPhysics(),
                                // ),
                                SizedBox(
                                  width: 235.w,
                                  child: ButtonWidget(
                                    text: "Save",
                                    onTap: () {
                                      print('1xcvxcfd');
                                      if (GetSnapChatObjectivesCubit.get(
                                                  context)
                                              .addedQuestions
                                              .contains('FIRST_NAME') &&
                                          GetSnapChatObjectivesCubit.get(
                                                  context)
                                              .addedQuestions
                                              .contains('LAST_NAME') &&
                                          (GetSnapChatObjectivesCubit.get(
                                                      context)
                                                  .addedQuestions
                                                  .contains('EMAIL') ||
                                              GetSnapChatObjectivesCubit.get(
                                                      context)
                                                  .addedQuestions
                                                  .contains('PHONE_NUMBER'))) {
                                        CreateSnapChatAdCubit.get(context)
                                            .snapChatAdModel = CreateSnapChatAdCubit.get(
                                                context)
                                            .snapChatAdModel
                                            .copyWith(
                                                formName:
                                                    GetSnapChatObjectivesCubit.get(context)
                                                        .leadHeadlineController
                                                        .text,
                                                formDescription:
                                                    GetSnapChatObjectivesCubit.get(context)
                                                        .leadDesc
                                                        .text,
                                                formPrivacyPolicy:
                                                    GetSnapChatObjectivesCubit.get(context)
                                                        .linkController
                                                        .text,
                                                formFieldsType:
                                                    GetSnapChatObjectivesCubit
                                                            .get(context)
                                                        .addedQuestions);
                                        Navigator.of(context).pop();
                                      } else {
                                        showErrorToast(
                                            'Must select first name ,last name and email or phone number');
                                      }

                                      // CreateAdCubit.get(context)
                                      //     .destinationType = "ON_AD";
                                      //
                                      // CreateAdCubit.get(context).adModel = CreateAdCubit.get(context).adModel.copyWith(
                                      //     formName: CreateAdCubit.get(context)
                                      //         .leadHeadlineController
                                      //         .text,
                                      //     formLocale: CreateAdCubit.get(context)
                                      //         .langValue,
                                      //     questions: CreateAdCubit.get(context)
                                      //         .addedQuestions,
                                      //     formLinkText:
                                      //     CreateAdCubit.get(context)
                                      //         .linkTextController
                                      //         .text,
                                      //     formUrl: CreateAdCubit.get(context)
                                      //         .linkController
                                      //         .text,
                                      //     formTitle: CreateAdCubit.get(context)
                                      //         .leadMessage
                                      //         .text,
                                      //     formBody: CreateAdCubit.get(context)
                                      //         .leadDesc
                                      //         .text,
                                      //     formButtonType: "VIEW_WEBSITE",
                                      //     destinationType:
                                      //     CreateAdCubit.get(context)
                                      //         .destinationType,
                                      //     formButtonText: CreateAdCubit.get(context).ctaController.text,
                                      //     formWebsiteUrl: CreateAdCubit.get(context).websiteLinkController.text);
                                      // CreateAdCubit.get(context)
                                      //     .isCampaignCreated = true;
                                      // Navigator.of(context).pop();
                                      // print("asfasf" +
                                      //     CreateAdCubit.get(context)
                                      //         .addedQuestions
                                      //         .toString());
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        color: _currentPage == 0 ? Colors.grey : null,
                        onPressed: _currentPage == 0 ? null : _goToPreviousPage,
                      ),
                      Text('${_currentPage + 1} of 4'),
                      IconButton(
                        icon: const Icon(Icons.arrow_forward),
                        color: _currentPage == 3 ? Colors.grey : null,
                        onPressed: () => _currentPage == 3
                            ? null
                            : _goToNextPage(
                                GetSnapChatObjectivesCubit.get(context)
                                    .leadsFormKey),
                      ),
                    ],
                  ),
                ],
              ),
            )
            //     :
            // Expanded(
            //         child: Column(
            //           children: [
            //             Expanded(
            //               child: Center(
            //                 child:
            //                     BlocBuilder<GetFormsCubit, GetLeadsFormsState>(
            //                   builder: (leadContext, leadState) {
            //                     return leadState is GetLeadsFormsStateLoading
            //                         ? const LoadingWidget(
            //                             isCircle: true,
            //                           )
            //                         : leadState is GetLeadsFormsStateError
            //                             ? HandleErrorWidget(
            //                                 fun: () {
            //                                   // GetFormsCubit.get(leadContext)
            //                                   //     .getLeadForms(
            //                                   //   pageAccessToken: instance<
            //                                   //       HiveHelper>()
            //                                   //       .getUser()
            //                                   //       ?.defaultPageAccessToken ??
            //                                   //       CreateAdCubit.get(context)
            //                                   //           .metaPages
            //                                   //           ?.accessToken ??
            //                                   //       "",
            //                                   //   pageId: instance<HiveHelper>()
            //                                   //       .getUser()
            //                                   //       ?.defaultPageId ??
            //                                   //       CreateAdCubit.get(context)
            //                                   //           .metaPages
            //                                   //           ?.id ??
            //                                   //       "",
            //                                   //   context: Constants.navigatorKey
            //                                   //       .currentContext ??
            //                                   //       leadContext,
            //                                   // );
            //                                 },
            //                                 failure: leadState.message)
            //                             : Padding(
            //                                 padding: EdgeInsets.symmetric(
            //                                     horizontal: 20.sp,
            //                                     vertical: 16.sp),
            //                                 child: ListView.builder(
            //                                   padding: EdgeInsets.zero,
            //                                   itemBuilder: (item, index) {
            //                                     return FittedBox(
            //                                       child: Row(
            //                                         mainAxisAlignment:
            //                                             MainAxisAlignment
            //                                                 .spaceBetween,
            //                                         children: [
            //                                           Radio<int>(
            //                                             value: index,
            //                                             groupValue:
            //                                                 GetSnapChatObjectivesCubit
            //                                                         .get(
            //                                                             context)
            //                                                     .formIndex,
            //                                             onChanged:
            //                                                 (int? newValue) {
            //                                               setState(() {
            //                                                 GetSnapChatObjectivesCubit
            //                                                         .get(
            //                                                             context)
            //                                                     .setSelectedForm(
            //                                                         GetFormsCubit.get(
            //                                                                 leadContext)
            //                                                             .forms[
            //                                                                 index]
            //                                                             .id,
            //                                                         index);
            //                                               });
            //                                               print("agdsgdsfs" +
            //                                                   GetSnapChatObjectivesCubit
            //                                                           .get(
            //                                                               context)
            //                                                       .formId
            //                                                       .toString());
            //                                             },
            //                                             activeColor:
            //                                                 AppColors.mainColor,
            //                                           ),
            //                                           Padding(
            //                                             padding:
            //                                                 const EdgeInsets
            //                                                     .symmetric(
            //                                                     horizontal:
            //                                                         8.0),
            //                                             child: SizedBox(
            //                                               width: 200.h,
            //                                               child: CustomText(
            //                                                 text: GetFormsCubit.get(
            //                                                             leadContext)
            //                                                         .forms[
            //                                                             index]
            //                                                         .name ??
            //                                                     "",
            //                                                 maxLines: 10,
            //                                                 textAlign:
            //                                                     TextAlign.left,
            //                                               ),
            //                                             ),
            //                                           ),
            //                                         ],
            //                                       ),
            //                                     );
            //                                   },
            //                                   itemCount:
            //                                       GetFormsCubit.get(leadContext)
            //                                           .forms
            //                                           .length,
            //                                 ),
            //                               );
            //                   },
            //                 ),
            //               ),
            //             ),
            //             SizedBox(
            //               width: 235.w,
            //               child: ButtonWidget(
            //                 text: "Save",
            //                 onTap: () {
            //                   print('2');
            //                   if (GetSnapChatObjectivesCubit.get(context)
            //                           .addedQuestions
            //                           .contains('FIRST_NAME') &&
            //                       GetSnapChatObjectivesCubit.get(context)
            //                           .addedQuestions
            //                           .contains('LAST_NAME') &&
            //                       (GetSnapChatObjectivesCubit.get(context)
            //                               .addedQuestions
            //                               .contains('EMAIL') ||
            //                           GetSnapChatObjectivesCubit.get(context)
            //                               .addedQuestions
            //                               .contains('PHONE_NUMBER'))) {
            //                     CreateSnapChatAdCubit.get(context)
            //                         .snapChatAdModel
            //                         .copyWith(
            //                             formName:
            //                                 GetSnapChatObjectivesCubit
            //                                         .get(context)
            //                                     .leadHeadlineController
            //                                     .text,
            //                             formDescription:
            //                                 GetSnapChatObjectivesCubit.get(
            //                                         context)
            //                                     .leadDesc
            //                                     .text,
            //                             formPrivacyPolicy:
            //                                 GetSnapChatObjectivesCubit.get(
            //                                         context)
            //                                     .linkTextController
            //                                     .text,
            //                             formFieldsType:
            //                                 GetSnapChatObjectivesCubit.get(
            //                                         context)
            //                                     .addedQuestions);
            //                     Navigator.of(context).pop();
            //                   } else {
            //                     showErrorToast(
            //                         'Must select first name ,last name and email or phone number');
            //                   }
            //                   // if (GetSnapChatObjectivesCubit.get(context)
            //                   //         .formId ==
            //                   //     null) {
            //                   //   showErrorToast("Please select you form");
            //                   // } else {
            //                   //   // CreateAdCubit.get(context).adModel =
            //                   //   //     CreateAdCubit.get(context).adModel.copyWith(
            //                   //   //       formId:
            //                   //   //       CreateAdCubit.get(context).formId,
            //                   //   //     );
            //                   //   // CreateAdCubit.get(context).destinationType =
            //                   //   // "ON_AD";
            //                   //   Navigator.pop(context);
            //                   // }
            //                 },
            //               ),
            //             ),
            //             20.verticalSpace,
            //           ],
            //         ),
            //       ),
          ],
        ),
      ),
    );
  }
}

import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_campaign_widget/snapChat_new_campaign_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../create_campaigns/presentation/views/widgets/create_campaign/new_campaign/tabs_widget.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import '../../controllers/snapchat_campaign/snap_chat_campaign_cubit.dart';
import 'existing_snapChat_campaign_widget.dart';

class SnapChatCampaignWidget extends StatefulWidget {
  const SnapChatCampaignWidget({super.key});

  @override
  State<SnapChatCampaignWidget> createState() => _SnapChatCampaignWidgetState();
}

class _SnapChatCampaignWidgetState extends State<SnapChatCampaignWidget> {
  @override
  void initState() {
    // TiktokCampaignCubit.get(context).getCampaigns(
    //     context: context,
    //     advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TiktokCampaignCubit(),
      child: BlocBuilder<SnapChatCampaignCubit, SnapChatCampaignState>(
        builder: (campaignContext, campaignState) {
          return ExpansionTileBorderItem(
            expansionKey: Constants.snapChatCampaignTileKey,
            onExpansionChanged: (val) {
              // TiktokCampaignCubit.get(campaignContext)
              //     .setCampaignExpansionState(val);
            },
            childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
            iconColor: AppColors.secondColor,
            collapsedIconColor: AppColors.secondColor,
            expandedAlignment: Alignment.center,
            expandedCrossAxisAlignment: CrossAxisAlignment.center,
            leading:
                SvgPicture.asset(AppAssets.goal, color: AppColors.mainColor),
            trailing: BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
              builder: (context, state) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularPercentIndicator(
                      circularStrokeCap: CircularStrokeCap.round,
                      radius: 12.0,
                      lineWidth: 5.5,
                      percent:
                          CreateSnapChatAdCubit.get(context).campaignPercentage,
                      linearGradient: Constants.secGradient,
                      backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                      reverse: true,
                    ),
                    const Padding(
                      padding: EdgeInsets.only(left: 10.0),
                      child: CustomText(
                        text: "|",
                        color: AppColors.mainColor,
                        fontSize: 35,
                        fontWeight: FontWeight.w200,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 6.0),
                      child: ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return Constants.secGradient.createShader(bounds);
                        },
                        child: Icon(
                          SnapChatCampaignCubit.get(campaignContext)
                                  .isCampaignTileExpanded
                              ? Icons.expand_less
                              : Icons.expand_more,
                          size: 24.0,
                          color: Colors.white,
                        ),
                      ),
                    )
                  ],
                );
              },
            ),
            title: const CustomText(
                text: 'Campaign',
                color: AppColors.mainColor,
                fontSize: 22,
                fontWeight: FontWeight.w700),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppColors.mainColor)),
            children: [
              const SizedBox(height: 20),
              Container(
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(40),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x3D000000),
                      blurRadius: 13.93,
                      offset: Offset(0, 0),
                      spreadRadius: -3.80,
                    )
                  ],
                ),
                child: TabsWidget(
                  newObject: 'Create New',
                  existObject: 'Use Existing',
                  // selectTab: TiktokCampaignCubit.get(campaignContext)
                  //     .selectCampaignTab,
                  // callback: (tab) {
                  //   if (TiktokCampaignCubit.get(campaignContext)
                  //           .selectCampaignTab !=
                  //       tab) {
                  //     TiktokCampaignCubit.get(campaignContext)
                  //         .changeCampaignTabIndex(tab);
                  //   }
                  // },
                  selectedTab: SnapChatCampaignCubit.get(campaignContext)
                      .selectCampaignTab,
                  onTabChanged: (int tab) {
                    if (SnapChatCampaignCubit.get(campaignContext)
                            .selectCampaignTab !=
                        tab) {
                      SnapChatCampaignCubit.get(campaignContext)
                          .changeCampaignTabIndex(tab);
                    }
                  },
                ),
              ),
              (SnapChatCampaignCubit.get(campaignContext).selectCampaignTab ==
                      0)
                  ? SnapChatNewCampaignWidget(
                      expansionTileKey: Constants.snapChatCampaignTileKey)
                  : const ExistingSnapChatCampaignWidget(
                      // expansionTileKey: Constants.snapChatCampaignTileKey
                      ),
              const SizedBox(height: 25),
            ],
          );
        },
      ),
    );
  }
}

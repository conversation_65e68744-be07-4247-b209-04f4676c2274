import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import '../../controllers/snapChat_objectives/get_snap_chat_objectives_cubit.dart';
import '../../controllers/snapChat_optimizations/get_snap_chat_optimizations_cubit.dart';
import '../../controllers/snapchat_campaign/snap_chat_campaign_cubit.dart';

class ExistingSnapChatCampaignWidget extends StatefulWidget {
  // GlobalKey<ExpansionTileCustomState> expansionTileKey;

  const ExistingSnapChatCampaignWidget({
    super.key,
    // required this.expansionTileKey
  });

  @override
  State<ExistingSnapChatCampaignWidget> createState() =>
      _ExistingSnapChatCampaignWidgetState();
}

class _ExistingSnapChatCampaignWidgetState
    extends State<ExistingSnapChatCampaignWidget> {
  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      await SnapChatCampaignCubit.get(context).getCampaigns(
          context: context,
          accountId: instance.get<HiveHelper>().getSnapAdAccountId() ?? "");
      // CreateSnapChatAdCubit.get(context).campaignPercentage = 0.0;
      // setState(() {});
      // print(
      //     '.,mxcm.vmncx ${CreateSnapChatAdCubit.get(context).campaignPercentage}');
      // print(
      //     'existingTiktokCampaign ${TiktokCampaignCubit.get(context).tiktokCampaigns?.data?.tiktokCampaigns}');
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SnapChatCampaignCubit, SnapChatCampaignState>(
      builder: (context, state) {
        return Column(
          children: [
            const SizedBox(height: 20),
            state is GetSnapChatCampaignsStateLoading
                ? const LoadingWidget(
                    isCircle: true,
                  )
                : SnapChatCampaignCubit.get(context).campaigns?.isEmpty == true
                    ? const SizedBox()
                    : Column(
                        children: [
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Row(
                              children: [
                                CustomText(
                                  text: "select campaign".tr,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                  alignment: AlignmentDirectional.centerStart,
                                  color: Constants.primaryTextColor,
                                ),
                                CustomText(
                                  text: "*",
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w400,
                                  alignment: AlignmentDirectional.centerStart,
                                  color: Constants.redColor,
                                ),
                              ],
                            ),
                          ),
                          20.verticalSpace,
                          ExpansionTileItem(
                            expansionKey: Constants.snapChatOptimizationKey,
                            onExpansionChanged: (val) {},
                            childrenPadding: EdgeInsets.zero,
                            iconColor: AppColors.secondColor,
                            collapsedIconColor: AppColors.secondColor,
                            expandedAlignment: Alignment.center,
                            expandedCrossAxisAlignment:
                                CrossAxisAlignment.center,
                            trailing: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(left: 6.0),
                                  child: Icon(
                                    Icons.expand_more,
                                    size: 30.0,
                                    color: Constants.darkColor,
                                  ),
                                )
                              ],
                            ),
                            title: SnapChatCampaignCubit.get(context)
                                        .selectedCampaign !=
                                    null
                                ? CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: SnapChatCampaignCubit.get(context)
                                            .selectedCampaign
                                            ?.campaign
                                            ?.name ??
                                        "")
                                : AccountHintText(
                                    isDefaultHint: false,
                                    hint: 'choose your campaign'.tr,
                                  ),
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x3F000000),
                                  blurRadius: 40,
                                  offset: Offset(0, 0),
                                  spreadRadius: -10,
                                )
                              ],
                            ),
                            children: [
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: SnapChatCampaignCubit.get(context)
                                        .campaigns
                                        ?.length ??
                                    0,
                                // 3 for the first text + 3 for the second text
                                itemBuilder: (context, index) {
                                  // Check if the index is for the first section title
                                  // if (index == 0) {
                                  //   return Padding(
                                  //     padding: const EdgeInsets.all(16.0),
                                  //     child: CustomText(
                                  //       text: 'Drive More Visits Objectives'.tr,
                                  //       fontSize: 13.sp,
                                  //       fontWeight: FontWeight.w600,
                                  //     ),
                                  //   );
                                  // } else
                                  // if (index >=
                                  //       1 &&
                                  //   index <=
                                  //       3)
                                  // {
                                  // Return the first three items
                                  final campIndex =
                                      index; // Adjust index for the optimization list
                                  return InkWell(
                                    onTap: () {
                                      SnapChatCampaignCubit.get(context)
                                          .setSelectedCampaign(
                                              SnapChatCampaignCubit.get(context)
                                                  .campaigns?[campIndex]);
                                      CreateSnapChatAdCubit.get(context)
                                          .campaignPercentage = 1.0;
                                      setState(() {});
                                      // GetBillingEventsCubit
                                      //         .get(
                                      //             billContext)
                                      //     .getBillingEvents(
                                      //   context:
                                      //       context,
                                      //   optimizationId:
                                      //       GetOptimizationsCubit.get(optContext)
                                      //               .opt[optIndex]
                                      //               .id ??
                                      //           0,
                                      // );
                                      // CreateAdCubit.get(
                                      //         context)
                                      //     .billingEvent = null;
                                      Constants
                                          .snapChatOptimizationKey.currentState
                                          ?.collapse();
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Constants.gray.withOpacity(0.15),
                                        border: Border.symmetric(
                                          horizontal: BorderSide(
                                              color: Constants.gray
                                                  .withOpacity(0.3)),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 18, horizontal: 20),
                                        child: CustomText(
                                          maxLines: 3,
                                          fontSize: 12.sp,
                                          text:
                                              SnapChatCampaignCubit.get(context)
                                                      .campaigns?[index]
                                                      .campaign
                                                      ?.name ??
                                                  "",
                                        ),
                                      ),
                                    ),
                                  );
                                  // }
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
            const SizedBox(height: 20),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            SizedBox(
              width: 235.w,
              child: ButtonWidget(
                text: "Save",
                onTap: () {
                  SnapChatCampaignCubit.get(context)
                      .campaignNameController
                      .clear();
                  GetSnapChatObjectivesCubit.get(context).objective = null;
                  GetSnapChatObjectivesCubit.get(context).selectedGoal = null;
                  GetSnapChatOptimizationsCubit.get(context).optimization =
                      null;
                  CreateSnapChatAdCubit.get(context).snapChatAdModel =
                      CreateSnapChatAdCubit.get(context)
                          .snapChatAdModel
                          .copyWith(
                              existingCampaignId:
                                  SnapChatCampaignCubit.get(context)
                                      .selectedCampaign
                                      ?.campaign
                                      ?.id,
                              objective: null,
                              optimizationGoal: null,
                              campaignName: null);
                  CreateSnapChatAdCubit.get(context).completeCampaign(true);
                  Constants.snapChatCampaignTileKey.currentState?.collapse();
                  setState(() {});
                  // if (TiktokCampaignCubit.get(context)
                  //     .campaignFormKey
                  //     .currentState!
                  //     .validate()) {
                  //   widget.expansionTileKey.currentState?.collapse();
                  //   TiktokAdCubit.get(context).tiktokAdModel =
                  //       TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                  //           campaignName: TiktokCampaignCubit.get(context)
                  //                   .selectedCampaign
                  //                   ?.campaignName ??
                  //               "",
                  //           objectiveType: TiktokCampaignCubit.get(context)
                  //               .selectedCampaign
                  //               ?.objectiveType,
                  //           optimizationGoal: "");
                  //   TiktokAdCubit.get(context).isTiktokCampaign = true;
                  //   // TiktokAdCubit.get(context).createAD(
                  //   //     context: context,
                  //   //     imagesFiles: [],
                  //   //     videosFiles: [],
                  //   //     advertiserId:
                  //   //         instance.get<HiveHelper>().getAdvertiserId() ??
                  //   //             "",
                  //   //     campaignName: TiktokCampaignCubit.get(ctx1)
                  //   //         .campaignNameController
                  //   //         .text,
                  //   //     objectiveType: TiktokCampaignCubit.get(ctx1)
                  //   //             .objective
                  //   //             ?.actualName ??
                  //   //         "",
                  //   //     optimizationGoal: TiktokCampaignCubit.get(ctx1)
                  //   //             .optimization
                  //   //             ?.actualName ??
                  //   //         "");
                  // }
                },
              ),
            ),
          ],
        );
      },
    );
  }
}

import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_campaign_widget/snapChat_leads_dialoge_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_campaign/tiktok_objective_widget.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';

import 'package:ads_dv/utils/res/validations.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../create_campaigns/data/models/question.dart';
import '../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../controllers/snapChat_objectives/get_snap_chat_objectives_cubit.dart';
import '../../controllers/snapChat_optimizations/get_snap_chat_optimizations_cubit.dart';
import '../../controllers/snapchat_campaign/snap_chat_campaign_cubit.dart';

class SnapChatNewCampaignWidget extends StatefulWidget {
  GlobalKey<ExpansionTileCustomState> expansionTileKey;

  SnapChatNewCampaignWidget({super.key, required this.expansionTileKey});

  @override
  State<SnapChatNewCampaignWidget> createState() =>
      _SnapChatNewCampaignWidgetState();
}

class _SnapChatNewCampaignWidgetState extends State<SnapChatNewCampaignWidget> {
  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      await GetSnapChatObjectivesCubit.get(context)
          .getObjectives(context: context);
    });
    super.initState();
  }

  void _createLeadFormDialog(BuildContext context,
      GetSnapChatObjectivesCubit getSnapChatObjectivesCubit) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppColors.whiteColor,
          child: SnapChatPageViewDialog(
            getSnapChatObjectivesCubit: GetSnapChatObjectivesCubit.get(context),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SnapChatCampaignCubit, SnapChatCampaignState>(
      builder: (ctx1, camState) {
        return BlocBuilder<GetSnapChatOptimizationsCubit,
            GetSnapChatOptimizationsState>(
          builder: (ctx2, optState) {
            return Column(
              children: [
                const SizedBox(height: 20),
                Form(
                  key: SnapChatCampaignCubit.get(ctx1).campaignFormKey,
                  child: CustomTextFormField(
                    label: "Campaign Name",
                    showIsReqiredFlag: true,
                    textFontSize: 12,
                    key: const ValueKey('campaign_name'),
                    hintText: "Campaign Name",
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    controller:
                        SnapChatCampaignCubit.get(ctx1).campaignNameController,
                    validator: (value) =>
                        AppValidator.validateIdentity(value, context),
                    onChanged: (va) {
                      // if (SnapChatCampaignCubit.get(ctx1)
                      //         .campaignNameController
                      //         .text
                      //         .isNotEmpty ==
                      //     true) {
                      //   TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage =
                      //       0.25;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                      //   setState(() {});
                      // } else {
                      //   TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage =
                      //       0.0;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                      //   setState(() {});
                      // }
                    },
                  ),
                ),
                const SizedBox(height: 20),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Divider(color: Constants.textColor),
                ),
                const SizedBox(height: 5),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: CustomText(
                    text: "Select Your Goal",
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    alignment: AlignmentDirectional.centerStart,
                    color: Constants.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 20),
                BlocBuilder<GetSnapChatObjectivesCubit,
                    GetSnapChatObjectivesState>(
                  builder: (context, snapChatObjState) {
                    if (snapChatObjState is GetSnapChatObjectivesStateLoaded) {
                      return AlignedGridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: 3,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        itemCount: snapChatObjState.data.length,
                        itemBuilder: (context, index) {
                          return TiktokObjectiveWidget(
                            // icon: TiktokCampaignCubit.get(ctx1).goals[index].icon,
                            isSelected: index ==
                                GetSnapChatObjectivesCubit.get(context)
                                    .selectedGoal,
                            callback: (_) {
                              print(
                                  'objTest ${snapChatObjState.data[index].actualName}');
                              GetSnapChatObjectivesCubit.get(context)
                                  .setSelectedGoal(
                                      index, snapChatObjState.data[index]);
                              GetSnapChatOptimizationsCubit.get(context)
                                  .getOptimizations(
                                      context: context,
                                      objectiveActualName: snapChatObjState
                                          .data[index].actualName!);
                              if (CreateSnapChatAdCubit.get(context)
                                      .campaignPercentage >
                                  1.0) {
                                CreateSnapChatAdCubit.get(context)
                                    .campaignPercentage = 0.66;
                              }

                              setState(() {});
                              // print(
                              //     'asdasdvxcxc ${CreateSnapChatAdCubit.get(context).campaignPercentage}');
                              // if (TiktokCampaignCubit.get(ctx1).selectedGoal !=
                              //         null &&
                              //     TiktokCampaignCubit.get(ctx1)
                              //             .isObjectiveUpdated ==
                              //         false) {
                              //   TiktokCampaignCubit.get(ctx1)
                              //       .isObjectiveUpdated = true;
                              //   TiktokCampaignCubit.get(ctx1)
                              //           .tiktokCampaignPercentage =
                              //       TiktokCampaignCubit.get(ctx1)
                              //               .tiktokCampaignPercentage +
                              //           0.50;
                              //   print(
                              //       'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                              setState(() {});
                              // }
                            },
                            name: snapChatObjState.data[index].showName ?? "",
                            index: index,
                            isSnapChat: true,
                          );
                        },
                      );
                    } else {
                      return const Center(child: CircularProgressIndicator());
                    }
                  },
                ),
                const SizedBox(height: 20),
                optState is GetSnapChatOptimizationsStateLoading
                    ? const LoadingWidget(
                        isCircle: true,
                      )
                    : GetSnapChatOptimizationsCubit.get(ctx2).opt.isEmpty
                        ? const SizedBox()
                        : Column(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  children: [
                                    CustomText(
                                      text: "Advertising Objective".tr,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.primaryTextColor,
                                    ),
                                    CustomText(
                                      text: "*",
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.redColor,
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              ExpansionTileItem(
                                expansionKey: Constants.snapChatOptimizationKey,
                                onExpansionChanged: (val) {},
                                childrenPadding: EdgeInsets.zero,
                                iconColor: AppColors.secondColor,
                                collapsedIconColor: AppColors.secondColor,
                                expandedAlignment: Alignment.center,
                                expandedCrossAxisAlignment:
                                    CrossAxisAlignment.center,
                                trailing: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(left: 6.0),
                                      child: Icon(
                                        Icons.expand_more,
                                        size: 30.0,
                                        color: Constants.darkColor,
                                      ),
                                    )
                                  ],
                                ),
                                title: GetSnapChatOptimizationsCubit.get(ctx1)
                                            .optimization !=
                                        null
                                    ? CustomText(
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                        text: GetSnapChatOptimizationsCubit.get(
                                                    ctx1)
                                                .optimization
                                                ?.showName ??
                                            "")
                                    : AccountHintText(
                                        isDefaultHint: false,
                                        hint: 'Advertising Objective'.tr,
                                      ),
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x3F000000),
                                      blurRadius: 40,
                                      offset: Offset(0, 0),
                                      spreadRadius: -10,
                                    )
                                  ],
                                ),
                                children: [
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount:
                                        GetSnapChatOptimizationsCubit.get(ctx2)
                                            .opt
                                            .length,
                                    // 3 for the first text + 3 for the second text
                                    itemBuilder: (context, index) {
                                      // Check if the index is for the first section title
                                      // if (index == 0) {
                                      //   return Padding(
                                      //     padding: const EdgeInsets.all(16.0),
                                      //     child: CustomText(
                                      //       text: 'Drive More Visits Objectives'
                                      //           .tr,
                                      //       fontSize: 13.sp,
                                      //       fontWeight: FontWeight.w600,
                                      //     ),
                                      //   );
                                      // } else
                                      // if (index >=
                                      //       1 &&
                                      //   index <=
                                      //       3)
                                      // {
                                      // Return the first three items
                                      final optIndex =
                                          index; // Adjust index for the optimization list
                                      return InkWell(
                                        onTap: () {
                                          print(
                                              'opGoalTest ${GetSnapChatOptimizationsCubit.get(ctx2).opt[optIndex].actualName}');
                                          GetSnapChatOptimizationsCubit.get(
                                                  ctx1)
                                              .setSelectedOptimization(
                                                  GetSnapChatOptimizationsCubit
                                                          .get(ctx2)
                                                      .opt[optIndex]);

                                          GetSnapChatOptimizationsCubit.get(
                                                          ctx1)
                                                      .optimization
                                                      ?.actualName ==
                                                  "LEAD_FORM_SUBMISSIONS"
                                              ? _createLeadFormDialog(
                                                  context,
                                                  GetSnapChatObjectivesCubit
                                                      .get(ctx1))
                                              : null;
                                          if (GetSnapChatOptimizationsCubit.get(
                                                      ctx1)
                                                  .optimization
                                                  ?.actualName !=
                                              "LEAD_FORM_SUBMISSIONS") {
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .leadDesc
                                                .text = "";
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .leadMessage
                                                .text = "";
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .ctaController
                                                .text = "";
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .websiteLinkController
                                                .text = "";
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .linkTextController
                                                .text = "";
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .linkController
                                                .text = "";
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .leadHeadlineController
                                                .text = "";
                                            // CreateAdCubit.get(context).addedQuestions=[];
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .langValue = null;
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .langIndex = null;

                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .formId = null;
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .formIndex = null;
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .addedQuestions
                                                .clear();
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .questions
                                                .clear();
                                            GetSnapChatObjectivesCubit.get(
                                                    context)
                                                .questions = [
                                              // Question(
                                              //     name: 'What is your city?',
                                              //     value: 'CITY',
                                              //     isChecked: false),
                                              Question(
                                                  name:
                                                      'What is your company name?',
                                                  value: 'COMPANY_NAME',
                                                  isChecked: false),
                                              // Question(
                                              //     name: 'What is your country name?',
                                              //     value: 'COUNTRY',
                                              //     isChecked: false),
                                              // Question(
                                              //     name: 'What is your gender?',
                                              //     value: 'GENDER',
                                              //     isChecked: false),
                                              Question(
                                                  name:
                                                      'What is your first name?',
                                                  value: 'FIRST_NAME',
                                                  isChecked: false),
                                              // Question(
                                              //     name: 'What is your full name?',
                                              //     value: 'FULL_NAME',
                                              //     isChecked: false),
                                              Question(
                                                  name:
                                                      'What is your job title?',
                                                  value: 'JOB_TITLE',
                                                  isChecked: false),
                                              Question(
                                                  name:
                                                      'What is your date of birth?',
                                                  value: 'BIRTHDAY_DATE',
                                                  isChecked: false),
                                              Question(
                                                  name: 'What is your email?',
                                                  value: 'EMAIL',
                                                  isChecked: false),
                                              Question(
                                                  name:
                                                      'What is your last name?',
                                                  value: 'LAST_NAME',
                                                  isChecked: false),
                                              Question(
                                                  name:
                                                      'What is your postal code?',
                                                  value: 'POSTAL_CODE',
                                                  isChecked: false),
                                              Question(
                                                  name: 'What is your phone?',
                                                  value: 'PHONE_NUMBER',
                                                  isChecked: false),
                                              // Question(
                                              //     name: 'What is your state?',
                                              //     value: 'STATE',
                                              //     isChecked: false),
                                              Question(
                                                  name: 'What is your address?',
                                                  value: 'ADDRESS',
                                                  isChecked: false),
                                            ];
                                          }
                                          // if (TiktokCampaignCubit.get(ctx1)
                                          //             .optimization !=
                                          //         null &&
                                          //     TiktokCampaignCubit.get(ctx1)
                                          //             .isOptimizeUpdated ==
                                          //         false) {
                                          //   TiktokCampaignCubit.get(ctx1)
                                          //       .isOptimizeUpdated = true;
                                          //   TiktokCampaignCubit.get(ctx1)
                                          //           .tiktokCampaignPercentage =
                                          //       TiktokCampaignCubit.get(ctx1)
                                          //               .tiktokCampaignPercentage +
                                          //           0.25;
                                          //   print(
                                          //       'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                                          //   setState(() {});
                                          // }
                                          // GetBillingEventsCubit
                                          //         .get(
                                          //             billContext)
                                          //     .getBillingEvents(
                                          //   context:
                                          //       context,
                                          //   optimizationId:
                                          //       GetOptimizationsCubit.get(optContext)
                                          //               .opt[optIndex]
                                          //               .id ??
                                          //           0,
                                          // );
                                          // CreateAdCubit.get(
                                          //         context)
                                          //     .billingEvent = null;
                                          if (CreateSnapChatAdCubit.get(context)
                                                  .campaignPercentage <=
                                              0.66) {
                                            CreateSnapChatAdCubit.get(context)
                                                    .campaignPercentage =
                                                CreateSnapChatAdCubit.get(
                                                            context)
                                                        .campaignPercentage +
                                                    0.34;
                                          }
                                          setState(() {});
                                          Constants.snapChatOptimizationKey
                                              .currentState
                                              ?.collapse();
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Constants.gray
                                                .withOpacity(0.15),
                                            border: Border.symmetric(
                                              horizontal: BorderSide(
                                                  color: Constants.gray
                                                      .withOpacity(0.3)),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 18, horizontal: 20),
                                            child: CustomText(
                                              maxLines: 3,
                                              fontSize: 12.sp,
                                              text:
                                                  GetSnapChatOptimizationsCubit
                                                              .get(ctx2)
                                                          .opt[optIndex]
                                                          .showName ??
                                                      "",
                                            ),
                                          ),
                                        ),
                                      );
                                      // }
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                // const SizedBox(height: 20),
                // const Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                //   child: Divider(color: Constants.textColor),
                // ),
                // const SizedBox(height: 20),
                // Padding(
                //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     crossAxisAlignment: CrossAxisAlignment.center,
                //     children: [
                //       CustomText(
                //         text: 'Create split test',
                //         fontSize: 12.sp,
                //         color: Constants.primaryTextColor,
                //         fontWeight: FontWeight.w400,
                //         alignment: AlignmentDirectional.centerStart,
                //       ),
                //       CustomSwitch(
                //         value: TiktokCampaignCubit.get(ctx1).isCreateActive,
                //         onChanged: (newValue) {
                //           TiktokCampaignCubit.get(ctx1)
                //               .changeCreateStatus(newValue);
                //         },
                //       ),
                //     ],
                //   ),
                // ),
                SizedBox(height: 50.h),
                SizedBox(
                  width: 235.w,
                  child: ButtonWidget(
                    text: "Save",
                    onTap: () {
                      if (SnapChatCampaignCubit.get(ctx1)
                          .campaignFormKey
                          .currentState!
                          .validate()) {
                        widget.expansionTileKey.currentState?.collapse();
                        SnapChatCampaignCubit.get(context).selectedCampaign =
                            null;
                        CreateSnapChatAdCubit.get(context).snapChatAdModel =
                            CreateSnapChatAdCubit.get(context)
                                .snapChatAdModel
                                .copyWith(
                                    campaignName:
                                        SnapChatCampaignCubit.get(ctx1)
                                            .campaignNameController
                                            .text,
                                    adAccountId:
                                        instance
                                            .get<HiveHelper>()
                                            .getSnapAdAccountId(),
                                    optimizationGoal:
                                        GetSnapChatOptimizationsCubit.get(ctx2)
                                            .optimization
                                            ?.actualName,
                                    objective:
                                        GetSnapChatObjectivesCubit.get(context)
                                            .objective
                                            ?.actualName,
                                    existingCampaignId: null);
                        CreateSnapChatAdCubit.get(context)
                            .completeCampaign(true);
                        CreateSnapChatAdCubit.get(context)
                            .changeUrlViewStatus();
                        CreateSnapChatAdCubit.get(context)
                            .changePhoneNumberStatus();
                        setState(() {});
                        // TiktokAdCubit.get(context).tiktokAdModel =
                        //     TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                        //         campaignName: TiktokCampaignCubit.get(ctx1)
                        //             .campaignNameController
                        //             .text,
                        //         objectiveType: TiktokCampaignCubit.get(ctx1)
                        //             .objective
                        //             ?.actualName,
                        //         optimizationGoal: TiktokCampaignCubit.get(ctx1)
                        //             .optimization
                        //             ?.actualName);
                        // TiktokAdCubit.get(context).isTiktokCampaign = true;
                        // TiktokAdCubit.get(context).createAD(
                        //     context: context,
                        //     imagesFiles: [],
                        //     videosFiles: [],
                        //     advertiserId:
                        //         instance.get<HiveHelper>().getAdvertiserId() ??
                        //             "",
                        //     campaignName: TiktokCampaignCubit.get(ctx1)
                        //         .campaignNameController
                        //         .text,
                        //     objectiveType: TiktokCampaignCubit.get(ctx1)
                        //             .objective
                        //             ?.actualName ??
                        //         "",
                        //     optimizationGoal: TiktokCampaignCubit.get(ctx1)
                        //             .optimization
                        //             ?.actualName ??
                        //         "");
                      }
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

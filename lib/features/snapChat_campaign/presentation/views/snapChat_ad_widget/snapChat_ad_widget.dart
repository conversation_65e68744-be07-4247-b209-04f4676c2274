import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_ad_widget/snapChat_destination_widget.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/custom_text_field.dart';
import '../../../../create_campaigns/presentation/views/widgets/create_campaign/new_campaign/tabs_widget.dart';
import '../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';

class SnapChatAdCreativeWidget extends StatefulWidget {
  // CreateAdCubit adCubit;
  // GlobalKey<ExpansionTileCustomState> expansionTileKey;

  const SnapChatAdCreativeWidget({
    super.key,
    // required this.expansionTileKey
  });

  @override
  State<SnapChatAdCreativeWidget> createState() =>
      _SnapChatAdCreativeWidgetState();
}

class _SnapChatAdCreativeWidgetState extends State<SnapChatAdCreativeWidget> {
  late int selectTab;

  // late int postsSelectTab;

  @override
  void initState() {
    selectTab = 0;
    Future.delayed(const Duration(), () async {
      await CreateSnapChatAdCubit.get(context)
          .getSnapChatProfilesId(context: context);
      await CreateSnapChatAdCubit.get(context)
          .getSnapChatPhoneNumbers(context: context);
    });
    // selectTab = (widget.adCubit.optimization != null &&
    //         (widget.adCubit.optimization?.actualName ==
    //                 "TWO_SECOND_CONTINUOUS_VIDEO_VIEWS" ||
    //             widget.adCubit.optimization?.actualName == "THRUPLAY"))
    //     ? 1
    //     : 0;
    // CreateAdCubit.get(context).changePostValue(null);
    // CreateAdCubit.get(context).changeSocialPostsTabIndex(0);
    // if (CreateAdCubit.get(context).selectSocialPostsTab == 0) {
    //   GetPostsCubit.get(context).getFbPosts(
    //     context: context,
    //     pageAccessToken:
    //         instance<HiveHelper>().getUser()?.defaultPageAccessToken ??
    //             CreateAdCubit.get(context).metaPages?.accessToken ??
    //             "",
    //     pageId: instance<HiveHelper>().getUser()?.defaultPageId ??
    //         CreateAdCubit.get(context).metaPages?.id ??
    //         "",
    //   );
    // }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant SnapChatAdCreativeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    selectTab = 0;
    // Only change the tab if the optimization has changed significantly
    // if (widget.adCubit.optimization?.actualName !=
    //     oldWidget.adCubit.optimization?.actualName) {
    //   setState(() {
    //     selectTab = (widget.adCubit.optimization != null &&
    //             (widget.adCubit.optimization?.actualName ==
    //                     "TWO_SECOND_CONTINUOUS_VIDEO_VIEWS" ||
    //                 widget.adCubit.optimization?.actualName == "THRUPLAY"))
    //         ? 1
    //         : 0;
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
      builder: (context, state) {
        return ExpansionTileItem(
          onExpansionChanged: (val) {
            CreateAdCubit.get(context).setAdCreativeExpansionState(val);
          },
          expansionKey: Constants.snapChatAdTileKey,
          childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
          iconColor: AppColors.secondColor,
          collapsedIconColor: AppColors.secondColor,
          expandedAlignment: Alignment.center,
          expandedCrossAxisAlignment: CrossAxisAlignment.center,
          leading: SvgPicture.asset(AppAssets.ad, color: AppColors.mainColor),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularPercentIndicator(
                circularStrokeCap: CircularStrokeCap.round,
                radius: 12.0,
                lineWidth: 5.5,
                percent: CreateSnapChatAdCubit.get(context).adPercentage,
                linearGradient: Constants.secGradient,
                backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                reverse: true,
                // isAdSet: false,
                // isDemographic: false,
                // isAdCreative: true,
                // isLocation: false,
                // isTargeting: false,
                // adCubit: CreateAdCubit.get(context)
              ),
              const Padding(
                padding: EdgeInsets.only(left: 10.0),
                child: CustomText(
                  text: "|",
                  color: AppColors.mainColor,
                  fontSize: 35,
                  fontWeight: FontWeight.w200,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 6.0),
                child: ShaderMask(
                  shaderCallback: (Rect bounds) {
                    return const LinearGradient(
                      colors: [
                        Color(0xFFFF006F),
                        Color(0xFFF6BA00),
                      ],
                    ).createShader(bounds);
                  },
                  child: Icon(
                    CreateAdCubit.get(context).isAdCreativeTileExpanded
                        ? Icons.expand_less
                        : Icons.expand_more,
                    size: 24.0,
                    color: Colors
                        .white, // This color will be replaced by the gradient
                  ),
                ),
              )
            ],
          ),

          title: CustomText(
              text: 'Ad'.tr,
              color: AppColors.mainColor,
              fontSize: 22,
              fontWeight: FontWeight.w700),

          // childrenPadding:  const EdgeInsets.symmetric(
          //     horizontal: 10, vertical:20),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: AppColors.mainColor)
              // color: AppColors.borderColor,
              ),
          children: [
            Column(
              children: [
                const SizedBox(height: 25),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CustomText(
                      text: 'Ad Name'.tr,
                      color: AppColors.mainColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                CustomTextFormField(
                  controller: CreateSnapChatAdCubit.get(context).adName,
                  textFontSize: 12,
                  borderRadius: 12,
                  key: const ValueKey('Ad Name'),
                  hintText: "Ad Name".tr,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.text,
                  validator: (value) =>
                      AppValidator.validateIdentity(value, context),
                  onChanged: (val) {
                    // if (CreateSnapChatAdCubit.get(context).adName.text.isNotEmpty) {
                    //   CreateAdCubit.get(context).updateAdCreativeProcess1();
                    // } else {
                    //   CreateAdCubit.get(context).undoAdCreativeProcess1();
                    // }
                  },
                ),

                const SizedBox(height: 20),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Divider(color: Constants.textColor),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Row(
                    children: [
                      CustomText(
                        text: "Public Profiles".tr,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        alignment: AlignmentDirectional.centerStart,
                        color: Constants.primaryTextColor,
                      ),
                      CustomText(
                        text: "*",
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        alignment: AlignmentDirectional.centerStart,
                        color: Constants.redColor,
                      ),
                    ],
                  ),
                ),
                20.verticalSpace,
                ExpansionTileItem(
                  expansionKey: Constants.savedAudienceKey,
                  onExpansionChanged: (val) {},
                  childrenPadding: EdgeInsets.zero,
                  iconColor: AppColors.secondColor,
                  collapsedIconColor: AppColors.secondColor,
                  expandedAlignment: Alignment.center,
                  expandedCrossAxisAlignment: CrossAxisAlignment.center,
                  trailing: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 6.0),
                        child: Icon(
                          Icons.expand_more,
                          size: 30.0,
                          color: Constants.darkColor,
                        ),
                      )
                    ],
                  ),
                  title: CreateSnapChatAdCubit.get(context)
                              .selectedPublicProfiles !=
                          null
                      ? CustomText(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          text: CreateSnapChatAdCubit.get(context)
                                  .selectedPublicProfiles
                                  ?.displayName ??
                              "")
                      : AccountHintText(
                          isDefaultHint: false,
                          hint: 'Public Profiles'.tr,
                        ),
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x3F000000),
                        blurRadius: 40,
                        offset: Offset(0, 0),
                        spreadRadius: -10,
                      )
                    ],
                  ),
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: CreateSnapChatAdCubit.get(context)
                          .publicProfiles
                          .length,
                      // 3 for the first text + 2 for the last text
                      itemBuilder: (context, index) {
                        // Check if the index is for the text before the first three items
                        // if (index == 0) {
                        //   return Padding(
                        //     padding: const EdgeInsets.all(16.0),
                        //     child: CustomText(
                        //         text: 'Brand Awareness'.tr,
                        //         fontSize: 13.sp,
                        //         fontWeight: FontWeight.w600),
                        //   );
                        // } else
                        //   if (index >= 1
                        //     // && index <= 3
                        // )
                        // {
                        // Return the first three items
                        // final optIndex =
                        //     index - 1; // Adjust index for the optimization list
                        return InkWell(
                          onTap: () {
                            CreateSnapChatAdCubit.get(context)
                                .selectPublicProfile(
                                    CreateSnapChatAdCubit.get(context)
                                        .publicProfiles[index]);
                            CreateSnapChatAdCubit.get(context).snapChatAdModel =
                                CreateSnapChatAdCubit.get(context)
                                    .snapChatAdModel
                                    .copyWith(
                                        profileId:
                                            CreateSnapChatAdCubit.get(context)
                                                .selectedPublicProfiles
                                                ?.id);
                            if (CreateSnapChatAdCubit.get(context)
                                    .adPercentage <
                                0.40) {
                              CreateSnapChatAdCubit.get(context).adPercentage =
                                  0.40;
                            }

                            Constants.savedAudienceKey.currentState?.collapse();
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Constants.gray.withOpacity(0.15),
                              border: Border.symmetric(
                                horizontal: BorderSide(
                                    color: Constants.gray.withOpacity(0.3)),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 18, horizontal: 20),
                              child: CustomText(
                                maxLines: 3,
                                fontSize: 12.sp,
                                text: CreateSnapChatAdCubit.get(context)
                                        .publicProfiles[index]
                                        .displayName ??
                                    "",
                              ),
                            ),
                          ),
                        );
                        // }
                      },
                    ),
                  ],
                ),

                SizedBox(
                  height: 20.0.h,
                ),
                Container(
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(40),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x3D000000),
                        blurRadius: 13.93,
                        offset: Offset(0, 0),
                        spreadRadius: -3.80,
                      )
                    ],
                  ),
                  child: TabsWidget(
                    newObject: 'Image Advertising'.tr,
                    existObject: 'Video Advertising'.tr,
                    selectedTab: selectTab,
                    onTabChanged: (tab) {
                      selectTab = tab;
                      setState(() {});
                    },
                  ),
                ),
                // (selectTab == 0)
                //     ? const AdImageWidget()
                //     : const AdVideoWidget(),
                SnapChatDestinationWidget(
                  selectedTab: selectTab,
                  isPrevPost: false,
                ),
                SizedBox(
                  width: 235.w,
                  child: ButtonWidget(
                    text: "Save".tr,
                    onTap: () async {
                      if (CreateSnapChatAdCubit.get(context)
                                  .urlFieldViewRequired ==
                              true &&
                          CreateSnapChatAdCubit.get(context)
                                  .webSiteLink
                                  .text
                                  .isEmpty ==
                              true) {
                        showErrorToast('must enter your website link');
                        return;
                      }
                      if (CreateSnapChatAdCubit.get(context)
                                  .phoneFieldViewRequired ==
                              true &&
                          CreateSnapChatAdCubit.get(context).selectedNumber ==
                              null) {
                        showErrorToast('must choose your phone');
                        return;
                      }
                      Constants.snapChatAdTileKey.currentState?.collapse();
                      CreateSnapChatAdCubit.get(context)
                          .snapChatAdModel = CreateSnapChatAdCubit.get(
                              context)
                          .snapChatAdModel
                          .copyWith(
                              // creativeName: CreateSnapChatAdCubit.get(context)
                              //         .adCreativeName
                              //         .text ??
                              //     "",
                              adName: CreateSnapChatAdCubit.get(context).adName.text ??
                                  "",
                              callToAction:
                                  CreateSnapChatAdCubit.get(context).type,
                              countryCode: CreateSnapChatAdCubit.get(context)
                                  .countryCode,
                              file: CreateSnapChatAdCubit.get(context)
                                      .adImages
                                      .isNotEmpty
                                  ? CreateSnapChatAdCubit.get(context)
                                      .adImages
                                      .first
                                  : CreateSnapChatAdCubit.get(context)
                                      .adVideo
                                      .first,
                              mediaType: CreateSnapChatAdCubit.get(context)
                                      .adImages
                                      .isNotEmpty
                                  ? "IMAGE"
                                  : "VIDEO",
                              link: CreateSnapChatAdCubit.get(context)
                                  .webSiteLink
                                  .text
                              // lifeTimeBudgetMicro:
                              //     SnapChatAdSetCubit.get(context)
                              //         .dailyBudget
                              //         .text,
                              // startTime:
                              //     SnapChatAdSetCubit.get(context).startDate.text,
                              // endTime:
                              //     SnapChatAdSetCubit.get(context).endDate.text,
                              );
                      print(
                          'imageOrVideoAdded ${CreateSnapChatAdCubit.get(context).snapChatAdModel.file} ${CreateSnapChatAdCubit.get(context).snapChatAdModel.mediaType}');
                      CreateSnapChatAdCubit.get(context).completeAd(true);
                      setState(() {});
                      // print(
                      //     'snapChatModel ${CreateSnapChatAdCubit.get(context).adImages}');
                      // await CreateSnapChatAdCubit.get(context).createAD(
                      //     context: context,
                      //     imagesFiles: CreateSnapChatAdCubit.get(context).adImages,
                      //     videosFiles: CreateSnapChatAdCubit.get(context).adVideo,
                      //     snapChatAdModel:
                      //         CreateSnapChatAdCubit.get(context).snapChatAdModel);
                      // else {
                      print("sdfsdfjkdlm,bcnx.vb");
                      //   // Show error toast if the form is invalid
                      //   showErrorToast(
                      //       "Please fill in all the required fields correctly.");
                      // }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 25),
          ],
        );
      },
    );
  }
}

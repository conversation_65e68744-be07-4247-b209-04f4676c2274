import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_ad_widget/snapChat_ad_image_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_ad_widget/snapChat_ad_video_image.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import '../../../../create_campaigns/data/models/call_to_action.dart';
import '../../../../create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import '../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../../data/models/attachment_model.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';

class SnapChatDestinationWidget extends StatefulWidget {
  int selectedTab = 0;
  bool isPrevPost = false;

  SnapChatDestinationWidget(
      {super.key, required this.selectedTab, required this.isPrevPost});

  @override
  State<SnapChatDestinationWidget> createState() =>
      _SnapChatDestinationWidgetState();
}

class _SnapChatDestinationWidgetState extends State<SnapChatDestinationWidget> {
  bool isSelectedDestination = false;
  int? selectedDestinationIndex;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      bloc: CreateAdCubit.get(context),
      builder: (context, state) {
        return Column(
          children: [
            if (widget.isPrevPost == false) ...[
              (widget.selectedTab == 0)
                  ? SnapChatAdImageWidget(
                      isDestination: true,
                    )
                  : const SnapChatAdVideoWidget(),
            ],
            SizedBox(height: 25.h),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.start,
            //   children: [
            //     CustomText(
            //       text: 'CreativeName'.tr,
            //       color: AppColors.mainColor,
            //       fontSize: 14,
            //       fontWeight: FontWeight.w400,
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 10),
            // CustomTextFormField(
            //   controller: CreateSnapChatAdCubit.get(context).adCreativeName,
            //   textFontSize: 12,
            //   borderRadius: 12,
            //   key: const ValueKey('Creative Name'),
            //   hintText: "CreativeName".tr,
            //   textInputAction: TextInputAction.next,
            //   keyboardType: TextInputType.text,
            //   validator: (value) =>
            //       AppValidator.validateIdentity(value, context),
            //   onChanged: (val) {
            //     if (CreateSnapChatAdCubit.get(context)
            //         .adCreativeName
            //         .text
            //         .isNotEmpty) {
            //       // CreateAdCubit.get(context).updateAdCreativeProcess1();
            //     } else {
            //       // CreateAdCubit.get(context).undoAdCreativeProcess1();
            //     }
            //   },
            // ),
            // SizedBox(height: 20.h),
            // Column(
            //   children: [
            //     CustomText(
            //       text: 'Attachments'.tr,
            //       color: AppColors.mainColor,
            //       fontSize: 16,
            //       fontWeight: FontWeight.w500,
            //     ),
            //     const SizedBox(height: 25),
            //     SizedBox(
            //       height: 80.h,
            //       child: Material(
            //         elevation: 0,
            //         color: Colors.transparent,
            //         child: ListView.builder(
            //           scrollDirection: Axis.horizontal,
            //           itemCount: attachments.length,
            //           itemBuilder: (context, index) {
            //             final e = attachments[index];
            //
            //             // Update the destination index
            //             // CreateSnapChatAdCubit.get(context).destinationIndex = index;
            //
            //             // Determine if the destination is selected
            //             isSelectedDestination =
            //                 index == selectedDestinationIndex;
            //
            //             return Padding(
            //               padding: const EdgeInsets.symmetric(horizontal: 5.0),
            //               child: DestinationTypeWidget(
            //                 isSelected: isSelectedDestination ?? false,
            //                 callback: (selectedIndex) {
            //                   // CreateAdCubit.get(context).webSiteLink =
            //                   //     TextEditingController();
            //                   // CreateAdCubit.get(context).linkDesc =
            //                   //     TextEditingController();
            //                   // CreateAdCubit.get(context).headline =
            //                   //     TextEditingController();
            //                   // CreateAdCubit.get(context).message =
            //                   //     TextEditingController();
            //                   // CreateAdCubit.get(context)
            //                   //     .isAdCreativeProcess1Updated = false;
            //                   // CreateAdCubit.get(context)
            //                   //     .isAdCreativeProcess2Updated = false;
            //                   // CreateAdCubit.get(context)
            //                   //     .isAdCreativeProcess3Updated = false;
            //                   // CreateAdCubit.get(context)
            //                   //     .isAdCreativeProcess4Updated = false;
            //                   // CreateAdCubit.get(context)
            //                   //     .isAdCreativeProcess5Updated = false;
            //                   // CreateAdCubit.get(context)
            //                   //     .isAdCreativeProcess6Updated = false;
            //                   // setState(() {
            //                   selectedDestinationIndex = selectedIndex;
            //                   CreateSnapChatAdCubit.get(context)
            //                           .snapChatAdModel =
            //                       CreateSnapChatAdCubit.get(context)
            //                           .snapChatAdModel
            //                           .copyWith(
            //                             creativeType: attachments[selectedIndex]
            //                                 .attachmentType,
            //                             // lifeTimeBudgetMicro:
            //                             //     SnapChatAdSetCubit.get(context)
            //                             //         .dailyBudget
            //                             //         .text,
            //                             // startTime:
            //                             //     SnapChatAdSetCubit.get(context).startDate.text,
            //                             // endTime:
            //                             //     SnapChatAdSetCubit.get(context).endDate.text,
            //                           );
            //                   setState(() {});
            //                   //
            //                   //   // Update destination type based on index
            //                   //   if (e == "Whatsapp") {
            //                   //     CreateAdCubit.get(context)
            //                   //         .destinationType = "WHATSAPP";
            //                   //   } else if (e == "Messenger") {
            //                   //     CreateAdCubit.get(context)
            //                   //         .destinationType = "MESSENGER";
            //                   //   } else if (e == "Website") {
            //                   //     CreateAdCubit.get(context)
            //                   //         .destinationType = "WEBSITE";
            //                   //   } else {
            //                   //     CreateAdCubit.get(context)
            //                   //             .destinationType =
            //                   //         "INSTAGRAM_DIRECT";
            //                   //   }
            //                   //
            //                   //   print("destinationType" +
            //                   //       CreateAdCubit.get(context)
            //                   //           .destinationType);
            //                   // });
            //                 },
            //                 name: e.attachmentName ?? "",
            //                 icon: e.attachmentName == "WebSite"
            //                     ? AppAssets.snapChatAppWebsite
            //                     : e.attachmentName == "Call"
            //                         ? AppAssets.snapChatCall
            //                         : e.attachmentName == "App Install"
            //                             ? AppAssets.snapChatAppInstall
            //                             : e.attachmentName == "Deep Link"
            //                                 ? AppAssets.snapChatDeepLink
            //                                 : "",
            //                 index: index,
            //               ),
            //             );
            //           },
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
            // SizedBox(height: 25.h),
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomText(
                    text: 'Call to action',
                    fontSize: 12.sp,
                    color: Constants.primaryTextColor,
                    fontWeight: FontWeight.w400,
                    alignment: AlignmentDirectional.centerStart,
                  ),
                  const SizedBox(height: 25),
                  BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
                    builder: (context, state) {
                      return ExpansionTileItem(
                        expansionKey: Constants.callToActionKey,
                        onExpansionChanged: (val) {},
                        childrenPadding:
                            const EdgeInsets.symmetric(vertical: 8),
                        iconColor: AppColors.secondColor,
                        collapsedIconColor: AppColors.secondColor,
                        expandedAlignment: Alignment.center,
                        expandedCrossAxisAlignment: CrossAxisAlignment.center,
                        trailing: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(left: 6.0),
                              child: Icon(
                                Icons.expand_more,
                                size: 30.0,
                                color: Constants.darkColor,
                              ),
                            )
                          ],
                        ),
                        title:
                            CreateSnapChatAdCubit.get(context).typeName != null
                                ? CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: CreateSnapChatAdCubit.get(context)
                                            .typeName ??
                                        "")
                                : CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: 'callToAction'.tr),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x3F000000),
                              blurRadius: 40,
                              offset: Offset(0, 0),
                              spreadRadius: -10,
                            )
                          ],
                        ),
                        children: [
                          ListView.separated(
                            shrinkWrap: true,
                            physics: const BouncingScrollPhysics(
                                parent: NeverScrollableScrollPhysics()),
                            scrollDirection: Axis.vertical,
                            itemCount: (CreateSnapChatAdCubit.get(context)
                                            .phoneNumberFieldView ==
                                        false &&
                                    CreateSnapChatAdCubit.get(context)
                                            .urlFieldView ==
                                        true)
                                ? CreateSnapChatAdCubit.get(context)
                                    .allCallToAction
                                    .length
                                : (CreateSnapChatAdCubit.get(context)
                                                .phoneNumberFieldView ==
                                            false &&
                                        CreateSnapChatAdCubit.get(context)
                                                .urlFieldView ==
                                            false)
                                    ? CreateSnapChatAdCubit.get(context)
                                        .allCallToAction
                                        .length
                                    : CreateSnapChatAdCubit.get(context)
                                        .phoneCallToAction
                                        .length,
                            clipBehavior: Clip.none,
                            separatorBuilder: (context, index) =>
                                SizedBox(height: 10.h),
                            itemBuilder: (context, index) {
                              List<CallToAction>? cta =
                                  (CreateSnapChatAdCubit.get(context)
                                                  .phoneNumberFieldView ==
                                              false &&
                                          CreateSnapChatAdCubit.get(context)
                                                  .urlFieldView ==
                                              true)
                                      ? CreateSnapChatAdCubit.get(context)
                                          .allCallToAction
                                      : (CreateSnapChatAdCubit.get(context)
                                                      .phoneNumberFieldView ==
                                                  false &&
                                              CreateSnapChatAdCubit.get(context)
                                                      .urlFieldView ==
                                                  false)
                                          ? CreateSnapChatAdCubit.get(context)
                                              .allCallToAction
                                          : CreateSnapChatAdCubit.get(context)
                                              .phoneCallToAction;
                              return GestureDetector(
                                onTap: () {
                                  CreateSnapChatAdCubit.get(context)
                                      .setCallToAction(cta[index]);
                                  // if (CreateSnapChatAdCubit
                                  //     .get(context)
                                  //     .typeName !=
                                  //     null) {
                                  //   CreateSnapChatAdCubit
                                  //       .get(context)
                                  //       .tiktokAdPercentage =
                                  //       TiktokAdCubit
                                  //           .get(ctx1)
                                  //           .tiktokAdPercentage +
                                  //           0.02;
                                  //   print(
                                  //       'tiktokCampaignPercentage ${TiktokAdCubit
                                  //           .get(ctx1)
                                  //           .tiktokAdPercentage}');
                                  //   setState(() {});
                                  // } else {
                                  //   TiktokAdCubit
                                  //       .get(ctx1)
                                  //       .tiktokAdPercentage =
                                  //       TiktokAdCubit
                                  //           .get(ctx1)
                                  //           .tiktokAdPercentage -
                                  //           0.02;
                                  //   print(
                                  //       'tiktokCampaignPercentage ${TiktokAdCubit
                                  //           .get(ctx1)
                                  //           .tiktokAdPercentage}');
                                  //   setState(() {});
                                  // }
                                  if (CreateSnapChatAdCubit.get(context)
                                          .adPercentage <
                                      0.60) {
                                    CreateSnapChatAdCubit.get(context)
                                            .adPercentage =
                                        CreateSnapChatAdCubit.get(context)
                                                .adPercentage +
                                            0.20;
                                  }

                                  Constants.callToActionKey.currentState
                                      ?.collapse();
                                },
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  child: Container(
                                    height: 40.h,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(25.r),
                                      color: CreateSnapChatAdCubit.get(context)
                                                  .type ==
                                              cta[index].value
                                          ? AppColors.mainColor
                                          : Colors.white,
                                      gradient:
                                          CreateSnapChatAdCubit.get(context)
                                                      .type ==
                                                  cta[index].value
                                              ? Constants.defGradient
                                              : null,
                                      boxShadow: Constants.unSelectedShadow,
                                      border: null,
                                    ),
                                    width: 80.h,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: CustomText(
                                        text: cta[index].name ?? "",
                                        fontSize: 12.sp,
                                        fontWeight:
                                            CreateSnapChatAdCubit.get(context)
                                                        .type ==
                                                    cta[index].value
                                                ? FontWeight.w600
                                                : FontWeight.w400,
                                        color:
                                            CreateSnapChatAdCubit.get(context)
                                                        .type ==
                                                    cta[index].value
                                                ? AppColors.white
                                                : Constants.textColor,
                                        textAlign: TextAlign.center,
                                        alignment: AlignmentDirectional.center,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
            SizedBox(height: 25.h),
            if (CreateSnapChatAdCubit.get(context).urlFieldView == true)
              Column(
                children: [
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          CustomText(
                            text: 'Website Link'.tr,
                            color: AppColors.mainColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      CustomTextFormField(
                        controller:
                            CreateSnapChatAdCubit.get(context).webSiteLink,
                        textFontSize: 12,
                        borderRadius: 12,
                        key: const ValueKey('website_name'),
                        hintText: 'Website Link'.tr,
                        onChanged: (val) {
                          // if (CreateSnapChatAdCubit.get(context)
                          //     .webSiteLink
                          //     .text
                          //     .isNotEmpty) {
                          //   // CreateAdCubit.get(context).updateAdCreativeProcess4();
                          // } else {
                          //   // CreateAdCubit.get(context).undoAdCreativeProcess4();
                          // }
                        },
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.text,
                        // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                        // onSaved: (value) => controller.newCampaign.name = value,
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                ],
              ),
            Column(
              children: [
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.start,
                //   children: [
                //     CustomText(
                //       text: 'App name'.tr,
                //       color: AppColors.mainColor,
                //       fontSize: 14,
                //       fontWeight: FontWeight.w400,
                //     ),
                //   ],
                // ),
                // const SizedBox(height: 10),
                // CustomTextFormField(
                //   validator: (value) =>
                //       AppValidator.validateIdentity(value, context),
                //   onChanged: (val) {
                //     if (CreateSnapChatAdCubit.get(context)
                //         .appName
                //         .text
                //         .isNotEmpty) {
                //       // CreateAdCubit.get(context).updateAdCreativeProcess3();
                //     } else {
                //       // CreateAdCubit.get(context).undoAdCreativeProcess3();
                //     }
                //   },
                //   controller: CreateSnapChatAdCubit.get(context).appName,
                //   textFontSize: 12,
                //   borderRadius: 12,
                //   key: const ValueKey('App name'),
                //   hintText: "App name".tr,
                //
                //   textInputAction: TextInputAction.next,
                //   keyboardType: TextInputType.text,
                //   // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                //   // onSaved: (value) => controller.newCampaign.name = value,
                // ),
                if (CreateSnapChatAdCubit.get(context).phoneNumberFieldView ==
                    true) ...[
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      children: [
                        CustomText(
                          text: "Phone Numbers".tr,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          alignment: AlignmentDirectional.centerStart,
                          color: Constants.primaryTextColor,
                        ),
                        CustomText(
                          text: "*",
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          alignment: AlignmentDirectional.centerStart,
                          color: Constants.redColor,
                        ),
                      ],
                    ),
                  ),
                ],
                if (CreateSnapChatAdCubit.get(context).phoneNumberFieldView ==
                    true) ...[
                  20.verticalSpace,
                  ExpansionTileItem(
                    expansionKey: Constants.phoneNumbersKey,
                    onExpansionChanged: (val) {},
                    childrenPadding: EdgeInsets.zero,
                    iconColor: AppColors.secondColor,
                    collapsedIconColor: AppColors.secondColor,
                    expandedAlignment: Alignment.center,
                    expandedCrossAxisAlignment: CrossAxisAlignment.center,
                    trailing: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 6.0),
                          child: Icon(
                            Icons.expand_more,
                            size: 30.0,
                            color: Constants.darkColor,
                          ),
                        )
                      ],
                    ),
                    title: CreateSnapChatAdCubit.get(context)
                                .phoneNumbers
                                .isNotEmpty ==
                            true
                        ? CustomText(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                            text: CreateSnapChatAdCubit.get(context)
                                    .selectedNumber
                                    ?.phoneNumber ??
                                "")
                        : AccountHintText(
                            isDefaultHint: false,
                            hint: 'Choose phone numbers'.tr,
                          ),
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x3F000000),
                          blurRadius: 40,
                          offset: Offset(0, 0),
                          spreadRadius: -10,
                        )
                      ],
                    ),
                    children: [
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: CreateSnapChatAdCubit.get(context)
                            .phoneNumbers
                            .length,
                        // 3 for the first text + 2 for the last text
                        itemBuilder: (context, index) {
                          // Check if the index is for the text before the first three items
                          // if (index == 0) {
                          //   return Padding(
                          //     padding: const EdgeInsets.all(16.0),
                          //     child: CustomText(
                          //         text: 'Brand Awareness'.tr,
                          //         fontSize: 13.sp,
                          //         fontWeight: FontWeight.w600),
                          //   );
                          // } else
                          //   if (index >= 1
                          //     // && index <= 3
                          // )
                          // {
                          // Return the first three items
                          // final optIndex =
                          //     index - 1; // Adjust index for the optimization list
                          return InkWell(
                            onTap: () {
                              CreateSnapChatAdCubit.get(context)
                                  .selectPhoneNumber(
                                      CreateSnapChatAdCubit.get(context)
                                          .phoneNumbers[index]);
                              CreateSnapChatAdCubit.get(context)
                                      .snapChatAdModel =
                                  CreateSnapChatAdCubit.get(context)
                                      .snapChatAdModel
                                      .copyWith(
                                          phoneId:
                                              CreateSnapChatAdCubit.get(context)
                                                  .selectedNumber
                                                  ?.id);
                              Constants.phoneNumbersKey.currentState
                                  ?.collapse();
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Constants.gray.withOpacity(0.15),
                                border: Border.symmetric(
                                  horizontal: BorderSide(
                                      color: Constants.gray.withOpacity(0.3)),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 18, horizontal: 20),
                                child: CustomText(
                                  maxLines: 3,
                                  fontSize: 12.sp,
                                  text: CreateSnapChatAdCubit.get(context)
                                          .phoneNumbers[index]
                                          .phoneNumber ??
                                      "",
                                ),
                              ),
                            ),
                          );
                          // }
                        },
                      ),
                    ],
                  ),
                ]
                // Column(
                //   children: [
                //     Row(
                //       mainAxisAlignment: MainAxisAlignment.start,
                //       children: [
                //         CustomText(
                //           text: 'Phone'.tr,
                //           color: AppColors.mainColor,
                //           fontSize: 14,
                //           fontWeight: FontWeight.w400,
                //         ),
                //       ],
                //     ),
                //     const SizedBox(height: 10),
                //     CustomTextFormField(
                //       label: "Phone",
                //       showIsReqiredFlag: true,
                //       textFontSize: 12,
                //       key: const ValueKey('Phone'),
                //       hintText: "Phone",
                //       textInputAction: TextInputAction.next,
                //       keyboardType: TextInputType.phone,
                //       controller: CreateSnapChatAdCubit.get(context).phone,
                //       validator: (value) =>
                //           AppValidator.validateIdentity(value, context),
                //       onChanged: (va) {
                //         bool isValid = va.isNotEmpty;
                //         if (isValid) {
                //           // TiktokAdCubit.get(context).tiktokAdPercentage += 0.02;
                //           // _adNameValid = true;
                //         } else if (!isValid) {
                //           // TiktokAdCubit.get(context).tiktokAdPercentage -= 0.02;
                //         }
                //       },
                //       prefixIcon: CountryCodePicker(
                //         onChanged: (countryCode) {
                //           CreateSnapChatAdCubit.get(context).countryCode =
                //               countryCode.code!;
                //           CreateSnapChatAdCubit.get(context).countryDialCode =
                //               countryCode.dialCode!;
                //           // TiktokAdCubit.get(context).phoneNumber =
                //           //     TiktokAdCubit.get(ctx1).phoneController.text;
                //           // TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                //           //     countryCode: countryCode.code,
                //           //     countryCallingCode: countryCode.dialCode,TiktokAdCubit.get(ctx1).phoneController);
                //         },
                //         onInit: (countryCode) {
                //           CreateSnapChatAdCubit.get(context).countryCode =
                //               countryCode!.code!;
                //           CreateSnapChatAdCubit.get(context).countryDialCode =
                //               countryCode.dialCode!;
                //         },
                //         // Initial selection and favorite can be one of code ('IT') OR dial_code('+39')
                //         initialSelection: 'EG',
                //         // favorite: ['+39', 'FR'],
                //         // optional. Shows only country name and flag
                //         showCountryOnly: false,
                //         // optional. Shows only country name and flag when popup is closed.
                //         showOnlyCountryWhenClosed: false,
                //         // optional. aligns the flag and the Text left
                //         alignLeft: false,
                //       ),
                //     ),
                //     const SizedBox(height: 10),
                //   ],
                // ),
              ],
            ),
            const SizedBox(height: 25),
          ],
        );
      },
    );
  }
}

// List<String> attachments = ["Website", "App install", "Call"];
List<AttachmentsModel> attachments = [
  AttachmentsModel(attachmentName: "WebSite", attachmentType: "WEB_VIEW"),
  AttachmentsModel(
      attachmentName: "App Install", attachmentType: "APP_INSTALL"),
  AttachmentsModel(attachmentName: "Call", attachmentType: "AD_TO_CALL"),
  AttachmentsModel(attachmentName: "Deep Link", attachmentType: "DEEP_LINK"),
  // AttachmentsModel(attachmentName: "WebSite", attachmentType: "WEB_VIEW"),
  // AttachmentsModel(attachmentName: "WebSite", attachmentType: "WEB_VIEW"),
];

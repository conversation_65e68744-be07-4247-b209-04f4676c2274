import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_adSet_review_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_ad_review_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_campaign_review_widget.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_review_ad_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../widgets/appbar.dart';
import '../../../../../widgets/stepper/bottom_nav.dart';
import '../../../../review_screen/presentation/review_screen.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';

class SnapChatReviewCampaignScreen extends StatelessWidget {
  const SnapChatReviewCampaignScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
      builder: (context, state) {
        return state is CreateSnapChatAdLoading
            ? ReviewScreen(
                tiktok: false,
                snapChat: true,
              )
            : Scaffold(
                key: const ValueKey('snap_chat_screen'),
                bottomNavigationBar: const CustomBottomNavBar(
                  isReview: false,
                  isTiktok: false,
                  isSnapChat: true,
                ),
                appBar: const CustomAppBar(
                  title: "Campaign Summary",
                  showBackButton: false,
                  hasDrawer: true,
                ),
                body:
                    // CreateAdCubit.get(context).isAddCreated
                    //     ? const SizedBox()
                    //     :
                    SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        // if (TiktokAccountsCubit.get(context)
                        //     .addedAdAccount
                        //     ?.advertiserName !=
                        //     null)
                        //   Column(
                        //     children: [
                        //       5.verticalSpace,
                        //       AccountHintText(
                        //         isDefaultHint: true,
                        //         hint:
                        //         "${TiktokAccountsCubit.get(context).addedAdAccount?.advertiserName ?? ""}'s Ad Account",
                        //       ),
                        //       20.verticalSpace,
                        //     ],
                        //   )
                        // else
                        const SizedBox(),
                        const SnapChatCampaignReviewWidget(),
                        20.verticalSpace,
                        const SnapChatAdSetReviewWidget(),
                        20.verticalSpace,
                        const SnapChatAdReviewWidget(),
                        20.verticalSpace,
                        const SnapChatReviewAdWidget(),
                      ],
                    ),
                  ),
                ),
              );
      },
    );
  }
}

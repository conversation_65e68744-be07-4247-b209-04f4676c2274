import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_video_screen.dart';
import 'package:ads_dv/utils/res/constants.dart';

import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';

import '../../../../../../widgets/custom_text.dart';

class SnapChatAdReviewExpandedWidget extends StatefulWidget {
  const SnapChatAdReviewExpandedWidget({super.key});

  @override
  State<SnapChatAdReviewExpandedWidget> createState() =>
      _SnapChatAdReviewExpandedWidgetState();
}

class _SnapChatAdReviewExpandedWidgetState
    extends State<SnapChatAdReviewExpandedWidget> {
  @override
  Widget build(BuildContext context) {
    return ExpansionTileBorderItem(
      onExpansionChanged: (val) {},
      expansionKey: Constants.snapChatReviewTileKey,
      childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
      iconColor: AppColors.secondColor,
      collapsedIconColor: AppColors.secondColor,
      expandedAlignment: Alignment.center,
      expandedCrossAxisAlignment: CrossAxisAlignment.center,
      leading: SvgPicture.asset(
        AppAssets.review,
        color: AppColors.mainColor,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // CircularPercentIndicator(
          //   circularStrokeCap: CircularStrokeCap.round,
          //   radius: 12.0,
          //   lineWidth: 5.5,
          //   percent: 0.7,
          //   linearGradient: Constants.secGradient,
          //   backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
          //   reverse: true,
          // ),
          const Padding(
            padding: EdgeInsets.only(left: 10.0),
            child: CustomText(
              text: "|",
              color: AppColors.mainColor,
              fontSize: 35,
              fontWeight: FontWeight.w200,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 6.0),
            child: ShaderMask(
              shaderCallback: (Rect bounds) {
                return const LinearGradient(
                  colors: [
                    Color(0xFFFF006F),
                    Color(0xFFF6BA00),
                  ],
                ).createShader(bounds);
              },
              child: const Icon(
                Icons.expand_more,
                size: 24.0,
                color:
                    Colors.white, // This color will be replaced by the gradient
              ),
            ),
          )
        ],
      ),
      title: const CustomText(
          text: 'Review',
          color: AppColors.mainColor,
          fontSize: 22,
          fontWeight: FontWeight.w700),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: AppColors.mainColor)
          // color: AppColors.borderColor,
          ),
      children: const [
        SizedBox(height: 40),
        SnapChatVideoScreen(),
        SizedBox(height: 40),
      ],
    );
  }
}

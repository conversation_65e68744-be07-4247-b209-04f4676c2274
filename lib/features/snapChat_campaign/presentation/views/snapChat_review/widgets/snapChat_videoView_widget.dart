import 'dart:io';

import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_full_screen_video_player_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/tiktok_widgets/video_buttons.dart';
import 'package:flutter/material.dart';

import '../../../../../../widgets/uploaded_image_widget.dart';

class SnapChatVideoView extends StatelessWidget {
  final File? videos;
  final File? images;
  final String? caption;
  final String? websiteLink;
  final String? callToAction;

  const SnapChatVideoView(
      {super.key,
      this.videos,
      this.images,
      required this.caption,
      required this.websiteLink,
      required this.callToAction});

  @override
  Widget build(BuildContext context) {
    // print('imageasdasfsd ${images}');
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: SizedBox(
        height: 500,
        child: Stack(
          children: [
            if (videos?.path != '')
              SizedBox.expand(
                  child: SnapChatFullScreenPlayer(
                      videoUrl: videos!,
                      caption: caption ?? "",
                      websiteLink: websiteLink ?? "",
                      callToAction: callToAction ?? "")),
            if (images?.path != '')
              ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: Stack(
                  alignment: AlignmentDirectional.bottomCenter,
                  children: [
                    UploadedImageWidget(
                      text: "",
                      onPressed: () {},
                      image: images!,
                      height: MediaQuery.of(context).size.height,
                    ),
                    const SizedBox(
                      width: double.infinity,
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              size: 25,
                              color: Colors.white,
                            ),
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 25,
                              color: Colors.white,
                            ),
                            Icon(
                              Icons.camera_alt_outlined,
                              size: 25,
                              color: Colors.white,
                            ),
                            Icon(
                              Icons.person_outline,
                              size: 25,
                              color: Colors.white,
                            ),
                            Icon(
                              Icons.play_arrow_outlined,
                              size: 25,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            // SizedBox.expand(
            //   child: Image.file(
            //     images!,
            //   ),
            // ),
            Positioned(
                bottom: 60, right: 20, child: VideoButtons(isSnapChat: true))
          ],
        ),
      ),
    );
  }
}

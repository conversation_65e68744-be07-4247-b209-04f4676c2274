import 'dart:io';

import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_review/widgets/snapChat_videoView_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SnapChatVideoScreen extends StatelessWidget {
  const SnapChatVideoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
      builder: (adContext, state) {
        return
            // TiktokAdCubit.get(adContext).initialLoading
            //   ? const Center(child: CircularProgressIndicator(strokeWidth: 2))
            //   :
            SnapChatVideoView(
          videos: (CreateSnapChatAdCubit.get(adContext).snapChatAdModel.file !=
                      null &&
                  CreateSnapChatAdCubit.get(adContext)
                          .snapChatAdModel
                          .mediaType ==
                      "VIDEO")
              ? CreateSnapChatAdCubit.get(adContext).snapChatAdModel.file
              : File(''),
          images: (CreateSnapChatAdCubit.get(adContext).snapChatAdModel.file !=
                      null &&
                  CreateSnapChatAdCubit.get(adContext)
                          .snapChatAdModel
                          .mediaType ==
                      "IMAGE")
              ? CreateSnapChatAdCubit.get(adContext).snapChatAdModel.file
              : File(''),
          caption: TiktokAdCubit.get(adContext).adText.text,
          websiteLink: TiktokAdCubit.get(adContext).url.text,
          callToAction: TiktokAdCubit.get(adContext).typeName ?? "",
        );
      },
    );
  }
}

import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_campaign/new_campaign/tabs_widget.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import '../../controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';
import 'new_snapChat_adSet_widget.dart';

class CreateSnapChatAdSetWidget extends StatefulWidget {
  const CreateSnapChatAdSetWidget({super.key});

  @override
  State<CreateSnapChatAdSetWidget> createState() =>
      _CreateSnapChatAdSetWidgetState();
}

class _CreateSnapChatAdSetWidgetState extends State<CreateSnapChatAdSetWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SnapChatAdSetCubit, SnapChatAdSetState>(
      builder: (context, state) {
        return ExpansionTileItem(
          onExpansionChanged: (val) {
            CreateAdCubit.get(context).setAdSetExpansionState(val);
          },
          expansionKey: Constants.snapChatAdSetTileKey,
          childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
          iconColor: AppColors.secondColor,
          collapsedIconColor: AppColors.secondColor,
          expandedAlignment: Alignment.center,
          expandedCrossAxisAlignment: CrossAxisAlignment.center,
          leading:
              SvgPicture.asset(AppAssets.adset, color: AppColors.mainColor),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularPercentIndicator(
                circularStrokeCap: CircularStrokeCap.round,
                radius: 12.0,
                lineWidth: 5.5,
                // percent:
                // CreateSnapChatAdCubit.get(context).campaignPercentage,
                linearGradient: Constants.secGradient,
                backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                reverse: true,
                percent: CreateSnapChatAdCubit.get(context).adSetPercentage,
                // isDemographic: false,
                // isAdSet: true,
                // isLocation: false,
                // isTargeting: false,
                // isAdCreative: false,
                // adCubit: CreateAdCubit.get(context)
              ),
              const Padding(
                padding: EdgeInsets.only(left: 10.0),
                child: CustomText(
                  text: "|",
                  color: AppColors.mainColor,
                  fontSize: 35,
                  fontWeight: FontWeight.w200,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 6.0),
                child: ShaderMask(
                  shaderCallback: (Rect bounds) {
                    return Constants.secGradient.createShader(bounds);
                  },
                  child: Icon(
                    CreateAdCubit.get(context).isAdSetTileExpanded
                        ? Icons.expand_less
                        : Icons.expand_more,
                    size: 24.0,
                    color: Colors
                        .white, // This color will be replaced by the gradient
                  ),
                ),
              )
            ],
          ),
          title: CustomText(
              text: 'Ad Set'.tr,
              color: AppColors.mainColor,
              fontSize: 22,
              fontWeight: FontWeight.w700),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: AppColors.mainColor)),
          children: [
            const SizedBox(height: 20),
            Container(
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(40),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x3D000000),
                    blurRadius: 13.93,
                    offset: Offset(0, 0),
                    spreadRadius: -3.80,
                  )
                ],
              ),
              child: TabsWidget(
                  newObject: 'Create New Advertising'.tr,
                  existObject: 'Previous Advertisings'.tr,
                  selectedTab: CreateAdCubit.get(context).selectAdSetTab,
                  onTabChanged: (tab) {
                    // Check if there is an existing campaign
                    if (CreateAdCubit.get(context).existingCampaign == null &&
                        tab == 1) {
                      // Assuming tab 1 is the second tab
                      showErrorToast("Please select an existing campaign".tr);
                      setState(() {
                        CreateAdCubit.get(context).selectAdSetTab = 0;
                      });
                      return; // Prevent switching to the second tab
                    }

                    // If there is an existing campaign, proceed with the tab change
                    if (CreateAdCubit.get(context).selectAdSetTab != tab) {
                      // Resetting fields when changing tabs
                      CreateAdCubit.get(context).changeAdSetTabIndex(tab);
                      CreateAdCubit.get(context).adSetPercentage = 0.0;
                      CreateAdCubit.get(context).existingAdSet = null;
                      CreateAdCubit.get(context).selectedExistingAdSet = null;

                      // Clear all relevant fields
                      CreateAdCubit.get(context).adSetNameController.clear();
                      CreateAdCubit.get(context).dailyBudget.clear();
                      CreateAdCubit.get(context).minAge.clear();
                      CreateAdCubit.get(context).maxAge.clear();
                      CreateAdCubit.get(context).startDate.clear();
                      CreateAdCubit.get(context).endDate.clear();

                      // Reset lists and other fields
                      CreateAdCubit.get(context).languages.clear();
                      CreateAdCubit.get(context).majors.clear();
                      CreateAdCubit.get(context).lifeEvents.clear();
                      CreateAdCubit.get(context).userOs.clear();
                      CreateAdCubit.get(context).userDevices.clear();
                      CreateAdCubit.get(context).behaviours.clear();
                      CreateAdCubit.get(context).educationStatuses.clear();
                      CreateAdCubit.get(context).selectedInterests.clear();
                      CreateAdCubit.get(context).genders.clear();
                      CreateAdCubit.get(context).destinationType = "";
                      CreateAdCubit.get(context).adSetStatusType = "PAUSED";
                      CreateAdCubit.get(context).isAdSetActive = false;

                      // Resetting other properties as needed
                      // ... (rest of your resets)

                      // Resetting percentages
                      CreateAdCubit.get(context).demoPercentage = 0.0;
                      CreateAdCubit.get(context).targetingPercentage = 0.0;
                      CreateAdCubit.get(context).locationPercentage = 0.0;

                      // Resetting platform positions
                      CreateAdCubit.get(context).publisherPlatforms.clear();
                      CreateAdCubit.get(context).fbPositions.clear();
                      CreateAdCubit.get(context).igPositions.clear();
                      CreateAdCubit.get(context).mPositions.clear();

                      // Resetting platform activity
                      CreateAdCubit.get(context).fbPlatformActive = false;
                      CreateAdCubit.get(context).instagramPlatformActive =
                          false;
                      CreateAdCubit.get(context).messengerPlatformActive =
                          false;

                      // Resetting positions
                      // ... (rest of your positions reset)

                      // Clear text controllers
                      CreateAdCubit.get(context).textController.clear();
                    }
                  }),
            ),
            // (CreateAdCubit.get(context).selectAdSetTab == 0)
            //     ?
            const NewSnapChatAdSetWidget(),
            // : ExistingAdSetWidget(
            //     createAdCubit: CreateAdCubit.get(context),
            //   ),
            const SizedBox(height: 25),
          ],
        );
      },
    );
  }
}

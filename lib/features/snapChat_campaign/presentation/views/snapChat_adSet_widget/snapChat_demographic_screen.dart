import 'package:ads_dv/features/create_campaigns/presentation/controllers/search/search_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/gender_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import '../../controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';

class SnapChatDemographicScreen extends StatefulWidget {
  // SnapChatAdSetCubit SnapChatAdSetCubit;

  const SnapChatDemographicScreen({
    super.key,
    // required this.SnapChatAdSetCubit
  });

  @override
  State<SnapChatDemographicScreen> createState() =>
      _SnapChatDemographicScreenState();
}

class _SnapChatDemographicScreenState extends State<SnapChatDemographicScreen> {
  FocusNode fromNode = FocusNode();
  int fromAgeValue = 0;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SearchCubit>(
      create: (context) => SearchCubit(),
      child: BlocBuilder<SearchCubit, SearchState>(
        builder: (context, state) {
          return Scaffold(
            appBar: CustomAppBar(
              title: "Demographic".tr,
              showBackButton: true,
              hasDrawer: true,
            ),
            body: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.age,
                          width: 24.w,
                          height: 24.h,
                          //    color: Colors.white,
                        ),
                        SizedBox(width: 10.w),
                        CustomText(
                          text: 'Age'.tr,
                          fontSize: 18.sp,
                          color: Constants.primaryTextColor,
                          fontWeight: FontWeight.w700,
                          alignment: AlignmentDirectional.centerStart,
                        )
                      ],
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    BlocBuilder<SnapChatAdSetCubit, SnapChatAdSetState>(
                      bloc: SnapChatAdSetCubit.get(context),
                      builder: (context, state) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Container(
                              width: 125.w,
                              height: 42.h,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(37),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 20,
                                    offset: Offset(0, 0),
                                    spreadRadius: -4,
                                  )
                                ],
                              ),
                              child: Row(
                                children: [
                                  // SizedBox(
                                  //   width: 80.w,
                                  //   child: CustomTextField(
                                  //     borderColor: Colors.transparent,
                                  //     hintText: "From".tr,
                                  //     textInputType: TextInputType.number,
                                  //     hintStyle: const TextStyle(fontSize: 14),
                                  //     controller: widget.SnapChatAdSetCubit.minAge,
                                  //     node: fromNode,
                                  //     inputFormatters: [
                                  //       FilteringTextInputFormatter.digitsOnly,
                                  //       // Only allow numbers
                                  //     ],
                                  //     onChanged: (val) {
                                  //       fromAgeValue = int.tryParse(val) ?? 18;
                                  //       setState(() {});
                                  //       // if (val.isNotEmpty) {
                                  //       //   int ageValue = int.parse(val);
                                  //       //
                                  //       //   // Check if the value is less than 18
                                  //       //   if (ageValue < 18 || ageValue > 65) {
                                  //       //     // If less than 18, set the value to 18
                                  //       //     widget.SnapChatAdSetCubit.minAge.text =
                                  //       //         '18';
                                  //       //     widget.SnapChatAdSetCubit.minAge
                                  //       //             .selection =
                                  //       //         TextSelection.fromPosition(
                                  //       //       TextPosition(
                                  //       //           offset: widget.SnapChatAdSetCubit
                                  //       //               .minAge.text.length),
                                  //       //     );
                                  //       //     ageValue = 18;
                                  //       //   }
                                  //       //
                                  //       //   widget.SnapChatAdSetCubit
                                  //       //       .updateDemoProcess1();
                                  //       //   widget.SnapChatAdSetCubit
                                  //       //       .updateAdSetProcess9();
                                  //       //
                                  //       //   widget.SnapChatAdSetCubit.adModel =
                                  //       //       SnapChatAdSetCubit.get(context)
                                  //       //           .adModel
                                  //       //           .copyWith(ageMin: ageValue);
                                  //       //
                                  //       //   ReachEstimateCubit.get(context)
                                  //       //       .getReachEstimate(
                                  //       //     context: context,
                                  //       //     imagesFiles:
                                  //       //         SnapChatAdSetCubit.get(context)
                                  //       //             .adImages,
                                  //       //     videosFiles:
                                  //       //         SnapChatAdSetCubit.get(context)
                                  //       //                 .adVideo +
                                  //       //             SnapChatAdSetCubit.get(context)
                                  //       //                 .videoImage,
                                  //       //   );
                                  //       // } else {
                                  //       //   widget.SnapChatAdSetCubit
                                  //       //       .undoDemoProcess1();
                                  //       //   widget.SnapChatAdSetCubit
                                  //       //       .updateAdSetProcess9();
                                  //       // }
                                  //     },
                                  //   ),
                                  // ),
                                  5.horizontalSpace,
                                  Text(
                                    "From".tr,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  5.horizontalSpace,
                                  const VerticalDivider(
                                    width: 2,
                                    color: Constants.primaryTextColor,
                                  ),
                                  SizedBox(
                                    width: 80.w,
                                    child: FittedBox(
                                      child: DropdownButton<int>(
                                        padding: EdgeInsets.zero,
                                        // selectedItemBuilder:
                                        //     (BuildContext context) {
                                        //   return []; // Return an empty list to disable displaying the selected item next to the icon
                                        // },
                                        disabledHint: const SizedBox(),
                                        borderRadius:
                                            BorderRadius.circular(37.0),
                                        underline: const SizedBox(),
                                        hint: const SizedBox(),
                                        icon: const Icon(
                                          Icons.expand_more,
                                          color: Constants.primaryTextColor,
                                          size: 30,
                                        ),
                                        value: SnapChatAdSetCubit.get(context)
                                            .selectedMinAge,
                                        style: const TextStyle(
                                            color: Colors.black),
                                        onChanged: (int? value) {
                                          setState(() {
                                            SnapChatAdSetCubit.get(context)
                                                .selectedMinAge = value!;

                                            // widget.SnapChatAdSetCubit.minAge.text =
                                            //     SnapChatAdSetCubit.get(context)
                                            //         .selectedMinAge
                                            //         .toString();
                                          });
                                          // widget.SnapChatAdSetCubit
                                          //     .updateDemoProcess1();
                                          // widget.SnapChatAdSetCubit
                                          //     .updateAdSetProcess9();
                                          // widget.SnapChatAdSetCubit.adModel =
                                          //     SnapChatAdSetCubit.get(context)
                                          //         .adModel
                                          //         .copyWith(
                                          //           ageMin: widget.SnapChatAdSetCubit
                                          //               .selectedMinAge,
                                          //         );
                                          // ReachEstimateCubit.get(context)
                                          //     .getReachEstimate(
                                          //         context: context,
                                          //         imagesFiles:
                                          //             SnapChatAdSetCubit.get(context)
                                          //                 .adImages,
                                          //         videosFiles:
                                          //             SnapChatAdSetCubit.get(context)
                                          //                 .adVideo);
                                        },
                                        items: List<
                                                DropdownMenuItem<int>>.generate(
                                            28, (int index) {
                                          return DropdownMenuItem<int>(
                                            value: index + 18,
                                            child: Text(
                                              (index + 18).toString(),
                                              style: const TextStyle(
                                                  color: Colors.black),
                                            ),
                                          );
                                        }),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              width: 125.w,
                              height: 42.h,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(37),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 20,
                                    offset: Offset(0, 0),
                                    spreadRadius: -4,
                                  )
                                ],
                              ),
                              child: Row(
                                children: [
                                  5.horizontalSpace,
                                  Text(
                                    "To".tr,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  5.horizontalSpace,
                                  const VerticalDivider(
                                    width: 2,
                                    color: Constants.primaryTextColor,
                                  ),
                                  SizedBox(
                                    width: 80.w,
                                    child: FittedBox(
                                      child: DropdownButton<int>(
                                        padding: EdgeInsets.zero,
                                        // selectedItemBuilder:
                                        //     (BuildContext context) {
                                        //   return []; // Return an empty list to disable displaying the selected item next to the icon
                                        // },
                                        disabledHint: const SizedBox(),
                                        borderRadius:
                                            BorderRadius.circular(37.0),
                                        underline: const SizedBox(),
                                        hint: const SizedBox(),
                                        icon: const Icon(
                                          Icons.expand_more,
                                          color: Constants.primaryTextColor,
                                          size: 30,
                                        ),
                                        value: SnapChatAdSetCubit.get(context)
                                            .selectedMaxAge,
                                        style: const TextStyle(
                                            color: Colors.black),
                                        onChanged: (int? value) {
                                          setState(() {
                                            SnapChatAdSetCubit.get(context)
                                                .selectedMaxAge = value!;

                                            // widget.SnapChatAdSetCubit.minAge.text =
                                            //     SnapChatAdSetCubit.get(context)
                                            //         .selectedMinAge
                                            //         .toString();
                                          });
                                          // widget.SnapChatAdSetCubit
                                          //     .updateDemoProcess1();
                                          // widget.SnapChatAdSetCubit
                                          //     .updateAdSetProcess9();
                                          // widget.SnapChatAdSetCubit.adModel =
                                          //     SnapChatAdSetCubit.get(context)
                                          //         .adModel
                                          //         .copyWith(
                                          //           ageMin: widget.SnapChatAdSetCubit
                                          //               .selectedMaxAge,
                                          //         );
                                          // ReachEstimateCubit.get(context)
                                          //     .getReachEstimate(
                                          //         context: context,
                                          //         imagesFiles:
                                          //             SnapChatAdSetCubit.get(context)
                                          //                 .adImages,
                                          //         videosFiles:
                                          //             SnapChatAdSetCubit.get(context)
                                          //                 .adVideo);
                                        },
                                        items: List<
                                                DropdownMenuItem<int>>.generate(
                                            38, (int index) {
                                          return DropdownMenuItem<int>(
                                            value: index + 18,
                                            child: Text(
                                              (index + 18).toString(),
                                              style: const TextStyle(
                                                  color: Colors.black),
                                            ),
                                          );
                                        }),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Divider(color: Constants.textColor, thickness: 2),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.gender,
                          width: 24.w,
                          height: 24.h,
                          //    color: Colors.white,
                        ),
                        SizedBox(width: 10.w),
                        CustomText(
                          text: 'Gender'.tr,
                          fontSize: 18.sp,
                          color: Constants.primaryTextColor,
                          fontWeight: FontWeight.w700,
                          alignment: AlignmentDirectional.centerStart,
                        )
                      ],
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: genders.map((e) {
                        return GenderWidget(
                          isSelected: genders.indexOf(e) ==
                              SnapChatAdSetCubit.get(context).selectedIndex,
                          callback: (index) {
                            // widget.SnapChatAdSetCubit.updateDemoProcess3();
                            // widget.SnapChatAdSetCubit.updateAdSetProcess9();
                            setState(() {
                              // Update selected index when an ObjectiveWidget is tapped
                              SnapChatAdSetCubit.get(context).selectedIndex =
                                  index;
                              e == "Male".tr
                                  ? SnapChatAdSetCubit.get(context).genders = [
                                      "MALE"
                                    ]
                                  : e == "Female".tr
                                      ? SnapChatAdSetCubit.get(context)
                                          .genders = ['FEMALE']
                                      : SnapChatAdSetCubit.get(context)
                                          .genders = [""];
                            });
                            // widget.SnapChatAdSetCubit.adModel =
                            //     SnapChatAdSetCubit.get(context).adModel.copyWith(
                            //         genders: widget.SnapChatAdSetCubit.genders);
                            // ReachEstimateCubit.get(context).getReachEstimate(
                            //     context: context,
                            //     imagesFiles:
                            //         SnapChatAdSetCubit.get(context).adImages,
                            //     videosFiles:
                            //         SnapChatAdSetCubit.get(context).adVideo);
                          },
                          name: e,
                          index: genders.indexOf(e),
                        );
                      }).toList(),
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    // const Padding(
                    //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                    //   child: Divider(color: Constants.textColor, thickness: 2),
                    // ),
                    // SizedBox(
                    //   height: 30.h,
                    // ),
                    // Row(
                    //   children: [
                    //     CustomSvgWidget(
                    //       svg: AppAssets.lang,
                    //       width: 24.w,
                    //       height: 24.h,
                    //       //    color: Colors.white,
                    //     ),
                    //     SizedBox(width: 10.w),
                    //     CustomText(
                    //       text: 'Language'.tr,
                    //       fontSize: 18.sp,
                    //       color: Constants.primaryTextColor,
                    //       fontWeight: FontWeight.w700,
                    //       alignment: AlignmentDirectional.centerStart,
                    //     )
                    //   ],
                    // ),
                    // SizedBox(
                    //   height: 40.h,
                    // ),
                    // Container(
                    //   //    width: 125.w,
                    //   height: 46.h,
                    //   decoration: ShapeDecoration(
                    //     color: Colors.white,
                    //     shape: RoundedRectangleBorder(
                    //       borderRadius: BorderRadius.circular(37),
                    //     ),
                    //     shadows: const [
                    //       BoxShadow(
                    //         color: Color(0x33000000),
                    //         blurRadius: 20,
                    //         offset: Offset(0, 0),
                    //         spreadRadius: -4,
                    //       )
                    //     ],
                    //   ),
                    //   child: Row(
                    //     children: [
                    //       Expanded(
                    //         child: CustomTextField(
                    //           onChanged: (val) {
                    //             //  searchWord = textController.text;
                    //           },
                    //           borderColor: Colors.transparent,
                    //           hintText: "Search for ...".tr,
                    //           controller: SnapChatAdSetCubit.get(context)
                    //               .textController,
                    //           hintStyle: const TextStyle(fontSize: 14),
                    //           icon: ShaderMask(
                    //             shaderCallback: (Rect bounds) {
                    //               return const LinearGradient(
                    //                 colors: [
                    //                   Color(0xFFFF006F),
                    //                   Color(0xFFF6BA00),
                    //                 ],
                    //               ).createShader(bounds);
                    //             },
                    //             child: const Padding(
                    //               padding: EdgeInsets.all(12.0),
                    //               child: CustomSvgWidget(
                    //                   width: 13,
                    //                   height: 13,
                    //                   svg: AppAssets.search,
                    //                   color: Colors.white),
                    //             ),
                    //           ),
                    //           // TextField properties
                    //         ),
                    //       ),
                    //       InkWell(
                    //         onTap: () {
                    //           SnapChatAdSetCubit.get(context)
                    //                   .textController
                    //                   .text
                    //                   .isEmpty
                    //               ? null
                    //               : SearchCubit.get(context).search(
                    //                   context: context,
                    //                   type: "adlocale",
                    //                   keyword: SnapChatAdSetCubit.get(context)
                    //                       .textController
                    //                       .text);
                    //         },
                    //         child: Container(
                    //             width: 81.w,
                    //             padding: EdgeInsets.zero,
                    //             decoration: const ShapeDecoration(
                    //               gradient: LinearGradient(
                    //                 begin: Alignment(-0.99, -0.10),
                    //                 end: Alignment(0.99, 0.1),
                    //                 colors: [
                    //                   Color(0xFF0B0F26),
                    //                   Color(0xFF1C4294)
                    //                 ],
                    //               ),
                    //               shape: RoundedRectangleBorder(
                    //                 borderRadius: BorderRadius.only(
                    //                   topRight: Radius.circular(100),
                    //                   bottomRight: Radius.circular(100),
                    //                 ),
                    //               ),
                    //             ),
                    //             child: Padding(
                    //               padding: const EdgeInsets.symmetric(
                    //                   horizontal: 15.0, vertical: 15.0),
                    //               child: CustomText(
                    //                   text: "Search".tr,
                    //                   fontSize: 14,
                    //                   fontWeight: FontWeight.w400,
                    //                   color: Colors.white),
                    //             )),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                    // SizedBox(
                    //   height: 20.h,
                    // ),
                    // SnapChatAdSetCubit.get(context).textController.text.isEmpty
                    //     ? Column(
                    //         crossAxisAlignment: CrossAxisAlignment.center,
                    //         children: [
                    //           Lottie.asset(AppAssets.searchLo,
                    //               width: 111.h, height: 112.h),
                    //           SizedBox(
                    //             height: 10.h,
                    //           ),
                    //           Row(
                    //             mainAxisAlignment: MainAxisAlignment.center,
                    //             children: [
                    //               CustomText(
                    //                 text:
                    //                     'Search for  your  Target Language'.tr,
                    //                 fontSize: 14.sp,
                    //                 color: AppColors.iconBottomColor,
                    //                 fontWeight: FontWeight.w400,
                    //                 alignment: AlignmentDirectional.centerStart,
                    //               ),
                    //             ],
                    //           ),
                    //         ],
                    //       )
                    //     : const SizedBox(),
                    // if (state is SearchStateLoading)
                    //   const LoadingWidget(isCircle: true)
                    // else if (state is SearchStateError)
                    //   HandleErrorWidget(
                    //       fun: () {
                    //         SearchCubit.get(context).search(
                    //             context: context,
                    //             type: "adlocale",
                    //             keyword: SnapChatAdSetCubit.get(context)
                    //                 .textController
                    //                 .text);
                    //       },
                    //       failure: state.message)
                    // else if (state is SearchStateLoaded)
                    //   Column(
                    //     children: [
                    //       CustomText(
                    //         text:
                    //             '${"Search Result".tr} (${state.data.length})',
                    //         fontSize: 14.sp,
                    //         color: AppColors.iconBottomColor,
                    //         fontWeight: FontWeight.w400,
                    //         alignment: AlignmentDirectional.centerStart,
                    //       ),
                    //       SizedBox(
                    //         height: 20.h,
                    //       ),
                    //       BlocBuilder<SnapChatAdSetCubit, SnapChatAdSetState>(
                    //         builder: (context, adState) {
                    //           return ListView.builder(
                    //             shrinkWrap: true,
                    //             physics: const NeverScrollableScrollPhysics(),
                    //             padding: EdgeInsets.zero,
                    //             itemCount: state.data.length,
                    //             itemBuilder: (item, index) {
                    //               return (!SnapChatAdSetCubit.get(context)
                    //                       .languages
                    //                       .contains(state.data[index]))
                    //                   ? InkWell(
                    //                       onTap: () {
                    //                         SnapChatAdSetCubit.get(context)
                    //                             .setSelectedLanguages(
                    //                                 state.data[index]);
                    //                         // SnapChatAdSetCubit.get(context)
                    //                         //     .updateDemoProcess4();
                    //                         // widget.SnapChatAdSetCubit
                    //                         //     .updateAdSetProcess9();
                    //                         // widget.SnapChatAdSetCubit.adModel =
                    //                         //     SnapChatAdSetCubit.get(context)
                    //                         //         .adModel
                    //                         //         .copyWith(
                    //                         //           languages: widget
                    //                         //               .SnapChatAdSetCubit
                    //                         //               .languages,
                    //                         //         );
                    //                         // ReachEstimateCubit.get(context)
                    //                         //     .getReachEstimate(
                    //                         //         context: context,
                    //                         //         imagesFiles:
                    //                         //             SnapChatAdSetCubit.get(
                    //                         //                     context)
                    //                         //                 .adImages,
                    //                         //         videosFiles:
                    //                         //             SnapChatAdSetCubit.get(
                    //                         //                     context)
                    //                         //                 .adVideo);
                    //                         // print("asdasda" +
                    //                         //     widget.SnapChatAdSetCubit.languages
                    //                         //         .toString());
                    //                       },
                    //                       child: Column(
                    //                         children: [
                    //                           Row(
                    //                             mainAxisAlignment:
                    //                                 MainAxisAlignment
                    //                                     .spaceBetween,
                    //                             children: [
                    //                               CustomText(
                    //                                 text: state
                    //                                         .data[index].name ??
                    //                                     "",
                    //                                 color: Constants.darkColor,
                    //                                 fontSize: 14,
                    //                                 fontWeight: FontWeight.w400,
                    //                               ),
                    //                               Container(
                    //                                 height: 22,
                    //                                 width: 22,
                    //                                 decoration: BoxDecoration(
                    //                                   border: Border.all(
                    //                                     color:
                    //                                         Constants.darkColor,
                    //                                     width: 1.5,
                    //                                   ),
                    //                                   shape: BoxShape.circle,
                    //                                 ),
                    //                               ),
                    //                             ],
                    //                           ),
                    //                           const SizedBox(height: 5),
                    //                           const Divider(
                    //                               color: Constants.gray),
                    //                         ],
                    //                       ),
                    //                     )
                    //                   : Padding(
                    //                       padding: const EdgeInsets.only(
                    //                           bottom: 10.0, left: 8),
                    //                       child: InkWell(
                    //                         onTap: () {
                    //                           SnapChatAdSetCubit.get(context)
                    //                               .setSelectedLanguages(
                    //                                   state.data[index]);
                    //                           // widget.SnapChatAdSetCubit
                    //                           //     .undoDemoProcess4();
                    //                           // widget.SnapChatAdSetCubit
                    //                           //     .updateAdSetProcess9();
                    //
                    //                           // ReachEstimateCubit.get(context)
                    //                           //     .getReachEstimate(
                    //                           //         context: context,
                    //                           //         imagesFiles:
                    //                           //             SnapChatAdSetCubit.get(
                    //                           //                     context)
                    //                           //                 .adImages,
                    //                           //         videosFiles:
                    //                           //             SnapChatAdSetCubit.get(
                    //                           //                     context)
                    //                           //                 .adVideo);
                    //                           // print("asdasda" +
                    //                           //     widget.SnapChatAdSetCubit.languages
                    //                           //         .toString());
                    //                         },
                    //                         child: Container(
                    //                           alignment: Alignment.center,
                    //                           padding: EdgeInsets.zero,
                    //                           decoration: ShapeDecoration(
                    //                             color: Colors.white,
                    //                             shape: RoundedRectangleBorder(
                    //                                 borderRadius:
                    //                                     BorderRadius.circular(
                    //                                         8)),
                    //                             shadows: const [
                    //                               BoxShadow(
                    //                                 color: Color(0x33000000),
                    //                                 blurRadius: 20,
                    //                                 offset: Offset(0, 0),
                    //                                 spreadRadius: -6,
                    //                               )
                    //                             ],
                    //                           ),
                    //                           child: Padding(
                    //                             padding:
                    //                                 const EdgeInsets.symmetric(
                    //                                     horizontal: 10.0,
                    //                                     vertical: 12),
                    //                             child: Row(
                    //                               crossAxisAlignment:
                    //                                   CrossAxisAlignment.center,
                    //                               mainAxisAlignment:
                    //                                   MainAxisAlignment
                    //                                       .spaceBetween,
                    //                               children: [
                    //                                 Center(
                    //                                   child: ShaderMask(
                    //                                     shaderCallback:
                    //                                         (Rect bounds) {
                    //                                       return const LinearGradient(
                    //                                         colors: [
                    //                                           Color(0xFFFF006F),
                    //                                           Color(0xFFF6BA00),
                    //                                         ],
                    //                                       ).createShader(
                    //                                           bounds);
                    //                                     },
                    //                                     child: CustomText(
                    //                                       text: state
                    //                                               .data[index]
                    //                                               .name ??
                    //                                           "",
                    //                                       color: Colors.white,
                    //                                       fontSize: 14,
                    //                                       fontWeight:
                    //                                           FontWeight.w400,
                    //                                     ),
                    //                                   ),
                    //                                 ),
                    //                                 ShaderMask(
                    //                                   shaderCallback:
                    //                                       (Rect bounds) {
                    //                                     return const LinearGradient(
                    //                                       colors: [
                    //                                         Color(0xFFFF006F),
                    //                                         Color(0xFFF6BA00),
                    //                                       ],
                    //                                     ).createShader(bounds);
                    //                                   },
                    //                                   child: const Icon(
                    //                                     Icons.check_circle,
                    //                                     size: 22,
                    //                                     color: Colors.white,
                    //                                   ),
                    //                                 )
                    //                               ],
                    //                             ),
                    //                           ),
                    //                         ),
                    //                       ),
                    //                     );
                    //             },
                    //           );
                    //         },
                    //       ),
                    //       SizedBox(
                    //         height: 10.h,
                    //       ),
                    //     ],
                    //   ),
                    // BlocBuilder<SnapChatAdSetCubit, SnapChatAdSetState>(
                    //   builder: (context, adState) {
                    //     return SnapChatAdSetCubit.get(context).languages.isEmpty
                    //         ? const SizedBox()
                    //         : Column(
                    //             children: [
                    //               CustomText(
                    //                 text: 'Selected Languages'.tr,
                    //                 fontSize: 14.sp,
                    //                 color: AppColors.iconBottomColor,
                    //                 fontWeight: FontWeight.w400,
                    //                 alignment: AlignmentDirectional.centerStart,
                    //               ),
                    //               SizedBox(
                    //                 height: 10.h,
                    //               ),
                    //               ListView.builder(
                    //                 shrinkWrap: true,
                    //                 physics:
                    //                     const NeverScrollableScrollPhysics(),
                    //                 padding: EdgeInsets.zero,
                    //                 itemCount: SnapChatAdSetCubit.get(context)
                    //                     .languages
                    //                     .length,
                    //                 itemBuilder: (item, index) {
                    //                   return Padding(
                    //                     padding: const EdgeInsets.only(
                    //                         bottom: 10.0, left: 8),
                    //                     child: InkWell(
                    //                       onTap: () {
                    //                         SnapChatAdSetCubit.get(context)
                    //                             .setSelectedLanguages(
                    //                                 SnapChatAdSetCubit.get(
                    //                                         context)
                    //                                     .languages[index]);
                    //
                    //                         // widget.SnapChatAdSetCubit
                    //                         //     .undoDemoProcess4();
                    //                         // widget.SnapChatAdSetCubit
                    //                         //     .updateAdSetProcess9();
                    //
                    //                         // print("asdasda" +
                    //                         //     widget.SnapChatAdSetCubit.languages
                    //                         //         .toString());
                    //                       },
                    //                       child: Container(
                    //                         alignment: Alignment.center,
                    //                         padding: EdgeInsets.zero,
                    //                         decoration: ShapeDecoration(
                    //                           color: Colors.white,
                    //                           shape: RoundedRectangleBorder(
                    //                               borderRadius:
                    //                                   BorderRadius.circular(8)),
                    //                           shadows: const [
                    //                             BoxShadow(
                    //                               color: Color(0x33000000),
                    //                               blurRadius: 20,
                    //                               offset: Offset(0, 0),
                    //                               spreadRadius: -6,
                    //                             )
                    //                           ],
                    //                         ),
                    //                         child: Padding(
                    //                           padding:
                    //                               const EdgeInsets.symmetric(
                    //                                   horizontal: 10.0,
                    //                                   vertical: 12),
                    //                           child: Row(
                    //                             crossAxisAlignment:
                    //                                 CrossAxisAlignment.center,
                    //                             mainAxisAlignment:
                    //                                 MainAxisAlignment
                    //                                     .spaceBetween,
                    //                             children: [
                    //                               Center(
                    //                                 child: ShaderMask(
                    //                                   shaderCallback:
                    //                                       (Rect bounds) {
                    //                                     return const LinearGradient(
                    //                                       colors: [
                    //                                         Color(0xFFFF006F),
                    //                                         Color(0xFFF6BA00),
                    //                                       ],
                    //                                     ).createShader(bounds);
                    //                                   },
                    //                                   child: CustomText(
                    //                                     text: SnapChatAdSetCubit
                    //                                                 .get(
                    //                                                     context)
                    //                                             .languages[
                    //                                                 index]
                    //                                             .name ??
                    //                                         "",
                    //                                     color: Colors.white,
                    //                                     fontSize: 14,
                    //                                     fontWeight:
                    //                                         FontWeight.w400,
                    //                                   ),
                    //                                 ),
                    //                               ),
                    //                               ShaderMask(
                    //                                 shaderCallback:
                    //                                     (Rect bounds) {
                    //                                   return const LinearGradient(
                    //                                     colors: [
                    //                                       Color(0xFFFF006F),
                    //                                       Color(0xFFF6BA00),
                    //                                     ],
                    //                                   ).createShader(bounds);
                    //                                 },
                    //                                 child: const Icon(
                    //                                   Icons.check_circle,
                    //                                   size: 22,
                    //                                   color: Colors.white,
                    //                                 ),
                    //                               )
                    //                             ],
                    //                           ),
                    //                         ),
                    //                       ),
                    //                     ),
                    //                   );
                    //                 },
                    //               ),
                    //             ],
                    //           );
                    //   },
                    // ),
                    // const SizedBox(height: 25),
                    // const Padding(
                    //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                    //   child: Divider(color: Constants.textColor),
                    // ),
                    // BlocBuilder<ReachEstimateCubit, ReachEstimateState>(
                    //   builder: (context, state) {
                    //     if(state is ReachStateLoaded){
                    //       return EstimatedCardWidget(upperNum: state.reachEstimatedModel?.usersUpperBound?.toDouble() ?? 0.0, lowerNum: state.reachEstimatedModel?.usersLowerBound?.toDouble() ?? 0.0,);
                    //     }else{
                    //       return const SizedBox();
                    //     }
                    //   },
                    // ),
                    SizedBox(
                      width: 235.w,
                      child: ButtonWidget(
                        text: "Save".tr,
                        onTap: () {
                          // if (SnapChatAdSetCubit.get(context).selectedMinAge ==
                          //     null) {
                          //   showErrorToast("please select min age".tr);
                          // } else if (SnapChatAdSetCubit.get(context)
                          //         .selectedMaxAge ==
                          //     null) {
                          //   showErrorToast("please select max age".tr);
                          // } else if (SnapChatAdSetCubit.get(context)
                          //             .selectedMaxAge! <
                          //         18 ||
                          //     SnapChatAdSetCubit.get(context).selectedMinAge! <
                          //         18) {
                          //   showErrorToast("Valid age must be over or 18".tr);
                          // } else if (SnapChatAdSetCubit.get(context)
                          //         .selectedMaxAge! <
                          //     SnapChatAdSetCubit.get(context).selectedMinAge!) {
                          //   showErrorToast(
                          //       "Min age must be less than max age".tr);
                          // } else if (SnapChatAdSetCubit.get(context)
                          //     .genders
                          //     .isEmpty) {
                          //   showErrorToast("please select the gender".tr);
                          // } else if (SnapChatAdSetCubit.get(context)
                          //     .languages
                          //     .isEmpty) {
                          //   showErrorToast("please select the languages".tr);
                          // }
                          // else {
                          // widget.SnapChatAdSetCubit.adModel =
                          //     SnapChatAdSetCubit.get(context).adModel.copyWith(
                          //         ageMin: widget.SnapChatAdSetCubit.selectedMinAge,
                          //         ageMax: widget.SnapChatAdSetCubit.selectedMaxAge,
                          //         languages: widget.SnapChatAdSetCubit.languages,
                          //         genders: widget.SnapChatAdSetCubit.genders);
                          // print("testadasd" +
                          //     widget.SnapChatAdSetCubit.adModel
                          //         .toJson()
                          //         .toString());
                          CreateSnapChatAdCubit.get(context).snapChatAdModel =
                              CreateSnapChatAdCubit.get(context)
                                  .snapChatAdModel
                                  .copyWith(
                                    minAge: SnapChatAdSetCubit.get(context)
                                        .selectedMinAge
                                        .toString(),
                                    maxAge: SnapChatAdSetCubit.get(context)
                                        .selectedMaxAge
                                        .toString(),
                                    gender: SnapChatAdSetCubit.get(context)
                                            .genders
                                            .first ??
                                        "",
                                  );
                          if (CreateSnapChatAdCubit.get(context)
                                  .adSetPercentage <=
                              0.83) {
                            CreateSnapChatAdCubit.get(context).adSetPercentage =
                                CreateSnapChatAdCubit.get(context)
                                        .adSetPercentage +
                                    0.17;
                          }
                          CreateSnapChatAdCubit.get(context)
                              .updateDemographicPercentage();
                          Navigator.of(context).pop();
                          // }
                        },
                      ),
                    ),
                    SizedBox(
                      height: 60.h,
                    ),
                  ],
                ),
              ),
            ),
            // floatingActionButton: Column(
            //   mainAxisAlignment: MainAxisAlignment.end,
            //   crossAxisAlignment: CrossAxisAlignment.center,
            //   children: [
            //     BlocBuilder<ReachEstimateCubit, ReachEstimateState>(
            //       builder: (context, state) {
            //         if (state is ReachStateLoaded) {
            //           return Card(
            //               margin: EdgeInsets.symmetric(horizontal: 20.w),
            //               elevation: 0.0,
            //               color: Colors.white,
            //               shape: RoundedRectangleBorder(
            //                   side: const BorderSide(color: Colors.black),
            //                   borderRadius: BorderRadius.circular(25.0)),
            //               child: EstimatedCardWidget(
            //                 upperNum: state.reachEstimatedModel?.usersUpperBound
            //                         ?.toDouble() ??
            //                     0.0,
            //                 lowerNum: state.reachEstimatedModel?.usersLowerBound
            //                         ?.toDouble() ??
            //                     0.0,
            //                 isDemoGraphic: true,
            //               ));
            //         } else {
            //           return const SizedBox.shrink();
            //         }
            //       },
            //     ),
            //   ],
            // ),
            // floatingActionButtonLocation:
            //     FloatingActionButtonLocation.centerFloat,
          );
        },
      ),
    );
  }

  List<String> genders = ["Male".tr, "Female".tr, "All genders".tr];
}

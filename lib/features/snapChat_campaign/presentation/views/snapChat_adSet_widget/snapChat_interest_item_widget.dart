import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/res/constants.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../data/models/snapChat_interests_response.dart';

class SnapChatInterestItem extends StatelessWidget {
  final SnapChatInterestsModel interest;
  final bool isSelected;
  final VoidCallback onTap;

  const SnapChatInterestItem({
    super.key,
    required this.interest,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: ShapeDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFFF6BA00), Color(0xFFFF006F)])
              : null,
          color: !isSelected ? const Color(0xFFFB533E).withOpacity(0.1) : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(36.59),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: CustomText(
                  text: interest.name?.replaceAll(RegExp(r'\(.*?\)'), "") ?? "",
                  fontWeight: FontWeight.w700,
                  color: isSelected ? Colors.white : Constants.primaryTextColor,
                  fontSize: 10.sp,
                  textOverflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                isSelected ? Icons.check : Icons.add,
                color: isSelected ? Colors.white : Constants.primaryTextColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

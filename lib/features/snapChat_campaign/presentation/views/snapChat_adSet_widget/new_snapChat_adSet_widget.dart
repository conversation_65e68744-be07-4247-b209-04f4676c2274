import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/maps/presentation/controllers/select_map_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/utils/res/router/routes.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/circular_percent_indicator_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../../../../../widgets/text_field_widget.dart';
import '../../../../../../../widgets/unfinished_target_widget.dart';
import '../../../../sidebar/ad_accounts/presentation/controllers/get_snapChat_add_accounts/get_snap_chat_add_accounts_cubit.dart';
import '../../controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';

class NewSnapChatAdSetWidget extends StatefulWidget {
  const NewSnapChatAdSetWidget({super.key});

  @override
  State<NewSnapChatAdSetWidget> createState() => _NewSnapChatAdSetWidgetState();
}

class _NewSnapChatAdSetWidgetState extends State<NewSnapChatAdSetWidget> {
  FocusNode dailyBudgetNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => SelectMapCubit(),
        ),
        // BlocProvider(
        //   create: (context) =>
        //       GetAdAccountsCubit()..getAdAccounts(context: context),
        // ),
      ],
      child: BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
        builder: (context, state) {
          return BlocBuilder<SnapChatAdSetCubit, SnapChatAdSetState>(
            builder: (context, state) {
              return Column(
                children: [
                  const SizedBox(height: 20),
                  Form(
                    key: SnapChatAdSetCubit.get(context).adSetFormKey,
                    child: CustomTextFormField(
                      controller:
                          SnapChatAdSetCubit.get(context).adSetNameController,
                      textFontSize: 12,
                      key: const ValueKey('adset_name'),
                      hintText: "Ad Set Name".tr,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                      validator: (value) =>
                          AppValidator.validateIdentity(value, context),
                      onChanged: (val) {
                        if (SnapChatAdSetCubit.get(context)
                            .adSetNameController
                            .text
                            .isNotEmpty) {
                          // CreateAdCubit.get(context).updateAdSetProcess1();
                        } else {
                          // CreateAdCubit.get(context).undoAdSetProcess1();
                        }
                      },
                    ),
                  ),
                  // const SizedBox(height: 20),
                  // const Padding(
                  //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                  //   child: Divider(color: Constants.textColor),
                  // ),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  //   child: Row(
                  //     children: [
                  //       CustomText(
                  //         text: "Saved Audience".tr,
                  //         fontSize: 12.sp,
                  //         fontWeight: FontWeight.w400,
                  //         alignment: AlignmentDirectional.centerStart,
                  //         color: Constants.primaryTextColor,
                  //       ),
                  //       CustomText(
                  //         text: "*",
                  //         fontSize: 16.sp,
                  //         fontWeight: FontWeight.w400,
                  //         alignment: AlignmentDirectional.centerStart,
                  //         color: Constants.redColor,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // 20.verticalSpace,
                  // ExpansionTileItem(
                  //   expansionKey: Constants.optimizationKey,
                  //   onExpansionChanged: (val) {},
                  //   childrenPadding: EdgeInsets.zero,
                  //   iconColor: AppColors.secondColor,
                  //   collapsedIconColor: AppColors.secondColor,
                  //   expandedAlignment: Alignment.center,
                  //   expandedCrossAxisAlignment: CrossAxisAlignment.center,
                  //   trailing: const Row(
                  //     mainAxisSize: MainAxisSize.min,
                  //     children: [
                  //       Padding(
                  //         padding: EdgeInsets.only(left: 6.0),
                  //         child: Icon(
                  //           Icons.expand_more,
                  //           size: 30.0,
                  //           color: Constants.darkColor,
                  //         ),
                  //       )
                  //     ],
                  //   ),
                  //   title: CreateAdCubit.get(context).optimization != null
                  //       ? CustomText(
                  //           fontSize: 12.sp,
                  //           fontWeight: FontWeight.w500,
                  //           text:
                  //               CreateAdCubit.get(context).optimization?.showName ??
                  //                   "")
                  //       : AccountHintText(
                  //           isDefaultHint: false,
                  //           hint: 'Saved Audience'.tr,
                  //         ),
                  //   decoration: ShapeDecoration(
                  //     color: Colors.white,
                  //     shape: RoundedRectangleBorder(
                  //       borderRadius: BorderRadius.circular(20),
                  //     ),
                  //     shadows: const [
                  //       BoxShadow(
                  //         color: Color(0x3F000000),
                  //         blurRadius: 40,
                  //         offset: Offset(0, 0),
                  //         spreadRadius: -10,
                  //       )
                  //     ],
                  //   ),
                  //   children: [
                  //     ListView.builder(
                  //       shrinkWrap: true,
                  //       physics: const NeverScrollableScrollPhysics(),
                  //       itemCount:
                  //           GetOptimizationsCubit.get(context).opt.length + 1,
                  //       // 3 for the first text + 2 for the last text
                  //       itemBuilder: (context, index) {
                  //         // Check if the index is for the text before the first three items
                  //         if (index == 0) {
                  //           return Padding(
                  //             padding: const EdgeInsets.all(16.0),
                  //             child: CustomText(
                  //                 text: 'Brand Awareness'.tr,
                  //                 fontSize: 13.sp,
                  //                 fontWeight: FontWeight.w600),
                  //           );
                  //         } else
                  //         //   if (index >= 1
                  //         //     // && index <= 3
                  //         // )
                  //         {
                  //           // Return the first three items
                  //           final optIndex =
                  //               index - 1; // Adjust index for the optimization list
                  //           return InkWell(
                  //             onTap: () {
                  //               // CreateAdCubit.get(context).updateCampaignProcess4();
                  //               GetOptimizationsCubit.get(context)
                  //                   .setSelectedOptimization(
                  //                       GetOptimizationsCubit.get(context)
                  //                           .opt[optIndex]);
                  //               print("asdfafgdsg" +
                  //                   CreateAdCubit.get(context)
                  //                       .optimization!
                  //                       .actualName
                  //                       .toString());
                  //               GetBillingEventsCubit.get(context).getBillingEvents(
                  //                   context: context,
                  //                   optimizationId:
                  //                       GetOptimizationsCubit.get(context)
                  //                               .opt[optIndex]
                  //                               .id ??
                  //                           0);
                  //               CreateAdCubit.get(context).billingEvent = null;
                  //               Constants.optimizationKey.currentState?.collapse();
                  //             },
                  //             child: Container(
                  //               decoration: BoxDecoration(
                  //                 color: Constants.gray.withOpacity(0.15),
                  //                 border: Border.symmetric(
                  //                   horizontal: BorderSide(
                  //                       color: Constants.gray.withOpacity(0.3)),
                  //                 ),
                  //               ),
                  //               child: Padding(
                  //                 padding: const EdgeInsets.symmetric(
                  //                     vertical: 18, horizontal: 20),
                  //                 child: CustomText(
                  //                   maxLines: 3,
                  //                   fontSize: 12.sp,
                  //                   text: GetOptimizationsCubit.get(context)
                  //                           .opt[optIndex]
                  //                           .showName ??
                  //                       "",
                  //                 ),
                  //               ),
                  //             ),
                  //           );
                  //         }
                  //       },
                  //     ),
                  //   ],
                  // ),

                  const SizedBox(height: 20),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  //   child: CustomText(
                  //     text: 'Ad Type'.tr,
                  //     color: Constants.primaryTextColor,
                  //     fontSize: 14.sp,
                  //     fontWeight: FontWeight.w600,
                  //   ),
                  // ),
                  // const SizedBox(height: 10),
                  // Container(
                  //   width: double.infinity,
                  //   padding:
                  //       const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  //   decoration: ShapeDecoration(
                  //     shape: RoundedRectangleBorder(
                  //       side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                  //       borderRadius: BorderRadius.circular(50),
                  //     ),
                  //   ),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           Image.asset(
                  //             AppAssets.adTypeImg,
                  //             height: 30.h,
                  //             width: 30.h,
                  //           ),
                  //           10.horizontalSpace,
                  //           CustomText(
                  //             text: 'Image/Video'.tr,
                  //             color: Constants.primaryTextColor,
                  //             fontSize: 14.sp,
                  //             fontWeight: FontWeight.w700,
                  //           )
                  //         ],
                  //       ),
                  //       CustomSwitch(
                  //         value: CreateSnapChatAdCubit.get(context)
                  //             .imageAndVideoPlatformActive,
                  //         onChanged: (newValue) {
                  //           CreateSnapChatAdCubit.get(context)
                  //               .imageAndVideoPlatformActiveStatus(newValue);
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // const SizedBox(height: 10),
                  // Container(
                  //   width: double.infinity,
                  //   padding:
                  //       const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  //   decoration: ShapeDecoration(
                  //     shape: RoundedRectangleBorder(
                  //       side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                  //       borderRadius: BorderRadius.circular(50),
                  //     ),
                  //   ),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           CustomSvgWidget(
                  //             svg: AppAssets.adTypeStoryAd,
                  //             height: 30.h,
                  //             width: 30.h,
                  //           ),
                  //           10.horizontalSpace,
                  //           CustomText(
                  //             text: 'Story Ad'.tr,
                  //             color: Constants.primaryTextColor,
                  //             fontSize: 14.sp,
                  //             fontWeight: FontWeight.w700,
                  //           )
                  //         ],
                  //       ),
                  //       CustomSwitch(
                  //         value: CreateSnapChatAdCubit.get(context)
                  //             .storyAdPlatformActive,
                  //         onChanged: (newValue) {
                  //           CreateSnapChatAdCubit.get(context)
                  //               .storyAdPlatformActiveStatus(newValue);
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // const SizedBox(height: 10),
                  // Container(
                  //   width: double.infinity,
                  //   padding:
                  //       const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  //   decoration: ShapeDecoration(
                  //     shape: RoundedRectangleBorder(
                  //       side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                  //       borderRadius: BorderRadius.circular(50),
                  //     ),
                  //   ),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           Image.asset(
                  //             AppAssets.adTypeCollectionAndAR,
                  //             height: 30.h,
                  //             width: 30.h,
                  //           ),
                  //           10.horizontalSpace,
                  //           CustomText(
                  //             text: 'Collection Ad'.tr,
                  //             color: Constants.primaryTextColor,
                  //             fontSize: 14.sp,
                  //             fontWeight: FontWeight.w700,
                  //           )
                  //         ],
                  //       ),
                  //       CustomSwitch(
                  //         value: CreateSnapChatAdCubit.get(context)
                  //             .collectionAdPlatformActive,
                  //         onChanged: (newValue) {
                  //           CreateSnapChatAdCubit.get(context)
                  //               .collectionAdPlatformActiveStatus(newValue);
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // const SizedBox(height: 10),
                  // Container(
                  //   width: double.infinity,
                  //   padding:
                  //       const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  //   decoration: ShapeDecoration(
                  //     shape: RoundedRectangleBorder(
                  //       side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                  //       borderRadius: BorderRadius.circular(50),
                  //     ),
                  //   ),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           Image.asset(
                  //             AppAssets.adTypeCollectionAndAR,
                  //             height: 30.h,
                  //             width: 30.h,
                  //           ),
                  //           10.horizontalSpace,
                  //           CustomText(
                  //             text: 'AR Ad'.tr,
                  //             color: Constants.primaryTextColor,
                  //             fontSize: 14.sp,
                  //             fontWeight: FontWeight.w700,
                  //           )
                  //         ],
                  //       ),
                  //       CustomSwitch(
                  //         value:
                  //             CreateSnapChatAdCubit.get(context).aRPlatformActive,
                  //         onChanged: (newValue) {
                  //           CreateSnapChatAdCubit.get(context)
                  //               .aRPlatformActiveStatus(newValue);
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // const SizedBox(height: 20),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(color: Constants.textColor),
                  ),
                  const SizedBox(height: 10),
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, Routes.map, arguments: {
                        "cubit": CreateAdCubit.get(context),
                        "isFromSnapChat": true
                      });
                    },
                    child: UnFinishedTargetWidget(
                      name: "Location".tr,
                      icon: AppAssets.location,
                      processPercentage: CircularIndicatorWidget(
                        isDemographic: false,
                        isLocation: true,
                        isTargeting: false,
                        isAdSet: false,
                        isAdCreative: false,
                        adCubit: CreateAdCubit.get(context),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  BlocProvider(
                    create: (context) => CreateSnapChatAdCubit(),
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                            context, Routes.snapChatDemographicScreen,
                            arguments: SnapChatAdSetCubit.get(context));
                      },
                      child: UnFinishedTargetWidget(
                        name: 'Demographic'.tr,
                        icon: AppAssets.demo,
                        processPercentage: CircularPercentIndicator(
                          circularStrokeCap: CircularStrokeCap.round,
                          radius: 12.0,
                          lineWidth: 5.5,
                          // percent:
                          // CreateSnapChatAdCubit.get(context).campaignPercentage,
                          linearGradient: Constants.secGradient,
                          backgroundColor:
                              const Color(0xFFFB533E).withOpacity(0.1),
                          reverse: true,
                          percent: CreateSnapChatAdCubit.get(context)
                              .demographicPercentage,
                          // isDemographic: true,
                          // isLocation: false,
                          // isTargeting: false,
                          // isAdSet: false,
                          // isAdCreative: false,
                          // adCubit: CreateAdCubit.get(context),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  BlocProvider(
                    create: (context) => CreateSnapChatAdCubit(),
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          Routes.snapChatInterestsScreen,
                        );
                      },
                      child: UnFinishedTargetWidget(
                        name: "Audience Interests".tr,
                        icon: AppAssets.target,
                        processPercentage: CircularPercentIndicator(
                          circularStrokeCap: CircularStrokeCap.round,
                          radius: 12.0,
                          lineWidth: 5.5,
                          // percent:
                          // CreateSnapChatAdCubit.get(context).campaignPercentage,
                          linearGradient: Constants.secGradient,
                          backgroundColor:
                              const Color(0xFFFB533E).withOpacity(0.1),
                          reverse: true,
                          percent: CreateSnapChatAdCubit.get(context)
                              .audienceInterestsPercentage,
                          // isDemographic: false,
                          // isLocation: false,
                          // isTargeting: true,
                          // isAdSet: false,
                          // isAdCreative: false,
                          // adCubit: CreateAdCubit.get(context),
                        ),
                      ),
                    ),
                  ),
                  // const SizedBox(height: 10),
                  // InkWell(
                  //   onTap: () {
                  //     Navigator.pushNamed(context, Routes.demo,
                  //         arguments: CreateAdCubit.get(context));
                  //   },
                  //   child: UnFinishedTargetWidget(
                  //     name: 'Goal & Bid'.tr,
                  //     icon: AppAssets.demo,
                  //     processPercentage: CircularIndicatorWidget(
                  //       isDemographic: true,
                  //       isLocation: false,
                  //       isTargeting: false,
                  //       isAdSet: false,
                  //       isAdCreative: false,
                  //       adCubit: CreateAdCubit.get(context),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(height: 40.h),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  //   children: destinationType.map((e) {
                  //
                  //      CreateAdCubit.get(context).destinationIndex = destinationType.indexOf(e);
                  //      CreateAdCubit.get(context).isSelectedDestination= CreateAdCubit.get(context).destinationIndex == CreateAdCubit.get(context).selectedDestinationIndex;
                  //
                  //
                  //     return DestinationTypeWidget(
                  //       isSelected: CreateAdCubit.get(context).isSelectedDestination ?? false,
                  //       callback: (index) {
                  //         CreateAdCubit.get(context).updateAdSetProcess3();
                  //
                  //         setState(() {
                  //
                  //           CreateAdCubit.get(context).selectedDestinationIndex = index;
                  //
                  //           // Update destination type based on index
                  //           if (e == "Whatsapp") {
                  //             CreateAdCubit.get(context).destinationType =
                  //                 "WHATSAPP";
                  //           } else if (e == "Messenger") {
                  //             CreateAdCubit.get(context).destinationType =
                  //                 "MESSENGER";
                  //           } else {
                  //             CreateAdCubit.get(context).destinationType =
                  //                 "INSTAGRAM_DIRECT";
                  //           }
                  //           print("INSTAGRAM" +
                  //               CreateAdCubit.get(context).destinationType);
                  //         });
                  //       },
                  //       name: e,
                  //       index: CreateAdCubit.get(context).destinationIndex ?? 0,
                  //     );
                  //   }).toList(),
                  // ),
                  // SizedBox(height: 40.h),
                  const SizedBox(height: 20),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(color: Constants.textColor),
                  ),
                  const SizedBox(height: 20),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: CustomText(
                      text: 'Budget Modes'.tr,
                      color: Constants.primaryTextColor,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 10),
                  buildBudgetModeRadio(),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: "Daily Budget".tr,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        color: Constants.primaryTextColor,
                      ),
                      Container(
                        width: 125.w,
                        height: 42.h,
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x33000000),
                              blurRadius: 20,
                              offset: Offset(0, 0),
                              spreadRadius: -4,
                            )
                          ],
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 80.w,
                              child: CustomTextField(
                                textInputType: TextInputType.number,
                                borderColor: Colors.transparent,
                                hintText: "0",
                                maxLength: 5,
                                hintStyle: const TextStyle(
                                    fontSize: 16, color: Constants.gray),
                                controller:
                                    SnapChatAdSetCubit.get(context).dailyBudget,
                                onChanged: (val) {
                                  if (SnapChatAdSetCubit.get(context)
                                      .dailyBudget
                                      .text
                                      .isNotEmpty) {
                                    // CreateAdCubit.get(context)
                                    //     .updateAdSetProcess4();

                                    if (val.length > 1) {
                                      CreateSnapChatAdCubit.get(context)
                                              .snapChatAdModel =
                                          CreateSnapChatAdCubit.get(context)
                                              .snapChatAdModel
                                              .copyWith(
                                                lifeTimeBudgetMicro: (double
                                                        .parse(
                                                            SnapChatAdSetCubit
                                                                    .get(
                                                                        context)
                                                                .dailyBudget
                                                                .text))
                                                    .toInt()
                                                    .toString(), // Convert to int if needed for API
                                              );
                                    }
                                  } else {
                                    // CreateAdCubit.get(context).undoAdSetProcess4();

                                    // if (val.length > 1) {
                                    //   CreateAdCubit.get(context).adModel =
                                    //       CreateAdCubit.get(context)
                                    //           .adModel
                                    //           .copyWith(
                                    //             dailyBudget: (double.parse(
                                    //                         CreateAdCubit.get(
                                    //                                 context)
                                    //                             .dailyBudget
                                    //                             .text) *
                                    //                     100)
                                    //                 .toInt(),
                                    //           );
                                    // }
                                  }
                                },
                                validator: (value) =>
                                    AppValidator.validateIdentity(
                                        value, context),
                              ),
                            ),
                            const VerticalDivider(
                              width: 2,
                              color: Constants.primaryTextColor,
                            ),
                            SizedBox(
                                width: 40.w,
                                child: FittedBox(
                                  child: Padding(
                                    padding: const EdgeInsets.all(3.0),
                                    child: CustomText(
                                      text:
                                          // GetAdAccountsCubit.get(adContext)
                                          //         .adAccounts
                                          //         .where((element) =>
                                          //             element.id ==
                                          //             instance<HiveHelper>()
                                          //                 .getUser()
                                          //                 ?.defaultAccountId)
                                          //         .isEmpty
                                          //     ?
                                          GetSnapChatAddAccountsCubit.get(
                                                      context)
                                                  .selectedDefaultSnapChatAccount
                                                  ?.currency ??
                                              "AED",
                                      // : GetAdAccountsCubit.get(adContext)
                                      //         .adAccounts
                                      //         .where((element) =>
                                      //             element.id ==
                                      //             instance<HiveHelper>()
                                      //                 .getUser()
                                      //                 ?.defaultAccountId)
                                      //         .first
                                      //         .currency ??
                                      //     "",
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Constants.primaryTextColor,
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 15.h),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Divider(color: Constants.textColor),
                  ),
                  SizedBox(height: 15.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CustomText(
                            text: "Start Date".tr,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: Constants.primaryTextColor,
                          ),
                          10.horizontalSpace,
                          GestureDetector(
                            onTap: () {
                              dailyBudgetNode.unfocus();
                              // if (!dailyBudgetNode.hasFocus) {
                              //   ReachEstimateCubit.get(context).getReachEstimate(
                              //       context: context,
                              //       imagesFiles:
                              //           CreateAdCubit.get(context).adImages,
                              //       videosFiles:
                              //           CreateAdCubit.get(context).adVideo);
                              // }
                              _showDateTimePicker(
                                  context,
                                  SnapChatAdSetCubit.get(context).startDate,
                                  true);
                            },
                            child: AbsorbPointer(
                              child: Container(
                                width: 80.w,
                                height: 40.h,
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x33000000),
                                      blurRadius: 20,
                                      offset: Offset(0, 0),
                                      spreadRadius: -4,
                                    )
                                  ],
                                ),
                                child: CustomTextField(
                                  onSaved: (val) {
                                    // CreateAdCubit.get(context)
                                    //     .updateAdSetProcess5();
                                  },
                                  validator: (value) =>
                                      AppValidator.validateIdentity(
                                          value, context),
                                  textInputType: TextInputType.number,
                                  borderColor: Colors.transparent,
                                  hintText: "dd/mm/yy",
                                  style: const TextStyle(
                                      fontSize: 10, color: Colors.black),
                                  hintStyle: const TextStyle(
                                      fontSize: 12, color: Constants.gray),
                                  controller:
                                      SnapChatAdSetCubit.get(context).startDate,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          CustomText(
                            text: "End Date".tr,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: Constants.primaryTextColor,
                          ),
                          10.horizontalSpace,
                          GestureDetector(
                            onTap: () {
                              _showDateTimePicker(
                                  context,
                                  SnapChatAdSetCubit.get(context).endDate,
                                  false);
                            },
                            child: AbsorbPointer(
                              child: Container(
                                width: 80.w,
                                height: 40.h,
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x33000000),
                                      blurRadius: 20,
                                      offset: Offset(0, 0),
                                      spreadRadius: -4,
                                    )
                                  ],
                                ),
                                child: CustomTextField(
                                  textInputType: TextInputType.number,
                                  borderColor: Colors.transparent,

                                  hintText: "dd/mm/yy",
                                  style: const TextStyle(
                                      fontSize: 10, color: Colors.black),
                                  hintStyle: const TextStyle(
                                      fontSize: 12, color: Constants.gray),
                                  controller:
                                      SnapChatAdSetCubit.get(context).endDate,
                                  validator: (value) =>
                                      AppValidator.validateIdentity(
                                          value, context),
                                  // TextField properties
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 15.h),
                  // const Padding(
                  //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                  //   child: Divider(color: Constants.textColor),
                  // ),
                  // SizedBox(height: 15.h),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     CustomText(
                  //       text: "Total Budget",
                  //       fontSize: 16.sp,
                  //       fontWeight: FontWeight.w400,
                  //       color: Constants.primaryTextColor,
                  //     ),
                  //     Container(
                  //       padding: EdgeInsets.zero,
                  //       width: 125.w,
                  //       // height: 42.h,
                  //       decoration: ShapeDecoration(
                  //         color: Constants.gray.withOpacity(0.3),
                  //         shape: RoundedRectangleBorder(
                  //           borderRadius: BorderRadius.circular(12),
                  //         ),
                  //       ),
                  //       child: Padding(
                  //         padding: EdgeInsets.symmetric(
                  //             vertical: 15.sp, horizontal: 20.sp),
                  //         child: Row(
                  //           mainAxisAlignment: MainAxisAlignment.center,
                  //           children: [
                  //             BlocBuilder<GetAdAccountsCubit, GetAdAccountsState>(
                  //               builder: (adContext, state) {
                  //                 return CustomText(
                  //                   text:
                  //                       "${CreateAdCubit.get(context).dailyBudget.text} ${GetAdAccountsCubit.get(adContext).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).isEmpty ? "AED" : GetAdAccountsCubit.get(adContext).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.currency ?? ""}",
                  //                   fontSize: 14.sp,
                  //                   fontWeight: FontWeight.w400,
                  //                   color: Constants.primaryTextColor,
                  //                 );
                  //               },
                  //             ),
                  //           ],
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // ),
                  // const SizedBox(height: 20),
                  // const Padding(
                  //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                  //   child: Divider(color: Constants.textColor),
                  // ),
                  // const SizedBox(height: 5),
                  // BlocBuilder<ReachEstimateCubit, ReachEstimateState>(
                  //   builder: (context, state) {
                  //     if (state is ReachStateLoaded) {
                  //       return EstimatedCardWidget(
                  //         upperNum: state.reachEstimatedModel?.usersUpperBound
                  //                 ?.toDouble() ??
                  //             0.0,
                  //         lowerNum: state.reachEstimatedModel?.usersLowerBound
                  //                 ?.toDouble() ??
                  //             0.0,
                  //         isDemoGraphic: false,
                  //       );
                  //     } else {
                  //       return const SizedBox();
                  //     }
                  //   },
                  // ),
                  // SizedBox(height: 40.h),
                  SizedBox(
                    width: 235.w,
                    child: ButtonWidget(
                      text: "Save".tr,
                      onTap: () {
                        // Validate the form
                        if (SnapChatAdSetCubit.get(context)
                            .adSetFormKey
                            .currentState!
                            .validate()) {
                          // If the form is valid, check required fields
                          if (areRequiredFieldsValid()) {
                            // Proceed with model update if all fields are valid
                            if (SnapChatAdSetCubit.get(context)
                                .dailyBudget
                                .text
                                .contains('.')) {
                              showErrorToast(
                                  'Not accept decimal in daily budget');
                              return;
                            }
                            // if (CreateAdCubit.get(context)
                            //     .selectedInterests
                            //     .isEmpty) {
                            //   showErrorToast(
                            //       "Please select your detailed targeting");
                            //   return;
                            // }
                            if (SnapChatAdSetCubit.get(context)
                                        .selectedMinAge ==
                                    null &&
                                SnapChatAdSetCubit.get(context)
                                        .selectedMaxAge ==
                                    null &&
                                // CreateAdCubit.get(context).languages.isEmpty &&
                                SnapChatAdSetCubit.get(context)
                                    .genders
                                    .isEmpty) {
                              showErrorToast("Please select your demographic");
                              return;
                            }
                            if (SnapChatAdSetCubit.get(context)
                                .endDate
                                .text
                                .isNotEmpty) {
                              if (DateTime.tryParse(
                                      SnapChatAdSetCubit.get(context)
                                          .endDate
                                          .text)!
                                  .isBefore(DateTime.tryParse(
                                      SnapChatAdSetCubit.get(context)
                                          .startDate
                                          .text)!)) {
                                showErrorToast(
                                    'The end date should be after start date');
                                return;
                              }
                            }
                            CreateSnapChatAdCubit.get(context).snapChatAdModel =
                                CreateSnapChatAdCubit.get(context)
                                    .snapChatAdModel
                                    .copyWith(
                                      adSquadName:
                                          SnapChatAdSetCubit.get(context)
                                                  .adSetNameController
                                                  .text ??
                                              "",
                                      // lifeTimeBudgetMicro:
                                      //     SnapChatAdSetCubit.get(context)
                                      //         .dailyBudget
                                      //         .text,
                                      startTime: SnapChatAdSetCubit.get(context)
                                          .startDate
                                          .text,
                                      endTime: SnapChatAdSetCubit.get(context)
                                          .endDate
                                          .text,
                                      selectedBudgetModes:
                                          SnapChatAdSetCubit.get(context)
                                              .selectedBudgetMode,
                                    );
                            CreateSnapChatAdCubit.get(context)
                                .completeAdSetCubit(true);
                            if (CreateSnapChatAdCubit.get(context)
                                    .adSetPercentage !=
                                1.0) {
                              CreateSnapChatAdCubit.get(context)
                                      .adSetPercentage =
                                  CreateSnapChatAdCubit.get(context)
                                          .adSetPercentage +
                                      0.49;
                            }
                            setState(() {});
                            // if (CreateAdCubit.get(context)
                            //     .publisherPlatforms
                            //     .isEmpty) {
                            //   showErrorToast('Choose a platform');
                            //   return;
                            // }
                            // updateAdModel();
                            // CreateAdCubit.get(context).isAdSetCreated = true;
                            Constants.snapChatAdSetTileKey.currentState
                                ?.collapse();
                            // print("Geo Locations: " +
                            //     CreateAdCubit.get(context)
                            //         .adModel
                            //         .geoLocations
                            //         .toString());
                          }
                        } else {
                          // Show error toast if the form is invalid
                          showErrorToast(
                              "Please fill in all the required fields correctly.");
                        }
                      },
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  bool areRequiredFieldsValid() {
    final cubit = SnapChatAdSetCubit.get(context);

    if (cubit.dailyBudget.text.isEmpty) {
      showErrorToast("Please enter your budget".tr);
      return false;
    } else if (cubit.adSetNameController.text.isEmpty) {
      showErrorToast("Please enter ad set name".tr);
      return false;
    } else if (cubit.startDate.text.isEmpty) {
      showErrorToast("Please enter start date".tr);
      return false;
    } else if (cubit.endDate.text.isEmpty &&
        cubit.selectedBudgetMode == 'BUDGET_MODE_TOTAL') {
      showErrorToast("Please enter end date".tr);
      return false;
    } else if (CreateSnapChatAdCubit.get(context)
            .snapChatAdModel
            .geoLocations
            ?.isEmpty ==
        true) {
      showErrorToast("Please select target location".tr);
      return false;
    }
    return true;
  }

  void _showDateTimePicker(
    BuildContext context,
    TextEditingController controller,
    bool isStartDate,
  ) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Constants.primaryTextColor,
              onPrimary: Colors.white,
              onSurface: Constants.primaryTextColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Constants.primaryTextColor,
              ),
            ),
          ),
          child: child ?? const Text(""),
        );
      },
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      final DateTime now = DateTime.now();
      final bool isToday = pickedDate.year == now.year &&
          pickedDate.month == now.month &&
          pickedDate.day == now.day;

      TimeOfDay initialTime = isToday
          ? TimeOfDay.fromDateTime(now)
          : const TimeOfDay(hour: 0, minute: 0);

      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: initialTime,
        builder: (BuildContext context, Widget? child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: const ColorScheme.light(
                primary: Constants.primaryTextColor,
                onPrimary: Colors.white,
                onSurface: Constants.primaryTextColor,
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  foregroundColor: Constants.primaryTextColor,
                ),
              ),
            ),
            child: child ?? const Text(""),
          );
        },
      );

      if (pickedTime == null) {
        return; // User canceled time selection
      }

      final combinedDateTime = DateTime(
        pickedDate.year,
        pickedDate.month,
        pickedDate.day,
        pickedTime.hour,
        pickedTime.minute,
      );

      // Check if selected datetime is in the past
      if (combinedDateTime.isBefore(DateTime.now())) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text("Invalid Date/Time"),
              content:
                  const Text("Selected date and time cannot be in the past."),
              actions: [
                TextButton(
                  onPressed: () {
                    // if (combinedDateTime != null) {
                    //   TiktokAdGroupCubit
                    //       .get(context)
                    //       .tiktokAdGroupPercentage =
                    //       TiktokAdGroupCubit
                    //           .get(context)
                    //           .tiktokAdGroupPercentage +
                    //           0.11111111111111;
                    //   // print(
                    //   //     'tiktokCampaignPercentage ${TiktokAdGroupCubit
                    //   //         .get(context)
                    //   //         .tiktokAdGroupPercentage}');
                    //   setState(() {});
                    // } else {
                    //   // TiktokCampaignCubit
                    //   //     .get(context)
                    //   //     .tiktokCampaignPercentage =
                    //   //     TiktokCampaignCubit
                    //   //         .get(context)
                    //   //         .tiktokCampaignPercentage -
                    //   //         0.11111111111111;
                    //   // print(
                    //   //     'tiktokCampaignPercentage ${TiktokAdGroupCubit
                    //   //         .get(context)
                    //   //         .tiktokAdGroupPercentage}');
                    //   setState(() {});
                    // }
                    Navigator.pop(context);
                  },
                  child: const Text("OK"),
                ),
              ],
            );
          },
        );
        return;
      }

      // Validate against existing end or start date
      final cubit = SnapChatAdSetCubit.get(context);
      if (isStartDate) {
        final endDateText = cubit.endDate.text;
        if (endDateText.isNotEmpty) {
          try {
            final endDate = DateTime.parse(endDateText).toUtc();
            if (combinedDateTime.isAfter(endDate)) {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text("Invalid Date"),
                    content: const Text("Start date cannot be after end date."),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text("OK"),
                      ),
                    ],
                  );
                },
              );
              return;
            }
          } on FormatException {
            print('Error parsing end date');
          }
        }
      } else {
        final startDateText = cubit.startDate.text;
        if (startDateText.isNotEmpty) {
          try {
            final startDate = DateTime.parse(startDateText).toUtc();
            if (combinedDateTime.isBefore(startDate)) {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text("Invalid Date"),
                    content:
                        const Text("End date cannot be before start date."),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text("OK"),
                      ),
                    ],
                  );
                },
              );
              return;
            }
          } on FormatException {
            print('Error parsing start date');
          }
        }
      }

      // Update the controller with formatted datetime
      final formattedDate = DateTime.parse(combinedDateTime.toString()).toUtc();
      controller.text = formattedDate.toString().replaceAll(" ", "T");

      // Additional logic based on isStartDate if needed
      if (isStartDate) {
        // Handle start date specific actions
      } else {
        // Handle end date specific actions
      }
    }
  }

  // void _showDatePicker(BuildContext context, TextEditingController controller,
  //     bool isStartDate) async {
  //   final DateTime? startDate = await showDatePicker(
  //     context: context,
  //     builder: (BuildContext context, Widget? child) {
  //       return Theme(
  //         data: Theme.of(context).copyWith(
  //           colorScheme: const ColorScheme.light(
  //             primary: Constants.primaryTextColor, // header background color
  //             onPrimary: Colors.white, // header text color
  //             onSurface: Constants.primaryTextColor, // body text color
  //           ),
  //           textButtonTheme: TextButtonThemeData(
  //             style: TextButton.styleFrom(
  //               foregroundColor:
  //                   Constants.primaryTextColor, // button text color
  //             ),
  //           ),
  //         ),
  //         child: child ?? const Text(""),
  //       );
  //     },
  //     initialDate: DateTime.now(),
  //     firstDate: DateTime.now(),
  //     lastDate: DateTime(2100),
  //   );
  //
  //   if (startDate != null) {
  //     // Format the pickedDate as desired (e.g., "yyyy-MM-dd")
  //     final formattedDate = DateFormat("yyyy-MM-dd hh:mm").format(startDate);
  //
  //     // Check if the picked date is before the delivery date
  //     // if (CreateAdCubit.get(context).endDate.text.isNotEmpty) {
  //     //   final deliveryDate = DateFormat("yyyy-MM-dd")
  //     //       .parse(CreateAdCubit.get(context).endDate.text);
  //     //   if (startDate.isBefore(deliveryDate)) {
  //     //     showDialog(
  //     //       context: context,
  //     //       builder: (BuildContext context) {
  //     //         return AlertDialog(
  //     //           title: const Text("Invalid Date"),
  //     //           content: const Text("End Date can't be before start date"),
  //     //           actions: [
  //     //             TextButton(
  //     //               child: const Text("OK"),
  //     //               onPressed: () {
  //     //                 Navigator.of(context).pop();
  //     //               },
  //     //             ),
  //     //           ],
  //     //         );
  //     //       },
  //     //     );
  //     //     return;
  //     //   }
  //     // }
  //
  //     controller.text = formattedDate;
  //     if (isStartDate) {
  //       CreateAdCubit.get(context).updateAdSetProcess5();
  //     } else {
  //       CreateAdCubit.get(context).updateAdSetProcess6();
  //     }
  //   }
  // }

  Widget buildBudgetModeRadio() {
    const options = [
      'BUDGET_MODE_DAY',
      'BUDGET_MODE_TOTAL',
      // 'BUDGET_MODE_DYNAMIC_DAILY_BUDGET',
    ];

    return Column(
      children: options.map((mode) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: ListTile(
            dense: true,
            contentPadding: EdgeInsets.zero,
            leading: Radio<String>(
              value: mode,
              groupValue: SnapChatAdSetCubit.get(context).selectedBudgetMode,
              onChanged: (String? value) {
                SnapChatAdSetCubit.get(context)
                    .setSelectedBudgetMode(value ?? "");
                // setState(() {
                //   _selectedBudgetMode = value;
                // });
              },
            ),
            title: Text(
              _getDisplayText(mode),
              style: TextStyle(
                fontSize: 16,
                color:
                    SnapChatAdSetCubit.get(context).selectedBudgetMode == mode
                        ? Colors.blue.shade700
                        : Colors.black87,
              ),
            ),
            onTap: () {
              SnapChatAdSetCubit.get(context).setSelectedBudgetMode(mode ?? "");
              if (SnapChatAdSetCubit.get(context).selectedBudgetMode != null) {
                // SnapChatAdSetCubit
                //     .get(context)
                //     .tiktokAdGroupPercentage =
                //     SnapChatAdSetCubit
                //         .get(context)
                //         .tiktokAdGroupPercentage +
                //         0.11111111111111;
                // print(
                //     'tiktokCampaignPercentage ${TiktokCampaignCubit
                //         .get(context)
                //         .tiktokCampaignPercentage}');
                setState(() {});
              }
              // else {
              //   TiktokCampaignCubit.get(context).tiktokCampaignPercentage =
              //       TiktokCampaignCubit.get(context).tiktokCampaignPercentage -
              //           0.11111111111111;
              //   print(
              //       'tiktokCampaignPercentage ${TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage}');
              //   setState(() {});
              // }
              // setState(() {
              //   _selectedBudgetMode = mode;
              // });
            },
          ),
        );
      }).toList(),
    );
  }

  String _getDisplayText(String mode) {
    switch (mode) {
      case 'BUDGET_MODE_DAY':
        return 'Daily Budget';
      case 'BUDGET_MODE_TOTAL':
        return 'Total Budget';
      // case 'BUDGET_MODE_DYNAMIC_DAILY_BUDGET':
      //   return 'Dynamic Daily Budget';
      default:
        return '';
    }
  }
}

List<String> destinationType = ["Whatsapp", "Messenger", "Instagram"];

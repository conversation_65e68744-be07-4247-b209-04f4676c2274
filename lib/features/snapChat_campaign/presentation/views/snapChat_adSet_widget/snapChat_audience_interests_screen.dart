import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_adSet_widget/snapChat_interest_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../widgets/appbar.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../../../../widgets/text_field_widget.dart';

class SnapChatAudienceInterestsScreen extends StatefulWidget {
  const SnapChatAudienceInterestsScreen({super.key});

  @override
  State<SnapChatAudienceInterestsScreen> createState() =>
      _SnapChatAudienceInterestsScreenState();
}

class _SnapChatAudienceInterestsScreenState
    extends State<SnapChatAudienceInterestsScreen> {
  final TextEditingController _searchController = TextEditingController();

  final RegExp _pattern = RegExp(r'\(.*?\)');

  @override
  void initState() {
    SnapChatAdSetCubit.get(context).getSnapChatInterests(
      context: context,
      // advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "",
      // searchKey: "searchKey"
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Detailed Targeting".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: BlocBuilder<SnapChatAdSetCubit, SnapChatAdSetState>(
        builder: (context, state) {
          if (state is SnapChatInterestsLoading) {
            return const Center(child: LoadingWidget(isCircle: true));
          }

          if (state is SnapChatInterestsError) {
            return HandleErrorWidget(
              fun: () => context
                  .read<SnapChatAdSetCubit>()
                  .getSnapChatInterests(context: context
                      // advertiserId:
                      //     instance<HiveHelper>().getAdvertiserId() ?? "",
                      // context: context,
                      // searchKey: _searchController.text,
                      ),
              failure: state.message,
            );
          }

          if (state is SnapChatInterestsLoaded) {
            return Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                children: [
                  // _buildSearchHeader(context),
                  // SizedBox(height: 20.h),
                  // _buildSearchBar(context),
                  SizedBox(height: 20.h),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: GridView.builder(
                        // shrinkWrap: true,
                        // physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 4.0,
                          mainAxisSpacing: 8.0,
                          childAspectRatio: 2.3,
                        ),
                        itemCount: SnapChatAdSetCubit.get(context)
                            .selectedInterests
                            .length,
                        itemBuilder: (context, index) {
                          final interest = SnapChatAdSetCubit.get(context)
                              .selectedInterests[index];
                          final isSelected = SnapChatAdSetCubit.get(context)
                              .selectedInterests
                              .contains(interest);

                          return SnapChatInterestItem(
                            interest: interest,
                            isSelected: isSelected,
                            onTap: () => context
                                .read<SnapChatAdSetCubit>()
                                .removeFromSnapChatInterests(interest),
                          );
                        },
                      ),
                    ),
                  ),
                  // SizedBox(height: 20.h),
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 50.0),
                      child: _buildInterestsGrid(state),
                    ),
                  ),
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: _buildSaveButton(context),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildSearchHeader(BuildContext context) {
    return Row(
      children: [
        CustomSvgWidget(
          svg: AppAssets.interests,
          width: 24.w,
          height: 24.h,
        ),
        SizedBox(width: 10.w),
        CustomText(
          text: 'Interests'.tr,
          fontSize: 18.sp,
          color: Constants.primaryTextColor,
          fontWeight: FontWeight.w700,
        )
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      height: 46.h,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(37)),
        shadows: const [BoxShadow(color: Color(0x33000000), blurRadius: 20)],
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: _searchController,
              hintText: "Search".tr,
              radius: 37,
              icon: _buildSearchIcon(),
            ),
          ),
          _buildSearchButton(context),
        ],
      ),
    );
  }

  Widget _buildSearchIcon() {
    return ShaderMask(
      shaderCallback: (bounds) => const LinearGradient(
        colors: [Color(0xFFFF006F), Color(0xFFF6BA00)],
      ).createShader(bounds),
      child: const Padding(
        padding: EdgeInsets.all(12.0),
        child: CustomSvgWidget(
          svg: AppAssets.search,
          width: 13,
          height: 13,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildSearchButton(BuildContext context) {
    return InkWell(
      onTap: () => _performSearch(context),
      child: Container(
        width: 81.w,
        decoration: const ShapeDecoration(
          gradient:
              LinearGradient(colors: [Color(0xFF0B0F26), Color(0xFF1C4294)]),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(100),
              bottomRight: Radius.circular(100),
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15.0),
          child: CustomText(
            text: "Search".tr,
            fontSize: 14,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildInterestsGrid(SnapChatInterestsLoaded state) {
    return state.interests.isNotEmpty
        ? GridView.builder(
            // shrinkWrap: true,
            // physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 4.0,
              mainAxisSpacing: 8.0,
              childAspectRatio: 2.3,
            ),
            itemCount: state.interests.length,
            itemBuilder: (context, index) {
              final interest = state.interests[index];
              final isSelected = SnapChatAdSetCubit.get(context)
                  .selectedSnapInterestsIds
                  .contains(interest.id);

              return SnapChatInterestItem(
                  interest: interest,
                  isSelected: isSelected,
                  onTap: () {
                    if (!isSelected) {
                      print('notselected $isSelected');
                      context
                          .read<SnapChatAdSetCubit>()
                          .addToSnapChatInterests(interest);
                    } else {
                      showErrorToast('Its already chosen');
                    }
                  });
            },
          )
        : const SizedBox();
    // : GridView.builder(
    //     // shrinkWrap: true,
    //     // physics: const NeverScrollableScrollPhysics(),
    //     gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
    //       crossAxisCount: 3,
    //       crossAxisSpacing: 4.0,
    //       mainAxisSpacing: 8.0,
    //       childAspectRatio: 2.3,
    //     ),
    //     itemCount:
    //         SnapChatAdSetCubit.get(context).defaultSnapChatInterests.length,
    //     itemBuilder: (context, index) {
    //       final interest = SnapChatAdSetCubit.get(context)
    //           .defaultSnapChatInterests[index];
    //       final isSelected = state.selectedInterests.contains(interest);
    //
    //       return SnapChatInterestItem(
    //           interest: interest,
    //           isSelected: isSelected,
    //           onTap: () {
    //             if (!isSelected) {
    //               context
    //                   .read<SnapChatAdSetCubit>()
    //                   .addToSnapChatInterests(interest);
    //             } else {
    //               showErrorToast('Its already chosen');
    //             }
    //           });
    //     },
    //   );
  }

  Widget _buildSaveButton(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0, top: 40.0),
        child: SizedBox(
          width: 235.w,
          child: ButtonWidget(
            text: "Save",
            onTap: () => _handleSave(context),
          ),
        ),
      ),
    );
  }

  void _performSearch(BuildContext context) {
    if (_searchController.text.isNotEmpty) {
      context.read<SnapChatAdSetCubit>().getSnapChatInterests(
            // searchKey: _searchController.text,
            // advertiserId: instance<HiveHelper>().getAdvertiserId() ?? "",
            context: context,
          );
    }
  }

  void _handleSave(BuildContext context) {
    final cubit = context.read<SnapChatAdSetCubit>();

    if (cubit.state is SnapChatInterestsLoaded) {
      final state = cubit.state as SnapChatInterestsLoaded;

      if (SnapChatAdSetCubit.get(context).selectedInterests.isEmpty) {
        showErrorToast("Please select your interest");
        return;
      }
      CreateSnapChatAdCubit.get(context).snapChatAdModel =
          CreateSnapChatAdCubit.get(context).snapChatAdModel.copyWith(
              interests: SnapChatAdSetCubit.get(context).selectedInterests);
      // if (cubit.audiencePercentage == 0.0) {
      //   cubit.audiencePercentage = cubit.audiencePercentage + 1.0;
      //   setState(() {});
      // } else {
      //   if (cubit.audiencePercentage == 1.0) {
      //     cubit.audiencePercentage = cubit.audiencePercentage - 0.0;
      //     setState(() {});
      //   }
      // }

      // TiktokAdCubit.get(context).tiktokAdModel =
      //     TiktokAdCubit.get(context).tiktokAdModel.copyWith(
      //           selectedTiktokInterests: cubit.selectedTiktokInterestsIds,
      //         );
      // if (cubit.selectedTiktokInterestsIds.isNotEmpty == true) {
      //   TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage =
      //       TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage +
      //           0.11111111111111;
      //   print(
      //       'tiktokCampaignPercentage ${TiktokCampaignCubit.get(context).audiencePercentage}');
      //   setState(() {});
      // } else {
      //   TiktokCampaignCubit.get(context).tiktokCampaignPercentage =
      //       TiktokCampaignCubit.get(context).tiktokCampaignPercentage -
      //           0.11111111111111;
      //   print(
      //       'tiktokCampaignPercentage ${TiktokCampaignCubit.get(context).audiencePercentage}');
      //   setState(() {});
      // }
      // print(
      //     'selectedInterestsasd ${TiktokAdCubit.get(context).tiktokAdModel.selectedTiktokInterests}');
      // cubit.updateAdModelWithInterests();
      // context.read<ReachEstimateCubit>().getReachEstimate(
      //   context: context,
      //   imagesFiles: context.read<CreateAdCubit>().adImages,
      //   videosFiles: context.read<CreateAdCubit>().adVideo,
      // );
      if (CreateSnapChatAdCubit.get(context).adSetPercentage <= 0.49) {
        CreateSnapChatAdCubit.get(context).adSetPercentage =
            CreateSnapChatAdCubit.get(context).adSetPercentage + 0.17;
      }

      CreateSnapChatAdCubit.get(context).updateAudiencePercentage();
      Navigator.pop(context);
    }
  }
}

import 'package:ads_dv/features/snapChat_campaign/data/repos/snapChat_create_campaign_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/models/get_snapChat_campaigns_response.dart';

part 'snap_chat_campaign_state.dart';

class SnapChatCampaignCubit extends Cubit<SnapChatCampaignState> {
  SnapChatCampaignCubit() : super(SnapChatCampaignInitial());

  static SnapChatCampaignCubit get(context) => BlocProvider.of(context);

  TextEditingController campaignNameController = TextEditingController();

  final campaignFormKey = GlobalKey<FormState>();

  List<ExistingCampaignsResult>? campaigns = [];
  ExistingCampaignsResult? selectedCampaign;

  getCampaigns(
      {required BuildContext context, required String accountId}) async {
    campaigns?.clear();
    emit(GetSnapChatCampaignsStateLoading());
    instance<CreateSnapChatCampaignRepo>()
        .getCampaigns(accountId: accountId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetSnapChatCampaignsStateError(l));
      }, (r) {
        campaigns = r.result;
        emit(GetSnapChatCampaignsStateLoaded(r));
      });
    });
  }

  void setSelectedCampaign(ExistingCampaignsResult? selectedCampaignx) {
    selectedCampaign = selectedCampaignx;

    print('selectedCampaign ${selectedCampaign?.campaign?.name}');

    emit(SetSelectedSnapChatCampaign());
  }

  bool isCampaignTileExpanded = false;
  int selectCampaignTab = 0;

  void setCampaignExpansionState(bool isClosed) {
    isCampaignTileExpanded = isClosed;
    emit(SetCampaignExpansionState());
  }

  void changeCampaignTabIndex(int index) {
    selectCampaignTab = index;
    emit(ChangeCampaignTab());
  }

  void clear() {
    campaignNameController.clear();
    campaigns?.clear();
    selectedCampaign = null;
    isCampaignTileExpanded = false;
    selectCampaignTab = 0;
    emit(SnapChatCampaignInitial());
  }
}

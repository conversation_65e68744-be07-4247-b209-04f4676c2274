part of 'snap_chat_campaign_cubit.dart';

@immutable
sealed class Snap<PERSON>hatCampaignState {}

final class Snap<PERSON>hatCampaignInitial extends SnapChatCampaignState {}

class GetSnapChatCampaignsStateLoading extends SnapChatCampaignState {}

final class SetSelectedSnapChatCampaign extends SnapChatCampaignState {}

final class SetCampaignExpansionState extends SnapChatCampaignState {}

final class ChangeCampaignTab extends SnapChatCampaignState {}

final class UpdateStatus extends SnapChatCampaignState {}

class GetSnapChatCampaignsStateLoaded extends SnapChatCampaignState {
  final GetSnapChatCampaignsResponse data;

  GetSnapChatCampaignsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetSnapChatCampaignsStateLoaded copyWith({
    GetSnapChatCampaignsResponse? data,
  }) {
    return GetSnapChatCampaignsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetSnapChatCampaignsStateError extends SnapChatCampaignState {
  final Failure message;

  GetSnapChatCampaignsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

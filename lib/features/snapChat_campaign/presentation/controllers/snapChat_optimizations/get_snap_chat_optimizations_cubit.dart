import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/models/snapChat_objectives_response.dart';
import '../../../data/repos/snapChat_create_campaign_repo.dart';

part 'get_snap_chat_optimizations_state.dart';

class GetSnapChatOptimizationsCubit
    extends Cubit<GetSnapChatOptimizationsState> {
  GetSnapChatOptimizationsCubit() : super(GetSnapChatOptimizationsInitial());

  static GetSnapChatOptimizationsCubit get(context) => BlocProvider.of(context);

  List<SnapChatObjective> opt = [];
  SnapChatObjective? optimization;

  getOptimizations(
      {required BuildContext context,
      required String objectiveActualName}) async {
    emit(GetSnapChatOptimizationsStateLoading());
    instance<CreateSnapChatCampaignRepo>()
        .getSnapChatOptimizations(objectiveActualName: objectiveActualName)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetSnapChatOptimizationsStateError(l));
      }, (r) {
        opt = r;
        emit(GetSnapChatOptimizationsStateLoaded(r));
      });
    });
  }

  void setSelectedOptimization(SnapChatObjective obt) {
    // selectOptimizationIndex = index;
    optimization = obt;
    emit(SetSelectedSnapChatOptimization());
  }

  void clearData() {
    opt = [];
    optimization = null;
    emit(GetSnapChatOptimizationsInitial());
  }
}

part of 'get_snap_chat_optimizations_cubit.dart';

@immutable
sealed class GetSnapChatOptimizationsState {}

final class GetSnapChatOptimizationsInitial
    extends GetSnapChatOptimizationsState {}

class GetSnapChatOptimizationsStateLoading
    extends GetSnapChatOptimizationsState {}

class SetSelectedSnapChatOptimization extends GetSnapChatOptimizationsState {}

class GetSnapChatOptimizationsStateLoaded
    extends GetSnapChatOptimizationsState {
  final List<SnapChatObjective> data;

  GetSnapChatOptimizationsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetSnapChatOptimizationsStateLoaded copyWith({
    List<SnapChatObjective>? data,
  }) {
    return GetSnapChatOptimizationsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetSnapChatOptimizationsStateError extends GetSnapChatOptimizationsState {
  final Failure message;

  GetSnapChatOptimizationsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

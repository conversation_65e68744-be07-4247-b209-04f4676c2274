import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/common_utils.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/cached__image.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../create_campaigns/data/models/call_to_action.dart';
import '../../../../create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import '../../../../maps/presentation/controllers/select_map_cubit.dart';
import '../../../data/models/Get_snapChat_profilesId_response.dart';
import '../../../data/models/phone_number_model.dart';
import '../../../data/models/snapChat_ad_model.dart';
import '../../../data/repos/snapChat_create_campaign_repo.dart';
import '../snapChat_objectives/get_snap_chat_objectives_cubit.dart';
import '../snapChat_optimizations/get_snap_chat_optimizations_cubit.dart';

part 'create_snap_chat_ad_state.dart';

class CreateSnapChatAdCubit extends Cubit<CreateSnapChatAdState> {
  CreateSnapChatAdCubit() : super(CreateSnapChatAdInitial());

  static CreateSnapChatAdCubit get(context) => BlocProvider.of(context);

  TextEditingController adName = TextEditingController();

  TextEditingController adCreativeName = TextEditingController();

  TextEditingController webSiteLink = TextEditingController();

  TextEditingController appName = TextEditingController();

  TextEditingController phone = TextEditingController();

  String countryCode = "";

  String countryDialCode = "";

  TextEditingController profilePublic = TextEditingController();

  SnapChatAdModel snapChatAdModel = SnapChatAdModel();
  double campaignPercentage = 0.0;
  double adSetPercentage = 0.0;
  double adPercentage = 0.0;
  double demographicPercentage = 0.0;
  double audienceInterestsPercentage = 0.0;

  bool imageAndVideoPlatformActive = false;
  bool storyAdPlatformActive = false;
  bool collectionAdPlatformActive = false;
  bool aRPlatformActive = false;
  bool isSnapChatAdComplete = false;
  bool isSnapChatCampaignComplete = false;
  bool isSnapChatAdSetComplete = false;
  String destinationType = "";
  String? type;
  String? typeName;
  final adCreativeFormKey = GlobalKey<FormState>();
  final adCreativeVideoFormKey = GlobalKey<FormState>();

  List<File> adImages = [];
  List<PhoneNumberModel> phoneNumbers = [];
  List<PublicProfile> publicProfiles = [];
  PublicProfile? selectedPublicProfiles;

  List<File> adVideo = [];

  List<CallToAction> allCallToAction = [
    ///awareness
    ///APPLY_NOW, MORE, ORDER_NOW, PLAY, READ, SHOP_NOW, SHOW, SIGN_UP, VIEW, SHOW, WATCH, DONATE, DOWNLOAD, APPLY_NOW, ORDER_NOW, RESPOND, BUY_TICKETS, SHOWTIMES, BOOK_NOW, GET_NOW, LISTEN, TRY, VOTE, VIEW_MENU, PRE_REGISTER, PLAY_GAME
    CallToAction(name: 'Get Now'.tr, value: 'GET_NOW'),
    CallToAction(name: 'ApplyNow'.tr, value: 'APPLY_NOW'),
    CallToAction(name: 'More'.tr, value: 'MORE'),
    CallToAction(name: 'Order Now'.tr, value: 'ORDER_NOW'),
    CallToAction(name: 'Play'.tr, value: 'PLAY'),
    CallToAction(name: 'Read'.tr, value: 'READ'),

    CallToAction(name: 'Shop Now'.tr, value: 'SHOP_NOW'),
    CallToAction(name: 'Show'.tr, value: 'SHOW'),
    CallToAction(name: 'Respond'.tr, value: 'RESPOND'),
    CallToAction(name: 'Sign Up'.tr, value: 'SIGN_UP'),
    CallToAction(name: 'View'.tr, value: 'VIEW'),

    CallToAction(name: 'Watch'.tr, value: 'WATCH'),
    CallToAction(name: 'Donate'.tr, value: 'DONATE'),

    CallToAction(name: 'Download'.tr, value: 'DOWNLOAD'),
    CallToAction(name: 'Buy Tickets'.tr, value: 'BUY_TICKETS'),
    CallToAction(name: 'Show Times'.tr, value: 'SHOWTIMES'),
    CallToAction(name: 'Watch More'.tr, value: 'WATCH_MORE'),
    CallToAction(name: 'Donate Now'.tr, value: 'DONATE_NOW'),
    CallToAction(name: 'Book Now'.tr, value: 'BOOK_NOW'),
    CallToAction(name: 'Listen'.tr, value: 'LISTEN'),
    CallToAction(name: 'Try'.tr, value: 'TRY'),
    CallToAction(name: 'Vote'.tr, value: 'VOTE'),
    CallToAction(name: 'View Menu'.tr, value: 'VIEW_MENU'),
    CallToAction(name: 'Preregister'.tr, value: 'PRE_REGISTER'),
    CallToAction(name: 'Play Game'.tr, value: 'PLAY_GAME'),
  ];
  List<CallToAction> phoneCallToAction = [
    ///awareness
    ///APPLY_NOW, MORE, ORDER_NOW, PLAY, READ, SHOP_NOW, SHOW, SIGN_UP, VIEW, SHOW, WATCH, DONATE, DOWNLOAD, APPLY_NOW, ORDER_NOW, RESPOND, BUY_TICKETS, SHOWTIMES, BOOK_NOW, GET_NOW, LISTEN, TRY, VOTE, VIEW_MENU, PRE_REGISTER, PLAY_GAME
    CallToAction(name: 'Call Now'.tr, value: 'CALL_NOW'),
  ];

  void setCallToAction(CallToAction callToAction) {
    // if (destinationType == "WHATSAPP" && callToAction.name == "Send Message") {
    //   callToAction.value = "WHATSAPP_MESSAGE";
    // } else if (destinationType == "INSTAGRAM_DIRECT" &&
    //     callToAction.name == "Send Message") {
    //   callToAction.value = "INSTAGRAM_MESSAGE";
    // } else if (destinationType == "MESSENGER" &&
    //     callToAction.name == "Send Message") {
    //   callToAction.value = "MESSAGE_PAGE";
    // }
    type = callToAction.value;
    typeName = callToAction.name;
    emit(ChangeAdStatus());
  }

  updateDemographicPercentage() {
    demographicPercentage = 1.0;
    emit(UpdateStatus());
  }

  updateAudiencePercentage() {
    audienceInterestsPercentage = 1.0;
    emit(UpdateStatus());
  }

  void removeImage(int index, bool isImage) {
    adImages.removeAt(index);
    // if (adImages.isEmpty) {
    //   isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
    //   isAdCreativeProcess5Updated = false;
    // }
    emit(RemoveImageState());
  }

  void completeAd(bool campaignStatus) {
    isSnapChatAdComplete = campaignStatus;
    // if (adImages.isEmpty) {
    //   isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
    //   isAdCreativeProcess5Updated = false;
    // }
    emit(UpdateStatus());
  }

  void completeCampaign(bool campaignStatus) {
    isSnapChatCampaignComplete = campaignStatus;
    // if (adImages.isEmpty) {
    //   isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
    //   isAdCreativeProcess5Updated = false;
    // }
    emit(UpdateStatus());
  }

  void selectPublicProfile(PublicProfile? currentPublicProfiles) {
    selectedPublicProfiles = currentPublicProfiles;
    // if (adImages.isEmpty) {
    //   isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
    //   isAdCreativeProcess5Updated = false;
    // }
    emit(UpdateStatus());
  }

  PhoneNumberModel? selectedNumber;

  void selectPhoneNumber(PhoneNumberModel? selectedNumberx) {
    selectedNumber = selectedNumberx;
    // if (adImages.isEmpty) {
    //   isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
    //   isAdCreativeProcess5Updated = false;
    // }
    emit(UpdateStatus());
  }

  void completeAdSetCubit(bool adSetStatusStatus) {
    isSnapChatAdSetComplete = adSetStatusStatus;
    // if (adImages.isEmpty) {
    //   isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
    //   isAdCreativeProcess5Updated = false;
    // }
    emit(UpdateStatus());
  }

  void imageAndVideoPlatformActiveStatus(bool status) {
    imageAndVideoPlatformActive = status;
    if (imageAndVideoPlatformActive) {
      imageAndVideoPlatformActive = true;
    } else {
      imageAndVideoPlatformActive = false;
    }

    emit(ChangeAdStatus());
  }

  void storyAdPlatformActiveStatus(bool status) {
    storyAdPlatformActive = status;
    if (storyAdPlatformActive) {
      storyAdPlatformActive = true;
    } else {
      storyAdPlatformActive = false;
    }

    emit(ChangeAdStatus());
  }

  void collectionAdPlatformActiveStatus(bool status) {
    collectionAdPlatformActive = status;
    if (collectionAdPlatformActive) {
      collectionAdPlatformActive = true;
    } else {
      collectionAdPlatformActive = false;
    }

    emit(ChangeAdStatus());
  }

  void aRPlatformActiveStatus(bool status) {
    aRPlatformActive = status;
    if (aRPlatformActive) {
      aRPlatformActive = true;
    } else {
      aRPlatformActive = false;
    }

    emit(ChangeAdStatus());
  }

  getSnapChatProfilesId({required BuildContext context}) async {
    // tiktokCampaigns?.clear();

    emit(GetSnapChatProfilesIdLoading());
    instance<CreateSnapChatCampaignRepo>()
        .getSnapChatProfilesId()
        .then((value) {
      value.fold((l) {
        print('snapChatProfilesIdError ${l.message}');
        FailureHelper.instance.handleFailures(l, context);
        emit(CreateSnapChatADStateError(l));
      }, (r) {
        publicProfiles = r;
        print('snapChatProfilesId ${publicProfiles.first.displayName}');
        emit(GetSnapChatProfilesIdStateLoaded(publicProfiles: r));
      });
    });
  }

  getSnapChatPhoneNumbers({required BuildContext context}) async {
    // tiktokCampaigns?.clear();

    emit(GetSnapChatPhoneNumbersLoading());
    instance<CreateSnapChatCampaignRepo>()
        .getSnapChatPhoneNumbers()
        .then((value) {
      value.fold((l) {
        print('snapChatPhoneNumbersError ${l.message}');
        FailureHelper.instance.handleFailures(l, context);
        emit(CreateSnapChatADStateError(l));
      }, (r) {
        phoneNumbers = r;
        print('snapChatPhoneNumbers ${phoneNumbers.first.phoneNumber}');
        emit(GetSnapChatPhoneNumbersStateLoaded(phoneNumbers: r));
      });
    });
  }

  Future<void> createAD({
    required BuildContext context,
    required List<File> imagesFiles,
    required List<File> videosFiles,
    SnapChatAdModel? snapChatAdModel,
    // required List<File> thumbFiles
  }) async {
    emit(CreateSnapChatAdLoading());
    // snapChatAdModel.title = headline.text;
    // CreateAdCubit.get(context)
    //     .adModel
    //     .toJson()
    //     .then((value) => print('creationAdxx $value'));
    // print('creationAdxx ${adModel.toJson()}');
    instance<CreateSnapChatCampaignRepo>()
        .createAD(
      snapChatAdModel: snapChatAdModel,
      imagesFiles: imagesFiles,
      videosFiles: videosFiles,
      // thumbFiles: thumbFiles
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(CreateSnapChatADStateError(l));
      }, (r) async {
        // isAddCreated = true;
        // Navigator.pop(context);
        await CommonUtils.showBottomDialog(
            context,
            isDismissible: false,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 34.sp),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomText(
                    text: "Congratulations",
                    fontSize: 26.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  CachedImageWidget(
                    assetsImage: AppAssets.adSuccess,
                    height: 120.h,
                  ),
                  20.verticalSpace,
                  CustomText(
                    text:
                        "Congratulations! Your ad has been successfully published",
                    fontSize: 16.sp,
                    color: const Color(0xFF808080),
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 50.sp),
                    child: ButtonWidget(
                      text: "Return To Home",
                      fontSize: 16.sp,
                      padding: 16.sp,
                      onTap: () async {
                        clearData();
                        GetSnapChatObjectivesCubit.get(context).clearData();
                        GetSnapChatOptimizationsCubit.get(context).clearData();
                        CreateAdCubit.get(context).geoLocations.clear();
                        SelectMapCubit.get(context).selectedLocations.clear();
                        CreateAdCubit.get(context).locationPercentage = 0.0;
                        // navigatorKey.currentState?.pushNamedAndRemoveUntil(
                        //     Routes.splash, (route) => false);
                        await Navigator.pushNamedAndRemoveUntil(
                            context, Routes.splash, (route) => false);
                      },
                    ),
                  )
                ],
              ),
            ));
        emit(CreateSnapChatADStateLoaded(data: r));
        // clearCampaignData(context);
      });
    });
  }

  bool urlFieldView = false;
  bool urlFieldViewRequired = false;
  bool phoneFieldViewRequired = false;

  changeUrlViewStatus() {
    // if (snapChatAdModel.objective == "TRAFFIC" &&
    //     (snapChatAdModel.optimizationGoal == "LANDING_PAGE_VIEW" ||
    //         snapChatAdModel.optimizationGoal == "SWIPES" ||
    //         snapChatAdModel.optimizationGoal == "IMPRESSIONS")) {
    //   urlFieldView = true;
    // } else
    if (snapChatAdModel.objective == "AWARENESS_AND_ENGAGEMENT" ||
        (snapChatAdModel.objective == "TRAFFIC" &&
            snapChatAdModel.optimizationGoal == "LANDING_PAGE_VIEW"
        // snapChatAdModel.optimizationGoal == "SWIPES" ||
        // snapChatAdModel.optimizationGoal == "IMPRESSIONS"
        )) {
      if (snapChatAdModel.optimizationGoal == "LANDING_PAGE_VIEW") {
        urlFieldViewRequired = true;
      }
      urlFieldView = true;
    } else {
      urlFieldView = false;
    }
    print('changeUrl $urlFieldView');
    emit(UpdateStatus());
  }

  bool phoneNumberFieldView = false;

  changePhoneNumberStatus() {
    if ((snapChatAdModel.objective == "LEADS" ||
            snapChatAdModel.objective == "TRAFFIC") &&
        (snapChatAdModel.optimizationGoal == "IMPRESSIONS" ||
            snapChatAdModel.optimizationGoal == "SWIPES")) {
      if (snapChatAdModel.objective == "LEADS" &&
          (snapChatAdModel.optimizationGoal == "IMPRESSIONS" ||
              snapChatAdModel.optimizationGoal == "SWIPES")) {
        phoneFieldViewRequired = true;
      }
      phoneNumberFieldView = true;
    }
    // else if (snapChatAdModel.objective == "TRAFFIC" &&
    //     (snapChatAdModel.optimizationGoal == "IMPRESSIONS" ||
    //         snapChatAdModel.optimizationGoal == "SWIPES")) {
    //   phoneNumberFieldView = true;
    // }
    else {
      phoneNumberFieldView = false;
    }
    print('changePhone $phoneNumberFieldView');
    emit(UpdateStatus());
  }

  void clearData() {
    adName.clear();
    adCreativeName.clear();
    webSiteLink.clear();
    appName.clear();
    phone.clear();
    profilePublic.clear();
    countryCode = "";
    countryDialCode = "";
    snapChatAdModel = SnapChatAdModel();
    campaignPercentage = 0.0;
    adSetPercentage = 0.0;
    adPercentage = 0.0;
    demographicPercentage = 0.0;
    audienceInterestsPercentage = 0.0;
    imageAndVideoPlatformActive = false;
    storyAdPlatformActive = false;
    collectionAdPlatformActive = false;
    aRPlatformActive = false;
    isSnapChatAdComplete = false;
    isSnapChatCampaignComplete = false;
    isSnapChatAdSetComplete = false;
    destinationType = "";
    type = null;
    typeName = null;
    adImages.clear();
    adVideo.clear();
    phoneNumbers.clear();
    publicProfiles.clear();
    selectedPublicProfiles = null;
    selectedNumber = null;
    urlFieldView = false;
    urlFieldViewRequired = false;
    phoneFieldViewRequired = false;
    phoneNumberFieldView = false;
    emit(UpdateStatus());
  }
}

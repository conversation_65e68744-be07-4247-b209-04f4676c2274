part of 'create_snap_chat_ad_cubit.dart';

@immutable
sealed class CreateSnapChatAdState {}

final class CreateSnapChatAdInitial extends CreateSnapChatAdState {}

final class ChangeAdStatus extends CreateSnapChatAdState {}

final class UpdateStatus extends CreateSnapChatAdState {}

final class CreateSnapChatAdLoading extends CreateSnapChatAdState {}

final class GetSnapChatProfilesIdLoading extends CreateSnapChatAdState {}

final class GetSnapChatPhoneNumbersLoading extends CreateSnapChatAdState {}

final class RemoveImageState extends CreateSnapChatAdState {}

class CreateSnapChatADStateLoaded extends CreateSnapChatAdState {
  final String? data;

  // final SnapChatAdModel? snapChatAdModel;

  CreateSnapChatADStateLoaded({this.data
      // ,this.snapChatAdModel,
      });

  @override
  List<Object?> get props => [
        data
        // ,snapChatAdModel
      ];

  CreateSnapChatADStateLoaded copyWith({
    String? data,
    // SnapChatAdModel? snapChatAdModel,
  }) {
    return CreateSnapChatADStateLoaded(
      data: data ?? this.data,
      // snapChatAdModel: snapChatAdModel ?? this.snapChatAdModel,
    );
  }
}

class GetSnapChatProfilesIdStateLoaded extends CreateSnapChatAdState {
  List<PublicProfile> publicProfiles = [];

  // final SnapChatAdModel? snapChatAdModel;

  GetSnapChatProfilesIdStateLoaded({required this.publicProfiles
      // ,this.snapChatAdModel,
      });

  @override
  List<Object?> get props => [
        publicProfiles
        // ,snapChatAdModel
      ];

  GetSnapChatProfilesIdStateLoaded copyWith({
    List<PublicProfile>? publicProfiles,
    // SnapChatAdModel? snapChatAdModel,
  }) {
    return GetSnapChatProfilesIdStateLoaded(
      publicProfiles: publicProfiles ?? this.publicProfiles,
      // snapChatAdModel: snapChatAdModel ?? this.snapChatAdModel,
    );
  }
}

class GetSnapChatPhoneNumbersStateLoaded extends CreateSnapChatAdState {
  List<PhoneNumberModel> phoneNumbers = [];

  // final SnapChatAdModel? snapChatAdModel;

  GetSnapChatPhoneNumbersStateLoaded({required this.phoneNumbers
      // ,this.snapChatAdModel,
      });

  @override
  List<Object?> get props => [
        phoneNumbers
        // ,snapChatAdModel
      ];

  GetSnapChatPhoneNumbersStateLoaded copyWith({
    List<PhoneNumberModel>? phoneNumbers,
    // SnapChatAdModel? snapChatAdModel,
  }) {
    return GetSnapChatPhoneNumbersStateLoaded(
      phoneNumbers: phoneNumbers ?? this.phoneNumbers,
      // snapChatAdModel: snapChatAdModel ?? this.snapChatAdModel,
    );
  }
}

class CreateSnapChatADStateError extends CreateSnapChatAdState {
  final Failure message;

  CreateSnapChatADStateError(this.message);

  @override
  List<Object?> get props => [message];
}

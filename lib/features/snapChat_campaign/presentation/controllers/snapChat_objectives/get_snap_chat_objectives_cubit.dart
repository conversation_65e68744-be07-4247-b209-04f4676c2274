import 'package:ads_dv/features/snapChat_campaign/data/repos/snapChat_create_campaign_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../create_campaigns/data/models/language.dart';
import '../../../../create_campaigns/data/models/question.dart';
import '../../../data/models/snapChat_objectives_response.dart';

part 'get_snap_chat_objectives_state.dart';

class GetSnapChatObjectivesCubit extends Cubit<GetSnapChatObjectivesState> {
  GetSnapChatObjectivesCubit() : super(GetSnapChatObjectivesInitial());

  static GetSnapChatObjectivesCubit get(context) => BlocProvider.of(context);

  SnapChatObjective? objective;
  String? objectiveType;
  int? selectedGoal;

  final leadsFormKey = GlobalKey<FormState>();
  final TextEditingController leadHeadlineController = TextEditingController();
  final TextEditingController linkController = TextEditingController();
  final TextEditingController linkTextController = TextEditingController();
  final TextEditingController websiteLinkController = TextEditingController();
  final TextEditingController ctaController = TextEditingController();

  final TextEditingController leadMessage = TextEditingController();
  final TextEditingController leadDesc = TextEditingController();

  int? langIndex;

  String? langValue;

  int selectFormTab = 0;

  int? selectedForm;
  int? formIndex;

  String? formId;

  List<Question> questions = [
    Question(
        name: 'What is your company name?',
        value: 'COMPANY_NAME',
        isChecked: false),
    // Question(
    //     name: 'What is your country name?',
    //     value: 'COUNTRY',
    //     isChecked: false),
    // Question(
    //     name: 'What is your gender?',
    //     value: 'GENDER',
    //     isChecked: false),
    Question(
        name: 'What is your first name?',
        value: 'FIRST_NAME',
        isChecked: false),
    // Question(
    //     name: 'What is your full name?',
    //     value: 'FULL_NAME',
    //     isChecked: false),
    Question(
        name: 'What is your job title?', value: 'JOB_TITLE', isChecked: false),
    Question(
        name: 'What is your date of birth?',
        value: 'BIRTHDAY_DATE',
        isChecked: false),
    Question(name: 'What is your email?', value: 'EMAIL', isChecked: false),
    Question(
        name: 'What is your last name?', value: 'LAST_NAME', isChecked: false),
    Question(
        name: 'What is your postal code?',
        value: 'POSTAL_CODE',
        isChecked: false),
    Question(
        name: 'What is your phone?', value: 'PHONE_NUMBER', isChecked: false),
    // Question(
    //     name: 'What is your state?',
    //     value: 'STATE',
    //     isChecked: false),
    Question(name: 'What is your address?', value: 'ADDRESS', isChecked: false),
    // Question(name: 'What is your city?', value: 'CITY', isChecked: false),
    // Question(
    //     name: 'What is your company name?',
    //     value: 'COMPANY_NAME',
    //     isChecked: false),
    // Question(
    //     name: 'What is your country name?', value: 'COUNTRY', isChecked: false),
    // Question(name: 'What is your gender?', value: 'GENDER', isChecked: false),
    // Question(
    //     name: 'What is your first name?',
    //     value: 'FIRST_NAME',
    //     isChecked: false),
    // Question(
    //     name: 'What is your full name?', value: 'FULL_NAME', isChecked: false),
    // Question(
    //     name: 'What is your job title?', value: 'JOB_TITLE', isChecked: false),
  ];

  List<Language> lang = [
    Language(name: 'Arabic', value: 'AR_AR'),
    Language(name: 'English', value: 'EN_US'),
  ];

  void setSelectedLang(String? selectedLangValue, int index) {
    langValue = selectedLangValue;
    langIndex = index;
    // emit(UpdateStates());
  }

  void setSelectedQuestion() {
    // Use a Set to prevent duplicates
    Set<String> addedQuestionsSet = Set<String>.from(addedQuestions);

    for (int i = 0; i < questions.length; i++) {
      String? value = questions[i].value;

      if (questions[i].isChecked) {
        // Add to set if it's checked
        addedQuestionsSet.add(value ?? "");
      } else {
        // Remove from set if it's unchecked
        addedQuestionsSet.remove(value ?? "");
      }
    }

    // Convert back to List if needed
    addedQuestions = addedQuestionsSet.toList();

    // emit(UpdateStates());

    // Debugging output
    print("Added Questions: $addedQuestions");
  }

  void setSelectedForm(String? selectedFormId, int index) {
    formId = selectedFormId;
    formIndex = index;
    // emit(UpdateStates());
  }

  List<String> addedQuestions = [];

  void changeFormTabIndex(int index) {
    selectFormTab = index;
    // emit(UpdateStates());
  }

  getObjectives({
    required BuildContext context,
  }) async {
    emit(GetSnapChatObjectivesStateLoading());
    instance<CreateSnapChatCampaignRepo>()
        .getSnapChatObjectives()
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetSnapChatObjectivesStateError(l));
      }, (r) {
        print("objectiveszxcxc $r");
        emit(GetSnapChatObjectivesStateLoaded(r.data ?? []));
      });
    });
  }

  void setSelectedGoal(int index, SnapChatObjective? objectivex) {
    selectedGoal = index;
    objective = objectivex;
    objectiveType = objectivex?.showName;
    // emit(SetSelectedSnapChatGoal());
  }

  void clearData() {
    objective = null;
    objectiveType = null;
    selectedGoal = null;
    langIndex = null;
    langValue = null;
    selectFormTab = 0;
    selectedForm = null;
    formIndex = null;
    formId = null;
    addedQuestions.clear();
    leadHeadlineController.clear();
    linkController.clear();
    linkTextController.clear();
    websiteLinkController.clear();
    ctaController.clear();
    leadMessage.clear();
    leadDesc.clear();
    emit(GetSnapChatObjectivesInitial());
  }
}

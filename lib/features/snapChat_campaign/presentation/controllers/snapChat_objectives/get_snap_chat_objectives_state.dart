part of 'get_snap_chat_objectives_cubit.dart';

@immutable
sealed class GetSnapChatObjectivesState {}

final class GetSnapChatObjectivesInitial extends GetSnapChatObjectivesState {}

class GetSnapChatObjectivesStateLoading extends GetSnapChatObjectivesState {}

class ChangeChatObjectivesState extends GetSnapChatObjectivesState {}

class UpdateStates extends GetSnapChatObjectivesState {}

final class SetSelectedSnapChatGoal extends GetSnapChatObjectivesState {}

class GetSnapChatObjectivesStateLoaded extends GetSnapChatObjectivesState {
  final List<SnapChatObjective> data;

  GetSnapChatObjectivesStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetSnapChatObjectivesStateLoaded copyWith({
    List<SnapChatObjective>? data,
  }) {
    return GetSnapChatObjectivesStateLoaded(
      data ?? this.data,
    );
  }
}

class GetCurrentSnapChatObjectiveStateLoaded
    extends GetSnapChatObjectivesState {
  final SnapChatObjective? objective;

  GetCurrentSnapChatObjectiveStateLoaded(this.objective);

  @override
  List<Object?> get props => [objective];

  GetCurrentSnapChatObjectiveStateLoaded copyWith({
    SnapChatObjective? objective,
  }) {
    return GetCurrentSnapChatObjectiveStateLoaded(
      objective ?? this.objective,
    );
  }
}

class GetSnapChatObjectivesStateError extends GetSnapChatObjectivesState {
  final Failure message;

  GetSnapChatObjectivesStateError(this.message);

  @override
  List<Object?> get props => [message];
}

part of 'snap_chat_ad_set_cubit.dart';

@immutable
sealed class SnapChatAdSetState {}

final class SnapChatAdSetInitial extends SnapChatAdSetState {}

final class UpdateStates extends SnapChatAdSetState {}

final class SnapChatInterestsLoading extends SnapChatAdSetState {}

final class ChangeBudgetModeState extends SnapChatAdSetState {}

final class AdToInterestsState extends SnapChatAdSetState {}

final class RemoveFromInterestsState extends SnapChatAdSetState {}

final class SnapChatRemoveFromLanguageState extends SnapChatAdSetState {}

final class SetAdGroupClearAllDataState extends SnapChatAdSetState {}

final class SnapChatAdToLanguageState extends SnapChatAdSetState {}

final class GetSnapChatLanguagesStateLoading extends SnapChatAdSetState {}

class SnapChatInterestsLoaded extends SnapChatAdSetState {
  final List<SnapChatInterestsModel> interests;

  // final List<SnapChatInterestsModel> selectedInterests;
  final double percentage;

  SnapChatInterestsLoaded({
    required this.interests,
    // required this.selectedInterests,
    required this.percentage,
  });

  @override
  List<Object> get props => [
        interests,
        // selectedInterests,
        percentage
      ];

  SnapChatInterestsLoaded copyWith({
    List<SnapChatInterestsModel>? interests,
    List<SnapChatInterestsModel>? selectedInterests,
    double? percentage,
  }) {
    return SnapChatInterestsLoaded(
      interests: interests ?? this.interests,
      // selectedInterests: selectedInterests ?? this.selectedInterests,
      percentage: percentage ?? this.percentage,
    );
  }
}

final class GetSnapChatLanguagesState extends SnapChatAdSetState {
  final TiktokLanguagesResponseModel data;

  GetSnapChatLanguagesState(this.data);

  @override
  List<Object?> get props => [data];

  GetSnapChatLanguagesState copyWith({
    TiktokLanguagesResponseModel? data,
  }) {
    return GetSnapChatLanguagesState(
      data ?? this.data,
    );
  }
}

final class GetSnapChatLanguagesStateError extends SnapChatAdSetState {
  final Failure failure;

  GetSnapChatLanguagesStateError(this.failure);

  @override
  List<Object?> get props => [failure];
}

class SnapChatInterestsError extends SnapChatAdSetState {
  final String message;

  SnapChatInterestsError(this.message);

  @override
  List<Object> get props => [message];
}

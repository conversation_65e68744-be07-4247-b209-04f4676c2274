import 'package:ads_dv/features/snapChat_campaign/data/repos/snapChat_create_campaign_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../create_campaigns/data/models/adset_location.dart';
import '../../../../create_campaigns/data/models/search_result.dart';
import '../../../../tiktok_campigns/data/models/tiktok_lang_model.dart';
import '../../../../tiktok_campigns/repos/create_tiktok_campaign_repo.dart';
import '../../../data/models/snapChat_interests_response.dart';

part 'snap_chat_ad_set_state.dart';

class SnapChatAdSetCubit extends Cubit<SnapChatAdSetState> {
  SnapChatAdSetCubit() : super(SnapChatAdSetInitial());

  static SnapChatAdSetCubit get(context) => BlocProvider.of(context);

  final TextEditingController adSetNameController = TextEditingController();

  final adSetFormKey = GlobalKey<FormState>();

  TiktokLanguagesResponseModel? snapChatLangs;
  bool isAddNewLocation = false;

  int? selectedMinAge;
  int? selectedMaxAge;
  List<SnapChatInterestsModel> selectedInterests = [];
  List<String> genders = [];
  final campaignFormKey = GlobalKey<FormState>();
  int? selectedIndex;

  final TextEditingController textController = TextEditingController();

  final TextEditingController dailyBudget = TextEditingController();

  var startDate = TextEditingController(text: "");
  var endDate = TextEditingController(text: "");

  var minAge = TextEditingController();
  var maxAge = TextEditingController();

  final adFormKey = GlobalKey<FormState>();

  // List<SnapChatInterestsModel> selectedSnapChatInterests = [];
  List<String> selectedSnapInterestsIds = [];

  // List<SnapChatInterestsModel> defaultSnapChatInterests = [
  //   SnapChatInterestsModel(id: "**********", name: "skin beauty"),
  //   SnapChatInterestsModel(id: "**********", name: "beauty industry"),
  //   SnapChatInterestsModel(id: "**********", name: "makeup and beauty"),
  //   SnapChatInterestsModel(id: "**********", name: "beauty and health"),
  //   SnapChatInterestsModel(id: "**********", name: "lipstick makeup"),
  //   SnapChatInterestsModel(id: "**********", name: "lip gloss"),
  //   SnapChatInterestsModel(id: "**********", name: "lip oil"),
  //   SnapChatInterestsModel(id: "**********", name: "new makeup"),
  //   SnapChatInterestsModel(id: "**********", name: "trendy makeup"),
  //   SnapChatInterestsModel(id: "**********", name: "Facial Makeup"),
  //   SnapChatInterestsModel(id: "**********", name: "beauty & makeup"),
  //   SnapChatInterestsModel(id: "**********", name: "beauty and makeup"),
  //   SnapChatInterestsModel(id: "**********", name: "sports watches"),
  //   SnapChatInterestsModel(id: "**********", name: "Sports car"),
  //   SnapChatInterestsModel(id: "**********", name: "sports fashion"),
  //   SnapChatInterestsModel(id: "**********", name: "sports clothing"),
  //   SnapChatInterestsModel(id: "**********", name: "Gym clothes"),
  //   SnapChatInterestsModel(id: "**********", name: "women’s clothes"),
  //   SnapChatInterestsModel(id: "3883455198", name: "sports clothes"),
  //   SnapChatInterestsModel(id: "2061658054", name: "huge selection of clothes"),
  //   SnapChatInterestsModel(id: "2061920862", name: "clothes for women"),
  //   SnapChatInterestsModel(id: "2061658055", name: "clothes and shoes"),
  //   SnapChatInterestsModel(id: "2064632557", name: "fashionable clothes"),
  //   SnapChatInterestsModel(id: "2065337097", name: "trendy clothes"),
  //   SnapChatInterestsModel(id: "2027735914", name: "Fast food"),
  //   SnapChatInterestsModel(id: "2027796796", name: "food lovers"),
  //   SnapChatInterestsModel(id: "2065307841", name: "delicious foods"),
  //   SnapChatInterestsModel(id: "3531951436", name: "food tracker"),
  //   SnapChatInterestsModel(id: "1915591109", name: "Food"),
  // ]..shuffle();

  List<AdSetGeoLocations> geoLocations = [];

  void setSelectedLocation(List<AdSetGeoLocations> adLocations) {
    if (geoLocations.isEmpty) {
      geoLocations = adLocations;
      isAddNewLocation = false;
    } else {
      geoLocations.first.customLocations!
          .addAll(adLocations.first.customLocations!.map((e) => e).toList());
      isAddNewLocation = false;
    }
    emit(UpdateStates());
  }

  String? selectedBudgetMode;

  setSelectedBudgetMode(String mode) {
    print("budgetMode $mode");
    selectedBudgetMode = mode;
    emit(ChangeBudgetModeState());
  }

  // bool isAddNewLocation = false;

  void setAddNewLocationStatus() {
    isAddNewLocation = true;
    emit(UpdateStates());
  }

  Future<void> getSnapChatInterests({
    required BuildContext context,
    // required String advertiserId,
    // required String searchKey,
  }) async {
    try {
      emit(SnapChatInterestsLoading());

      final result =
          await instance<CreateSnapChatCampaignRepo>().getSnapChatInterests(
              // advertiserId: advertiserId,
              // searchKey: searchKey,
              );

      result.fold(
        (failure) {
          final message =
              FailureHelper.instance.handleFailures(failure, context);
          emit(SnapChatInterestsError(failure.message));
        },
        (interests) {
          emit(SnapChatInterestsLoaded(
            interests: interests ?? [],
            // selectedInterests: selectedSnapChatInterests,
            percentage: 1.0,
          ));
        },
      );
    } catch (e) {
      emit(SnapChatInterestsError('Failed to load interests'));
    }
  }

  void removeLocation(List<AdSetGeoLocations> adLocations, int indexToRemove) {
    if (geoLocations.isEmpty) {
      geoLocations = adLocations;

      isAddNewLocation = true;
    } else {
      // Check if the indexToRemove is valid
      if (indexToRemove >= 0 &&
          indexToRemove < geoLocations.first.customLocations!.length) {
        // Remove the location at the specified index
        geoLocations.first.customLocations!.removeAt(indexToRemove);
        if (geoLocations.first.customLocations!.isEmpty) {
          geoLocations = adLocations;
          isAddNewLocation = true;
          // locationPercentage = 0.0;
        }
      }
    }
    emit(UpdateStates());
  }

  void addToSnapChatInterests(SnapChatInterestsModel interest) {
    final state = this.state;
    if (state is! SnapChatInterestsLoaded) return;

    if (selectedInterests.any((i) => i.name == interest.name)) {
      emit(SnapChatInterestsError('Interest already selected'));
      return;
    }

    selectedInterests.add(interest);
    selectedSnapInterestsIds.add(interest.id!);
    // tiktokInterestsPercentage =
    //     (selectedInterests.length / 10).clamp(0.0, 1.0);

    emit(state.copyWith(
      selectedInterests: List.from(selectedInterests),
      percentage: 1.0,
    ));
  }

  void removeFromSnapChatInterests(SnapChatInterestsModel interest) {
    final state = this.state;
    if (state is! SnapChatInterestsLoaded) return;

    selectedInterests.removeWhere((i) => i.name == interest.name);
    selectedSnapInterestsIds.remove(interest.id!);
    // tiktokInterestsPercentage = (selectedInterests.length / 10).clamp(0.0, 1.0);

    emit(state.copyWith(
      selectedInterests: List.from(selectedInterests),
      percentage: 1.0,
    ));
  }

  getLanguages(
      {required BuildContext context, required String advertiserId}) async {
    // tiktokCampaigns?.clear();

    emit(GetSnapChatLanguagesStateLoading());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokLanguages(advertiserId: advertiserId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetSnapChatLanguagesStateError(l));
      }, (r) {
        snapChatLangs = r;
        print('tikTokLangs ${snapChatLangs?.result?.first.name}');
        emit(GetSnapChatLanguagesState(r));
      });
    });
  }

  List<SearchResult> languages = [];
  List<int> selectedLanguages = [];

  void setSelectedLanguages(SearchResult language) {
    if (languages.where((element) => element.key == language.key).isNotEmpty) {
      languages.removeWhere((element) => language.key == element.key);
      selectedLanguages.remove(language.key!);
      // languages.forEach((element) {
      //   selectedLanguages.remove(element.name);
      // });
      emit(SnapChatRemoveFromLanguageState());
    } else {
      languages.add(language);
      selectedLanguages.add(language.key!);
      // languages.forEach((element) {
      //   selectedLanguages.add(element.code!);
      // });
      emit(SnapChatAdToLanguageState());
    }
  }

  void clearAllAdGroupData() {
    adSetNameController.clear();
    snapChatLangs = null;
    selectedMinAge = null;
    selectedMaxAge = null;
    selectedBudgetMode = null;
    selectedIndex = null;
    selectedInterests.clear();
    selectedSnapInterestsIds.clear();
    genders.clear();
    languages.clear();
    selectedLanguages.clear();
    dailyBudget.clear();
    startDate.clear();
    endDate.clear();
    emit(SetAdGroupClearAllDataState());
  }
}

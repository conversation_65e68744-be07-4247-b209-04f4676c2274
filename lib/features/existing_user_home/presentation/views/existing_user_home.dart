import 'dart:async';

import 'package:ads_dv/features/sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/appbar.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../../utils/network/urls/services_urls.dart';
import '../../../../utils/res/custom_widgets.dart';
import '../../../../utils/res/router/routes.dart';
import '../../../../widgets/custom_button.dart';
import '../../../create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import '../../../home_layout/presentation/controllers/home_layout_cubit.dart';
import '../../../sidebar/ad_accounts/presentation/controllers/get_add_accounts/get_add_accounts_cubit.dart';

class ExistingUserHomeScreen extends StatefulWidget {
  const ExistingUserHomeScreen({super.key});

  @override
  State<ExistingUserHomeScreen> createState() => _ExistingUserHomeScreenState();
}

class _ExistingUserHomeScreenState extends State<ExistingUserHomeScreen>
    with TickerProviderStateMixin {
  List<String> banners = [
    AppAssets.dummy,
    AppAssets.dummy1,
    AppAssets.dummy2,
    AppAssets.dummy3,
    AppAssets.dummy4,
  ];
  int _bannerIndex = 0;

  late AnimationController _animationController;
  late Animation<double> _jumpAnimation;
  late YoutubePlayerController _dvController;

  bool isWatching = false;
  int _counter1 = 0;
  int _counter2 = 0;
  int _counter3 = 0;
  Timer? _timer1;
  Timer? _timer2;
  Timer? _timer3;

  final int _targetValue1 = 100000;
  final int _targetValue2 = 60000;
  final int _targetValue3 = 90000;
  final user = instance<HiveHelper>().getUser();

  void _startCounterAnimation(int targetValue, Function incrementCounter) {
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (incrementCounter() <= targetValue) {
        setState(() {
          incrementCounter();
        });
      } else {
        setState(() {
          incrementCounterToTarget(targetValue);
        });
        timer
            .cancel(); // Stop the timer when the counter reaches the target value
      }
    });
  }

  void _incrementCounter1() => _counter1 += 1000; // Increment by 1k
  void _incrementCounter2() => _counter2 += 1000; // Increment by 1k
  void _incrementCounter3() => _counter3 += 1000; // Increment by 1k

  // Method to ensure counter stops exactly at the target
  void incrementCounterToTarget(int targetValue) {
    if (_counter1 > targetValue) _counter1 = targetValue;
    if (_counter2 > targetValue) _counter2 = targetValue;
    if (_counter3 > targetValue) _counter3 = targetValue;
  }

  String _formatCounter(int counter) {
    // Format the counter with "k" if it's greater than or equal to 1000
    if (counter >= 1000) {
      return "${(counter / 1000).toStringAsFixed(0)}k"; // Show result as 'Xk'
    }
    return counter.toString();
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _jumpAnimation = Tween<double>(begin: 0.0, end: -20.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.repeat(reverse: true);

    _dvController = YoutubePlayerController(
      initialVideoId: '7SfpbV3Dj5E',
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
      ),
    );

    _startCounterAnimation(_targetValue1, _incrementCounter1);
    _startCounterAnimation(_targetValue2, _incrementCounter2);
    _startCounterAnimation(_targetValue3, _incrementCounter3);
  }

  @override
  void dispose() {
    _timer1?.cancel();
    _timer2?.cancel();
    _timer3?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  // @override
  // void initState() {
  //   print(
  //       'photoUrlxcxcz ${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.balance!.toInt() / 100}');
  //   super.initState();
  // }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return Stack(
          children: [
            Scaffold(
              appBar: CustomAppBar(
                height: 180.h,
                logo: AppAssets.mainLogo,
                actions: [
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, Routes.notifications);
                    },
                    child: CustomSvgWidget(
                      svg: AppAssets.notifications,
                      height: 30.h,
                      width: 30.h,
                    ),
                  )
                ],
                leading: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: InkWell(
                    onTap: () {
                      Constants.scaffoldKey.currentState!.openDrawer();
                    },
                    child: CustomSvgWidget(
                      svg: AppAssets.drawer,
                      height: 4.h,
                      width: 12.h,
                    ),
                  ),
                ),
              ),
              floatingActionButton: Padding(
                padding: const EdgeInsets.only(bottom: 35.0),
                child: ExpandableFab(
                  distance: 90.0,
                  type: ExpandableFabType.fan,
                  pos: ExpandableFabPos.center,
                  closeButtonBuilder: FloatingActionButtonBuilder(
                    size: 56.0,
                    builder: (context, onPressed, animation) => AnimatedBuilder(
                      animation: animation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: 1.0 + (0.2 * animation.value),
                          // Slight pop effect
                          child: ClipOval(
                            child: Material(
                              color: Colors.white,
                              // Button background color
                              child: InkWell(
                                onTap: onPressed,
                                child: SizedBox(
                                  width: 40.0.w,
                                  height: 40.0.h,
                                  child: Center(
                                    child: Transform.rotate(
                                      angle: animation.value * 0.5 * 3.14,
                                      // Rotate icon
                                      child: const Icon(
                                        Icons.close,
                                        color: Constants.darkColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  openButtonBuilder: FloatingActionButtonBuilder(
                    size: 56.0,
                    builder: (context, onPressed, animation) => AnimatedBuilder(
                      animation: animation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: 1.0 + (0.2 * animation.value),
                          // Slight pop effect
                          child: ClipOval(
                            child: Material(
                              color: Colors.white,
                              // Button background color
                              child: InkWell(
                                onTap: onPressed,
                                child: SizedBox(
                                  width: 56.0.w,
                                  height: 56.0.h,
                                  child: Center(
                                    child: Transform.rotate(
                                      angle: animation.value * 0.5 * 3.14,
                                      // Rotate icon
                                      child: const Icon(
                                        Icons.add,
                                        color: Constants.darkColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  children: [
                    FloatingActionButton(
                      heroTag: 'meta-fab',
                      backgroundColor: Colors.white,
                      onPressed: () {
                        if (user?.defaultAccountId == null &&
                            user?.defaultPageAccessToken == null &&
                            user?.defaultPageId == null) {
                          Navigator.pushNamed(context, Routes.accounts);
                        } else if (user?.accessToken == null ||
                            user?.accessToken == "") {
                          Navigator.pushNamed(context, Routes.accounts)
                              .then((value) {
                            HomeLayoutCubit.get(context)
                                .hasCampaign(context: context);
                          });
                          showErrorToast(
                              "Please connect with your Facebook account");
                        } else {
                          Navigator.pushNamed(context, Routes.createCampaign);
                        }
                      },
                      child: Center(
                        // Wrap the image with Center widget
                        child: Image.asset(
                          'assets/images/meta.png',
                          height: 30,
                        ),
                      ),
                    ),
                    FloatingActionButton(
                      heroTag: 'tiktok-fab',
                      backgroundColor: Colors.white,
                      onPressed: () {
                        Navigator.pushNamed(context, Routes.tiktokCampaign);
                      },
                      child:
                          const Icon(Icons.video_library, color: Colors.black),
                    ),
                    // FloatingActionButton(
                    //   heroTag: 'close-fab',
                    //   backgroundColor: Colors.white,
                    //   onPressed: () {},
                    //   child: const Icon(Icons.close, color: Colors.blueGrey),
                    // ),
                  ],
                ),
              ),
              // floatingActionButton: IconButton(
              //     onPressed: () async {
              //       await Navigator.of(context)
              //           .pushNamedAndRemoveUntil(Routes.chat, (route) => false);
              //     },
              //     icon: Icon(Icons.add)),
              // floatingActionButtonLocation:
              //     FloatingActionButtonLocation.startDocked,
            ),
            Padding(
              padding: EdgeInsets.only(top: 160.h, left: 12.sp, right: 12.sp),
              child: Container(
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(35.sp),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x4C000000),
                      blurRadius: 61.90,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 32.sp),
                  child: SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        Column(
                          children: [
                            if (instance<HiveHelper>()
                                    .getUser()
                                    ?.defaultAccountName !=
                                null)
                              Column(
                                children: [
                                  5.verticalSpace,
                                  AccountHintText(
                                    isDefaultHint: true,
                                    hint:
                                        "${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}",
                                  ),
                                  20.verticalSpace,
                                ],
                              )
                            else
                              const SizedBox(),
                            Row(
                              children: [
                                (instance<HiveHelper>().getUser()!.photo ==
                                            null ||
                                        instance<HiveHelper>()
                                                .getUser()!
                                                .photo ==
                                            "")
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(50),
                                        child: Container(
                                          height: 70.h,
                                          width: 70.h,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(50),
                                          ),
                                          child: CachedImageWidget(
                                            assetsImage: AppAssets.dummyProfile,
                                            fit: BoxFit.fill,
                                            height: 70.h,
                                          ),
                                        ),
                                      )
                                    : ClipRRect(
                                        borderRadius: BorderRadius.circular(50),
                                        child: Container(
                                          height: 70.h,
                                          width: 70.h,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(50),
                                          ),
                                          child: CachedImageWidget(
                                            image: ServicesURLs.assetsUrl +
                                                instance<HiveHelper>()
                                                    .getUser()!
                                                    .photo
                                                    .toString(),
                                            height: 70.h,
                                          ),
                                        ),
                                      ),
                                10.horizontalSpace,
                                Row(
                                  children: [
                                    CustomText(
                                      text: "Hello, ".tr,
                                      fontSize: 18.sp,
                                      color: Colors.black,
                                    ),
                                    CustomText(
                                      text: instance<HiveHelper>()
                                              .getUser()
                                              ?.name ??
                                          "",
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.black,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                        20.verticalSpace,
                        BlocBuilder<GetAdAccountsCubit, GetAdAccountsState>(
                          builder: (context, walletState) {
                            return Padding(
                              padding: EdgeInsets.symmetric(horizontal: 12.sp),
                              child: SizedBox(
                                height: 250.h,
                                width: double.infinity,
                                child: PageView(
                                  children: [
                                    Container(
                                      height: 250.h,
                                      width: double.infinity,
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x33000000),
                                            blurRadius: 30,
                                            offset: Offset(0, 4),
                                            spreadRadius: -6,
                                          )
                                        ],
                                      ),
                                      child: Center(
                                        child: Stack(
                                          children: [
                                            Center(
                                              child: walletState
                                                      is GetAdAccountsStateLoading
                                                  ? CustomText(
                                                      text: 'Loading ...'.tr,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    )
                                                  : CircularPercentIndicator(
                                                      radius: 80.sp,
                                                      animation: true,
                                                      animationDuration: 1200,
                                                      lineWidth: 13.sp,
                                                      percent: 0.65,
                                                      center: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          const CustomSvgWidget(
                                                              svg: AppAssets
                                                                  .wallet1),
                                                          6.verticalSpace,
                                                          ShaderMask(
                                                            shaderCallback:
                                                                (Rect bounds) {
                                                              return Constants
                                                                  .defGradient
                                                                  .createShader(
                                                                      bounds);
                                                            },
                                                            child: SizedBox(
                                                              width: 110.h,
                                                              child: GetAdAccountsCubit
                                                                          .get(
                                                                              context)
                                                                      .adAccounts
                                                                      .where((element) =>
                                                                          element
                                                                              .id ==
                                                                          instance<HiveHelper>()
                                                                              .getUser()
                                                                              ?.defaultAccountId)
                                                                      .isEmpty
                                                                  ? CustomText(
                                                                      text:
                                                                          "0 AED",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    )
                                                                  : CustomText(
                                                                      text:
                                                                          "${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.balance!.toInt() / 100 ?? ""} ${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.currency ?? ""}",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      reverse: true,
                                                      circularStrokeCap:
                                                          CircularStrokeCap
                                                              .round,
                                                      linearGradient:
                                                          Constants.secGradient,
                                                      backgroundColor:
                                                          const Color(
                                                                  0xFFFB533E)
                                                              .withOpacity(0.1),
                                                    ),
                                            ),
                                            Positioned(
                                              top: 10,
                                              left: 10,
                                              child: CustomText(
                                                text: "Total Balance".tr,
                                                fontWeight: FontWeight.w700,
                                                fontSize: 16.sp,
                                                alignment: AlignmentDirectional
                                                    .centerStart,
                                                color: Colors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Container(
                                      height: 250.h,
                                      width: double.infinity,
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x33000000),
                                            blurRadius: 30,
                                            offset: Offset(0, 4),
                                            spreadRadius: -6,
                                          )
                                        ],
                                      ),
                                      child: Center(
                                        child: Stack(
                                          children: [
                                            Center(
                                              child: walletState
                                                      is GetAdAccountsStateLoading
                                                  ? CustomText(
                                                      text: 'Loading ...'.tr,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    )
                                                  : CircularPercentIndicator(
                                                      radius: 80.sp,
                                                      animation: true,
                                                      animationDuration: 1200,
                                                      lineWidth: 13.sp,
                                                      percent: 0.65,
                                                      center: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          const CustomSvgWidget(
                                                              svg: AppAssets
                                                                  .wallet1),
                                                          6.verticalSpace,
                                                          ShaderMask(
                                                            shaderCallback:
                                                                (Rect bounds) {
                                                              return Constants
                                                                  .defGradient
                                                                  .createShader(
                                                                      bounds);
                                                            },
                                                            child: SizedBox(
                                                              width: 110.h,
                                                              child: GetAdAccountsCubit
                                                                          .get(
                                                                              context)
                                                                      .adAccounts
                                                                      .where((element) =>
                                                                          element
                                                                              .id ==
                                                                          instance<HiveHelper>()
                                                                              .getUser()
                                                                              ?.defaultAccountId)
                                                                      .isEmpty
                                                                  ? CustomText(
                                                                      text:
                                                                          "0 AED",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    )
                                                                  : CustomText(
                                                                      text:
                                                                          "${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.amountSpent.toInt() / 100 ?? ""} ${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.currency ?? ""}",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      reverse: true,
                                                      circularStrokeCap:
                                                          CircularStrokeCap
                                                              .round,
                                                      linearGradient:
                                                          Constants.secGradient,
                                                      backgroundColor:
                                                          const Color(
                                                                  0xFFFB533E)
                                                              .withOpacity(0.1),
                                                    ),
                                            ),
                                            Positioned(
                                              top: 10,
                                              left: 10,
                                              child: CustomText(
                                                text: "Amount Spent".tr,
                                                fontWeight: FontWeight.w700,
                                                fontSize: 16.sp,
                                                alignment: AlignmentDirectional
                                                    .centerStart,
                                                color: Colors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                        25.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomText(
                              text: "You don't have any campaigns now!".tr,
                              fontSize: 14.sp,
                              color: Constants.gray,
                            ),
                          ],
                        ),
                        30.verticalSpace,
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15.0),
                              gradient: Constants.defGradient),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 60.0, vertical: 20.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    // Using _formatCounter to display the values in "k" format
                                    Container(
                                      padding: const EdgeInsets.all(5.0),
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: Constants.secGradient,
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 60.0.h,
                                        width: 60.0.w,
                                        decoration: const BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: Constants.defGradient,
                                        ),
                                        child: Text(
                                          _formatCounter(_counter1),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 20.0.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.all(5.0),
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: Constants.secGradient,
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 60.0.h,
                                        width: 60.0.w,
                                        decoration: const BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: Constants.defGradient,
                                        ),
                                        child: Text(
                                          _formatCounter(_counter2),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 20.0.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.all(5.0),
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: Constants.secGradient,
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 60.0.h,
                                        width: 60.0.w,
                                        decoration: const BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: Constants.defGradient,
                                        ),
                                        child: Text(
                                          _formatCounter(_counter3),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 20.0.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                children: [
                                  const Expanded(
                                    child: CustomText(
                                      text:
                                          "Start your campaigns to reach this successful numbers! 🎉",
                                      alignment: AlignmentDirectional.topCenter,
                                      textAlign: TextAlign.center,
                                      fontSize: 15.0,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                      maxLines: 2,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10.0, vertical: 10.0),
                                    child: Container(
                                      padding: EdgeInsets.zero,
                                      width: 100.h,
                                      height: 42.h,
                                      decoration: ShapeDecoration(
                                        gradient: Constants.secGradient,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(24),
                                        ),
                                      ),
                                      child: CustomButton(
                                        text: 'start'.tr,
                                        onPressed: () {
                                          // Navigator.pushNamed(
                                          //     context, Routes.tiktokCampaign);
                                          if (instance<HiveHelper>()
                                                      .getUser()
                                                      ?.defaultAccountId ==
                                                  null ||
                                              instance<HiveHelper>()
                                                      .getUser()
                                                      ?.defaultPageAccessToken ==
                                                  null ||
                                              instance<HiveHelper>()
                                                      .getUser()
                                                      ?.defaultPageId ==
                                                  null) {
                                            Navigator.pushNamed(
                                                context, Routes.metaAccounts);
                                          } else {
                                            Navigator.pushNamed(
                                                context, Routes.createCampaign);
                                          }
                                          //Navigator.pushNamed(context, Routes.createCampaign);
                                        },
                                        color: Colors.transparent,
                                        textColor: Constants.darkColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        25.verticalSpace,
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15.0),
                              gradient: Constants.defGradient),
                          child: Column(
                            children: [
                              20.verticalSpace,
                              const CustomText(
                                text: "Coming Soon !",
                                alignment: AlignmentDirectional.topCenter,
                                textAlign: TextAlign.center,
                                fontSize: 30.0,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                                maxLines: 2,
                              ),
                              20.verticalSpace,
                              const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CachedImageWidget(
                                    // height: 170.h,
                                    // width: double.infinity,
                                    assetsImage:
                                        "assets/images/Animation - 1738077734800 1.png",
                                  ),
                                  CachedImageWidget(
                                    // height: 170.h,
                                    // width: double.infinity,
                                    assetsImage:
                                        "assets/images/Animation - 1738077851274 2.png",
                                  ),
                                ],
                              ),
                              20.verticalSpace,
                            ],
                          ),
                        ),
                        // BlocBuilder<CreateAdCubit, CreateAdState>(
                        //   builder: (submitContext, state) {
                        //     return GestureDetector(
                        //       onTap: () {
                        //         if (instance<HiveHelper>()
                        //                     .getUser()
                        //                     ?.defaultAccountId ==
                        //                 null ||
                        //             instance<HiveHelper>()
                        //                     .getUser()
                        //                     ?.defaultPageAccessToken ==
                        //                 null ||
                        //             instance<HiveHelper>()
                        //                     .getUser()
                        //                     ?.defaultPageId ==
                        //                 null) {
                        //           Navigator.pushNamed(
                        //               context, Routes.metaAccounts);
                        //         } else {
                        //           Navigator.pushNamed(
                        //               context, Routes.createCampaign);
                        //         }
                        //         //Navigator.pushNamed(context, Routes.createCampaign);
                        //       },
                        //       child: Container(
                        //         padding: EdgeInsets.zero,
                        //         width: 142.h,
                        //         height: 42.h,
                        //         decoration: ShapeDecoration(
                        //           gradient: Constants.secGradient,
                        //           shape: RoundedRectangleBorder(
                        //             borderRadius: BorderRadius.circular(24),
                        //           ),
                        //         ),
                        //         child: Padding(
                        //           padding: EdgeInsets.only(
                        //               top: 11.sp, left: 14.sp, right: 14.sp),
                        //           child: CustomText(
                        //             text: "Start campaign".tr,
                        //             fontSize: 22.sp,
                        //             fontWeight: FontWeight.w500,
                        //             color: Colors.white,
                        //           ),
                        //         ),
                        //       ),
                        //     );
                        //   },
                        // ),
                        25.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 50.sp),
                          child: const Divider(color: Constants.gray),
                        ),
                        20.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 280.w,
                              child: CustomText(
                                alignment: AlignmentDirectional.centerStart,
                                maxLines: 2,
                                text:
                                    "Choose an Occasion to launch your campaign"
                                        .tr,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                        10.verticalSpace,
                        CarouselSlider(
                          items: List.generate(banners.length, (index) {
                            return InkWell(
                              onTap: () {},
                              child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: Opacity(
                                    opacity: _bannerIndex != index ? 0.5 : 1.0,
                                    child: CachedImageWidget(
                                      assetsImage: banners[index],
                                      fit: BoxFit.fitWidth,
                                      width: double.infinity,
                                    ),
                                  )),
                            );
                          }),
                          options: CarouselOptions(
                            onPageChanged: (index, reason) {
                              setState(() {
                                _bannerIndex = index;
                              });
                            },
                            scrollPhysics: const BouncingScrollPhysics(),
                            height: 130.h,
                            enlargeCenterPage: true,
                            initialPage: 0,
                            viewportFraction: 0.57,
                            enlargeStrategy: CenterPageEnlargeStrategy.scale,
                            enableInfiniteScroll: true,
                            autoPlay: true,
                            reverse: false,
                            autoPlayInterval: const Duration(seconds: 3),
                            autoPlayAnimationDuration:
                                const Duration(seconds: 1),
                            autoPlayCurve: Curves.fastOutSlowIn,
                            scrollDirection: Axis.horizontal,
                          ),
                        ),
                        80.verticalSpace,
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(20),
                                child: YoutubePlayer(
                                    controller: _dvController,
                                    showVideoProgressIndicator: true,
                                    progressIndicatorColor:
                                        Constants.primaryTextColor),
                              ),
                              if (isWatching == false)
                                Positioned(
                                  top: 0,
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  child: IconButton(
                                    onPressed: () {
                                      setState(() {
                                        isWatching = true;
                                      });
                                      if (_dvController.value.isPlaying) {
                                        _dvController.pause();
                                      } else {
                                        _dvController.play();
                                      }
                                    },
                                    icon: const Icon(
                                      Icons.play_circle_outline,
                                      size: 100,
                                      color: Colors.white,
                                    ),
                                    color: Colors.transparent,
                                  ),
                                )
                            ],
                          ),
                        ),
                        80.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 100.0,
              right: 20.0,
              child: GestureDetector(
                onTap: () async {
                  await Navigator.of(context)
                      .pushNamedAndRemoveUntil(Routes.chat, (route) => false);
                },
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _jumpAnimation.value),
                      // Move the button up and down
                      child: child,
                    );
                  },
                  child: Image.asset(
                      "assets/images/chat.png"), // Your chat button image
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/appbar.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_button.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'package:get/get.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../utils/network/urls/services_urls.dart';
import '../../../../utils/res/custom_widgets.dart';
import '../../../../utils/res/router/routes.dart';
import '../../../home_layout/presentation/controllers/home_layout_cubit.dart';

class DefaultHomeScreen extends StatefulWidget {
  const DefaultHomeScreen({super.key});

  @override
  State<DefaultHomeScreen> createState() => _DefaultHomeScreenState();
}

class _DefaultHomeScreenState extends State<DefaultHomeScreen>
    with TickerProviderStateMixin {
  List<String> banners = [
    AppAssets.dummy,
    AppAssets.dummy1,
    AppAssets.dummy2,
    AppAssets.dummy3,
    AppAssets.dummy4,
  ];
  int _bannerIndex = 0;
  bool isWatching = false;
  bool isWatching2 = false;
  late YoutubePlayerController _controller;

  late YoutubePlayerController _dvController;

  late AnimationController _animationController;
  late Animation<double> _jumpAnimation;
  final user = instance<HiveHelper>().getUser();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1), // Duration of one jump
      vsync: this,
    );

    // Define the jumping animation (goes from 0 to -20 to 0, creating a bouncing effect)
    _jumpAnimation = Tween<double>(begin: 0.0, end: -20.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut, // Smooth easing in and out
      ),
    );

    // Loop the animation to make it "jump" repeatedly
    _animationController.repeat(reverse: true);

    _controller = YoutubePlayerController(
      initialVideoId: '7SfpbV3Dj5E',
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
      ),
    );
    _dvController = YoutubePlayerController(
      initialVideoId: '7SfpbV3Dj5E',
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _controller.dispose();
    _dvController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: CustomAppBar(
            height: 180.h,
            logo: AppAssets.mainLogo,
            actions: [
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, Routes.notifications);
                },
                child: CustomSvgWidget(
                  svg: AppAssets.notifications,
                  height: 30.h,
                  width: 30.h,
                ),
              )
            ],
            leading: Padding(
              padding: const EdgeInsets.all(8.0),
              child: InkWell(
                onTap: () {
                  Constants.scaffoldKey.currentState!.openDrawer();
                },
                child: CustomSvgWidget(
                  svg: AppAssets.drawer,
                  height: 4.h,
                  width: 12.h,
                ),
              ),
            ),
          ),
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(bottom: 35.0),
            child: ExpandableFab(
              distance: 90.0,
              type: ExpandableFabType.fan,
              pos: ExpandableFabPos.center,
              closeButtonBuilder: FloatingActionButtonBuilder(
                size: 56.0,
                builder: (context, onPressed, animation) => AnimatedBuilder(
                  animation: animation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (0.2 * animation.value),
                      // Slight pop effect
                      child: ClipOval(
                        child: Material(
                          color: Colors.white, // Button background color
                          child: InkWell(
                            onTap: onPressed,
                            child: SizedBox(
                              width: 40.0.w,
                              height: 40.0.h,
                              child: Center(
                                child: Transform.rotate(
                                  angle: animation.value * 0.5 * 3.14,
                                  // Rotate icon
                                  child: const Icon(
                                    Icons.close,
                                    color: Constants.darkColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              openButtonBuilder: FloatingActionButtonBuilder(
                size: 56.0,
                builder: (context, onPressed, animation) => AnimatedBuilder(
                  animation: animation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (0.2 * animation.value),
                      // Slight pop effect
                      child: ClipOval(
                        child: Material(
                          color: Colors.white, // Button background color
                          child: InkWell(
                            onTap: onPressed,
                            child: SizedBox(
                              width: 56.0.w,
                              height: 56.0.h,
                              child: Center(
                                child: Transform.rotate(
                                  angle: animation.value * 0.5 * 3.14,
                                  // Rotate icon
                                  child: const Icon(
                                    Icons.add,
                                    color: Constants.darkColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              children: [
                FloatingActionButton(
                  heroTag: 'meta-fab',
                  backgroundColor: Colors.white,
                  onPressed: () {
                    if (user?.defaultAccountId == null &&
                        user?.defaultPageAccessToken == null &&
                        user?.defaultPageId == null) {
                      Navigator.pushNamed(context, Routes.accounts);
                    } else if (user?.accessToken == null ||
                        user?.accessToken == "") {
                      Navigator.pushNamed(context, Routes.accounts)
                          .then((value) {
                        HomeLayoutCubit.get(context)
                            .hasCampaign(context: context);
                      });
                      showErrorToast(
                          "Please connect with your Facebook account");
                    } else {
                      Navigator.pushNamed(context, Routes.createCampaign);
                    }
                  },
                  child: Center(
                    // Wrap the image with Center widget
                    child: Image.asset(
                      'assets/images/meta.png',
                      height: 30,
                    ),
                  ),
                ),
                FloatingActionButton(
                  heroTag: 'tiktok-fab',
                  backgroundColor: Colors.white,
                  onPressed: () {
                    Navigator.pushNamed(context, Routes.tiktokCampaign);
                  },
                  child: const Icon(Icons.video_library, color: Colors.black),
                ),
                // FloatingActionButton(
                //   heroTag: 'close-fab',
                //   backgroundColor: Colors.white,
                //   onPressed: () {},
                //   child: const Icon(Icons.close, color: Colors.blueGrey),
                // ),
              ],
            ),
          ),
          // floatingActionButton: IconButton(
          //     onPressed: () async {
          //       await Navigator.of(context)
          //           .pushNamedAndRemoveUntil(Routes.chat, (route) => false);
          //     },
          //     icon: Icon(Icons.add)),
          // floatingActionButtonLocation:
          // FloatingActionButtonLocation.startDocked,
          // floatingActionButton: Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 40.0),
          //   child: FloatingActionButton(
          //     heroTag: 'meta-fab',
          //     backgroundColor: Colors.white,
          //     onPressed: () {
          //       if (user?.defaultAccountId == null &&
          //           user?.defaultPageAccessToken == null &&
          //           user?.defaultPageId == null) {
          //         Navigator.pushNamed(context, Routes.accounts);
          //       } else if (user?.accessToken == null ||
          //           user?.accessToken == "") {
          //         Navigator.pushNamed(context, Routes.accounts).then((value) {
          //           HomeLayoutCubit.get(context).hasCampaign(context: context);
          //         });
          //         showErrorToast("Please connect with your Facebook account");
          //       } else {
          //         Navigator.pushNamed(context, Routes.createCampaign);
          //       }
          //     },
          //     child: Center(
          //       // Wrap the image with Center widget
          //       child: Image.asset(
          //         'assets/images/meta.png',
          //         height: 30,
          //       ),
          //     ),
          //   ),
          // ),
          // floatingActionButtonLocation:
          //     FloatingActionButtonLocation.centerDocked,
          // floatingActionButtonLocation: ExpandableFab.location,
        ),
        Padding(
          padding: EdgeInsets.only(top: 160.h, left: 12.sp, right: 12.sp),
          child: Container(
            decoration: ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(35.sp),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x4C000000),
                  blurRadius: 61.90,
                  offset: Offset(0, 4),
                  spreadRadius: 0,
                )
              ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 32.sp),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      children: [
                        if (instance<HiveHelper>().getUser() != null)
                          (instance<HiveHelper>().getUser() != null &&
                                      instance<HiveHelper>().getUser()!.photo ==
                                          null ||
                                  instance<HiveHelper>().getUser()!.photo == "")
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(50),
                                  child: Container(
                                    height: 70.h,
                                    //   width: 70.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                    child: CachedImageWidget(
                                      assetsImage: AppAssets.dummyProfile,
                                      fit: BoxFit.fill,
                                      height: 70.h,
                                    ),
                                  ),
                                )
                              : ClipRRect(
                                  borderRadius: BorderRadius.circular(50),
                                  child: Container(
                                    height: 70.h,
                                    width: 70.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                    child: CachedImageWidget(
                                      image: ServicesURLs.assetsUrl +
                                          instance<HiveHelper>()
                                              .getUser()!
                                              .photo
                                              .toString(),
                                      height: 70.h,
                                    ),
                                  ),
                                ),
                        10.horizontalSpace,
                        Expanded(
                          child: Row(
                            children: [
                              CustomText(
                                text: "Hello, ".tr,
                                fontSize: 18.sp,
                                color: Colors.black,
                              ),
                              Expanded(
                                child: CustomText(
                                  text:
                                      instance<HiveHelper>().getUser()?.name ??
                                          "",
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.black,
                                  maxLines: 2,
                                  textOverflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    20.verticalSpace,
                    (isWatching)
                        ? Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: YoutubePlayer(
                                  controller: _controller,
                                  showVideoProgressIndicator: true,
                                  progressIndicatorColor:
                                      Constants.primaryTextColor),
                            ),
                          )
                        : InkWell(
                            onTap: () {
                              setState(() {
                                isWatching = true;
                              });
                              if (_controller.value.isPlaying) {
                                _controller.pause();
                              } else {
                                _controller.play();
                              }
                            },
                            child: CachedImageWidget(
                              height: 170.h,
                              width: double.infinity,
                              assetsImage: AppAssets.watchVideo,
                            ),
                          ),
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15.0),
                          gradient: Constants.defGradient),
                      child: Column(
                        children: [
                          const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CachedImageWidget(
                                assetsImage: 'assets/images/kWLImxvqCc 1.png',
                              ),
                              CachedImageWidget(
                                assetsImage: 'assets/images/9MvkihxoYG 1.png',
                              ),
                            ],
                          ),
                          const CustomText(
                            text: "Your success starts here! 🎉",
                            alignment: AlignmentDirectional.topCenter,
                            textAlign: TextAlign.center,
                            fontSize: 15.0,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 120.0, vertical: 10.0),
                            child: CustomButton(
                              text: 'connect'.tr,
                              onPressed: () {
                                Navigator.pushNamed(context, Routes.accounts);
                              },
                              color: Colors.white,
                              textColor: Constants.darkColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    25.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 280.w,
                          child: CustomText(
                            alignment: AlignmentDirectional.centerStart,
                            maxLines: 2,
                            text:
                                "Choose an Occasion to launch your campaign".tr,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                    10.verticalSpace,
                    CarouselSlider(
                      items: List.generate(banners.length, (index) {
                        return InkWell(
                          onTap: () {},
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: Opacity(
                                opacity: _bannerIndex != index ? 0.5 : 1.0,
                                child: CachedImageWidget(
                                  assetsImage: banners[index],
                                  fit: BoxFit.fitWidth,
                                  width: double.infinity,
                                ),
                              )),
                        );
                      }),
                      options: CarouselOptions(
                        onPageChanged: (index, reason) {
                          setState(() {
                            _bannerIndex = index;
                          });
                        },
                        scrollPhysics: const BouncingScrollPhysics(),
                        height: 130.h,
                        enlargeCenterPage: true,
                        initialPage: 0,
                        viewportFraction: 0.57,
                        enlargeStrategy: CenterPageEnlargeStrategy.scale,
                        enableInfiniteScroll: true,
                        autoPlay: true,
                        reverse: false,
                        autoPlayInterval: const Duration(seconds: 3),
                        autoPlayAnimationDuration: const Duration(seconds: 1),
                        autoPlayCurve: Curves.fastOutSlowIn,
                        scrollDirection: Axis.horizontal,
                      ),
                    ),
                    80.verticalSpace,
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: YoutubePlayer(
                                controller: _dvController,
                                showVideoProgressIndicator: true,
                                progressIndicatorColor:
                                    Constants.primaryTextColor),
                          ),
                          if (isWatching2 == false)
                            Positioned(
                              top: 0,
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: IconButton(
                                onPressed: () {
                                  setState(() {
                                    isWatching = true;
                                  });
                                  if (_dvController.value.isPlaying) {
                                    _dvController.pause();
                                  } else {
                                    _dvController.play();
                                  }
                                },
                                icon: const Icon(
                                  Icons.play_circle_outline,
                                  size: 100,
                                  color: Colors.white,
                                ),
                                color: Colors.transparent,
                              ),
                            )
                        ],
                      ),
                    ),
                    80.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 100.0,
          right: 20.0,
          child: GestureDetector(
            onTap: () async {
              await Navigator.of(context)
                  .pushNamedAndRemoveUntil(Routes.chat, (route) => false);
            },
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _jumpAnimation.value),
                  // Move the button up and down
                  child: child,
                );
              },
              child: Image.asset(
                  "assets/images/chat.png"), // Your chat button image
            ),
          ),
        ),
      ],
    );
  }
}

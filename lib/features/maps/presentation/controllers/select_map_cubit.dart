import 'dart:async';

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../utils/functions.dart';
import '../../../../utils/res/colors.dart';
import '../../../../utils/res/custom_widgets.dart';
import '../../../../utils/res/meta_constants.dart';
import '../../../create_campaigns/data/models/adset_location.dart';
import '../../data/models/place.dart';
import '../../data/repos/maps_repository.dart';

part 'select_map_state.dart';

class SelectMapCubit extends Cubit<SelectMapState> {
  SelectMapCubit() : super(SelectMapInitial());

  static SelectMapCubit get(context) => BlocProvider.of(context);
  MapsRepoImpl repo = MapsRepoImpl();

  GoogleMapController? mapController;

  // late Completer<GoogleMapController> mapController;
  Set<Marker> setMarker = {};
  late Debouncer debounce;

  LatLng? curruntLatLng;
  Place? currentPlace;

  List<CustomLocations> selectedLocations = [];

  TextEditingController locationNameController = TextEditingController();

  // TextEditingController radiusController = TextEditingController();
  bool isLoading = false;
  Set<Circle> circles = <Circle>{};

  double radius = 1.0;

  void onInit() async {
    print('setSelectedControllervcxzxc');
    await getCurrentUserLocation();
    final arg = Get.arguments as List<CustomLocations>?;
    selectedLocations = arg ?? [];

    print('setSelectedControllervcxzxc $selectedLocations');

    debounce = Debouncer(milliseconds: 500);
  }

  void update() {
    emit(UpdateMapStates());
  }

  void setSelectedController(
      GoogleMapController controller, BuildContext context) async {
    // print('setSelectedControllervcxzxc');
    // if (!mapController.isCompleted) {
    //   mapController.complete(controller);
    //   // ReservationBloc.to.showMarkerInfoWindow(
    //   //     markerId: const MarkerId('origin'));
    // }
    mapController = controller;
    await moveMapToLocation(curruntLatLng!, context);
    emit(UpdateMapStates());
  }

  GlobalKey<FormState>? _key;

  Future<void> moveMapToLocation(LatLng location, BuildContext context) async {
    // Move the map camera to the specified location
    await mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: location,
          zoom: 14.0, // Adjust the zoom level as needed
        ),
      ),
    );
  }

  CustomLocations? selectedLocation;

  Future<void> addMarker(
      LatLng latlng, BuildContext context, CreateAdCubit createAdCubit,
      [bool askForRadius = false]) async {
    setMarker.clear();
    selectedLocations.clear();
    if (askForRadius) {
      // selectedLocation =
      // CustomLocations(latitude: latlng.latitude, longitude: latlng.longitude);
      markerAddedLatLng = LatLng(latlng.latitude, latlng.longitude);
      final selectedMarker = Marker(
        markerId: MarkerId('${latlng.latitude}${latlng.longitude}'),
        position: latlng,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
        // infoWindow: const InfoWindow(title: 'Current User Location'),
      );
      setMarker.add(selectedMarker);
      handleCircle(context, radius);
    }
    Future.delayed(const Duration(milliseconds: 1000), () async {
      await saveMyLocation(latlng, context, createAdCubit, askForRadius);
    });
    // return;

    emit(UpdateMapStates());
  }

  bool isLocationDetected = false;
  LatLng? markerAddedLatLng;

  Future<void> saveMyLocation(
      LatLng latlng, BuildContext context, CreateAdCubit createAdCubit,
      [bool askForRadius = false]) async {
    markerAddedLatLng = latlng;
    // Current progress value (0 to 100)
    // var unit = DistanceUnit.kilometer;
    // String? locationName;
    // int? locationRadius;
    // _key = GlobalKey<FormState>();

    // SelectMapCubit.get(context).circles.removeWhere((circle) {
    //   return circle.circleId.value == 'circle1';
    // });
    //
    // SelectMapCubit.get(context).circles.add(
    //       Circle(
    //         circleId: CircleId('circle1'),
    //         center: LatLng(
    //           SelectMapCubit.get(context).selectedLocations.last.latitude!,
    //           SelectMapCubit.get(context).selectedLocations.last.longitude!,
    //         ),
    //         radius: radius * 1000,
    //         // Assuming progress * 1000 to convert to meters
    //         fillColor: Colors.blue.withOpacity(0.3),
    //         strokeColor: Colors.blue,
    //         strokeWidth: 2,
    //       ),
    //     );

    await moveMapToLocation(latlng, context);
    isLocationDetected = true;
    emit(UpdateMapStates());
    // await customBottomSheet(
    // formKey: _key,
    // children: [
    //   CustomText(
    //     text: "Add New Location".tr,
    //     fontSize: 17.sp,
    //     fontWeight: FontWeight.w400,
    //     color: Constants.primaryTextColor,
    //   ),
    //   SizedBox(height: 34.sp),
    //   CustomTextFormField(
    //     controller: locationNameController,
    //     hintText: "Location Name".tr,
    //     // validator: (p0) => Validator.lengthValidator(p0, minLength: 5),
    //     onSaved: (p0) => locationName = p0,
    //     onChanged: (val) {
    //       if (locationNameController.text.isNotEmpty) {
    //         createAdCubit.updateLocationProcess1();
    //         CreateAdCubit.get(context).updateAdSetProcess8();
    //       } else {
    //         createAdCubit.undoLocationProcess1();
    //         CreateAdCubit.get(context).updateAdSetProcess8();
    //       }
    //     },
    //   ),
    //   SizedBox(height: 14.sp),
    //
    //   SizedBox(
    //     height: 30.h,
    //     width: 155.h,
    //     child: StatefulBuilder(builder: (context, setState) {
    //       return TabsWidget(
    //           isMap: true,
    //           selectedTab: (unit == DistanceUnit.kilometer) ? 0 : 1,
    //           onTabChanged: (tab) {
    //             if (unit == DistanceUnit.kilometer) {
    //               setState(() => unit = DistanceUnit.mile);
    //             } else if (unit == DistanceUnit.mile) {
    //               setState(() => unit = DistanceUnit.kilometer);
    //             }
    //             createAdCubit.updateLocationProcess3();
    //             CreateAdCubit.get(context).updateAdSetProcess8();
    //           },
    //           newObject: 'Kilo: (K)'.tr,
    //           fontWeight: FontWeight.w400,
    //           existObject: 'Mile: (M)'.tr);
    //     }),
    //   ),
    //   SizedBox(height: 14.sp),
    //
    //   // StatefulBuilder(
    //   //   builder: (context, setState) => CustomButton(
    //   //     text: 'Unit: ${unit.name[0].toUpperCase()}',
    //   //     color: Colors.transparent,
    //   //     textColor: Constants.mainColor,
    //   //     onPressed: () => switch (unit) {
    //   //       DistanceUnit.kilometer =>
    //   //         setState(() => unit = DistanceUnit.mile),
    //   //       DistanceUnit.mile =>
    //   //         setState(() => unit = DistanceUnit.kilometer),
    //   //     },
    //   //   ),
    //   // ),
    //   CustomTextFormField(
    //     keyboardType: TextInputType.number,
    //     hintText: "Location Radius".tr,
    //     validator: (p0) => AppValidator.validateIdentity(p0, context),
    //     controller: radiusController,
    //     onChanged: (val) {
    //       if (radiusController.text.isNotEmpty) {
    //         createAdCubit.updateLocationProcess2();
    //         CreateAdCubit.get(context).updateAdSetProcess8();
    //       } else {
    //         createAdCubit.undoLocationProcess2();
    //         CreateAdCubit.get(context).updateAdSetProcess8();
    //       }
    //     },
    //     // inputFormatters: [Validator.allowNumsOnly()],
    //     onSaved: (p0) =>
    //     locationRadius = int.tryParse(p0.toString().trim()),
    //   ),
    //   SizedBox(height: 24.sp),
    //   SizedBox(
    //     width: 123.h,
    //     height: 40.h,
    //     child: ButtonWidget(
    //       padding: 12.sp,
    //       text: "Confirm".tr,
    //       onTap: () {
    //         if (!_key!.currentState!.validate()) return;
    //         _key!.currentState!.save();
    //         selectedLocation = CustomLocations(
    //           latitude: latlng.latitude,
    //           longitude: latlng.longitude,
    //           addressString: locationName,
    //           distanceUnit: unit,
    //           radius: locationRadius,
    //         );
    //
    //         if (!selectedLocations.contains(selectedLocation)) {
    //           selectedLocations.add(selectedLocation!);
    //           circles.add(
    //             Circle(
    //               circleId: CircleId('circle1'),
    //               center: LatLng(selectedLocations.last.latitude!, selectedLocations.last.longitude!),
    //               radius: double.parse(radiusController.text),
    //               fillColor: Colors.blue.withOpacity(0.3),
    //               strokeColor: Colors.blue,
    //               strokeWidth: 2,
    //             ),
    //           );
    //         }
    //         // final selectedMarker = Marker(
    //         //   markerId: MarkerId('${latlng.latitude}${latlng.longitude}'),
    //         //   position: latlng,
    //         //   icon: BitmapDescriptor.defaultMarkerWithHue(
    //         //       BitmapDescriptor.hueAzure),
    //         //   // infoWindow: const InfoWindow(title: 'Current User Location'),
    //         // );
    //         // setMarker.add(selectedMarker);
    //         // if (!selectedLocations.contains(selectedLocation)) {
    //         //   selectedLocations.add(selectedLocation);
    //         //   // CreateAdCubit.get(context)
    //         //   //     .getReachEstimate(
    //         //   //     context: context,
    //         //   //     files:
    //         //   //     CreateAdCubit.get(context)
    //         //   //         .adImages);
    //           print("selectasdas" + selectedLocation!.addressString.toString() + selectedLocation!.distanceUnit.toString() + selectedLocation!.radius.toString() + selectedLocation!.latitude.toString() + selectedLocation!.longitude.toString() + locationRadius.toString() + locationName.toString() + unit.toString());
    //         // }
    //         // await moveMapToLocation(latlng,context);
    //
    //         emit(UpdateStates());
    //         Get.back();
    //       },
    //     ),
    //   ),
    // ],
    // );
    _key = null;
  }

  Widget handleRadiusCircle(BuildContext context) {
    return SizedBox(
      width: 150.0,
      child: Card(
        color: Colors.white,
        child: ExpansionTileItem(
          iconColor: AppColors.secondColor,
          collapsedIconColor: AppColors.secondColor,
          expandedAlignment: Alignment.center,
          expandedCrossAxisAlignment: CrossAxisAlignment.center,
          title: Text("${radius.toInt()} ${'Kilo: (K)'.tr}"),
          children: [
            Slider(
              value: radius,
              // Current value of the slider
              min: 1,
              // Minimum value (0)
              max: 60,
              // Maximum value (100)
              divisions: 60,
              // Optional: divisions between the values
              label: radius.toInt().toString(),
              onChanged: (double value) {
                // setState(() {
                radius = value;
                SelectMapCubit.get(context).handleCircle(
                  context,
                  radius,
                );
                emit(UpdateMapStates());
                // SelectMapCubit.get(context).circles.removeWhere((circle) {
                //   return circle.circleId.value == 'circle1';
                // });
                //
                // SelectMapCubit.get(context).circles.add(
                //       Circle(
                //         circleId: CircleId('circle1'),
                //         center: LatLng(
                //           SelectMapCubit.get(context).selectedLocations.last.latitude!,
                //           SelectMapCubit.get(context).selectedLocations.last.longitude!,
                //         ),
                //         radius: radius * 1000,
                //         // Assuming progress * 1000 to convert to meters
                //         fillColor: Colors.blue.withOpacity(0.3),
                //         strokeColor: Colors.blue,
                //         strokeWidth: 2,
                //       ),
                //     );
                // });
              },
            ),
          ],
        ),
      ),
    );
    // emit(UpdateStates());
  }

  handleCircle(BuildContext context, double? radius) {
    circles.removeWhere((circle) {
      return circle.circleId.value == 'circle1';
    });

    circles.add(
      Circle(
        circleId: const CircleId('circle1'),
        center: LatLng(
          markerAddedLatLng!.latitude,
          markerAddedLatLng!.longitude,
        ),
        radius: radius! * 1000,
        // Assuming progress * 1000 to convert to meters
        fillColor: Colors.blue.withOpacity(0.3),
        strokeColor: Colors.blue,
        strokeWidth: 2,
      ),
    );
    // });
    // if (SelectMapCubit.get(context).isLocationDetected) {
    //   return SizedBox(
    //     width: 150.0,
    //     child: Card(
    //       color: Colors.white,
    //       child: ExpansionTileItem(
    //         iconColor: AppColors.secondColor,
    //         collapsedIconColor: AppColors.secondColor,
    //         expandedAlignment: Alignment.center,
    //         expandedCrossAxisAlignment: CrossAxisAlignment.center,
    //         title: Text("${radius.toInt()} ${'Kilo: (K)'.tr}"),
    //         children: [
    //           Slider(
    //             value: radius,
    //             // Current value of the slider
    //             min: 0,
    //             // Minimum value (0)
    //             max: 100,
    //             // Maximum value (100)
    //             divisions: 100,
    //             // Optional: divisions between the values
    //             label: radius.toInt().toString(),
    //             onChanged: (double value) {
    //               // setState(() {
    //               radius = value;
    //
    //             },
    //           ),
    //         ],
    //       ),
    //     ),
    //   );
    // } else {
    //   return const SizedBox();
    // }

    emit(UpdateMapStates());
  }

  saveLocationData(BuildContext context) {
    selectedLocation = CustomLocations(
      latitude: markerAddedLatLng!.latitude,
      longitude: markerAddedLatLng!.longitude,
      addressString: "",
      distanceUnit: DistanceUnit.kilometer,
      radius: radius.toInt(),
    );

    if (!selectedLocations.contains(selectedLocation)) {
      selectedLocations.add(selectedLocation!);
      circles.add(
        Circle(
          circleId: const CircleId('circle1'),
          center: LatLng(selectedLocations.last.latitude!,
              selectedLocations.last.longitude!),
          radius: radius,
          fillColor: Colors.blue.withOpacity(0.3),
          strokeColor: Colors.blue,
          strokeWidth: 2,
        ),
      );
      if (selectedLocations.isNotEmpty) {
        CreateAdCubit.get(context).locationPercentage = 1.0;
      } else {
        CreateAdCubit.get(context).locationPercentage = 0.0;
      }
      // CreateAdCubit.get(context).locationPercentage = 1.0;
    }
  }

  Future<void> getCurrentUserLocation() async {
    try {
      // Check if location service is enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print("Location services are disabled.");
        showErrorToast("Please enable location services.");
        return; // Exit the function if location service is not enabled
      }

      // Check location permission status
      PermissionStatus status = await Permission.location.status;

      // If the location permission is granted, fetch the location
      if (status.isGranted) {
        final position = await Geolocator.getCurrentPosition();
        curruntLatLng = LatLng(position.latitude, position.longitude);
        update();
        print("Location permission granted! Current position: $curruntLatLng");
      }
      // If permission is denied, request it
      else if (status.isDenied) {
        PermissionStatus newStatus = await Permission.location.request();
        update();

        if (newStatus.isGranted) {
          final position = await Geolocator.getCurrentPosition();
          curruntLatLng = LatLng(position.latitude, position.longitude);
          update();
          print(
              "Location permission granted after request! Current position: $curruntLatLng");
        } else {
          // If permission is denied permanently or refused, ask the user to go to settings
          print("Location permission denied!");
          showErrorToast(
              "Location permission denied. Please enable it in settings.");
          openAppSettings();
        }
      }
      // If permission is permanently denied (user selected "Don't ask again"), prompt for settings
      else if (status.isPermanentlyDenied) {
        print("Location permission permanently denied!");
        showErrorToast(
            "Location permission permanently denied. Please enable it in settings.");
        openAppSettings();
      }
    } on LocationServiceDisabledException catch (ex) {
      // Handle location service disabled case
      '${ex.runtimeType} : $ex'.printLog(
        name: 'ERROR',
        color: ANSICOLOR.red,
        bgColor: ANSICOLOR.black,
      );
      showErrorToast("Location service is disabled.");
    } catch (e) {
      // Handle other exceptions
      '${e.runtimeType} : $e'.printLog(
        name: 'ERROR',
        color: ANSICOLOR.red,
        bgColor: ANSICOLOR.black,
      );
      showErrorToast("An unexpected error occurred: $e");
    }
  }

  void findPlace(String input) async {
    isLoading = true;
    currentPlace = null;
    emit(UpdateMapStates());

    final placeIdResponse = await repo.getPlaceId(input);
    placeIdResponse.fold(
      (l) => showErrorToast(l.message),
      (r) async {
        if (r.isEmpty) {
          showErrorToast('No Matiching Seach $r');
          return;
        }
        final placeResponse = await repo.getPlace(r.first);
        placeResponse.fold(
          (l) => showErrorToast(l.message),
          (r) {
            currentPlace = r;
            emit(UpdateMapStates());
          },
        );
      },
    );
    isLoading = false;
    emit(UpdateMapStates());
  }
}

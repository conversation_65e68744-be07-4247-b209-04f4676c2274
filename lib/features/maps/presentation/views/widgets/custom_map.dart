import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/maps/presentation/controllers/select_map_cubit.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';


class CustomMap extends StatefulWidget {
  CreateAdCubit createAdCubit;
  SelectMapCubit selectMapCubit;
  BuildContext context;

  CustomMap(
      {super.key,
      required this.createAdCubit,
      required this.selectMapCubit,
      required this.context});

  @override
  State<CustomMap> createState() => _CustomMapState();
}

class _CustomMapState extends State<CustomMap> {
  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      onTap: (latlng) async {
        await widget.selectMapCubit
            .addMarker(latlng, context, widget.createAdCubit, true);
      },
      onMapCreated: (mController) async {
        try {
          widget.selectMapCubit.setSelectedController(mController, context);
          widget.selectMapCubit.setMarker =
              widget.selectMapCubit.selectedLocations
                  .map(
                    (e) => Marker(
                      markerId: MarkerId('${e.latitude}${e.longitude}'),
                      position: LatLng(e.latitude!, e.longitude!),
                      icon: BitmapDescriptor.defaultMarkerWithHue(
                          BitmapDescriptor.hueAzure),
                    ),
                  )
                  .toSet();
          widget.selectMapCubit.update();
          // await widget.selectMapCubit.getCurrentUserLocation();
          // if (SelectMapCubit.get(context).curruntLatLng != null) {
          //   SelectMapCubit.get(context).moveToNewLocation(
          //       SelectMapCubit.get(context).curruntLatLng!, true);
          // }
        } catch (e) {
          e.printLog(name: "CustomMap , onMapCreated", color: ANSICOLOR.red);
        }
      },
      circles: widget.selectMapCubit.circles,
      mapType: MapType.normal,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      initialCameraPosition: CameraPosition(
        target: widget.selectMapCubit.curruntLatLng!,
        zoom: 11,
      ),
      markers: SelectMapCubit.get(context).setMarker,
    );
  }
}

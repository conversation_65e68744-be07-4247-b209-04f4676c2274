import 'package:ads_dv/features/create_campaigns/data/models/adset_location.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/maps/presentation/controllers/select_map_cubit.dart';
import 'package:ads_dv/features/maps/presentation/views/widgets/custom_map.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/utils/res/meta_constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../widgets/appbar.dart';
import '../../../../widgets/bouncing_widgets.dart';
import '../../../../widgets/button_widget.dart';
import '../../../../widgets/loading_widget.dart';
import '../../../../widgets/text_field_widget.dart';
import '../../../create_campaigns/presentation/controllers/get_reach_estimate/reach_estimate_cubit.dart';
import '../../../snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import '../../../snapChat_campaign/presentation/controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';

class SelectMapView extends StatefulWidget {
  CreateAdCubit createAdCubit;
  bool? isFromSnapChat;

  SelectMapView(
      {Key? key, required this.createAdCubit, this.isFromSnapChat = false})
      : super(key: key);

  @override
  State<SelectMapView> createState() => _SelectMapViewState();
}

class _SelectMapViewState extends State<SelectMapView> {
  double radius = 0.0;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SelectMapCubit()..onInit(),
      child: BlocBuilder<SelectMapCubit, SelectMapState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            resizeToAvoidBottomInset: false,
            appBar: CustomAppBar(
              title: "Location".tr,
              showBackButton: true,
              hasDrawer: true,
            ),
            body: Stack(
              children: [
                SelectMapCubit.get(context).curruntLatLng == null
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : CustomMap(
                        createAdCubit: widget.createAdCubit,
                        context: context,
                        selectMapCubit: SelectMapCubit.get(context),
                      ),
                Positioned(
                  top: 30.h,
                  right: 0.05.sw,
                  left: 0.05.sw,
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.sp),
                      child: Column(
                        children: [
                          Container(
                            //    width: 125.w,
                            height: 46.h,
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(37),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x33000000),
                                  blurRadius: 20,
                                  offset: Offset(0, 0),
                                  spreadRadius: -4,
                                )
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: CustomTextField(
                                    onChanged: (value) {
                                      SelectMapCubit.get(context)
                                          .debounce
                                          .run(() {
                                        if (value.trim().isNotEmpty) {
                                          value.printLog();
                                          SelectMapCubit.get(context)
                                              .findPlace(value);
                                        }
                                      });
                                    },
                                    borderColor: Colors.transparent,
                                    hintText: "Search for location...",
                                    // controller: textController,
                                    hintStyle: const TextStyle(fontSize: 14),
                                    icon: ShaderMask(
                                      shaderCallback: (Rect bounds) {
                                        return const LinearGradient(
                                          colors: [
                                            Color(0xFFFF006F),
                                            Color(0xFFF6BA00),
                                          ],
                                        ).createShader(bounds);
                                      },
                                      child: const Padding(
                                        padding: EdgeInsets.all(12.0),
                                        child: CustomSvgWidget(
                                            width: 13,
                                            height: 13,
                                            svg: AppAssets.search,
                                            color: Colors.white),
                                      ),
                                    ),
                                    // TextField properties
                                  ),
                                ),
                                Container(
                                    width: 81.w,
                                    padding: EdgeInsets.zero,
                                    decoration: const ShapeDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment(-0.99, -0.10),
                                        end: Alignment(0.99, 0.1),
                                        colors: [
                                          Color(0xFF0B0F26),
                                          Color(0xFF1C4294)
                                        ],
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(100),
                                          bottomRight: Radius.circular(100),
                                        ),
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 15.0, vertical: 15.0),
                                      child: CustomText(
                                          text: "Search".tr,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.white),
                                    )),
                              ],
                            ),
                          ),
                          // 50.verticalSpace,
                          // SizedBox(
                          //   height: 20.0,
                          //   width: 100.0,
                          //   child: Slider(
                          //     value: progress,
                          //     // Current value of the slider
                          //     min: 0,
                          //     // Minimum value (0)
                          //     max: 100,
                          //     // Maximum value (100)
                          //     divisions: 100,
                          //     // Optional: divisions between the values
                          //     label: progress.round().toString(),
                          //     onChanged: (double value) {
                          //       // setState(() {
                          //       progress = value; // Update progress value on drag
                          //       // });
                          //     },
                          //   ),
                          // ),
                          10.verticalSpace,
                          if (SelectMapCubit.get(context).isLocationDetected)
                            SelectMapCubit.get(context)
                                .handleRadiusCircle(context),
                          50.verticalSpace,
                          if (widget.createAdCubit.geoLocations.isNotEmpty &&
                              (widget.createAdCubit.isAddNewLocation == false))
                            Container(
                              height: 300.h,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 20,
                                    offset: Offset(0, 0),
                                    spreadRadius: -4,
                                  )
                                ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  children: [
                                    10.verticalSpace,
                                    CustomText(
                                      text: "Targeted Locations".tr,
                                      color: Constants.primaryTextColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    20.verticalSpace,
                                    Expanded(
                                      child: ListView.builder(
                                        itemBuilder: (item, index) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 8.0),
                                            child: Container(
                                              height: 60.h,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                  color: Constants
                                                      .primaryTextColor,
                                                  // Set the border color to blue
                                                  width: 1.0,
                                                ),
                                                borderRadius: BorderRadius.circular(
                                                    10.0), // Set the border radius to 16 pixels
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Row(
                                                          children: [
                                                            CustomText(
                                                              alignment:
                                                                  AlignmentDirectional
                                                                      .center,
                                                              text:
                                                                  "${"Location".tr}:",
                                                              color: Constants
                                                                  .primaryTextColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              fontSize: 14.sp,
                                                            ),
                                                            5.horizontalSpace,
                                                            CustomText(
                                                              alignment:
                                                                  AlignmentDirectional
                                                                      .center,
                                                              text:
                                                                  // widget
                                                                  //         .createAdCubit
                                                                  //         .geoLocations
                                                                  //         .first
                                                                  //         .customLocations![
                                                                  //             index]
                                                                  //         .addressString ??
                                                                  "myLocation"
                                                                      .tr,
                                                              fontSize: 12.sp,
                                                              color: Constants
                                                                  .primaryTextColor,
                                                            ),
                                                          ],
                                                        ),
                                                        Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            CustomText(
                                                              alignment:
                                                                  AlignmentDirectional
                                                                      .center,
                                                              text:
                                                                  "${"Distance".tr}:",
                                                              color: Constants
                                                                  .primaryTextColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              fontSize: 14.sp,
                                                            ),
                                                            5.horizontalSpace,
                                                            CustomText(
                                                              alignment:
                                                                  AlignmentDirectional
                                                                      .centerEnd,
                                                              text:
                                                                  "${widget.createAdCubit.geoLocations.first.customLocations![index].radius} ${widget.createAdCubit.geoLocations.first.customLocations![index].distanceUnit == DistanceUnit.kilometer ? "K" : "M"}",
                                                              fontSize: 12.sp,
                                                              color: Constants
                                                                  .primaryTextColor,
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                    InkWell(
                                                        onTap: () {
                                                          if (widget
                                                                  .isFromSnapChat ==
                                                              true) {
                                                            SnapChatAdSetCubit
                                                                    .get(
                                                                        context)
                                                                .removeLocation(
                                                                    SnapChatAdSetCubit.get(
                                                                            context)
                                                                        .geoLocations,
                                                                    index);
                                                            // SnapChatAdSetCubit
                                                            //         .get(
                                                            //             context)
                                                            //     .setAddNewLocationStatus();
                                                            if (SnapChatAdSetCubit
                                                                    .get(
                                                                        context)
                                                                .geoLocations
                                                                .isEmpty) {
                                                              widget
                                                                  .createAdCubit
                                                                  .setAddNewLocationStatus();
                                                            }
                                                          }
                                                          setState(() {
                                                            widget.createAdCubit
                                                                .removeLocation(
                                                                    widget
                                                                        .createAdCubit
                                                                        .geoLocations,
                                                                    index);
                                                            // SnapChatAdSetCubit.get(context)
                                                            //     .setAddNewLocationStatus();
                                                            if (CreateAdCubit
                                                                    .get(
                                                                        context)
                                                                .geoLocations
                                                                .isEmpty) {
                                                              widget
                                                                  .createAdCubit
                                                                  .setAddNewLocationStatus();
                                                            }
                                                          });
                                                        },
                                                        child: const Icon(
                                                          Icons.delete_outlined,
                                                          color: Constants
                                                              .primaryTextColor,
                                                        ))
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                        itemCount: widget
                                            .createAdCubit
                                            .geoLocations
                                            .first
                                            .customLocations!
                                            .length,
                                      ),
                                    ),
                                    ButtonWidget(
                                      text: "Add Location".tr,
                                      width: 100,
                                      highet: 35,
                                      onTap: () {
                                        if (widget.isFromSnapChat == true) {
                                          SnapChatAdSetCubit.get(context)
                                              .setAddNewLocationStatus();
                                          setState(() {
                                            widget.createAdCubit
                                                .setAddNewLocationStatus();
                                          });
                                        } else {
                                          setState(() {
                                            widget.createAdCubit
                                                .setAddNewLocationStatus();
                                          });
                                        }
                                        print(widget
                                            .createAdCubit.isAddNewLocation);
                                      },
                                    ),
                                    10.verticalSpace,
                                  ],
                                ),
                              ),
                            )
                          else
                            const SizedBox(),
                        ],
                      ),
                    ),
                  ),
                ),
                if (SelectMapCubit.get(context).currentPlace != null ||
                    SelectMapCubit.get(context).isLoading)
                  Positioned(
                      top: 100.h,
                      right: 0.05.sw,
                      left: 0.05.sw,
                      child: BounceIt(
                        onPressed: SelectMapCubit.get(context).isLoading
                            ? null
                            : () async {
                                SelectMapCubit.get(context).curruntLatLng =
                                    LatLng(
                                        SelectMapCubit.get(context)
                                            .currentPlace!
                                            .geometry!
                                            .location!
                                            .lat!,
                                        SelectMapCubit.get(context)
                                            .currentPlace!
                                            .geometry!
                                            .location!
                                            .lng!);
                                await SelectMapCubit.get(context)
                                    .moveMapToLocation(
                                        SelectMapCubit.get(context)
                                            .curruntLatLng!,
                                        context);
                                // await SelectMapCubit.get(context).saveMyLocation(SelectMapCubit.get(context).curruntLatLng!,context, widget.createAdCubit);
                                // SelectMapCubit.get(context).addMarker(
                                //     SelectMapCubit.get(context).curruntLatLng!,
                                //     context,
                                //     widget.createAdCubit,
                                //     true);

                                //TODO: Clear Searsh Resualt

                                SelectMapCubit.get(context).currentPlace = null;
                                SelectMapCubit.get(context).update();
                              },
                        child: SafeArea(
                          child: PhysicalModel(
                              color: Colors.white,
                              borderRadius: 8.bRadius,
                              clipBehavior: Clip.antiAlias,
                              elevation: 10,
                              shadowColor: Colors.black,
                              child: SelectMapCubit.get(context).isLoading
                                  ? Padding(
                                      padding: EdgeInsets.all(16.sp),
                                      child: const LoadingWidget(
                                        isCircle: true,
                                      ),
                                    )
                                  : Padding(
                                      padding: EdgeInsets.all(16.sp),
                                      child: CustomText(
                                        bgColor: Colors.white,
                                        textPadding: const EdgeInsets.all(16).r,
                                        text: SelectMapCubit.get(context)
                                                .currentPlace
                                                ?.formattedAddress ??
                                            '',
                                        alignment:
                                            AlignmentDirectional.centerStart,
                                        textAlign: TextAlign.start,
                                      ),
                                    )),
                        ),
                      )),

                //. Bottom Button

                Positioned(
                  bottom: 0.05.sh,
                  right: 0.05.sw,
                  left: 0.05.sw,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 80.sp),
                    child: SizedBox(
                      width: 195.w,
                      height: 56.h,
                      child: BlocBuilder<CreateAdCubit, CreateAdState>(
                        builder: (ctx, state) {
                          return ButtonWidget(
                            text: 'Save'.tr,
                            padding: 18.sp,
                            onTap: () async {
                              print(
                                  'saveselectedLocationzxcsd ${SelectMapCubit.get(ctx).selectedLocations}');
                              SelectMapCubit.get(ctx).saveLocationData(ctx);
                              Get.back(
                                  result: SelectMapCubit.get(ctx)
                                      .selectedLocations);
                              if (widget.isFromSnapChat == true) {
                                // SnapChatAdSetCubit.get(context)
                                //     .setSelectedLocation([
                                //   AdSetGeoLocations(
                                //       customLocations: SelectMapCubit.get(ctx)
                                //           .selectedLocations)
                                // ]);
                                CreateAdCubit.get(ctx).setSelectedLocation([
                                  AdSetGeoLocations(
                                      customLocations: SelectMapCubit.get(ctx)
                                          .selectedLocations)
                                ]);
                                CreateSnapChatAdCubit.get(context)
                                        .snapChatAdModel =
                                    CreateSnapChatAdCubit.get(context)
                                        .snapChatAdModel
                                        .copyWith(
                                            geoLocations: CreateAdCubit.get(ctx)
                                                .geoLocations);
                              } else {
                                CreateAdCubit.get(ctx).setSelectedLocation([
                                  AdSetGeoLocations(
                                      customLocations: SelectMapCubit.get(ctx)
                                          .selectedLocations)
                                ]);
                                CreateAdCubit.get(ctx).adModel =
                                    CreateAdCubit.get(ctx).adModel.copyWith(
                                          geoLocations: CreateAdCubit.get(ctx)
                                              .geoLocations,
                                          publisherPlatforms:
                                              CreateAdCubit.get(ctx)
                                                  .publisherPlatforms,
                                          facebookPositions:
                                              CreateAdCubit.get(ctx)
                                                  .fbPositions,
                                          instagramPositions:
                                              CreateAdCubit.get(ctx)
                                                  .igPositions,
                                          messengerPositions:
                                              CreateAdCubit.get(ctx).mPositions,
                                        );
                                // print('dsfdcxv ${CreateAdCubit.get(ctx)
                                //     .adModel.geoLocations}');
                                // users_lower_bound: 137200, users_upper_bound: 161400,
                                if (widget.isFromSnapChat == false && CreateAdCubit.get(ctx)
                                    .geoLocations
                                    .isNotEmpty) {
                                  CreateSnapChatAdCubit.get(context)
                                      .adSetPercentage = 0.17;
                                  print('dsfdcxv ${CreateAdCubit.get(ctx)
                                      .adModel.geoLocations}');
                                  CreateAdCubit.get(ctx).adModel =
                                      CreateAdCubit.get(ctx).adModel.copyWith(
                                        geoLocations: CreateAdCubit.get(ctx)
                                            .geoLocations,
                                        publisherPlatforms:
                                        CreateAdCubit.get(ctx)
                                            .publisherPlatforms,
                                        facebookPositions:
                                        CreateAdCubit.get(ctx)
                                            .fbPositions,
                                        instagramPositions:
                                        CreateAdCubit.get(ctx)
                                            .igPositions,
                                        messengerPositions:
                                        CreateAdCubit.get(ctx).mPositions,
                                      );
                                  await ReachEstimateCubit.get(context)
                                      .getReachEstimate(
                                      context: context,
                                      imagesFiles:
                                      CreateAdCubit.get(context)
                                          .adImages,
                                      videosFiles:
                                      CreateAdCubit.get(context).adVideo
                                    // CreateAdCubit.get(context)
                                    //     .videoImage
                                  );
                                  // CreateAdCubit.get(ctx).setSelectedLocation([
                                  //   AdSetGeoLocations(
                                  //       customLocations: SelectMapCubit.get(ctx)
                                  //           .selectedLocations)
                                  // ]);
                                  // CreateSnapChatAdCubit.get(context)
                                  //     .snapChatAdModel =
                                  //     CreateSnapChatAdCubit.get(context)
                                  //         .snapChatAdModel
                                  //         .copyWith(
                                  //         geoLocations: CreateAdCubit.get(ctx)
                                  //             .geoLocations);
                                  // print('dsfdcxv ${CreateSnapChatAdCubit.get(context)
                                  //     .snapChatAdModel.geoLocations}');
                                  // CreateAdCubit.get(ctx)
                                  //     .geoLocations
                                  //     .forEach((element) {
                                  //   print(
                                  //       "cam locations${element.customLocations}");
                                  // });
                                  // final cubit = CreateAdCubit.get(context);
                                  // cubit.adModel
                                  // CreateAdCubit.get(context).adModel.copyWith(
                                  //       publisherPlatforms:
                                  //           CreateAdCubit.get(context)
                                  //               .publisherPlatforms,
                                  //       facebookPositions:
                                  //           CreateAdCubit.get(context)
                                  //               .fbPositions,
                                  //       instagramPositions:
                                  //           CreateAdCubit.get(context)
                                  //               .igPositions,
                                  //       messengerPositions:
                                  //           CreateAdCubit.get(context).mPositions,
                                  //     );
                                  // CreateAdCubit.get(context)
                                  //     .adModel
                                  //     .toJson()
                                  //     .then((value) => print(
                                  //         "geoLocations is empty ${value}"));
                                  // await ReachEstimateCubit.get(context)
                                  //     .getReachEstimate(
                                  //         context: context,
                                  //         imagesFiles:
                                  //             CreateAdCubit.get(context)
                                  //                 .adImages,
                                  //         videosFiles:
                                  //             CreateAdCubit.get(context).adVideo
                                  //         // CreateAdCubit.get(context)
                                  //         //     .videoImage
                                  //         );
                                } else {
                                  print("geoLocations is empty");
                                }
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

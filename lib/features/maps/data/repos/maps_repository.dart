import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/res/constants.dart';
import '../models/place.dart';

abstract class MapsRepo {
  Future<EitherFunction<List<String>>> getPlaceId(String input);

  Future<EitherFunction<Place>> getPlace(String id);
}

class MapsRepoImpl implements MapsRepo {
  MapsRepoImpl._();

  // Factory method to create an instance of MapsRepoImpl
  factory MapsRepoImpl() {
    return MapsRepoImpl._();
  }

  static const _domain = 'https://maps.googleapis.com/maps/api/place/';
  static const _key = 'AIzaSyCUUi4VQxwgkyQIT4DhGIoGIpfyAyA71Xg';

  static final Dio _dio = Dio(BaseOptions(baseUrl: _domain));

  @override
  Future<EitherFunction<Place>> getPlace(String id) async {
    final response = await _dio.get(
      'details/json',
      queryParameters: {
        "place_id": id,
        "key": _key,
      },
    );

    if (response.data == null || response.data['result'] == null) {
      return const Left(ServerFailure());
    }
    final place = Place.fromJson(response.data['result']);
    return Right(place);
  }

  @override
  Future<EitherFunction<List<String>>> getPlaceId(String input) async {
    final response = await _dio.get(
      'findplacefromtext/json',
      queryParameters: {
        "inputtype": "textquery",
        "input": input,
        "key": _key,
      },
    );

    print('getPlaceData $response');

    if (response.data == null) return const Left(ServerFailure());
    final candidates = response.data['candidates'] as List;
    final placeIds = candidates.map<String>((e) => e["place_id"]).toList();
    return Right(placeIds);
  }
}

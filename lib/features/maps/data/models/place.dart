class PlacePhotos {
  int? height;
  List<String?>? htmlAttributions;
  String? photoReference;
  int? width;
  PlacePhotos({
    this.height,
    this.htmlAttributions,
    this.photoReference,
    this.width,
  });
  PlacePhotos.fromJson(Map<String, dynamic> json) {
    height = int.tryParse(json['height']?.toString() ?? '');
    if (json['html_attributions'] != null &&
        (json['html_attributions'] is List)) {
      final v = json['html_attributions'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      htmlAttributions = arr0;
    }
    photoReference = json['photo_reference']?.toString();
    width = int.tryParse(json['width']?.toString() ?? '');
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['height'] = height;
    if (htmlAttributions != null) {
      final v = htmlAttributions;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['html_attributions'] = arr0;
    }
    data['photo_reference'] = photoReference;
    data['width'] = width;
    return data;
  }
}

class PlaceGeometryViewportSouthwest {
  double? lat;
  double? lng;
  PlaceGeometryViewportSouthwest({
    this.lat,
    this.lng,
  });
  PlaceGeometryViewportSouthwest.fromJson(Map<String, dynamic> json) {
    lat = double.tryParse(json['lat']?.toString() ?? '');
    lng = double.tryParse(json['lng']?.toString() ?? '');
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['lat'] = lat;
    data['lng'] = lng;
    return data;
  }
}

class PlaceGeometryViewportNortheast {
  double? lat;
  double? lng;
  PlaceGeometryViewportNortheast({
    this.lat,
    this.lng,
  });
  PlaceGeometryViewportNortheast.fromJson(Map<String, dynamic> json) {
    lat = double.tryParse(json['lat']?.toString() ?? '');
    lng = double.tryParse(json['lng']?.toString() ?? '');
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['lat'] = lat;
    data['lng'] = lng;
    return data;
  }
}

class PlaceGeometryViewport {
  PlaceGeometryViewportNortheast? northeast;
  PlaceGeometryViewportSouthwest? southwest;
  PlaceGeometryViewport({
    this.northeast,
    this.southwest,
  });
  PlaceGeometryViewport.fromJson(Map<String, dynamic> json) {
    northeast = (json['northeast'] != null && (json['northeast'] is Map))
        ? PlaceGeometryViewportNortheast.fromJson(json['northeast'])
        : null;
    southwest = (json['southwest'] != null && (json['southwest'] is Map))
        ? PlaceGeometryViewportSouthwest.fromJson(json['southwest'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (northeast != null) {
      data['northeast'] = northeast!.toJson();
    }
    if (southwest != null) {
      data['southwest'] = southwest!.toJson();
    }
    return data;
  }
}

class PlaceGeometryLocation {
  double? lat;
  double? lng;
  PlaceGeometryLocation({
    this.lat,
    this.lng,
  });
  PlaceGeometryLocation.fromJson(Map<String, dynamic> json) {
    lat = double.tryParse(json['lat']?.toString() ?? '');
    lng = double.tryParse(json['lng']?.toString() ?? '');
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['lat'] = lat;
    data['lng'] = lng;
    return data;
  }
}

class PlaceGeometry {
  PlaceGeometryLocation? location;
  PlaceGeometryViewport? viewport;
  PlaceGeometry({
    this.location,
    this.viewport,
  });
  PlaceGeometry.fromJson(Map<String, dynamic> json) {
    location = (json['location'] != null && (json['location'] is Map))
        ? PlaceGeometryLocation.fromJson(json['location'])
        : null;
    viewport = (json['viewport'] != null && (json['viewport'] is Map))
        ? PlaceGeometryViewport.fromJson(json['viewport'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (location != null) {
      data['location'] = location!.toJson();
    }
    if (viewport != null) {
      data['viewport'] = viewport!.toJson();
    }
    return data;
  }
}

class PlaceAddressComponents {
  String? longName;
  String? shortName;
  List<String?>? types;
  PlaceAddressComponents({
    this.longName,
    this.shortName,
    this.types,
  });
  PlaceAddressComponents.fromJson(Map<String, dynamic> json) {
    longName = json['long_name']?.toString();
    shortName = json['short_name']?.toString();
    if (json['types'] != null && (json['types'] is List)) {
      final v = json['types'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      types = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['long_name'] = longName;
    data['short_name'] = shortName;
    if (types != null) {
      final v = types;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['types'] = arr0;
    }
    return data;
  }
}

class Place {
  List<PlaceAddressComponents?>? addressComponents;
  String? adrAddress;
  String? formattedAddress;
  PlaceGeometry? geometry;
  String? icon;
  String? iconBackgroundColor;
  String? iconMaskBaseUri;
  String? name;
  List<PlacePhotos?>? photos;
  String? placeId;
  String? reference;
  List<String?>? types;
  String? url;
  int? utcOffset;
  String? vicinity;
  String? website;
  Place({
    this.addressComponents,
    this.adrAddress,
    this.formattedAddress,
    this.geometry,
    this.icon,
    this.iconBackgroundColor,
    this.iconMaskBaseUri,
    this.name,
    this.photos,
    this.placeId,
    this.reference,
    this.types,
    this.url,
    this.utcOffset,
    this.vicinity,
    this.website,
  });
  Place.fromJson(Map<String, dynamic> json) {
    if (json['address_components'] != null &&
        (json['address_components'] is List)) {
      final v = json['address_components'];
      final arr0 = <PlaceAddressComponents>[];
      v.forEach((v) {
        arr0.add(PlaceAddressComponents.fromJson(v));
      });
      addressComponents = arr0;
    }
    adrAddress = json['adr_address']?.toString();
    formattedAddress = json['formatted_address']?.toString();
    geometry = (json['geometry'] != null && (json['geometry'] is Map))
        ? PlaceGeometry.fromJson(json['geometry'])
        : null;
    icon = json['icon']?.toString();
    iconBackgroundColor = json['icon_background_color']?.toString();
    iconMaskBaseUri = json['icon_mask_base_uri']?.toString();
    name = json['name']?.toString();
    if (json['photos'] != null && (json['photos'] is List)) {
      final v = json['photos'];
      final arr0 = <PlacePhotos>[];
      v.forEach((v) {
        arr0.add(PlacePhotos.fromJson(v));
      });
      photos = arr0;
    }
    placeId = json['place_id']?.toString();
    reference = json['reference']?.toString();
    if (json['types'] != null && (json['types'] is List)) {
      final v = json['types'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      types = arr0;
    }
    url = json['url']?.toString();
    utcOffset = int.tryParse(json['utc_offset']?.toString() ?? '');
    vicinity = json['vicinity']?.toString();
    website = json['website']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (addressComponents != null) {
      final v = addressComponents;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['address_components'] = arr0;
    }
    data['adr_address'] = adrAddress;
    data['formatted_address'] = formattedAddress;
    if (geometry != null) {
      data['geometry'] = geometry!.toJson();
    }
    data['icon'] = icon;
    data['icon_background_color'] = iconBackgroundColor;
    data['icon_mask_base_uri'] = iconMaskBaseUri;
    data['name'] = name;
    if (photos != null) {
      final v = photos;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['photos'] = arr0;
    }
    data['place_id'] = placeId;
    data['reference'] = reference;
    if (types != null) {
      final v = types;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['types'] = arr0;
    }
    data['url'] = url;
    data['utc_offset'] = utcOffset;
    data['vicinity'] = vicinity;
    data['website'] = website;
    return data;
  }
}

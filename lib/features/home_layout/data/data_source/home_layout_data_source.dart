
import 'package:ads_dv/features/home_layout/data/models/has_campaign_response.dart';
import 'package:dio/dio.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/network/dio/enum.dart';
import '../../../../utils/network/dio/network_call.dart';
import '../../../../utils/network/urls/end_points.dart';

class HomeLayoutDataSource{


  Future<HasCampaign> hasCampaign() async {
    try {
      var response = await instance<NetworkCall>().request(
        EndPoints.hasCampaign,

        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return HasCampaign.fromJson(response['data']);
    } catch (error) {
      rethrow;
    }
  }
}
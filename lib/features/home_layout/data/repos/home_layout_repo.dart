import 'package:dartz/dartz.dart';

import '../../../../utils/network/connection/network_info.dart';
import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/network/failure_helper.dart';
import '../data_source/home_layout_data_source.dart';
import '../models/has_campaign_response.dart';

class HomeLayoutRepo {
  NetworkInfo networkInfo;
  HomeLayoutDataSource homeLayoutDataSource;

  HomeLayoutRepo(
      {required this.networkInfo, required this.homeLayoutDataSource});


  Future<Either<Failure,HasCampaign>> hasCampaign() {
    return FailureHelper.instance(method: () async{
      return await homeLayoutDataSource.hasCampaign();
    },networkInfo: networkInfo,);
  }
}
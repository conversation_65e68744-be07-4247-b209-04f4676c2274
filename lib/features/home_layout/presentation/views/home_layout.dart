import 'package:ads_dv/features/home_layout/presentation/controllers/home_layout_cubit.dart';

import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/drawer.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/location_helper/location_helper.dart';
import '../../../../utils/network/urls/services_urls.dart';
import '../../../../utils/res/router/routes.dart';
import '../../../../widgets/custom_dialogs.dart';
import '../../../../widgets/handle_error_widget.dart';
import '../../../sidebar/ad_accounts/presentation/controllers/get_add_accounts/get_add_accounts_cubit.dart';

class HomeLayoutScreen extends StatefulWidget {
  const HomeLayoutScreen({super.key});

  @override
  State<HomeLayoutScreen> createState() => _HomeLayoutState();
}

class _HomeLayoutState extends State<HomeLayoutScreen> {
  // final user = instance<HiveHelper>().getUser();

  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      // String? mylatLangs =
      await LocationHelper.getAddressFromCurrentLocation(context);
      if(instance<HiveHelper>().getUser()?.accessToken != null || instance<HiveHelper>().getUser()?.accessToken != ""){
        await GetAdAccountsCubit.get(context).checkAccessToken(context: context, accessToken: instance<HiveHelper>().getUser()!.accessToken!);
      }

      print('homeLayoutewretgf ${instance<HiveHelper>().getUser()!.accessToken}');
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => CustomDialogs.exitConfirmation(context),
      child: BlocProvider(
        create: (context) => HomeLayoutCubit()..hasCampaign(context: context),
        child: BlocBuilder<HomeLayoutCubit, HomeLayoutState>(
          builder: (homeContext, state) {
            var cubit = HomeLayoutCubit.get(homeContext);
            return SafeArea(
              bottom: true,
              child: Scaffold(
                key: Constants.scaffoldKey,
                drawer: HomeDrawer(
                  mainContext: Constants.navigatorKey.currentContext ?? context,
                ),
                body: state is HasCampaignsStateLoading
                    ? const LoadingWidget(
                        isCircle: true,
                      )
                    : state is HasCampaignsStateError
                        ? HandleErrorWidget(
                            fun: () {
                              HomeLayoutCubit.get(homeContext).hasCampaign(
                                context:
                                    Constants.navigatorKey.currentContext ??
                                        context,
                              );
                            },
                            failure: state.message)
                        : LayoutBuilder(builder: (ctx, constraints) {
                            return Stack(
                              clipBehavior: Clip.none,
                              children: [
                                Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    cubit.screens[cubit.screenIndex],
                                    Positioned(
                                      bottom: constraints.maxHeight -
                                          MediaQuery.of(context).size.height +
                                          MediaQuery.of(context)
                                              .viewInsets
                                              .bottom +
                                          40,
                                      right: -1.h,
                                      left: -1.h,
                                      child: Stack(
                                        children: [
                                          SizedBox(
                                            height: 126.h,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            child: const CachedImageWidget(
                                              fit: BoxFit.contain,
                                              assetsImage: AppAssets.nav,
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 40.h,
                                            right: 35.h,
                                            //left: 30.h,
                                            child: Row(
                                              children: [
                                                Row(
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        cubit.changeScreenIndex(
                                                            2);
                                                      },
                                                      child: Column(
                                                        children: [
                                                          CustomSvgWidget(
                                                            svg: cubit.screenIndex ==
                                                                    2
                                                                ? AppAssets
                                                                    .selectedWallet
                                                                : AppAssets
                                                                    .wallet,
                                                            height: 20.h,
                                                            width: 20.h,
                                                          ),
                                                          SizedBox(
                                                            height: 4.h,
                                                          ),
                                                          cubit.screenIndex == 2
                                                              ? ShaderMask(
                                                                  shaderCallback:
                                                                      (Rect
                                                                          bounds) {
                                                                    return Constants
                                                                        .secGradient
                                                                        .createShader(
                                                                            bounds);
                                                                  },
                                                                  child:
                                                                      CustomText(
                                                                    text:
                                                                        "Wallet"
                                                                            .tr,
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        11.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                  ),
                                                                )
                                                              : CustomText(
                                                                  text: "Wallet"
                                                                      .tr,
                                                                  color: Constants
                                                                      .primaryTextColor,
                                                                  fontSize:
                                                                      10.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                )
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 25.h,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        cubit.changeScreenIndex(
                                                            3);
                                                      },
                                                      child: Column(
                                                        children: [
                                                          (instance<HiveHelper>()
                                                                          .getUser()!
                                                                          .photo ==
                                                                      null ||
                                                                  instance<HiveHelper>()
                                                                          .getUser()!
                                                                          .photo ==
                                                                      "")
                                                              ? ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              40),
                                                                  child:
                                                                      Container(
                                                                    height:
                                                                        20.h,
                                                                    width: 20.h,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              40),
                                                                    ),
                                                                    child:
                                                                        CachedImageWidget(
                                                                      assetsImage:
                                                                          AppAssets
                                                                              .dummyProfile,
                                                                      fit: BoxFit
                                                                          .fill,
                                                                      height:
                                                                          20.h,
                                                                      width:
                                                                          20.h,
                                                                      //   width: 20.h,
                                                                    ),
                                                                  ),
                                                                )
                                                              : ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              40),
                                                                  child:
                                                                      Container(
                                                                    height:
                                                                        20.h,
                                                                    width: 20.h,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              40),
                                                                    ),
                                                                    child:
                                                                        CachedImageWidget(
                                                                      image: ServicesURLs
                                                                              .assetsUrl +
                                                                          instance<HiveHelper>()
                                                                              .getUser()!
                                                                              .photo
                                                                              .toString(),
                                                                      fit: BoxFit
                                                                          .fill,
                                                                      height:
                                                                          20.h,
                                                                      //   width: 20.h,
                                                                    ),
                                                                  ),
                                                                ),
                                                          SizedBox(
                                                            height: 4.h,
                                                          ),
                                                          cubit.screenIndex == 3
                                                              ? ShaderMask(
                                                                  shaderCallback:
                                                                      (Rect
                                                                          bounds) {
                                                                    return Constants
                                                                        .secGradient
                                                                        .createShader(
                                                                            bounds);
                                                                  },
                                                                  child:
                                                                      CustomText(
                                                                    text:
                                                                        "Profile"
                                                                            .tr,
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        11.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                  ),
                                                                )
                                                              : CustomText(
                                                                  text:
                                                                      "Profile"
                                                                          .tr,
                                                                  color: Constants
                                                                      .primaryTextColor,
                                                                  fontSize:
                                                                      10.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 40.h,
                                            left: 35.h,
                                            child: Row(
                                              children: [
                                                Row(
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        cubit.changeScreenIndex(
                                                            0);
                                                      },
                                                      child: Column(
                                                        children: [
                                                          CustomSvgWidget(
                                                            svg: cubit.screenIndex ==
                                                                    0
                                                                ? AppAssets
                                                                    .selectedHome
                                                                : AppAssets
                                                                    .home,
                                                            height: 20.h,
                                                            width: 20.h,
                                                          ),
                                                          SizedBox(
                                                            height: 4.h,
                                                          ),
                                                          cubit.screenIndex == 0
                                                              ? ShaderMask(
                                                                  shaderCallback:
                                                                      (Rect
                                                                          bounds) {
                                                                    return Constants
                                                                        .secGradient
                                                                        .createShader(
                                                                            bounds);
                                                                  },
                                                                  child:
                                                                      CustomText(
                                                                    text: "Home"
                                                                        .tr,
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        11.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                  ),
                                                                )
                                                              : CustomText(
                                                                  text:
                                                                      "Home".tr,
                                                                  color: Constants
                                                                      .primaryTextColor,
                                                                  fontSize:
                                                                      10.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                )
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 25.h,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        if (instance<HiveHelper>()
                                                                    .getUser()
                                                                    ?.accessToken ==
                                                                null ||
                                                            instance<HiveHelper>()
                                                                    .getUser()
                                                                    ?.accessToken ==
                                                                '') {
                                                          showErrorToast(
                                                              'Please Connect with Facebook');
                                                        } else if (instance<
                                                                        HiveHelper>()
                                                                    .getUser()
                                                                    ?.defaultAccountId ==
                                                                null ||
                                                            instance<HiveHelper>()
                                                                    .getUser()
                                                                    ?.defaultAccountId ==
                                                                '') {
                                                          showErrorToast(
                                                              'Please Choose Default AD Account');
                                                        } else {
                                                          cubit
                                                              .changeScreenIndex(
                                                                  1);
                                                        }
                                                      },
                                                      child: Column(
                                                        children: [
                                                          CustomSvgWidget(
                                                            svg: cubit.screenIndex ==
                                                                    1
                                                                ? AppAssets
                                                                    .selectedReports
                                                                : AppAssets
                                                                    .reports,
                                                            height: 20.h,
                                                            width: 20.h,
                                                          ),
                                                          SizedBox(
                                                            height: 4.h,
                                                          ),
                                                          cubit.screenIndex == 1
                                                              ? ShaderMask(
                                                                  shaderCallback:
                                                                      (Rect
                                                                          bounds) {
                                                                    return Constants
                                                                        .secGradient
                                                                        .createShader(
                                                                            bounds);
                                                                  },
                                                                  child:
                                                                      CustomText(
                                                                    text:
                                                                        "Reports"
                                                                            .tr,
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        11.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                  ),
                                                                )
                                                              : CustomText(
                                                                  text:
                                                                      "Reports"
                                                                          .tr,
                                                                  color: Constants
                                                                      .primaryTextColor,
                                                                  fontSize:
                                                                      10.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                // Positioned(
                                //   bottom: 30.0,
                                //   left: 0,
                                //   right: 0,
                                //   child: Center(
                                //     child: ExpandableFab(
                                //       distance: 90.0,
                                //       type: ExpandableFabType.up,
                                //       // backgroundColor: Colors.white,
                                //       // closeButtonHeroTag: 'close-fab',
                                //       // openButtonHeroTag: 'open-fab',
                                //       // child: Icon(Icons.add,
                                //       //     size: 35, color: Colors.blue),
                                //       children: [
                                //         FloatingActionButton(
                                //           heroTag: 'meta-fab',
                                //           backgroundColor: Colors.white,
                                //           onPressed: () {
                                //             // Meta button action
                                //             if (instance<HiveHelper>()
                                //                         .getUser()
                                //                         ?.defaultAccountId ==
                                //                     null &&
                                //                 instance<HiveHelper>()
                                //                         .getUser()
                                //                         ?.defaultPageAccessToken ==
                                //                     null &&
                                //                 instance<HiveHelper>()
                                //                         .getUser()
                                //                         ?.defaultPageId ==
                                //                     null) {
                                //               Navigator.pushNamed(
                                //                   context, Routes.accounts);
                                //             } else if (instance<HiveHelper>()
                                //                         .getUser()
                                //                         ?.accessToken ==
                                //                     null ||
                                //                 instance<HiveHelper>()
                                //                         .getUser()
                                //                         ?.accessToken ==
                                //                     "") {
                                //               Navigator.pushNamed(
                                //                       context, Routes.accounts)
                                //                   .then((value) {
                                //                 HomeLayoutCubit.get(context)
                                //                     .hasCampaign(
                                //                         context: homeContext);
                                //               });
                                //               showErrorToast(
                                //                   "Please connect with your Facebook account");
                                //             } else {
                                //               Navigator.pushNamed(context,
                                //                   Routes.createCampaign);
                                //             }
                                //           },
                                //           child: Image.asset(
                                //             'assets/images/meta.png',
                                //             height: 30,
                                //           ),
                                //         ),
                                //         FloatingActionButton(
                                //           heroTag: 'tiktok-fab',
                                //           backgroundColor: Colors.white,
                                //           onPressed: () {
                                //             Navigator.pushNamed(
                                //                 context, Routes.tiktokCampaign);
                                //             // TikTok button action
                                //             print('TikTok Button Pressed');
                                //           },
                                //           child: const Icon(Icons.tiktok,
                                //               color: Colors.black),
                                //         ),
                                //         FloatingActionButton(
                                //           heroTag: 'close-fab',
                                //           backgroundColor: Colors.white,
                                //           onPressed: () {
                                //             // Close button action
                                //             print('Close Button Pressed');
                                //           },
                                //           child: const Icon(Icons.close,
                                //               color: Colors.blueGrey),
                                //         ),
                                //       ],
                                //     ),
                                //   ),
                                // ),
                              ],
                            );
                          }),
                floatingActionButton: Padding(
                  padding: const EdgeInsets.only(bottom: 35.0),
                  child: ExpandableFab(
                    distance: 90.0,
                    type: ExpandableFabType.fan,
                    pos: ExpandableFabPos.center,
                    closeButtonBuilder: FloatingActionButtonBuilder(
                      size: 56.0,
                      builder: (context, onPressed, animation) =>
                          AnimatedBuilder(
                        animation: animation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: 1.0 + (0.2 * animation.value),
                            // Slight pop effect
                            child: ClipOval(
                              child: Material(
                                color: Colors.white,
                                // Button background color
                                child: InkWell(
                                  onTap: onPressed,
                                  child: SizedBox(
                                    width: 40.0.w,
                                    height: 40.0.h,
                                    child: Center(
                                      child: Transform.rotate(
                                        angle: animation.value * 0.5 * 3.14,
                                        // Rotate icon
                                        child: const Icon(
                                          Icons.close,
                                          color: Constants.darkColor,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    openButtonBuilder: FloatingActionButtonBuilder(
                      size: 56.0,
                      builder: (context, onPressed, animation) => Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                            side: const BorderSide(
                                width: 2.0, color: Constants.darkColor)),
                        child: AnimatedBuilder(
                          animation: animation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 1.0 + (0.2 * animation.value),
                              // Slight pop effect
                              child: ClipOval(
                                child: Material(
                                  color: Colors.white,
                                  // Button background color
                                  child: InkWell(
                                    onTap: onPressed,
                                    child: SizedBox(
                                      width: 56.0.w,
                                      height: 56.0.h,
                                      child: Center(
                                        child: Transform.rotate(
                                          angle: animation.value * 0.5 * 3.14,
                                          // Rotate icon
                                          child: const Icon(
                                            Icons.add,
                                            color: Constants.darkColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    children: [
                      FloatingActionButton(
                        // heroTag: 'meta-fab',
                        backgroundColor: Colors.white,
                        onPressed: () {
                          if (instance<HiveHelper>()
                                      .getUser()
                                      ?.defaultAccountId ==
                                  null &&
                              instance<HiveHelper>()
                                      .getUser()
                                      ?.defaultPageAccessToken ==
                                  null &&
                              instance<HiveHelper>().getUser()?.defaultPageId ==
                                  null) {
                            Navigator.pushNamed(context, Routes.accounts);
                          } else if (instance<HiveHelper>()
                                      .getUser()
                                      ?.accessToken ==
                                  null ||
                              instance<HiveHelper>().getUser()?.accessToken ==
                                  "") {
                            Navigator.pushNamed(context, Routes.accounts)
                                .then((value) {
                              HomeLayoutCubit.get(context)
                                  .hasCampaign(context: homeContext);
                            });
                            showErrorToast(
                                "Please connect with your Facebook account");
                          } else {
                            Navigator.pushNamed(context, Routes.createCampaign);
                          }
                        },
                        child: Image.asset(
                          'assets/images/meta.png',
                          height: 30,
                        ),
                      ),
                      // SizedBox(
                      //   width: 20.0.w,
                      // ),
                      FloatingActionButton(
                        // heroTag: 'tiktok-fab',
                        backgroundColor: Colors.white,
                        onPressed: () {
                          if (instance.get<HiveHelper>().getTiktokPageName() ==
                                  null &&
                              instance.get<HiveHelper>().getAdvertiserId() ==
                                  null) {
                            Navigator.pushNamed(context, Routes.accounts);
                          } else if (instance
                                      .get<HiveHelper>()
                                      .getUser()
                                      ?.tiktokToken ==
                                  null ||
                              instance
                                      .get<HiveHelper>()
                                      .getUser()
                                      ?.tiktokToken ==
                                  "") {
                            Navigator.pushNamed(context, Routes.accounts)
                                .then((value) {
                              HomeLayoutCubit.get(context)
                                  .hasCampaign(context: homeContext);
                            });
                            showErrorToast(
                                "Please connect with your Tiktok Business account ${instance<HiveHelper>().getUser()?.tiktokToken}");
                            print(
                                'Please connect with your Tiktok Business account ${instance<HiveHelper>().getUser()?.tiktokToken}');
                          } else {
                            Navigator.pushNamed(context, Routes.tiktokCampaign);
                          }
                          // Navigator.pushNamed(context, Routes.tiktokCampaign);
                        },
                        child: Image.asset(
                          'assets/images/tiktok.png',
                          fit: BoxFit.cover,
                          height: 30,
                        ),
                      ),
                      // SizedBox(
                      //   width: 20.0.w,
                      // ),
                      FloatingActionButton(
                        // heroTag: 'snapChat-fab',
                        backgroundColor: Colors.white,
                        onPressed: () {
                          if (instance.get<HiveHelper>().getSnapAdAccountId() ==
                              null) {
                            Navigator.pushNamed(context, Routes.accounts);
                          } else if (instance<HiveHelper>()
                                      .getUser()
                                      ?.snapChatToken ==
                                  null ||
                              instance<HiveHelper>().getUser()?.snapChatToken ==
                                  "") {
                            Navigator.pushNamed(context, Routes.accounts)
                                .then((value) {
                              HomeLayoutCubit.get(context)
                                  .hasCampaign(context: homeContext);
                            });
                            showErrorToast(
                                "Please connect with your SnapChat Business account ${instance<HiveHelper>().getUser()?.snapChatToken}");
                            print(
                                'Please connect with your Tiktok Business account ${instance<HiveHelper>().getUser()?.snapChatToken}');
                          } else {
                            Navigator.pushNamed(
                                context, Routes.snapChatCampaign);
                          }
                          // if (instance.get<HiveHelper>().getTiktokPageName() ==
                          //     null &&
                          //     instance.get<HiveHelper>().getAdvertiserId() ==
                          //         null) {
                          //   Navigator.pushNamed(context, Routes.accounts);
                          // } else if (user?.tiktokToken == null ||
                          //     user?.tiktokToken == "") {
                          //   Navigator.pushNamed(context, Routes.accounts)
                          //       .then((value) {
                          //     HomeLayoutCubit.get(context)
                          //         .hasCampaign(context: homeContext);
                          //   });
                          //   showErrorToast(
                          //       "Please connect with your Tiktok Business account ${user?.tiktokToken}");
                          //   print(
                          //       'Please connect with your Tiktok Business account ${user?.tiktokToken}');
                          // } else {
                          //   Navigator.pushNamed(context, Routes.tiktokCampaign);
                          // }
                          // Navigator.pushNamed(context, Routes.snapChatCampaign);
                        },
                        child: Image.asset(
                          'assets/images/snap.png',
                          fit: BoxFit.cover,
                          height: 30,
                        ),
                      ),
                      const SizedBox(),
                      // FloatingActionButton(
                      //   heroTag: 'close-fab',
                      //   backgroundColor: Colors.white,
                      //   onPressed: () {
                      //     ExpandableFab.of(context).toggle();
                      //   },
                      //   child: const Icon(Icons.close, color: Colors.blueGrey),
                      // ),
                    ],
                  ),
                ),
                floatingActionButtonLocation: ExpandableFab.location,
                extendBody: true,
              ),
            );
          },
        ),
      ),
    );
  }
}

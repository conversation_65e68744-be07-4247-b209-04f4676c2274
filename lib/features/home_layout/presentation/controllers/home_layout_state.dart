part of 'home_layout_cubit.dart';

@immutable
abstract class HomeLayoutState {}

final class HomeLayoutInitial extends HomeLayoutState {}

final class ChangeIndexState extends HomeLayoutState {}


class HasCampaignsInitial extends HomeLayoutState {}

class HasCampaignsStateLoading extends HomeLayoutState {}

class HasCampaignsStateLoaded extends HomeLayoutState {
  final HasCampaign data;

  HasCampaignsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  HasCampaignsStateLoaded copyWith({
    HasCampaign? data,
  }) {
    return HasCampaignsStateLoaded(
      data ?? this.data,
    );
  }
}

class HasCampaignsStateError extends HomeLayoutState {
  final Failure message;

  HasCampaignsStateError(this.message);

  @override
  List<Object?> get props => [message];
}
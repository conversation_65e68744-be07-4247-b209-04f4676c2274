import 'package:ads_dv/features/default_home/presentation/views/default_home_screen.dart';
import 'package:ads_dv/features/existing_user_home/presentation/views/existing_user_home.dart';
import 'package:ads_dv/features/home_layout/data/models/has_campaign_response.dart';
import 'package:ads_dv/features/home_layout/data/repos/home_layout_repo.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/network/failure_helper.dart';
import '../../../campaigns_home/presentation/views/campaigns_home.dart';
import '../../../sidebar/profile/presentation/views/profile_screen.dart';
import '../../../sidebar/reports/presentation/views/reports_screen.dart';
import '../../../sidebar/wallet/presentation/views/wallet_screen.dart';

part 'home_layout_state.dart';

class HomeLayoutCubit extends Cubit<HomeLayoutState> {
  HomeLayoutCubit() : super(HomeLayoutInitial());

  static HomeLayoutCubit get(context) => BlocProvider.of(context);

  int screenIndex = 0;

  late List<Widget> screens;
  hasCampaign({required BuildContext context}) async {
    emit(HasCampaignsStateLoading());
    instance<HomeLayoutRepo>().hasCampaign().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(HasCampaignsStateError(l));
      }, (r) {
        if(instance<HiveHelper>().getUser()!.accessToken == null || instance<HiveHelper>().getUser()!.accessToken == ""){
          print('errorNotDeletedUserrr2 ${instance<HiveHelper>().getUser()?.toJson()}');
          screens = [
            const DefaultHomeScreen(),
            const ReportsScreen(),
            const WalletScreen(
              isWallet: true,
            ),
            const ProfileScreen()
          ];
        }else if (r.hasCampaign == "true") {
          print('errorNotDeletedUserrr3 ${instance<HiveHelper>().getUser()?.accessToken}');
          screens = [
            const CampaignsHomeScreen(),
            const ReportsScreen(),
            const WalletScreen(
              isWallet: true,
            ),
            const ProfileScreen()
          ];
        }else{
          print('errorNotDeletedUserrr4 ${instance<HiveHelper>().getUser()?.accessToken}');
          screens = [
            const ExistingUserHomeScreen(),
            const ReportsScreen(),
            const WalletScreen(
              isWallet: true,
            ),
            const ProfileScreen()
          ];
        }
        emit(HasCampaignsStateLoaded(r));
      });
    });
  }

  void changeScreenIndex(int index) {
    screenIndex = index;
    emit(ChangeIndexState());
  }
}

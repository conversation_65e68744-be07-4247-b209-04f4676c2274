import 'package:ads_dv/features/notifications/data/data_sources/notification_data_source.dart';
import 'package:ads_dv/features/notifications/data/models/notification.dart';
import 'package:dartz/dartz.dart';

import '../../../../utils/network/connection/network_info.dart';
import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/network/failure_helper.dart';

class NotificationRepo {
  NetworkInfo networkInfo;
  NotificationDataSource notificationDataSource;

  NotificationRepo(
      {required this.networkInfo, required this.notificationDataSource});

  Future<Either<Failure, List<Notifications>>> getAllNotifications() {
    return FailureHelper.instance(
      method: () async {
        return await notificationDataSource.getAllNotifications();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> markAllAsRead({List<int>? ids}) {
    return FailureHelper.instance(
      method: () async {
        return await notificationDataSource.markAllAsRead(ids: ids);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> changeNotificationStatus() {
    return FailureHelper.instance(
      method: () async {
        return await notificationDataSource.changeNotificationStatus();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, int>> getNotificationsStatus() {
    return FailureHelper.instance(
      method: () async {
        return await notificationDataSource.getNotificationsStatus();
      },
      networkInfo: networkInfo,
    );
  }
}

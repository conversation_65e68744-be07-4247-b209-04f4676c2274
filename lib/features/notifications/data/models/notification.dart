class Notifications {
  int? id;
  String? title;
  String? notifiableType;
  int? notifiableId;
  String? data;
  int? isRead;
  String? createdAt;
  String? updatedAt;

  Notifications(
      {this.id,
        this.title,
        this.notifiableType,
        this.notifiableId,
        this.data,
        this.isRead,
        this.createdAt,
        this.updatedAt});

  Notifications.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    notifiableType = json['notifiable_type'];
    notifiableId = json['notifiable_id'];
    data = json['data'];
    isRead = json['reaad'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['notifiable_type'] = notifiableType;
    data['notifiable_id'] = notifiableId;
    data['data'] = this.data;
    data['reaad'] = isRead;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
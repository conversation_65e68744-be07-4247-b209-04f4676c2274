import 'package:ads_dv/features/notifications/data/models/notification.dart';
import 'package:dio/dio.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/network/dio/enum.dart';
import '../../../../utils/network/dio/network_call.dart';
import '../../../../utils/network/urls/end_points.dart';

class NotificationDataSource {
  Future<List<Notifications>> getAllNotifications() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllNotification,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<Notifications> notifications = data
          .map((notifications) => Notifications.fromJson(notifications))
          .toList();
      return notifications;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> markAllAsRead({List<int>? ids}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.markAllAsRead,
        params: {
          'ids': ids,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<int> getNotificationsStatus() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getNotificationsStatus,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return response['data'];
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> changeNotificationStatus() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.changeNotificationsStatus,
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return true;
    } catch (error) {
      rethrow;
    }
  }
}

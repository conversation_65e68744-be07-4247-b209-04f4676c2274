import 'package:ads_dv/features/notifications/data/repos/notification_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';

part 'change_notifications_status_state.dart';

class ChangeNotificationsStatusCubit
    extends Cubit<ChangeNotificationsStatusState> {
  ChangeNotificationsStatusCubit() : super(ChangeNotificationsStatusInitial());

  static ChangeNotificationsStatusCubit get(context) =>
      BlocProvider.of(context);

  changeNotificationStatus({required BuildContext context}) async {
    emit(ChangeNotificationsStatusLoading());
    instance<NotificationRepo>().changeNotificationStatus().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ChangeNotificationsStatusError(l));
      }, (r) async {
        ChangeNotificationsStatusLoaded();
      });
    });
  }
}

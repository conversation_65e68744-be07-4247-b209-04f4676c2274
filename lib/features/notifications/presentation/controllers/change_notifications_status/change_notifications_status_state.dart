part of 'change_notifications_status_cubit.dart';

@immutable
abstract class ChangeNotificationsStatusState {
  const ChangeNotificationsStatusState();
  List<Object?> get props => [];
}

class ChangeNotificationsStatusInitial extends ChangeNotificationsStatusState {}

class ChangeNotificationsStatusLoading extends ChangeNotificationsStatusState {}

class ChangeNotificationsStatusLoaded extends ChangeNotificationsStatusState {
}

class ChangeNotificationsStatusError extends ChangeNotificationsStatusState {
  final Failure message;

  const ChangeNotificationsStatusError(this.message);

  @override
  List<Object?> get props => [message];
}
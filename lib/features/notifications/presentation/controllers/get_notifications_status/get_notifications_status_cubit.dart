

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/notification_repo.dart';

part 'get_notifications_status_state.dart';

class GetNotificationsStatusCubit extends Cubit<GetNotificationsStatusState> {
  GetNotificationsStatusCubit() : super(GetNotificationsStatusInitial());


  static GetNotificationsStatusCubit get(context) =>
      BlocProvider.of(context);

  getNotificationsStatus(
      {
        required BuildContext context}) async {
    emit(GetNotificationsStatusLoading());
    instance<NotificationRepo>().getNotificationsStatus().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetNotificationsStatusError(l));
      }, (r) async {
        emit(GetNotificationsStatusLoaded(r));
      });
    });
  }
}

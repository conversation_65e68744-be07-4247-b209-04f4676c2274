part of 'get_notifications_status_cubit.dart';

@immutable
abstract class GetNotificationsStatusState {
  const GetNotificationsStatusState();
  List<Object?> get props => [];
}

class GetNotificationsStatusInitial extends GetNotificationsStatusState {}

class GetNotificationsStatusLoading extends GetNotificationsStatusState {}

class GetNotificationsStatusLoaded extends GetNotificationsStatusState {
  final int data;

  const GetNotificationsStatusLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetNotificationsStatusLoaded copyWith({
   int? data,
  }) {
    return GetNotificationsStatusLoaded(
      data ?? this.data,
    );
  }
}
class GetNotificationsStatusError extends GetNotificationsStatusState {
  final Failure message;

  const GetNotificationsStatusError(this.message);

  @override
  List<Object?> get props => [message];
}
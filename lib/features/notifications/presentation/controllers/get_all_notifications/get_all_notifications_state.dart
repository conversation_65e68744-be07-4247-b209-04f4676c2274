part of 'get_all_notifications_cubit.dart';

@immutable
abstract class GetAllNotificationsState {
  const GetAllNotificationsState();
  List<Object?> get props => [];
}

class GetAllNotificationsInitial extends GetAllNotificationsState {}

class GetAllNotificationsLoading extends GetAllNotificationsState {}

class GetAllNotificationsLoaded extends GetAllNotificationsState {
  final List<Notifications> data;

  const GetAllNotificationsLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetAllNotificationsLoaded copyWith({
    List<Notifications>? data,
  }) {
    return GetAllNotificationsLoaded(
      data ?? this.data,
    );
  }
}

class GetAllNotificationsStateError extends GetAllNotificationsState {
  final Failure message;

  const GetAllNotificationsStateError(this.message);

  @override
  List<Object?> get props => [message];
}


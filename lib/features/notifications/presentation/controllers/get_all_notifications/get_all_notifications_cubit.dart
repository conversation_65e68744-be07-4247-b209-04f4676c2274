import 'package:ads_dv/features/notifications/data/models/notification.dart';
import 'package:ads_dv/features/notifications/data/repos/notification_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';

part 'get_all_notifications_state.dart';

class GetAllNotificationsCubit extends Cubit<GetAllNotificationsState> {
  GetAllNotificationsCubit() : super(GetAllNotificationsInitial());

  static GetAllNotificationsCubit get(context) => BlocProvider.of(context);

  List<Notifications> notifies = [];

  getAllNotifications({
    required BuildContext context,
  }) async {
    emit(GetAllNotificationsLoading());
    instance<NotificationRepo>().getAllNotifications().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAllNotificationsStateError(l));
      }, (r) {
        notifies = r;
        emit(GetAllNotificationsLoaded(r));
      });
    });
  }
}

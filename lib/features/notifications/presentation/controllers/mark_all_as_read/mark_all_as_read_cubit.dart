import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/custom_widgets.dart';
import '../../../data/repos/notification_repo.dart';

part 'mark_all_as_read_state.dart';

class MarkAllAsReadCubit extends Cubit<MarkAllNotificationsState> {
  MarkAllAsReadCubit() : super(MarkAllNotificationsInitial());

  static MarkAllAsReadCubit get(context) => BlocProvider.of(context);

  marAllNotifications({
    required BuildContext context,
    List<int>? ids,
  }) async {
    emit(MarkAllNotificationsLoading());
    instance<NotificationRepo>().markAllAsRead(ids: ids).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(MarkAllNotificationsStateError(l));
      }, (r) {
        showSuccessToast("All Notifications Marked As Read");

        emit(MarkAllNotificationsLoaded());
      });
    });
  }
}

part of 'mark_all_as_read_cubit.dart';

@immutable
abstract class MarkAllNotificationsState {
  const MarkAllNotificationsState();
  List<Object?> get props => [];
}

class MarkAllNotificationsInitial extends MarkAllNotificationsState {}

class MarkAllNotificationsLoading extends MarkAllNotificationsState {}

class MarkAllNotificationsLoaded extends MarkAllNotificationsState {

}

class MarkAllNotificationsStateError extends MarkAllNotificationsState {
  final Failure message;

  const MarkAllNotificationsStateError(this.message);

  @override
  List<Object?> get props => [message];
}


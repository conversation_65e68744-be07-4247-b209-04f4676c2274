import 'package:ads_dv/utils/res/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/res/constants.dart';
import '../../../../../widgets/custom_text.dart';

class NotificationWidget extends StatelessWidget {
  String role;
  String date;

  String description;
  bool isSelected;
  NotificationWidget(
      {super.key,
        required this.role,
        required this.date,

        required this.description,
        required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration:  ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x3F000000),
            blurRadius: 40,
            offset: Offset(0, 0),
            spreadRadius: -10,
          )
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 18.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: role,
                  color: Colors.black,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
                RSizedBox.vertical(6.h),
                CustomText(
                  text: description,
                  color: Constants.gray,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                ),
              ],
            ),
            isSelected
                ? Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,

              children: [
                    SizedBox(
                                  width: 22.h,
                                  height: 22.h,
                                  child: ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return Constants.secGradient.createShader(bounds);
                      },
                      child: const Icon(
                        Icons.check_circle,
                        color: Colors.white,
                      )),
                                ),
                20.verticalSpace,
                CustomText(
                  text: CommonUtils.convertDateString(date),
                  color: Constants.gray,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                ),
                  ],
                )
                : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                                  width: 22.h,
                                  height: 22.h,
                                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: const BorderSide(
                          width: 1, color: Color(0xFF131534)),
                      borderRadius: BorderRadius.circular(18),
                    ),
                                  ),
                                ),
                    20.verticalSpace,

                    CustomText(
                      text: CommonUtils.convertDateString(date),
                      color: Constants.gray,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ],
                )
          ],
        ),
      ),
    );
  }
}

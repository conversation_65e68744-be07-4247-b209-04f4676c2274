import 'package:ads_dv/features/notifications/presentation/controllers/get_all_notifications/get_all_notifications_cubit.dart';
import 'package:ads_dv/features/notifications/presentation/controllers/mark_all_as_read/mark_all_as_read_cubit.dart';
import 'package:ads_dv/features/notifications/presentation/views/widgets/notification_widget.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/handle_error_widget.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../widgets/appbar.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) =>
              GetAllNotificationsCubit()..getAllNotifications(context: context),
        ),
        BlocProvider(
          create: (context) => MarkAllAsReadCubit(),
        ),
      ],
      child: BlocListener<MarkAllAsReadCubit, MarkAllNotificationsState>(
        listener: (context, state) {
          if (state is MarkAllNotificationsLoaded) {
            GetAllNotificationsCubit.get(context)
                .getAllNotifications(context: context);
          }
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: const CustomAppBar(
            title: "Notifications",
            showBackButton: true,
            hasDrawer: true,
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 6.sp),
            child:
                BlocBuilder<GetAllNotificationsCubit, GetAllNotificationsState>(
              builder: (context, state) {
                return state is GetAllNotificationsLoading
                    ? const Center(
                        child: LoadingWidget(
                          isCircle: true,
                        ),
                      )
                    : state is GetAllNotificationsStateError
                        ? Center(
                            child: HandleErrorWidget(
                                fun: () {
                                  GetAllNotificationsCubit.get(context)
                                      .getAllNotifications(
                                    context: context,
                                  );
                                },
                                failure: state.message),
                          )
                        : state is GetAllNotificationsLoaded
                            ? state.data.isEmpty
                                ? const Center(
                                    child: CustomText(
                                      text:
                                          "There is no notifications right now.",
                                      alignment: AlignmentDirectional.center,
                                    ),
                                  )
                                : SingleChildScrollView(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        10.verticalSpace,
                                        BlocBuilder<MarkAllAsReadCubit,
                                            MarkAllNotificationsState>(
                                          builder: (context, state) {
                                            return state
                                                    is MarkAllNotificationsLoading
                                                ? const Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.end,
                                                    children: [
                                                      LoadingWidget(
                                                        isCircle: true,
                                                      )
                                                    ],
                                                  )
                                                : InkWell(
                                                    onTap: () {
                                                      List<int> notifiesIds =
                                                          [];
                                                      GetAllNotificationsCubit
                                                              .get(context)
                                                          .notifies
                                                          .forEach((notify) {
                                                        notifiesIds
                                                            .add(notify.id!);
                                                      });
                                                      MarkAllAsReadCubit.get(
                                                              context)
                                                          .marAllNotifications(
                                                              context: context,
                                                              ids: notifiesIds);
                                                    },
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment.end,
                                                      children: [
                                                        Icon(
                                                          Icons.mark_email_read,
                                                          color: Constants
                                                              .primaryTextColor,
                                                          size: 18.sp,
                                                        ),
                                                        10.horizontalSpace,
                                                        CustomText(
                                                          text:
                                                              "Mark all as read",
                                                          fontSize: 13.sp,
                                                          color: Constants
                                                              .primaryTextColor,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                          },
                                        ),
                                        10.verticalSpace,
                                        ListView.builder(
                                          itemBuilder: (item, index) {
                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8.0),
                                              child: InkWell(
                                                onTap: () {
                                                  MarkAllAsReadCubit.get(
                                                          context)
                                                      .marAllNotifications(
                                                          context: context,
                                                          ids: [
                                                        state.data[index].id!
                                                      ]);
                                                },
                                                child: NotificationWidget(
                                                    date: state.data[index]
                                                            .createdAt ??
                                                        "",
                                                    role: state.data[index]
                                                            .title ??
                                                        "",
                                                    description: state
                                                            .data[index].data ??
                                                        "",
                                                    isSelected: state
                                                            .data[index]
                                                            .isRead ==
                                                        1),
                                              ),
                                            );
                                          },
                                          itemCount: state.data.length,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          clipBehavior: Clip.none,
                                        ),
                                      ],
                                    ),
                                  )
                            : const SizedBox();
              },
            ),
          ),
        ),
      ),
    );
  }
}

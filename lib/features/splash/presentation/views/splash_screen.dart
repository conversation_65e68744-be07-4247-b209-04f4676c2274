import 'package:ads_dv/features/onboarding/presentation/views/onboarding_screen.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../utils/res/app_assets.dart';
import '../../../../utils/res/router/routes.dart';
import '../../../../widgets/cached__image.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  bool hideAd = false;
  bool hideManager = false;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.addListener(() {
      if (_animation.value >= 0.5 && !hideAd) {
        setState(() {
          hideAd = true;
        });
      }

      if (_animation.value >= 0.75 && !hideManager) {
        setState(() {
          hideManager = true;
        });
      }
    });

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (instance<HiveHelper>().getIsFirst()) {
          Navigator.of(context).pushAndRemoveUntil(
            PageRouteBuilder(
              transitionDuration: const Duration(seconds: 1),
              pageBuilder: (_, __, ___) => const OnBoardingScreen(),
              transitionsBuilder: (_, animation, __, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
            ),
            (route) => false,
          );
        } else if (instance<HiveHelper>().getToken() != "") {
          Navigator.of(context).pushNamedAndRemoveUntil(
            Routes.home,
            (route) => false,
          );
        } else {
          print('errorNotDeletedUserrr ${instance<HiveHelper>().getToken()}');
          Navigator.of(context).pushNamedAndRemoveUntil(
            Routes.login,
                (route) => false,
          );
        }
      }
    });

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const ShapeDecoration(
        gradient: LinearGradient(
          begin: Alignment(-0.88, -0.48),
          end: Alignment(0.88, 0.48),
          colors: [
            Color(0xFF0B0F26),
            Color(0xFF0B0F26),
            Color(0xFF0B0F26),
            Color(0xFF0B0F26),
            Color(0xFF0B0F26)
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
          ),
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Visibility(
                    visible: _animation.value >= 0.0,
                    child: Opacity(
                      opacity: _animation.value,
                      child: Transform.scale(
                        scale: 1.0 + (1.0 - _animation.value),
                        child: child,
                      ),
                    ),
                  );
                },
                child: CachedImageWidget(
                  assetsImage: AppAssets.logo,
                  height: 68.h,
                 // width: 88.w,
                ),
              ),
              SizedBox(height: 12.h),
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Visibility(
                    visible: hideAd ? _animation.value >= 0.5 : true,
                    child: Opacity(
                      opacity: _animation.value >= 0.5
                          ? _animation.value - 0.5
                          : 0.0,
                      child: child,
                    ),
                  );
                },
                child: CachedImageWidget(
                  assetsImage: AppAssets.dv,
                  height: 26.h,
                 // width: 123.w,
                ),
              ),
              SizedBox(height: 12.0.h),
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Visibility(
                    visible: hideManager ? _animation.value >= 0.75 : true,
                    child: Opacity(
                      opacity: _animation.value >= 0.75
                          ? _animation.value - 0.75
                          : 0.0,
                      child: child,
                    ),
                  );
                },
                child: CachedImageWidget(
                  assetsImage: AppAssets.manager,
                  height: 9.h,
                  //width: 56.w,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

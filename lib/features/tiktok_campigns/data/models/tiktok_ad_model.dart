import 'dart:io';


class TiktokAdModel {
  String? campaignName;
  int? existingCampaign;
  String? objectiveType;
  String? optimizationGoal;
  String? adGroupName;
  List<String>? location;
  String? adName;
  String? adText;
  String? websiteUrl;
  String? callToAction;
  String? dailyBudget;
  String? startDate;
  String? endDate;
  String? placementType;
  String? identity;
  List<String>? age;
  String? gender;
  String? countryCode;
  String? countryCallingCode;
  String? phoneNumber;
  File? adVideo;
  String? dailyBudgetMode;
  List<String>? selectedTiktokInterests = [];
  List<String>? palcementsPositions = [];

  TiktokAdModel({
    this.campaignName,
    this.existingCampaign,
    this.objectiveType,
    this.optimizationGoal,
    this.adGroupName,
    this.location,
    this.adName,
    this.adText,
    this.websiteUrl,
    this.callToAction,
    this.dailyBudget,
    this.startDate,
    this.endDate,
    this.age,
    this.identity,
    this.gender,
    this.countryCode,
    this.countryCallingCode,
    this.phoneNumber,
    this.adVideo,
    this.dailyBudgetMode,
    this.selectedTiktokInterests,
    this.placementType,
    this.palcementsPositions,
  });

  TiktokAdModel copyWith({
    String? campaignName,
    int? existingCampaign,
    String? objectiveType,
    String? optimizationGoal,
    String? adGroupName,
    List<String>? location,
    String? adName,
    String? adText,
    String? websiteUrl,
    String? callToAction,
    String? dailyBudget,
    String? startDate,
    String? endDate,
    String? identity,
    List<String>? age,
    String? gender,
    String? countryCode,
    String? countryCallingCode,
    String? phoneNumber,
    File? adVideo,
    List<String>? selectedTiktokInterests,
    String? placementType,
    String? dailyBudgetMode,
    List<String>? palcementsPositions,
  }) {
    return TiktokAdModel(
      campaignName: campaignName ?? this.campaignName,
      existingCampaign: existingCampaign ?? this.existingCampaign,
      objectiveType: objectiveType ?? this.objectiveType,
      optimizationGoal: optimizationGoal ?? this.optimizationGoal,
      adGroupName: adGroupName ?? this.adGroupName,
      location: location ?? this.location,
      adName: adName ?? this.adName,
      adText: adText ?? this.adText,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      callToAction: callToAction ?? this.callToAction,
      dailyBudget: dailyBudget ?? this.dailyBudget,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      age: age ?? this.age,
      identity: identity ?? this.identity,
      gender: gender ?? this.gender,
      countryCode: countryCode ?? this.countryCode,
      countryCallingCode: countryCallingCode ?? this.countryCallingCode,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      adVideo: adVideo ?? this.adVideo,
      placementType: placementType ?? this.placementType,
      dailyBudgetMode: dailyBudgetMode ?? this.dailyBudgetMode,
      palcementsPositions: palcementsPositions ?? this.palcementsPositions,
      selectedTiktokInterests:
          selectedTiktokInterests ?? this.selectedTiktokInterests,
    );
  }
}

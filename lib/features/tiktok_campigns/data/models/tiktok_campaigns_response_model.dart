// class TiktokCampaignsResponse {
//   int? code;
//   String? message;
//   String? requestId;
//   Data? data;
//
//   TiktokCampaignsResponse({this.code, this.message, this.requestId, this.data});
//
//   TiktokCampaignsResponse.fromJson(Map<String, dynamic> json) {
//     code = json['code'];
//     message = json['message'];
//     requestId = json['request_id'];
//     data = json['data'] != null ? new Data.fromJson(json['data']) : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['code'] = this.code;
//     data['message'] = this.message;
//     data['request_id'] = this.requestId;
//     // if (this.data != null) {
//     //   data['data'] = this.data!.toJson();
//     // }
//     return data;
//   }
// }
//
// class Data {
//   List<TiktokCampaignModel>? tiktokCampaigns;
//   PageInfo? pageInfo;
//
//   Data({this.tiktokCampaigns, this.pageInfo});
//
//   Data.fromJson(Map<String, dynamic> json) {
//     if (json['list'] != null) {
//       tiktokCampaigns = <TiktokCampaignModel>[];
//       json['list'].forEach((v) {
//         tiktokCampaigns!.add(new TiktokCampaignModel.fromJson(v));
//       });
//     }
//     pageInfo = json['page_info'] != null
//         ? new PageInfo.fromJson(json['page_info'])
//         : null;
//   }
//
// // Map<String, dynamic> toJson() {
// //   final Map<String, dynamic> data = new Map<String, dynamic>();
// //   if (this.tiktokCampaigns != null) {
// //     data['list'] = this.tiktokCampaigns!.map((v) => v.toJson()).toList();
// //   }
// //   if (this.pageInfo != null) {
// //     data['page_info'] = this.pageInfo!.toJson();
// //   }
// //   return data;
// // }
// }
//
// class TiktokCampaignModel {
//   bool? isAdvancedDedicatedCampaign;
//   String? objectiveType;
//   Null? deepBidType;
//   String? budgetMode;
//   bool? rtaProductSelectionEnabled;
//   bool? isNewStructure;
//   String? createTime;
//   int? budget;
//   String? modifyTime;
//   bool? isSmartPerformanceCampaign;
//   Null? rtaId;
//   Null? disableSkanCampaign;
//   String? campaignType;
//   String? operationStatus;
//   String? secondaryStatus;
//   int? roasBid;
//   String? advertiserId;
//   String? campaignId;
//   bool? isSearchCampaign;
//   String? objective;
//   String? campaignName;
//
//   TiktokCampaignModel(
//       {this.isAdvancedDedicatedCampaign,
//       this.objectiveType,
//       this.deepBidType,
//       this.budgetMode,
//       this.rtaProductSelectionEnabled,
//       this.isNewStructure,
//       this.createTime,
//       this.budget,
//       this.modifyTime,
//       this.isSmartPerformanceCampaign,
//       this.rtaId,
//       this.disableSkanCampaign,
//       this.campaignType,
//       this.operationStatus,
//       this.secondaryStatus,
//       this.roasBid,
//       this.advertiserId,
//       this.campaignId,
//       this.isSearchCampaign,
//       this.objective,
//       this.campaignName});
//
//   TiktokCampaignModel.fromJson(Map<String, dynamic> json) {
//     isAdvancedDedicatedCampaign = json['is_advanced_dedicated_campaign'];
//     objectiveType = json['objective_type'];
//     deepBidType = json['deep_bid_type'];
//     budgetMode = json['budget_mode'];
//     rtaProductSelectionEnabled = json['rta_product_selection_enabled'];
//     isNewStructure = json['is_new_structure'];
//     createTime = json['create_time'];
//     budget = json['budget'];
//     modifyTime = json['modify_time'];
//     isSmartPerformanceCampaign = json['is_smart_performance_campaign'];
//     rtaId = json['rta_id'];
//     disableSkanCampaign = json['disable_skan_campaign'];
//     campaignType = json['campaign_type'];
//     operationStatus = json['operation_status'];
//     secondaryStatus = json['secondary_status'];
//     roasBid = json['roas_bid'];
//     advertiserId = json['advertiser_id'];
//     campaignId = json['campaign_id'];
//     isSearchCampaign = json['is_search_campaign'];
//     objective = json['objective'];
//     campaignName = json['campaign_name'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['is_advanced_dedicated_campaign'] = this.isAdvancedDedicatedCampaign;
//     data['objective_type'] = this.objectiveType;
//     data['deep_bid_type'] = this.deepBidType;
//     data['budget_mode'] = this.budgetMode;
//     data['rta_product_selection_enabled'] = this.rtaProductSelectionEnabled;
//     data['is_new_structure'] = this.isNewStructure;
//     data['create_time'] = this.createTime;
//     data['budget'] = this.budget;
//     data['modify_time'] = this.modifyTime;
//     data['is_smart_performance_campaign'] = this.isSmartPerformanceCampaign;
//     data['rta_id'] = this.rtaId;
//     data['disable_skan_campaign'] = this.disableSkanCampaign;
//     data['campaign_type'] = this.campaignType;
//     data['operation_status'] = this.operationStatus;
//     data['secondary_status'] = this.secondaryStatus;
//     data['roas_bid'] = this.roasBid;
//     data['advertiser_id'] = this.advertiserId;
//     data['campaign_id'] = this.campaignId;
//     data['is_search_campaign'] = this.isSearchCampaign;
//     data['objective'] = this.objective;
//     data['campaign_name'] = this.campaignName;
//     return data;
//   }
// }
//
// class PageInfo {
//   int? page;
//   int? pageSize;
//   int? totalPage;
//   int? totalNumber;
//
//   PageInfo({this.page, this.pageSize, this.totalPage, this.totalNumber});
//
//   PageInfo.fromJson(Map<String, dynamic> json) {
//     page = json['page'];
//     pageSize = json['page_size'];
//     totalPage = json['total_page'];
//     totalNumber = json['total_number'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['page'] = this.page;
//     data['page_size'] = this.pageSize;
//     data['total_page'] = this.totalPage;
//     data['total_number'] = this.totalNumber;
//     return data;
//   }
// }

class TiktokCampaignsResponse {
  bool? success;
  String? message;
  List<TiktokCampaignModel>? result;

  TiktokCampaignsResponse({this.success, this.message, this.result});

  TiktokCampaignsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <TiktokCampaignModel>[];
      json['result'].forEach((v) {
        result!.add(TiktokCampaignModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TiktokCampaignModel {
  String? objectiveType;
  bool? rtaBidEnabled;
  String? campaignName;
  String? objective;
  bool? isSmartPerformanceCampaign;
  String? createTime;
  Null? deepBidType;
  String? secondaryStatus;
  String? modifyTime;
  String? campaignType;
  bool? isSearchCampaign;
  int? budget;
  int? rtaId;
  String? budgetMode;
  bool? rtaProductSelectionEnabled;
  String? campaignId;
  String? advertiserId;
  bool? isAdvancedDedicatedCampaign;
  String? operationStatus;
  Null? disableSkanCampaign;
  int? roasBid;
  bool? isNewStructure;
  bool? catalogEnabled;

  TiktokCampaignModel(
      {this.objectiveType,
      this.rtaBidEnabled,
      this.campaignName,
      this.objective,
      this.isSmartPerformanceCampaign,
      this.createTime,
      this.deepBidType,
      this.secondaryStatus,
      this.modifyTime,
      this.campaignType,
      this.isSearchCampaign,
      this.budget,
      this.rtaId,
      this.budgetMode,
      this.rtaProductSelectionEnabled,
      this.campaignId,
      this.advertiserId,
      this.isAdvancedDedicatedCampaign,
      this.operationStatus,
      this.disableSkanCampaign,
      this.roasBid,
      this.isNewStructure,
      this.catalogEnabled});

  TiktokCampaignModel.fromJson(Map<String, dynamic> json) {
    objectiveType = json['objective_type'];
    rtaBidEnabled = json['rta_bid_enabled'];
    campaignName = json['campaign_name'];
    objective = json['objective'];
    isSmartPerformanceCampaign = json['is_smart_performance_campaign'];
    createTime = json['create_time'];
    deepBidType = json['deep_bid_type'];
    secondaryStatus = json['secondary_status'];
    modifyTime = json['modify_time'];
    campaignType = json['campaign_type'];
    isSearchCampaign = json['is_search_campaign'];
    budget = json['budget'];
    rtaId = json['rta_id'];
    budgetMode = json['budget_mode'];
    rtaProductSelectionEnabled = json['rta_product_selection_enabled'];
    campaignId = json['campaign_id'];
    advertiserId = json['advertiser_id'];
    isAdvancedDedicatedCampaign = json['is_advanced_dedicated_campaign'];
    operationStatus = json['operation_status'];
    disableSkanCampaign = json['disable_skan_campaign'];
    roasBid = json['roas_bid'];
    isNewStructure = json['is_new_structure'];
    catalogEnabled = json['catalog_enabled'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['objective_type'] = objectiveType;
    data['rta_bid_enabled'] = rtaBidEnabled;
    data['campaign_name'] = campaignName;
    data['objective'] = objective;
    data['is_smart_performance_campaign'] = isSmartPerformanceCampaign;
    data['create_time'] = createTime;
    data['deep_bid_type'] = deepBidType;
    data['secondary_status'] = secondaryStatus;
    data['modify_time'] = modifyTime;
    data['campaign_type'] = campaignType;
    data['is_search_campaign'] = isSearchCampaign;
    data['budget'] = budget;
    data['rta_id'] = rtaId;
    data['budget_mode'] = budgetMode;
    data['rta_product_selection_enabled'] = rtaProductSelectionEnabled;
    data['campaign_id'] = campaignId;
    data['advertiser_id'] = advertiserId;
    data['is_advanced_dedicated_campaign'] = isAdvancedDedicatedCampaign;
    data['operation_status'] = operationStatus;
    data['disable_skan_campaign'] = disableSkanCampaign;
    data['roas_bid'] = roasBid;
    data['is_new_structure'] = isNewStructure;
    data['catalog_enabled'] = catalogEnabled;
    return data;
  }
}

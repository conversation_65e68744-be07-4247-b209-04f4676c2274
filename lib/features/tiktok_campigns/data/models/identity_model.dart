class IdentityResponseModel {
  bool? success;
  String? message;
  List<IdentityListModel>? result;

  IdentityResponseModel({this.success, this.message, this.result});

  IdentityResponseModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <IdentityListModel>[];
      json['result'].forEach((v) {
        result!.add(IdentityListModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class IdentityListModel {
  String? availableStatus;
  bool? canManageMessage;
  bool? canPullVideo;
  bool? canPushVideo;
  bool? canUseLiveAds;
  String? displayName;
  int? identityAuthorizedBcId;
  String? identityId;
  String? identityType;
  String? profileImage;

  IdentityListModel(
      {this.availableStatus,
      this.canManageMessage,
      this.canPullVideo,
      this.canPushVideo,
      this.canUseLiveAds,
      this.displayName,
      this.identityAuthorizedBcId,
      this.identityId,
      this.identityType,
      this.profileImage});

  IdentityListModel.fromJson(Map<String, dynamic> json) {
    availableStatus = json['available_status'];
    canManageMessage = json['can_manage_message'];
    canPullVideo = json['can_pull_video'];
    canPushVideo = json['can_push_video'];
    canUseLiveAds = json['can_use_live_ads'];
    displayName = json['display_name'];
    identityAuthorizedBcId = json['identity_authorized_bc_id'];
    identityId = json['identity_id'];
    identityType = json['identity_type'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['available_status'] = availableStatus;
    data['can_manage_message'] = canManageMessage;
    data['can_pull_video'] = canPullVideo;
    data['can_push_video'] = canPushVideo;
    data['can_use_live_ads'] = canUseLiveAds;
    data['display_name'] = displayName;
    data['identity_authorized_bc_id'] = identityAuthorizedBcId;
    data['identity_id'] = identityId;
    data['identity_type'] = identityType;
    data['profile_image'] = profileImage;
    return data;
  }
}

class TiktokInterestsResponseModel {
  bool? success;
  String? message;
  List<TiktokInterestsModel>? result;

  TiktokInterestsResponseModel({this.success, this.message, this.result});

  TiktokInterestsResponseModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <TiktokInterestsModel>[];
      json['result'].forEach((v) {
        result!.add(TiktokInterestsModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TiktokInterestsModel {
  String? keyword;
  String? keywordId;
  String? language;
  String? status;

  TiktokInterestsModel(
      {this.keyword, this.keywordId, this.language, this.status});

  TiktokInterestsModel.fromJson(Map<String, dynamic> json) {
    keyword = json['keyword'];
    keywordId = json['keyword_id'];
    language = json['language'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['keyword'] = keyword;
    data['keyword_id'] = keywordId;
    data['language'] = language;
    data['status'] = status;
    return data;
  }
}

class TiktokLocationResponseModel {
  bool? success;
  String? message;
  List<TiktokStringLocationModel>? result;

  TiktokLocationResponseModel({this.success, this.message, this.result});

  TiktokLocationResponseModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <TiktokStringLocationModel>[];
      json['result'].forEach((v) {
        result!.add(TiktokStringLocationModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TiktokStringLocationModel {
  Geo? geo;
  Null? isp;
  String? keyword;
  String? name;
  StatusInfo? statusInfo;
  String? targetingType;
  bool? isChecked = false;

  TiktokStringLocationModel(
      {this.geo,
      this.isp,
      this.keyword,
      this.name,
      this.statusInfo,
      this.targetingType,
      this.isChecked});

  TiktokStringLocationModel.fromJson(Map<String, dynamic> json) {
    geo = json['geo'] != null ? Geo.fromJson(json['geo']) : null;
    isp = json['isp'];
    keyword = json['keyword'];
    name = json['name'];
    statusInfo = json['status_info'] != null
        ? StatusInfo.fromJson(json['status_info'])
        : null;
    targetingType = json['targeting_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (geo != null) {
      data['geo'] = geo!.toJson();
    }
    data['isp'] = isp;
    data['keyword'] = keyword;
    data['name'] = name;
    if (statusInfo != null) {
      data['status_info'] = statusInfo!.toJson();
    }
    data['targeting_type'] = targetingType;
    return data;
  }
}

class Geo {
  String? description;
  String? geoId;
  String? geoType;
  String? parentId;
  String? regionCode;

  Geo(
      {this.description,
      this.geoId,
      this.geoType,
      this.parentId,
      this.regionCode});

  Geo.fromJson(Map<String, dynamic> json) {
    description = json['description'];
    geoId = json['geo_id'];
    geoType = json['geo_type'];
    parentId = json['parent_id'];
    regionCode = json['region_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['description'] = description;
    data['geo_id'] = geoId;
    data['geo_type'] = geoType;
    data['parent_id'] = parentId;
    data['region_code'] = regionCode;
    return data;
  }
}

class StatusInfo {
  Null? reason;
  String? status;

  StatusInfo({this.reason, this.status});

  StatusInfo.fromJson(Map<String, dynamic> json) {
    reason = json['reason'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reason'] = reason;
    data['status'] = status;
    return data;
  }
}

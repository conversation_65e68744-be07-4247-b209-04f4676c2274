class TiktokLanguagesResponseModel {
  bool? success;
  String? message;
  List<LanguagesResult>? result;

  TiktokLanguagesResponseModel({this.success, this.message, this.result});

  TiktokLanguagesResponseModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <LanguagesResult>[];
      json['result'].forEach((v) {
        result!.add(LanguagesResult.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LanguagesResult {
  String? code;
  String? name;

  LanguagesResult({this.code, this.name});

  LanguagesResult.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['name'] = name;
    return data;
  }
}

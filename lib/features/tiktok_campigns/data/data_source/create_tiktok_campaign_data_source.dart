import 'dart:io';
import 'package:dio/dio.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/network/dio/enum.dart';
import '../../../../utils/network/dio/network_call.dart';
import '../../../../utils/network/urls/end_points.dart';
import '../../../create_campaigns/data/models/objectives.dart';
import '../../../create_campaigns/data/models/optimization.dart';
import '../models/identity_model.dart';
import '../models/tiktok_campaigns_response_model.dart';
import '../models/tiktok_interests_response_model.dart';
import '../models/tiktok_lang_model.dart';
import '../models/tiktok_location_response_model.dart';

class CreateTikTokCampaignDataSource {
  Future<TiktokCampaignsResponse> getTiktokCampaigns(
      String advertiserId) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllTiktokCampaigns,
        queryParameters: {
          "advertiser_id": advertiserId,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      TiktokCampaignsResponse cam = TiktokCampaignsResponse.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<String> createIdentity(
      String advertiserId, File image, String displayName) async {
    try {
      FormData body = FormData.fromMap({
        "advertiser_id": advertiserId,
        "image": await MultipartFile.fromFile(image.path),
        "display_name": displayName,
      });
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.createTiktokIdentity,
        params: body,
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      print('createIdentityDate $data');
      // TiktokCampaignsResponse cam = TiktokCampaignsResponse.fromJson(data);
      return data["result"];
    } catch (error) {
      rethrow;
    }
  }

  Future<TiktokLocationResponseModel> getTiktokLocations(
      String advertiserId, String? searchKey, String? objectiveType) async {
    try {
      print('locationsValue $searchKey $objectiveType $advertiserId');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.searchInTiktokLocations,
        queryParameters: {
          "advertiser_id": advertiserId,
          "keywords[0]": searchKey,
          "objective_type": objectiveType,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      TiktokLocationResponseModel cam =
          TiktokLocationResponseModel.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<TiktokInterestsResponseModel> getTiktokInterests(
      String advertiserId, String? searchKey) async {
    try {
      print('locationsValue $searchKey $advertiserId');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.searchInTiktokInterests,
        queryParameters: {
          "advertiser_id": advertiserId,
          "keyword": searchKey,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      TiktokInterestsResponseModel cam =
          TiktokInterestsResponseModel.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<TiktokLanguagesResponseModel> getTiktokLanguages(
    String advertiserId,
    // String? searchKey
  ) async {
    try {
      // print('locationsValue ${searchKey} $advertiserId');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.searchInTiktokLanguages,
        queryParameters: {
          "advertiser_id": advertiserId,
          // "search_keywords[0]": searchKey,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      TiktokLanguagesResponseModel cam =
          TiktokLanguagesResponseModel.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<Objective>> getTiktokObjectives() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getTiktokObjectives,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['data'];
      List<Objective> objective =
          data.map((objective) => Objective.fromJson(objective)).toList();
      return objective;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<Optimizations>> getTiktokOptimizations(
      {required String objectiveActualName}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getTiktokOptimizations,
        params: {
          "objective_id": objectiveActualName,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<Optimizations> optimizations = data
          .map((optimizations) => Optimizations.fromJson(optimizations))
          .toList();
      return optimizations;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<IdentityListModel>> getTiktokIdentities(
      {required String advertiserId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getTiktokIdentities,
        params: {
          "advertiser_id": advertiserId,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<IdentityListModel> identities = data
          .map((identities) => IdentityListModel.fromJson(identities))
          .toList();
      return identities;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<IdentityListModel>> getTiktokRealIdentities(
      {required String advertiserId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getTiktokRealIdentities,
        params: {
          "advertiser_id": advertiserId,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<IdentityListModel> identities = data
          .map((identities) => IdentityListModel.fromJson(identities))
          .toList();
      return identities;
    } catch (error) {
      rethrow;
    }
  }

  Future<String> createAD({
    required List<File> imagesFiles,
    required List<File> videosFiles,
    required String advertiserId,
    required String campaignName,
    required String objectiveType,
    required String optimizationGoal,
    required String adGroupName,
    required List<String> location,
    required List<String> age,
    required String gender,
    required List<String> languages,
    required String dailyBudget,
    required String startDate,
    required String endDate,
    required String placementType,
    required List<String> selectedTiktokInterests,
    required List<String> selectedTiktokPositions,
    required String adName,
    required String identityId,
    required String adText,
    required String websiteUrl,
    required String callToAction,
    required File adVideo,
    File? image,
    String? countryCode,
    String? countryCallingCode,
    String? phone,
    int? existingCampaign,
    required String displayName,
    required String dailyBudgetMode,
    required String identityType,
    // required List<File> thumbFiles
  }) async {
    try {
      // List<bool> isImagesValid =
      //     imagesFiles.map((e) => e.existsSync()).toList();

      List<bool> isImagesValid =
          imagesFiles.map((e) => e.existsSync()).toList();
      List<bool> isVideosValid =
          videosFiles.map((e) => e.existsSync()).toList();

      print('testIdentityType $identityType');

      var body = FormData.fromMap({
        "advertiser_id": advertiserId,

        "budget_mode": dailyBudgetMode,
        "objective_type": objectiveType,
        "campaign_name": campaignName,
        // for (int i = 0; i < (adModel.questions?.length ?? 0); i++)
        //   "questions[$i][type]": adModel.questions?[i],
        "campaign_id": existingCampaign,
        "adgroup_name": adGroupName,
        for (int i = 0; i < (location.length ?? 0); i++)
          "location_ids[$i]": location[i],
        // "location_ids": location
        //     .map((loc) =>
        //         loc) // Replace `.id` with the actual property (e.g., `locationId`, `code`, etc.)
        //     .toList(),
        "budget": dailyBudget,
        "schedule_start_time": startDate,
        "schedule_end_time": endDate,
        "optimization_goal": optimizationGoal,
        "billing_event":
            bilingRetFunc(optimizationGoal, objectiveType, optimizationGoal),
        "promotion_type":
            (objectiveType == "LEAD_GENERATION" && optimizationGoal == "CLICK")
                ? "LEAD_GEN_CLICK_TO_CALL"
                : (objectiveType == "LEAD_GENERATION" &&
                        optimizationGoal == "CONVERSATION")
                    ? "LEAD_GEN_CLICK_TO_TT_DIRECT_MESSAGE"
                    : 'WEBSITE',
        "placement_type": placementType,
        "comment_disabled": false,
        "video_download_disabled": false,
        "share_disabled": false,
        "spending_power": 'ALL',
        "phone_region_code": countryCode,
        "phone_region_calling_code": countryCallingCode,
        "phone_number": phone,
        for (int i = 0; i < (age.length ?? 0); i++) "age_groups[$i]": age[i],
        // "age_groups[0]": age,
        "gender": gender
        // gender == 1
        //     ? 'GENDER_MALE'
        //     : gender == 2
        //         ? "GENDER_FEMALE"
        //         :
        // "GENDER_UNLIMITED"
        ,
        for (int i = 0; i < (languages.length ?? 0); i++)
          "languages[$i]": languages[i],
        for (int i = 0; i < (selectedTiktokPositions.length ?? 0); i++)
          "placements[$i]": selectedTiktokPositions[i],
        // "languages": languages
        //     .map((language) =>
        //         language) // Replace `.code` with the actual property (e.g., `id`, `name`, etc.)
        //     .toList(),
        "video[0]": await MultipartFile.fromFile(adVideo.path),
        if (image != null)
          "identity_image": await MultipartFile.fromFile(image.path),
        "display_name": displayName,
        // for (int i = 0; i < (videosFiles.length); i++)
        //   "video[$i]": videosFiles[i].existsSync()
        //       ? await MultipartFile.fromFile(videosFiles[i].path)
        //       : null,
        "ad_name": adName,
        "identity_id": identityId,
        "ad_text": adText,
        "call_to_action":
            (objectiveType == "LEAD_GENERATION" && optimizationGoal == "CLICK")
                ? "CALL_NOW"
                : (objectiveType == "LEAD_GENERATION" &&
                        optimizationGoal == "CONVERSATION")
                    ? "SEND_MESSAGE"
                    : callToAction,
        "landing_page_url": websiteUrl,
        "identity_type": identityType,
        // for (int i = 0; i < (imagesFiles.length); i++)
        //   "image_file[$i]": imagesFiles[i].existsSync()
        //       ? await MultipartFile.fromFile(imagesFiles[i].path)
        //       : null,
        // "image_file[0]":
        //     await MultipartFile.fromFile("assets/tikTokThumb.jpeg"),
        "ad_groub_id": null,
        for (int i = 0; i < (selectedTiktokInterests.length ?? 0); i++)
          "interest_keyword_ids[$i]": selectedTiktokInterests[i],
        // "interest_keyword_ids": selectedTiktokInterests
        //     .map((interest) => interest.keywordId)
        //     .toList(),
      });

      for (int i = 0; i < videosFiles.length; i++) {
        if (videosFiles[i].existsSync()) {
          body.files.add(MapEntry(
            "video[$i]",
            await MultipartFile.fromFile(videosFiles[i].path),
          ));
          // print(
          //     '✅ Does body.files contain a video? ${body.files.any((entry) => entry.key.startsWith("video"))}');
        } else {
          print("❌ Video file does not exist: ${videosFiles[i].path}");
        }
      }

      for (int i = 0; i < imagesFiles.length; i++) {
        if (imagesFiles[i].existsSync()) {
          body.files.add(MapEntry(
            "image_file[$i]",
            await MultipartFile.fromFile(imagesFiles[i].path),
          ));
          // print(
          //     '✅ Does body.files contain an image? ${body.files.any((entry) => entry.key.startsWith("image_file"))}');
        } else {
          print("❌ Image file does not exist: ${imagesFiles[i].path}");
        }
      }

      for (var entry in body.files) {
        print(
            "📂 Key: ${entry.key}, File Path: ${(entry.value).filename}");
      }

      print('jasdoasfdklf ${body.fields}');

      var response =
          await instance<NetworkCall>().request(EndPoints.tikTokFullCycle,
              params:
                  // isImagesValid.every((element) => element) ?
                  body
              //     :
              // null
              ,
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return response['result'];
    } catch (error) {
      rethrow;
    }
  }
}

String bilingRetFunc(
    String optActualName, String objectiveType, String optimizationGoal) {
  print('billingok $optActualName');
  if (optActualName == "CLICK" ||
      optActualName == "PAGE_VISIT" ||
      optActualName == "PROFILE_VIEWS") {
    return 'CPC';
  } else if (optActualName == "REACH") {
    return 'CPM';
  } else if (optActualName == "TRAFFIC_LANDING_PAGE_VIEW" ||
      optActualName == "FOLLOWERS") {
    return 'OCPM';
  } else if (optActualName == "ENGAGED_VIEW") {
    return 'CPV';
  } else {
    return (objectiveType == "LEAD_GENERATION" &&
            optimizationGoal == "CONVERSATION")
        ? 'OCPM'
        : 'CPC';
  }
}

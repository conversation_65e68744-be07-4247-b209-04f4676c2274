import 'dart:io';

import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:ads_dv/utils/extensions/image_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/common_utils.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/cached__image.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../create_campaigns/data/models/call_to_action.dart';
import '../../../data/models/identity_model.dart';
import '../../../data/models/tiktok_ad_model.dart';
import '../../../repos/create_tiktok_campaign_repo.dart';
import '../tiktok_ad_group/tiktok_ad_group_cubit.dart';
import 'package:image/image.dart' as img;

part 'tiktok_ad_state.dart';

class TiktokAdCubit extends Cubit<TiktokAdState> {
  TiktokAdCubit() : super(TiktokAdInitial());

  static TiktokAdCubit get(context) => BlocProvider.of(context);

  final TextEditingController adNameController = TextEditingController();
  final TextEditingController displayNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  final TextEditingController url = TextEditingController();

  final TextEditingController adText = TextEditingController();

  List<IdentityListModel> identities = [];

  IdentityListModel? identity;
  String? identityId;

  double tiktokAdPercentage = 0.0;

  final adFormKey = GlobalKey<FormState>();
  TiktokAdModel tiktokAdModel = TiktokAdModel();

  String? type;
  String? typeName;

  String? countryCode;
  String? countryCallingCode;

  // String? phoneNumber;

  bool isTiktokCampaign = false;
  bool isTiktokAdGroup = false;
  bool isTiktokAd = false;
  bool isTiktokReview = false;
  bool isTiktokAdCreated = false;
  bool isAutoMaticVisibleBool = false;

  isAutoMaticVisible() {
    // Disappear for these combinations
    final shouldHide = (tiktokAdModel.objectiveType == "LEAD_GENERATION" &&
            (tiktokAdModel.optimizationGoal == "CLICK" ||
                tiktokAdModel.optimizationGoal == "CONVERSATION")) ||
        tiktokAdModel.objectiveType == "REACH" ||
        tiktokAdModel.objectiveType == "VIDEO_VIEWS" ||
        tiktokAdModel.objectiveType == "ENGAGEMENT";

    // Visibility is inverse of shouldHide
    final shouldShow = !shouldHide;

    if (isAutoMaticVisibleBool != shouldShow) {
      isAutoMaticVisibleBool = shouldShow;
      emit(UpdateStatus());
    }
  }

  getIdentities(
      {required BuildContext context, required String advertiserId}) async {
    emit(AdIdentitiesLoadingStatus());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokIdentities(advertiserId: advertiserId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ADCreatedStateError(l));
      }, (r) {
        identities = r;
        emit(AdIdentitiesLoadedSuccessfullyStatus(r));
      });
    });
  }

  getRalIdentities(
      {required BuildContext context, required String advertiserId}) async {
    emit(AdIdentitiesLoadingStatus());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokRealIdentities(advertiserId: advertiserId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ADCreatedStateError(l));
      }, (r) {
        identities = r;
        emit(AdIdentitiesLoadedSuccessfullyStatus(r));
      });
    });
  }

  Future<File> cropToSquare() async {
    // Read the image bytes
    final bytes = await identityImage?.readAsBytes();

    // Decode the image
    final original = img.decodeImage(bytes!);
    if (original == null) throw Exception("Failed to decode image");

    // Make it square
    final squared = original.makeSquare();

    // Encode the result (e.g. as JPEG)
    final newBytes = img.encodeJpg(squared);

    // Write to a new file or overwrite
    final output = await identityImage?.writeAsBytes(newBytes);

    return output!;
  }

  // createIdentity({
  //   required BuildContext context,
  //   // required String advertiserId,
  //   // required File image,
  //   // required String displayName,
  // }) async {
  //   emit(AdIdentitiesLoadingStatus());
  //   instance<CreateTikTokCampaignRepo>()
  //       .createIdentity(
  //           advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "",
  //           image: await cropToSquare(),
  //           displayName: displayNameController.text)
  //       .then((value) {
  //     value.fold((l) {
  //       FailureHelper.instance.handleFailures(l, context);
  //       emit(ADCreatedStateError(l));
  //     }, (r) {
  //       identityId = r;
  //       emit(IdentityCreatedSuccessfullyStatus(r));
  //     });
  //   });
  // }

  createAD({
    required BuildContext context,
    required List<File> imagesFiles,
    required List<File> videosFiles,
    required String advertiserId,
    required String campaignName,
    required String objectiveType,
    required String optimizationGoal,
    required String adGroupName,
    required List<String> location,
    required List<String> age,
    required String gender,
    required List<String> languages,
    required String dailyBudget,
    required String startDate,
    required String endDate,
    required String identityId,
    required List<String> selectedTiktokInterests,
    required String adName,
    required String adText,
    required String websiteUrl,
    required String callToAction,
    required File adVideo,
    required String placementType,
    required String dailyBudgetMode,
    required List<String> selectedTiktokPositions,
    required int? existingCampaign,

    // required List<File> thumbFiles
  }) async {
    // final image1 = File('assets/images/tikTokThumb.jpeg');
    // // final image2 = File('/path/to/image2.jpg');
    // final video = File('assets/images/tikTokVideo.mp4');
    // List<String> imagesPaths = [
    //   AppAssets.dummyTiktok1,
    // ];
    // List<String> videosPaths = [
    //   AppAssets.dummyTiktok2,
    // ];
    //
    // imagesFiles = imagesPaths.map((path) => File(path)).toList();
    // videosFiles = videosPaths.map((path) => File(path)).toList();

    print(
        'ListFiles $mainSelectedTab  $advertiserId $campaignName $objectiveType $optimizationGoal $adGroupName $location $age $gender $languages $dailyBudget $startDate $endDate $selectedTiktokInterests');

    // imagesFiles.add(image1);
    // videosFiles.add(video);
    emit(ADCreatedStateLoading());
    // isTiktokAdCreated = true;
    instance<CreateTikTokCampaignRepo>()
        .createAD(
      imagesFiles: imagesFiles,
      videosFiles: videosFiles,
      advertiserId: advertiserId,
      campaignName: campaignName,
      objectiveType: objectiveType,
      optimizationGoal: optimizationGoal,
      adGroupName: adGroupName,
      location: location,
      age: age,
      gender: gender,
      languages: languages,
      dailyBudget: dailyBudget,
      startDate: startDate,
      endDate: endDate,
      selectedTiktokInterests: selectedTiktokInterests,
      adName: adName,
      adText: adText,
      websiteUrl: websiteUrl,
      callToAction: callToAction,
      adVideo: adVideo,
      placementType: placementType,
      selectedTiktokPositions: selectedTiktokPositions,
      identityId: identityId,
      image: identityImage != null ? await cropToSquare() : null,
      displayName: displayNameController.text,
      dailyBudgetMode: dailyBudgetMode,
      identityType: mainSelectedTab == 1
          ? "TT_USER"
          : mainSelectedTab == 0
              ? "CUSTOMIZED_USER"
              : "",
      countryCode: countryCode,
      countryCallingCode: countryCallingCode,
      phone: phoneController.text,
      existingCampaign: existingCampaign,
      // thumbFiles: thumbFiles
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        print('tikTokAdCreatedError ${l.message}');
        emit(ADCreatedStateError(l));
      }, (r) {
        // print('tikTokAdCreated');
        CommonUtils.showBottomDialog(
            navigatorKey.currentState?.context ?? context,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 34.sp),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomText(
                    text: "Congratulations",
                    fontSize: 26.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  CachedImageWidget(
                    assetsImage: AppAssets.adSuccess,
                    height: 120.h,
                  ),
                  20.verticalSpace,
                  CustomText(
                    text:
                        "Congratulations! Your ad has been successfully published",
                    fontSize: 16.sp,
                    color: const Color(0xFF808080),
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 50.sp),
                    child: ButtonWidget(
                      text: "Return To Home",
                      fontSize: 16.sp,
                      padding: 16.sp,
                      onTap: () {
                        // isTiktokAdCreated = false;
                        clearTiktokData();
                        TiktokCampaignCubit.get(context).clearAllCampaignData();
                        TiktokAdGroupCubit.get(context).clearAllAdGroupData();
                        Navigator.pushNamedAndRemoveUntil(
                            context, Routes.splash, (route) => false);
                      },
                    ),
                  )
                ],
              ),
            ));
        // Navigator.pop(context);
        emit(AdCreatedSuccessfullyStatus());
        // Navigator.pop(context);
        // Navigator.pushNamedAndRemoveUntil(
        //     context, Routes.home, (route) => false);
        // clearCampaignData(context);
      });
    });
  }

  List<CallToAction> callToAction = [
    ///awareness
    // CallToAction(name: 'Subscribe'.tr, value: 'SUBSCRIBE'),
    CallToAction(name: 'Call now'.tr, value: 'CALL_NOW'),
    CallToAction(name: 'Check availability'.tr, value: 'CHECK_AVAILABILITY'),
    CallToAction(name: 'Contact us'.tr, value: 'CONTACT_US'),
    CallToAction(name: 'Download'.tr, value: 'DOWNLOAD_NOW'),
    CallToAction(name: 'Experience now'.tr, value: 'EXPERIENCE_NOW'),
    CallToAction(name: 'Get quote'.tr, value: 'GET_QUOTE'),
    CallToAction(name: 'Get showtimes'.tr, value: 'GET_SHOWTIMES'),
    CallToAction(name: 'Get tickets now'.tr, value: 'GET_TICKETS_NOW'),
    CallToAction(name: 'Install now'.tr, value: 'INSTALL_NOW'),
    CallToAction(name: 'Interested'.tr, value: 'INTERESTED'),
    CallToAction(name: 'Learn more'.tr, value: 'LEARN_MORE'),
    CallToAction(name: 'Listen now'.tr, value: 'LISTEN_NOW'),
    CallToAction(name: 'Order now'.tr, value: 'ORDER_NOW'),
    CallToAction(name: 'Play game'.tr, value: 'PLAY_GAME'),
    CallToAction(name: 'Pre-order now'.tr, value: 'PREORDER_NOW'),
    CallToAction(name: 'Read more'.tr, value: 'READ_MORE'),
    CallToAction(name: 'Send message'.tr, value: 'SEND_MESSAGE'),
    CallToAction(name: 'Shop now'.tr, value: 'SHOP_NOW'),
    CallToAction(name: 'Sign up'.tr, value: 'SIGN_UP'),
    CallToAction(name: 'Subscribe'.tr, value: 'SUBSCRIBE'),
    CallToAction(name: 'View now'.tr, value: 'VIEW_NOW'),
    CallToAction(name: 'View profile'.tr, value: 'VIEW_PROFILE'),
    CallToAction(name: 'Visit store'.tr, value: 'VISIT_STORE'),
    CallToAction(name: 'Watch LIVE'.tr, value: 'WATCH_LIVE'),
    CallToAction(name: 'Watch now'.tr, value: 'WATCH_NOW'),
    CallToAction(name: 'Join this hashtag'.tr, value: 'JOIN_THIS_HASHTAG'),
    CallToAction(
        name: 'Shoot with this effect'.tr, value: 'SHOOT_WITH_THIS_EFFECT'),
    CallToAction(
        name: 'View video with this effect'.tr,
        value: 'VIEW_VIDEO_WITH_THIS_EFFECT'),
  ];

  void setCallToAction(CallToAction callToAction) {
    type = callToAction.value;
    typeName = callToAction.name;
    emit(UpdateStatus());
  }

  void closeKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  bool isAdTileExpanded = false;

  void setAdExpansionState(bool isClosed) {
    isAdTileExpanded = isClosed;
    emit(SetAdExpansionState());
  }

  bool isTiktokAccountActive = false;

  void changeTiktokAccountStatus(bool status) {
    isTiktokAccountActive = status;
    emit(ChangeAccountStatus());
  }

  bool isCallToActionActive = false;

  void changeCallToActionStatus(bool status) {
    isCallToActionActive = status;
    emit(ChangeCallToActionStatus());
  }

  bool isAccountSelected = false;

  setSelectedAccount(bool status, IdentityListModel identityx) {
    isAccountSelected = status;
    identity = identityx;
    emit(UpdateStatus());
  }

  clearIdentities() {
    identity = null;
    emit(UpdateStatus());
  }

  int selectedTab = 0;

  int mainSelectedTab = 0;

  setSelectedTab(int selectedTabx) {
    selectedTab = selectedTabx;
    emit(UpdateStatus());
  }

  mainSetSelectedTab(int mainSelectedTabx) {
    mainSelectedTab = mainSelectedTabx;
    print('tiktokMainSelectedTab $mainSelectedTab');
    emit(UpdateStatus());
  }

  late File adVideo = File('');
  File? identityImage;

  void updateStatus() {
    emit(UpdateStatus());
  }

  bool initialLoading = true;

  Future<void> loadNextPage() async {
    initialLoading = false;
    emit(UpdateStatus());
  }

  clearTiktokData() {
    tiktokAdModel = TiktokAdModel();
    adNameController.clear();
    url.clear();
    adText.clear();
    type = "";
    typeName = "";
    isTiktokCampaign = false;
    isTiktokAdGroup = false;
    isTiktokAd = false;
    isTiktokReview = false;
    adVideo = File('');
    emit(UpdateStatus());
  }
}

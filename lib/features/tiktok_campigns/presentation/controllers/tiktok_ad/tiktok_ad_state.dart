part of 'tiktok_ad_cubit.dart';

@immutable
sealed class TiktokAdState {}

final class TiktokAdInitial extends TiktokAdState {}

final class SetAdExpansionState extends TiktokAdState {}

final class ChangeAccountStatus extends TiktokAdState {}

final class ChangeCallToActionStatus extends TiktokAdState {}

final class AdCreatedSuccessfullyStatus extends TiktokAdState {}

final class AdIdentitiesLoadingStatus extends TiktokAdState {}

final class AdIdentitiesLoadedSuccessfullyStatus extends TiktokAdState {
  final List<IdentityListModel> data;

  AdIdentitiesLoadedSuccessfullyStatus(this.data);

  @override
  List<Object?> get props => [data];

  AdIdentitiesLoadedSuccessfullyStatus copyWith({
    List<IdentityListModel>? data,
  }) {
    return AdIdentitiesLoadedSuccessfullyStatus(
      data ?? this.data,
    );
  }
}

final class IdentityCreatedSuccessfullyStatus extends TiktokAdState {
  final String identityId;

  IdentityCreatedSuccessfullyStatus(this.identityId);

  @override
  List<Object?> get props => [identityId];

  IdentityCreatedSuccessfullyStatus copyWith({
    String? identityId,
  }) {
    return IdentityCreatedSuccessfullyStatus(
      identityId ?? this.identityId,
    );
  }
}

final class ADCreatedStateLoading extends TiktokAdState {}

final class ADCreatedStateError extends TiktokAdState {
  final Failure message;

  ADCreatedStateError(this.message);

  @override
  List<Object?> get props => [message];
}

final class UpdateStatus extends TiktokAdState {}

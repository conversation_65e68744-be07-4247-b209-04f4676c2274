import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../create_campaigns/data/models/optimization.dart';
import '../../../repos/create_tiktok_campaign_repo.dart';

part 'get_tiktok_optimizations_state.dart';

class GetTiktokOptimizationsCubit extends Cubit<GetTiktokOptimizationsState> {
  GetTiktokOptimizationsCubit() : super(GetTiktokOptimizationsInitial());

  static GetTiktokOptimizationsCubit get(context) => BlocProvider.of(context);

  List<Optimizations> opt = [];

  getOptimizations(
      {required BuildContext context,
      required String objectiveActualName}) async {
    emit(GetTiktokOptimizationsStateLoading());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokOptimizations(objectiveActualName: objectiveActualName)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetTiktokOptimizationsStateError(l));
      }, (r) {
        opt = r;
        emit(GetTiktokOptimizationsStateLoaded(r));
      });
    });
  }
}

part of 'get_tiktok_optimizations_cubit.dart';

@immutable
sealed class GetTiktokOptimizationsState {}

final class GetTiktokOptimizationsInitial extends GetTiktokOptimizationsState {}

class GetTiktokOptimizationsStateLoading extends GetTiktokOptimizationsState {}

class GetTiktokOptimizationsStateLoaded extends GetTiktokOptimizationsState {
  final List<Optimizations> data;

  GetTiktokOptimizationsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetTiktokOptimizationsStateLoaded copyWith({
    List<Optimizations>? data,
  }) {
    return GetTiktokOptimizationsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetTiktokOptimizationsStateError extends GetTiktokOptimizationsState {
  final Failure message;

  GetTiktokOptimizationsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

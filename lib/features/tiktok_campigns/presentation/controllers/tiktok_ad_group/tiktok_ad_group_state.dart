part of 'tiktok_ad_group_cubit.dart';

@immutable
sealed class TiktokAdGroupState {}

final class TiktokAdGroupInitial extends TiktokAdGroupState {}

final class SetAdGroupExpansionState extends TiktokAdGroupState {}

final class SetAdGroupClearAllDateState extends TiktokAdGroupState {}

final class ChangeAdGroupTab extends TiktokAdGroupState {}

final class ChangeBudgetModeState extends TiktokAdGroupState {}

final class ChangePlacementStatus extends TiktokAdGroupState {}

final class SetSelectedGoal extends TiktokAdGroupState {}

final class ChangeCreateStatus extends TiktokAdGroupState {}

final class GetTiktokLanguagesStateLoading extends TiktokAdGroupState {}

final class TiktokRemoveFromLanguageState extends TiktokAdGroupState {}

final class TiktokAdToLanguageState extends TiktokAdGroupState {}

final class GetTiktokLanguagesStateError extends TiktokAdGroupState {
  final Failure failure;

  GetTiktokLanguagesStateError(this.failure);

  @override
  List<Object?> get props => [failure];
}

final class GetTiktokLanguagesState extends TiktokAdGroupState {
  final TiktokLanguagesResponseModel data;

  GetTiktokLanguagesState(this.data);

  @override
  List<Object?> get props => [data];

  GetTiktokLanguagesState copyWith({
    TiktokLanguagesResponseModel? data,
  }) {
    return GetTiktokLanguagesState(
      data ?? this.data,
    );
  }
}

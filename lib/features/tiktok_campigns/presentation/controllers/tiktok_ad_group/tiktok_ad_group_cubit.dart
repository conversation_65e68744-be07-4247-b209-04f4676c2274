import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/models/tiktok_lang_model.dart';
import '../../../data/models/tiktok_placements_model.dart';
import '../../../repos/create_tiktok_campaign_repo.dart';

part 'tiktok_ad_group_state.dart';

class TiktokAdGroupCubit extends Cubit<TiktokAdGroupState> {
  TiktokAdGroupCubit() : super(TiktokAdGroupInitial());

  static TiktokAdGroupCubit get(context) => BlocProvider.of(context);

  final TextEditingController groupNameController = TextEditingController();

  final groupFormKey = GlobalKey<FormState>();

  TiktokLanguagesResponseModel? tiktokLangs;

  double tiktokAdGroupPercentage = 0.0;

  List<TiktokPlacementsModel> tiktokPositions = [
    // TiktokPlacementsModel(
    //     name: 'PLACEMENT_GLOBAL_APP_BUNDLE',
    //     value: 'PLACEMENT_GLOBAL_APP_BUNDLE',
    //     isChecked: false),
    TiktokPlacementsModel(
        name: 'Placement pangle', value: 'PLACEMENT_PANGLE', isChecked: false),
    TiktokPlacementsModel(
        name: 'Placement tiktok', value: 'PLACEMENT_TIKTOK', isChecked: false),
  ];

  List<String> tiktokSelectedPositions = [];

  // List<String> publisherPlatforms = [];

  String? placementType;

  bool isAutoActive = false;
  bool isManualActive = false;

  void automaticPlacementsStatus(bool status) {
    isAutoActive = status;
    if (isAutoActive) {
      placementType = "PLACEMENT_TYPE_AUTOMATIC";
      isManualActive = false;
    } else {
      placementType = "";
    }

    emit(ChangePlacementStatus());
  }

  void manualPlacemnetsStatus(bool status) {
    isManualActive = status;
    if (isManualActive) {
      placementType = "PLACEMENT_TYPE_NORMAL";
      isAutoActive = false;
    } else {
      placementType = "";
    }

    emit(ChangePlacementStatus());
  }

  String? selectedBudgetMode;

  setSelectedBudgetMode(String mode) {
    print("budgetMode $mode");
    selectedBudgetMode = mode;
    emit(ChangeBudgetModeState());
  }

  void setSelectedFbPositions() {
    for (int i = 0; i < tiktokPositions.length; i++) {
      if (!tiktokSelectedPositions.contains(tiktokPositions[i].value)) {
        if (tiktokPositions[i].isChecked) {
          tiktokSelectedPositions.add(tiktokPositions[i].value ?? "");
        }
      } else {
        if (!tiktokPositions[i].isChecked) {
          tiktokSelectedPositions.remove(tiktokPositions[i].value ?? "");
        }
      }
    }

    emit(ChangePlacementStatus());
  }

  getLanguages(
      {required BuildContext context, required String advertiserId}) async {
    // tiktokCampaigns?.clear();

    emit(GetTiktokLanguagesStateLoading());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokLanguages(advertiserId: advertiserId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetTiktokLanguagesStateError(l));
      }, (r) {
        tiktokLangs = r;
        print('tikTokLangs ${tiktokLangs?.result?.first.name}');
        emit(GetTiktokLanguagesState(r));
      });
    });
  }

  List<LanguagesResult> languages = [];
  List<String> selectedLanguages = [];

  void setSelectedLanguages(LanguagesResult language) {
    if (languages
        .where((element) => element.code == language.code)
        .isNotEmpty) {
      languages.removeWhere((element) => language.code == element.code);
      selectedLanguages.remove(language.code!);
      // languages.forEach((element) {
      //   selectedLanguages.remove(element.name);
      // });
      emit(TiktokRemoveFromLanguageState());
    } else {
      languages.add(language);
      selectedLanguages.add(language.code!);
      // languages.forEach((element) {
      //   selectedLanguages.add(element.code!);
      // });
      emit(TiktokAdToLanguageState());
    }
  }

  void closeKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  bool isCampaignTileExpanded = false;

  void setCampaignExpansionState(bool isClosed) {
    isCampaignTileExpanded = isClosed;
    emit(SetAdGroupExpansionState());
  }

  bool isUserCommentActive = false;

  void changeUserCommentStatus(bool status) {
    isUserCommentActive = status;
    emit(ChangeCreateStatus());
  }

  bool isVideoDownloadActive = false;

  void changeVideoDownloadStatus(bool status) {
    isVideoDownloadActive = status;
    emit(ChangeCreateStatus());
  }

  bool isVideoSharingActive = false;

  void changeVideoSharingStatus(bool status) {
    isVideoSharingActive = status;
    emit(ChangeCreateStatus());
  }

  List<String> exclusions = [
    "Expanded Inventory",
    "Standard Inventory",
    "Limited Inventory"
  ];
  bool? isSelectedExclusions;
  int? exclusionsIndex;
  int? selectedExclusionsIndex;

  final TextEditingController dailyBudget = TextEditingController();

  var startDate = TextEditingController(text: "");
  var endDate = TextEditingController(text: "");

  void clearAllAdGroupData() {
    groupNameController.clear();
    tiktokLangs = null;
    languages.clear();
    selectedLanguages.clear();
    isCampaignTileExpanded = false;
    isUserCommentActive = false;
    isVideoDownloadActive = false;
    isVideoSharingActive = false;
    isSelectedExclusions = null;
    exclusionsIndex = null;
    selectedExclusionsIndex = null;
    dailyBudget.clear();
    startDate.clear();
    endDate.clear();
    emit(SetAdGroupClearAllDateState());
  }
}

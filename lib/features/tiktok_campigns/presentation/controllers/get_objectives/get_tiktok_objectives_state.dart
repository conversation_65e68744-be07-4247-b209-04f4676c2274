part of 'get_tiktok_objectives_cubit.dart';

@immutable
sealed class GetTiktokObjectivesState {}

final class GetTiktokObjectivesInitial extends GetTiktokObjectivesState {}

class GetTiktokObjectivesStateLoading extends GetTiktokObjectivesState {}

class SelectedTiktokObjectivesStateLoading extends GetTiktokObjectivesState {}

class GetTiktokObjectivesStateLoaded extends GetTiktokObjectivesState {
  final List<Objective> data;

  GetTiktokObjectivesStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetTiktokObjectivesStateLoaded copyWith({
    List<Objective>? data,
  }) {
    return GetTiktokObjectivesStateLoaded(
      data ?? this.data,
    );
  }
}

class GetCurrentTiktokObjectiveStateLoaded extends GetTiktokObjectivesState {
  final Objective? objective;

  GetCurrentTiktokObjectiveStateLoaded(this.objective);

  @override
  List<Object?> get props => [objective];

  GetCurrentTiktokObjectiveStateLoaded copyWith({
    Objective? objective,
  }) {
    return GetCurrentTiktokObjectiveStateLoaded(
      objective ?? this.objective,
    );
  }
}

class GetTiktokObjectivesStateError extends GetTiktokObjectivesState {
  final Failure message;

  GetTiktokObjectivesStateError(this.message);

  @override
  List<Object?> get props => [message];
}

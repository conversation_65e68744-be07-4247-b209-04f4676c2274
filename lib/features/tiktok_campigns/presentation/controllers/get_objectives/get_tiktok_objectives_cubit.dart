import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../create_campaigns/data/models/objectives.dart';
import '../../../repos/create_tiktok_campaign_repo.dart';

part 'get_tiktok_objectives_state.dart';

class GetTiktokObjectivesCubit extends Cubit<GetTiktokObjectivesState> {
  GetTiktokObjectivesCubit() : super(GetTiktokObjectivesInitial());

  static GetTiktokObjectivesCubit get(context) => BlocProvider.of(context);

  getObjectives({
    required BuildContext context,
  }) async {
    emit(GetTiktokObjectivesStateLoading());
    instance<CreateTikTokCampaignRepo>().getTiktokObjectives().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetTiktokObjectivesStateError(l));
      }, (r) {
        print("objectiveszxcxc $r");
        emit(GetTiktokObjectivesStateLoaded(r));
      });
    });
  }
}

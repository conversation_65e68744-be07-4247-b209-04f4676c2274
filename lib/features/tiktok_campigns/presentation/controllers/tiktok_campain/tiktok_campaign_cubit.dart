import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../create_campaigns/data/models/objectives.dart';
import '../../../../create_campaigns/data/models/optimization.dart';
import '../../../data/models/age_model.dart';
import '../../../data/models/tiktok_campaigns_response_model.dart';
import '../../../data/models/tiktok_interests_response_model.dart';
import '../../../data/models/tiktok_location_response_model.dart';
import '../../../repos/create_tiktok_campaign_repo.dart';
import '../tiktok_ad/tiktok_ad_cubit.dart';

part 'tiktok_campaign_state.dart';

class TiktokCampaignCubit extends Cubit<TiktokCampaignState> {
  TiktokCampaignCubit() : super(TiktokCampaignInitial());

  static TiktokCampaignCubit get(context) => BlocProvider.of(context);

  int selectCampaignTab = 0;
  double tiktokCampaignPercentage = 0.0;

  double locationPercentage = 0.0;

  double demographicPercentage = 0.0;

  double audiencePercentage = 0.0;

  bool isObjectiveUpdated = false;
  bool isOptimizeUpdated = false;

  final TextEditingController campaignNameController = TextEditingController();
  String? campaignName;
  String? objectiveType;

  final campaignFormKey = GlobalKey<FormState>();

  void closeKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  bool isCampaignTileExpanded = false;

  void setCampaignExpansionState(bool isClosed) {
    isCampaignTileExpanded = isClosed;
    emit(SetCampaignExpansionState());
  }

  void changeCampaignTabIndex(int index) {
    selectCampaignTab = index;
    emit(ChangeCampaignTab());
  }

  // List<Goal> goals = [
  //   Goal(name: 'Reach', icon: AppAssets.reach),
  //   Goal(name: 'Traffic', icon: AppAssets.traffic),
  //   Goal(name: 'Video views', icon: AppAssets.views),
  //   Goal(name: 'Community Interaction', icon: AppAssets.community),
  //   // Goal(name: 'App Promotion', icon: AppAssets.promotion),
  //   Goal(name: 'Lead Generation ', icon: AppAssets.engagement),
  //   // Goal(name: 'Website Conversions', icon: AppAssets.website),
  //   // Goal(name: 'Sales', icon: AppAssets.sales),
  // ];

  int? selectedGoal;
  Optimizations? optimization;

  TiktokCampaignsResponse? tiktokCampaigns;

  TiktokLocationResponseModel? tiktokLocations;

  List<TiktokStringLocationModel>? selectedTiktokLocation = [];
  List<String>? selectedTiktokLocationIds = [];

  TiktokCampaignModel? selectedCampaign;

  Objective? objective;
  var textController = TextEditingController();

  List<TiktokInterestsModel> selectedTiktokInterests = [];
  List<TiktokInterestsModel> defaultTiktokInterests = [
    TiktokInterestsModel(keywordId: "**********", keyword: "skin beauty"),
    TiktokInterestsModel(keywordId: "**********", keyword: "beauty industry"),
    TiktokInterestsModel(keywordId: "**********", keyword: "makeup and beauty"),
    TiktokInterestsModel(keywordId: "**********", keyword: "beauty and health"),
    TiktokInterestsModel(keywordId: "**********", keyword: "lipstick makeup"),
    TiktokInterestsModel(keywordId: "**********", keyword: "lip gloss"),
    TiktokInterestsModel(keywordId: "**********", keyword: "lip oil"),
    TiktokInterestsModel(keywordId: "**********", keyword: "new makeup"),
    TiktokInterestsModel(keywordId: "**********", keyword: "trendy makeup"),
    TiktokInterestsModel(keywordId: "**********", keyword: "Facial Makeup"),
    TiktokInterestsModel(keywordId: "**********", keyword: "beauty & makeup"),
    TiktokInterestsModel(keywordId: "**********", keyword: "beauty and makeup"),
    TiktokInterestsModel(keywordId: "**********", keyword: "sports watches"),
    TiktokInterestsModel(keywordId: "**********", keyword: "Sports car"),
    TiktokInterestsModel(keywordId: "**********", keyword: "sports fashion"),
    TiktokInterestsModel(keywordId: "**********", keyword: "sports clothing"),
    TiktokInterestsModel(keywordId: "**********", keyword: "Gym clothes"),
    TiktokInterestsModel(keywordId: "2065393891", keyword: "women’s clothes"),
    TiktokInterestsModel(keywordId: "3883455198", keyword: "sports clothes"),
    TiktokInterestsModel(
        keywordId: "2061658054", keyword: "huge selection of clothes"),
    TiktokInterestsModel(keywordId: "2061920862", keyword: "clothes for women"),
    TiktokInterestsModel(keywordId: "2061658055", keyword: "clothes and shoes"),
    TiktokInterestsModel(
        keywordId: "2064632557", keyword: "fashionable clothes"),
    TiktokInterestsModel(keywordId: "2065337097", keyword: "trendy clothes"),
    TiktokInterestsModel(keywordId: "2027735914", keyword: "Fast food"),
    TiktokInterestsModel(keywordId: "2027796796", keyword: "food lovers"),
    TiktokInterestsModel(keywordId: "2065307841", keyword: "delicious foods"),
    TiktokInterestsModel(keywordId: "3531951436", keyword: "food tracker"),
    TiktokInterestsModel(keywordId: "1915591109", keyword: "Food"),
  ]..shuffle();
  List<String> selectedTiktokInterestsIds = [];
  double tiktokInterestsPercentage = 0.0;

  TiktokInterestsResponseModel? tiktokInterests;

  // [
  //   SearchResult(id: "6002839660079", name: "Cosmetics"),
  //   SearchResult(id: "6002866718622", name: "Science"),
  //   SearchResult(id: "6002867432822", name: "Beauty"),
  //   SearchResult(id: "6002868021822", name: "Adventure travel"),
  //   SearchResult(id: "6002868910910", name: "Organic food"),
  //   SearchResult(id: "6002884511422", name: "Small business"),
  //   SearchResult(id: "6002920953955", name: "Interior design"),
  //   SearchResult(id: "6002925538921", name: "Acting"),
  //   SearchResult(id: "6002926108721", name: "Vacations"),
  //   SearchResult(id: "6002929380259", name: "Volleyball"),
  //   SearchResult(id: "6002936693259", name: "Soft drinks"),
  //   SearchResult(id: "6002951587955", name: "Classical music"),
  //   SearchResult(id: "6002957026250", name: "Theatre"),
  //   SearchResult(id: "6002960574320", name: "Tablet computers"),
  //   SearchResult(id: "6002963523717", name: "Aviation"),
  //   SearchResult(id: "6002964239317", name: "Mexican cuisine"),
  //   SearchResult(id: "6002964500317", name: "Word games"),
  //   SearchResult(id: "6002970406974", name: "Concerts"),
  //   SearchResult(id: "6002971085794", name: "Mobile phones"),
  //   SearchResult(id: "6002971095994", name: "Action games"),
  // ];

  List<AgeModel> ages =
      (TiktokAdCubit.get(Constants.navigatorKey.currentContext!)
                          .tiktokAdModel
                          .objectiveType ==
                      "LEAD_GENERATION" &&
                  TiktokAdCubit.get(Constants.navigatorKey.currentContext!)
                          .tiktokAdModel
                          .optimizationGoal ==
                      "CLICK") ||
              (TiktokAdCubit.get(Constants.navigatorKey.currentContext!)
                          .tiktokAdModel
                          .objectiveType ==
                      "LEAD_GENERATION" &&
                  TiktokAdCubit.get(Constants.navigatorKey.currentContext!)
                          .tiktokAdModel
                          .optimizationGoal ==
                      "CONVERSATION")
          ? [
              AgeModel(ageId: "AGE_18_24", age: '18-24', isSelected: false),
              AgeModel(ageId: "AGE_25_34", age: '25-34', isSelected: false),
              AgeModel(ageId: "AGE_35_44", age: '35-44', isSelected: false),
              AgeModel(ageId: "AGE_45_54", age: '45-54', isSelected: false),
              AgeModel(ageId: "AGE_55_100", age: '55+', isSelected: false),
              // AgeModel(ageId: "AGE_18_24", age: '55-64', isSelected: false),
              // AgeModel(ageId: "AGE_18_24", age: '65+', isSelected: false),
              // AgeModel(ageId: "AGE_all_ages", age: "All ages", isSelected: false),
            ]
          : [
              AgeModel(ageId: "AGE_13_17", age: '13-17', isSelected: false),
              AgeModel(ageId: "AGE_18_24", age: '18-24', isSelected: false),
              AgeModel(ageId: "AGE_25_34", age: '25-34', isSelected: false),
              AgeModel(ageId: "AGE_35_44", age: '35-44', isSelected: false),
              AgeModel(ageId: "AGE_45_54", age: '45-54', isSelected: false),
              AgeModel(ageId: "AGE_55_100", age: '55+', isSelected: false),
            ];
  List<AgeModel>? selectedAgeModel = [];
  List<String>? selectedAgeModelIds = [];
  int? selectedIndex;

  String? gender;

  // List<int> genders = [1, 2, 3];

  void selectedAge(AgeModel ageModelx) {
    if (ageModelx.isSelected == true) {
      // Add to selected list if not already present
      if (!selectedAgeModel!
          .any((element) => element.ageId == ageModelx.ageId)) {
        selectedAgeModel?.add(ageModelx);
        if (demographicPercentage == 0.0) {
          demographicPercentage = demographicPercentage + 0.33;
        }
      }
    } else {
      // Remove from selected list
      selectedAgeModel
          ?.removeWhere((element) => element.ageId == ageModelx.ageId);
      if (demographicPercentage != 0.0) {
        demographicPercentage = demographicPercentage - 0.33;
      }
    }

    // Update the list of selected IDs
    selectedAgeModelIds = selectedAgeModel?.map((e) => e.ageId ?? "").toList();
    emit(SelectedAgeState());
  }

  selectedLocation(TiktokStringLocationModel? selectedTiktokLocationx) {
    if (selectedTiktokLocationx?.isChecked == true) {
      selectedTiktokLocation?.add(selectedTiktokLocationx!);
      selectedTiktokLocationIds?.add(selectedTiktokLocationx!.geo!.geoId!);
      if (locationPercentage == 0.0) {
        locationPercentage = 1.0;
      }
    } else {
      selectedTiktokLocation?.remove(selectedTiktokLocationx!);
      selectedTiktokLocationIds?.remove(selectedTiktokLocationx!.geo!.geoId!);
      if (locationPercentage == 1.0) {
        locationPercentage = 0.0;
      }
    }
    emit(SelectedStringLocationState());
  }

  void removeLocation(TiktokStringLocationModel location) {
    selectedTiktokLocation?.remove(location);
    selectedTiktokLocationIds?.remove(location.geo?.geoId);
    emit(SelectedStringLocationState());
  }

  Future<void> getCampaigns(
      {required BuildContext context, required String advertiserId}) async {
    // tiktokCampaigns?.clear();

    emit(GetTiktokCampaignsStateLoading());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokCampaigns(advertiserId: advertiserId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetTiktokCampaignsStateError(l));
      }, (r) {
        tiktokCampaigns = r;
        print(
            'tikTokCampaigns $tiktokCampaigns ${instance<HiveHelper>().getAdvertiserId()}');
        emit(GetTiktokCampaignsStateLoaded(r));
      });
    });
  }

  getTiktokLocations(
      {required BuildContext context,
      required String advertiserId,
      required String searchKey,
      required String objectiveType}) async {
    // tiktokCampaigns?.clear();

    emit(GetTiktokLocationsStateLoading());
    instance<CreateTikTokCampaignRepo>()
        .getTiktokLocations(
            advertiserId: advertiserId,
            searchKey: searchKey,
            objectiveType: objectiveType)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetTiktokLocationsStateError(l));
      }, (r) {
        tiktokLocations = r;
        if (selectedTiktokLocationIds?.isNotEmpty == true) {
          selectedTiktokLocationIds?.forEach((element) {
            tiktokLocations?.result
                ?.firstWhere(
                    (tiktokLocation) => tiktokLocation.geo?.geoId == element)
                .isChecked = true;
          });
        }
        // tiktokLocations?.result?.forEach((tiktokLocation) {selectedTiktokLocationIds?.firstWhere((element) => tiktokLocation.geo?.geoId == element);});
        print(
            'tikTokCampaigns $tiktokLocations ${instance<HiveHelper>().getAdvertiserId()}');
        emit(GetTiktokLocationsStateLoaded(r));
      });
    });
  }

  Future<void> getTiktokInterests({
    required BuildContext context,
    required String advertiserId,
    required String searchKey,
  }) async {
    try {
      emit(TiktokInterestsLoading());

      final result =
          await instance<CreateTikTokCampaignRepo>().getTiktokInterests(
        advertiserId: advertiserId,
        searchKey: searchKey,
      );

      result.fold(
        (failure) {
          final message =
              FailureHelper.instance.handleFailures(failure, context);
          emit(TiktokInterestsError(failure.message));
        },
        (interests) {
          emit(TiktokInterestsLoaded(
            interests: interests.result ?? [],
            selectedInterests: selectedTiktokInterests,
            percentage: tiktokInterestsPercentage,
          ));
        },
      );
    } catch (e) {
      emit(TiktokInterestsError('Failed to load interests'));
    }
  }

  void addToTiktokInterests(TiktokInterestsModel interest) {
    final state = this.state;
    if (state is! TiktokInterestsLoaded) return;

    if (selectedTiktokInterests.any((i) => i.keyword == interest.keyword)) {
      emit(TiktokInterestsError('Interest already selected'));
      return;
    }

    selectedTiktokInterests.add(interest);
    selectedTiktokInterestsIds.add(interest.keywordId!);
    tiktokInterestsPercentage =
        (selectedTiktokInterests.length / 10).clamp(0.0, 1.0);

    emit(state.copyWith(
      selectedInterests: List.from(selectedTiktokInterests),
      percentage: tiktokInterestsPercentage,
    ));
  }

  void removeFromTiktokInterests(TiktokInterestsModel interest) {
    final state = this.state;
    if (state is! TiktokInterestsLoaded) return;

    selectedTiktokInterests.removeWhere((i) => i.keyword == interest.keyword);
    selectedTiktokInterestsIds.remove(interest.keywordId!);
    tiktokInterestsPercentage =
        (selectedTiktokInterests.length / 10).clamp(0.0, 1.0);

    emit(state.copyWith(
      selectedInterests: List.from(selectedTiktokInterests),
      percentage: tiktokInterestsPercentage,
    ));
  }

  void setSelectedGoal(int index, Objective? objectivex) {
    selectedGoal = index;
    objective = objectivex;
    objectiveType = objectivex?.showName;
    emit(SetSelectedTiktokGoal());
  }

  void setSelectedCampaign(TiktokCampaignModel? selectedCampaignx) {
    selectedCampaign = selectedCampaignx;

    print('selectedCampaign ${selectedCampaign?.campaignName}');

    emit(SetSelectedTiktokCampaign());
  }

  void setSelectedOptimization(Optimizations obt) {
    // selectOptimizationIndex = index;
    optimization = obt;
    emit(SetSelectedTiktokOptimization());
  }

  bool isCreateActive = false;

  void changeCreateStatus(bool status) {
    isCreateActive = status;
    emit(ChangeCreateStatus());
  }

  void clearAllCampaignData() {
    campaignNameController.clear();
    campaignName = null;
    objectiveType = null;
    selectedGoal = null;
    optimization = null;
    tiktokCampaigns = null;
    tiktokLocations = null;
    selectedTiktokLocation?.clear();
    selectedTiktokLocationIds?.clear();
    selectedCampaign = null;
    objective = null;
    textController.clear();
    selectedTiktokInterests.clear();
    selectedTiktokInterestsIds.clear();
    tiktokInterestsPercentage = 0.0;
    selectedAgeModel?.clear();
    selectedAgeModelIds?.clear();
    tiktokCampaignPercentage = 0.0;
    // genders.clear();
    selectedIndex = null;
    emit(GetTiktokClearCampaignData());
  }
}

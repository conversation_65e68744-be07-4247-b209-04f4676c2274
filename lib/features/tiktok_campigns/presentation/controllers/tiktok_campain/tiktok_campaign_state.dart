part of 'tiktok_campaign_cubit.dart';

@immutable
sealed class TiktokCampaignState {}

final class TiktokCampaignInitial extends TiktokCampaignState {}

final class SetCampaignExpansionState extends TiktokCampaignState {}

final class ChangeCampaignTab extends TiktokCampaignState {}

final class SetSelectedTiktokGoal extends TiktokCampaignState {}

final class SetSelectedTiktokOptimization extends TiktokCampaignState {}

final class SetSelectedTiktokCampaign extends TiktokCampaignState {}

final class ChangeCreateStatus extends TiktokCampaignState {}

final class AdToInterestsState extends TiktokCampaignState {}

final class RemoveFromInterestsState extends TiktokCampaignState {}

final class SelectedStringLocationState extends TiktokCampaignState {}

final class SelectedAgeState extends TiktokCampaignState {}

class GetTiktokCampaignsStateLoaded extends TiktokCampaignState {
  final TiktokCampaignsResponse data;

  GetTiktokCampaignsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetTiktokCampaignsStateLoaded copyWith({
    TiktokCampaignsResponse? data,
  }) {
    return GetTiktokCampaignsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetTiktokLocationsStateLoaded extends TiktokCampaignState {
  final TiktokLocationResponseModel data;

  GetTiktokLocationsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetTiktokLocationsStateLoaded copyWith({
    TiktokLocationResponseModel? data,
  }) {
    return GetTiktokLocationsStateLoaded(
      data ?? this.data,
    );
  }
}

class TiktokInterestsLoading extends TiktokCampaignState {
  @override
  List<Object> get props => [];
}

class TiktokInterestsLoaded extends TiktokCampaignState {
  final List<TiktokInterestsModel> interests;
  final List<TiktokInterestsModel> selectedInterests;
  final double percentage;

  TiktokInterestsLoaded({
    required this.interests,
    required this.selectedInterests,
    required this.percentage,
  });

  @override
  List<Object> get props => [interests, selectedInterests, percentage];

  TiktokInterestsLoaded copyWith({
    List<TiktokInterestsModel>? interests,
    List<TiktokInterestsModel>? selectedInterests,
    double? percentage,
  }) {
    return TiktokInterestsLoaded(
      interests: interests ?? this.interests,
      selectedInterests: selectedInterests ?? this.selectedInterests,
      percentage: percentage ?? this.percentage,
    );
  }
}

class TiktokInterestsError extends TiktokCampaignState {
  final String message;

  TiktokInterestsError(this.message);

  @override
  List<Object> get props => [message];
}

// class GetTiktokInterestsStateLoaded extends TiktokCampaignState {
//   final TiktokInterestsResponseModel data;
//
//   GetTiktokInterestsStateLoaded(this.data);
//
//   @override
//   List<Object?> get props => [data];
//
//   GetTiktokInterestsStateLoaded copyWith({
//     TiktokInterestsResponseModel? data,
//   }) {
//     return GetTiktokInterestsStateLoaded(
//       data ?? this.data,
//     );
//   }
// }

class GetTiktokCampaignsStateLoading extends TiktokCampaignState {}

// class GetTiktokInterestsStateLoading extends TiktokCampaignState {}

class GetTiktokLocationsStateLoading extends TiktokCampaignState {}

class GetTiktokLocationsStateError extends TiktokCampaignState {
  final Failure message;

  GetTiktokLocationsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class GetTiktokCampaignsStateError extends TiktokCampaignState {
  final Failure message;

  GetTiktokCampaignsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class GetTiktokClearCampaignData extends TiktokCampaignState {}

// class GetTiktokInterestsStateError extends TiktokCampaignState {
//   final Failure message;
//
//   GetTiktokInterestsStateError(this.message);
//
//   @override
//   List<Object?> get props => [message];
// }

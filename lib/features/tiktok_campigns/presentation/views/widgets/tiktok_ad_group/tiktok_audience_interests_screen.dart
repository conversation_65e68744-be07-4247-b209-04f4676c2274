import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../widgets/appbar.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../../../../widgets/text_field_widget.dart';
import '../../../../data/models/tiktok_interests_response_model.dart';
import '../../../controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import '../../../controllers/tiktok_campain/tiktok_campaign_cubit.dart';

class TiktokAudienceInterestsScreen extends StatefulWidget {
  const TiktokAudienceInterestsScreen({super.key});

  @override
  State<TiktokAudienceInterestsScreen> createState() =>
      _TiktokAudienceInterestsScreenState();
}

class _TiktokAudienceInterestsScreenState
    extends State<TiktokAudienceInterestsScreen> {
  final TextEditingController _searchController = TextEditingController();

  final RegExp _pattern = RegExp(r'\(.*?\)');

  @override
  void initState() {
    TiktokCampaignCubit.get(context).getTiktokInterests(
        context: context,
        advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "",
        searchKey: "searchKey");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Detailed Targeting".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
        builder: (context, state) {
          if (state is TiktokInterestsLoading) {
            return const Center(child: LoadingWidget(isCircle: true));
          }

          if (state is TiktokInterestsError) {
            return HandleErrorWidget(
              fun: () => context.read<TiktokCampaignCubit>().getTiktokInterests(
                    advertiserId:
                        instance<HiveHelper>().getAdvertiserId() ?? "",
                    context: context,
                    searchKey: _searchController.text,
                  ),
              failure: state.message,
            );
          }

          if (state is TiktokInterestsLoaded) {
            return Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                children: [
                  _buildSearchHeader(context),
                  SizedBox(height: 20.h),
                  _buildSearchBar(context),
                  SizedBox(height: 20.h),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: GridView.builder(
                        // shrinkWrap: true,
                        // physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 4.0,
                          mainAxisSpacing: 8.0,
                          childAspectRatio: 2.3,
                        ),
                        itemCount: state.selectedInterests.length,
                        itemBuilder: (context, index) {
                          final interest = state.selectedInterests[index];
                          final isSelected =
                              state.selectedInterests.contains(interest);

                          return InterestItem(
                            interest: interest,
                            isSelected: isSelected,
                            onTap: () => context
                                .read<TiktokCampaignCubit>()
                                .removeFromTiktokInterests(interest),
                          );
                        },
                      ),
                    ),
                  ),
                  // SizedBox(height: 20.h),
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 50.0),
                      child: _buildInterestsGrid(state),
                    ),
                  ),
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: _buildSaveButton(context),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildSearchHeader(BuildContext context) {
    return Row(
      children: [
        CustomSvgWidget(
          svg: AppAssets.interests,
          width: 24.w,
          height: 24.h,
        ),
        SizedBox(width: 10.w),
        CustomText(
          text: 'Interests'.tr,
          fontSize: 18.sp,
          color: Constants.primaryTextColor,
          fontWeight: FontWeight.w700,
        )
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      height: 46.h,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(37)),
        shadows: const [BoxShadow(color: Color(0x33000000), blurRadius: 20)],
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: _searchController,
              hintText: "Search".tr,
              radius: 37,
              icon: _buildSearchIcon(),
            ),
          ),
          _buildSearchButton(context),
        ],
      ),
    );
  }

  Widget _buildSearchIcon() {
    return ShaderMask(
      shaderCallback: (bounds) => const LinearGradient(
        colors: [Color(0xFFFF006F), Color(0xFFF6BA00)],
      ).createShader(bounds),
      child: const Padding(
        padding: EdgeInsets.all(12.0),
        child: CustomSvgWidget(
          svg: AppAssets.search,
          width: 13,
          height: 13,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildSearchButton(BuildContext context) {
    return InkWell(
      onTap: () => _performSearch(context),
      child: Container(
        width: 81.w,
        decoration: const ShapeDecoration(
          gradient:
              LinearGradient(colors: [Color(0xFF0B0F26), Color(0xFF1C4294)]),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(100),
              bottomRight: Radius.circular(100),
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15.0),
          child: CustomText(
            text: "Search".tr,
            fontSize: 14,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildInterestsGrid(TiktokInterestsLoaded state) {
    return state.interests.isNotEmpty
        ? GridView.builder(
            // shrinkWrap: true,
            // physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 4.0,
              mainAxisSpacing: 8.0,
              childAspectRatio: 2.3,
            ),
            itemCount: state.interests.length,
            itemBuilder: (context, index) {
              final interest = state.interests[index];
              final isSelected = state.selectedInterests.contains(interest);

              return InterestItem(
                  interest: interest,
                  isSelected: isSelected,
                  onTap: () {
                    if (!isSelected) {
                      context
                          .read<TiktokCampaignCubit>()
                          .addToTiktokInterests(interest);
                    } else {
                      showErrorToast('Its already chosen');
                    }
                  });
            },
          )
        : GridView.builder(
            // shrinkWrap: true,
            // physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 4.0,
              mainAxisSpacing: 8.0,
              childAspectRatio: 2.3,
            ),
            itemCount:
                TiktokCampaignCubit.get(context).defaultTiktokInterests.length,
            itemBuilder: (context, index) {
              final interest = TiktokCampaignCubit.get(context)
                  .defaultTiktokInterests[index];
              final isSelected = state.selectedInterests.contains(interest);

              return InterestItem(
                  interest: interest,
                  isSelected: isSelected,
                  onTap: () {
                    if (!isSelected) {
                      context
                          .read<TiktokCampaignCubit>()
                          .addToTiktokInterests(interest);
                    } else {
                      showErrorToast('Its already chosen');
                    }
                  });
            },
          );
  }

  Widget _buildSaveButton(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0, top: 40.0),
        child: SizedBox(
          width: 235.w,
          child: ButtonWidget(
            text: "Save",
            onTap: () => _handleSave(context),
          ),
        ),
      ),
    );
  }

  void _performSearch(BuildContext context) {
    if (_searchController.text.isNotEmpty) {
      context.read<TiktokCampaignCubit>().getTiktokInterests(
            searchKey: _searchController.text,
            advertiserId: instance<HiveHelper>().getAdvertiserId() ?? "",
            context: context,
          );
    }
  }

  void _handleSave(BuildContext context) {
    final cubit = context.read<TiktokCampaignCubit>();

    if (cubit.state is TiktokInterestsLoaded) {
      final state = cubit.state as TiktokInterestsLoaded;

      if (state.selectedInterests.isEmpty) {
        showErrorToast("Please select your interest");
        return;
      }

      if (cubit.audiencePercentage == 0.0) {
        cubit.audiencePercentage = cubit.audiencePercentage + 1.0;
        setState(() {});
      } else {
        if (cubit.audiencePercentage == 1.0) {
          cubit.audiencePercentage = cubit.audiencePercentage - 0.0;
          setState(() {});
        }
      }

      TiktokAdCubit.get(context).tiktokAdModel =
          TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                selectedTiktokInterests: cubit.selectedTiktokInterestsIds,
              );
      if (cubit.selectedTiktokInterestsIds.isNotEmpty == true) {
        TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage =
            TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage +
                0.11111111111111;
        print(
            'tiktokCampaignPercentage ${TiktokCampaignCubit.get(context).audiencePercentage}');
        setState(() {});
      } else {
        TiktokCampaignCubit.get(context).tiktokCampaignPercentage =
            TiktokCampaignCubit.get(context).tiktokCampaignPercentage -
                0.11111111111111;
        print(
            'tiktokCampaignPercentage ${TiktokCampaignCubit.get(context).audiencePercentage}');
        setState(() {});
      }
      print(
          'selectedInterestsasd ${TiktokAdCubit.get(context).tiktokAdModel.selectedTiktokInterests}');
      // cubit.updateAdModelWithInterests();
      // context.read<ReachEstimateCubit>().getReachEstimate(
      //   context: context,
      //   imagesFiles: context.read<CreateAdCubit>().adImages,
      //   videosFiles: context.read<CreateAdCubit>().adVideo,
      // );

      Navigator.pop(context);
    }
  }
}

class InterestItem extends StatelessWidget {
  final TiktokInterestsModel interest;
  final bool isSelected;
  final VoidCallback onTap;

  const InterestItem({
    super.key,
    required this.interest,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: ShapeDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFFF6BA00), Color(0xFFFF006F)])
              : null,
          color: !isSelected ? const Color(0xFFFB533E).withOpacity(0.1) : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(36.59),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: CustomText(
                  text: interest.keyword?.replaceAll(RegExp(r'\(.*?\)'), "") ??
                      "",
                  fontWeight: FontWeight.w700,
                  color: isSelected ? Colors.white : Constants.primaryTextColor,
                  fontSize: 10.sp,
                  textOverflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                isSelected ? Icons.check : Icons.add,
                color: isSelected ? Colors.white : Constants.primaryTextColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/appbar.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../../../create_campaigns/presentation/views/widgets/create_adset/new_adset/gender_widget.dart';

class TiktokDemographicScreen extends StatefulWidget {
  const TiktokDemographicScreen({super.key});

  @override
  State<TiktokDemographicScreen> createState() =>
      _TiktokDemographicScreenState();
}

class _TiktokDemographicScreenState extends State<TiktokDemographicScreen> {
  @override
  void initState() {
    Future.delayed(const Duration(), () {
      TiktokAdGroupCubit.get(context).getLanguages(
          context: context,
          advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "");
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Demographic".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 30.0, vertical: 10.0),
              child: Card(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15.0),
                    side: const BorderSide(color: Colors.black)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 30.0,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15.0),
                      child: Row(
                        children: [
                          CustomSvgWidget(
                            svg: AppAssets.age,
                            width: 24.w,
                            height: 24.h,
                            //    color: Colors.white,
                          ),
                          SizedBox(width: 10.w),
                          CustomText(
                            text: 'Age'.tr,
                            fontSize: 18.sp,
                            color: Constants.primaryTextColor,
                            fontWeight: FontWeight.w700,
                            alignment: AlignmentDirectional.centerStart,
                          )
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10.0, vertical: 50.0),
                      child: Wrap(
                        children: TiktokCampaignCubit.get(context)
                            .ages
                            .map((e) => GestureDetector(
                                  onTap: () {
                                    e.isSelected = !e.isSelected!;
                                    TiktokCampaignCubit.get(context)
                                        .selectedAge(e);
                                    setState(() {});
                                  },
                                  child: Card(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20.0),
                                      side: BorderSide(
                                        width: 2,
                                        color: TiktokCampaignCubit.get(context)
                                                    .selectedAgeModel
                                                    ?.any((element) =>
                                                        element.age == e.age) ??
                                                false // Use .any() instead of .firstWhere()
                                            ? Colors.blue
                                            : Colors.black,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(15.0),
                                      child: Text(e.age ?? ""),
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15.0),
                      child: Row(
                        children: [
                          CustomSvgWidget(
                            svg: AppAssets.gender,
                            width: 24.w,
                            height: 24.h,
                            //    color: Colors.white,
                          ),
                          SizedBox(width: 10.w),
                          CustomText(
                            text: 'Gender'.tr,
                            fontSize: 18.sp,
                            color: Constants.primaryTextColor,
                            fontWeight: FontWeight.w700,
                            alignment: AlignmentDirectional.centerStart,
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: genders.map((e) {
                        return GenderWidget(
                          isSelected: genders.indexOf(e) ==
                              TiktokCampaignCubit.get(context).selectedIndex,
                          callback: (index) {
                            // widget.createAdCubit.updateDemoProcess3();
                            // widget.createAdCubit.updateAdSetProcess9();

                            setState(() {
                              // Update selected index when an ObjectiveWidget is tapped
                              TiktokCampaignCubit.get(context).selectedIndex =
                                  index;

                              e == 'Male'
                                  ? TiktokCampaignCubit.get(context).gender =
                                      "GENDER_MALE"
                                  : e == "Female"
                                      ? TiktokCampaignCubit.get(context)
                                          .gender = "GENDER_FEMALE"
                                      : e == "All genders"
                                          ? TiktokCampaignCubit.get(context)
                                              .gender = "GENDER_UNLIMITED"
                                          : "";
                              if (TiktokCampaignCubit.get(context)
                                      .demographicPercentage !=
                                  0.66) {
                                TiktokCampaignCubit.get(context)
                                        .demographicPercentage =
                                    TiktokCampaignCubit.get(context)
                                            .demographicPercentage +
                                        0.33;
                              }
                              print(
                                  'genderxzc ${TiktokCampaignCubit.get(context).gender}');
                              // e == "Male".tr
                              //     ? TiktokCampaignCubit.get(context).genders = [
                              //         1
                              //       ]
                              //     : e == "Female".tr
                              //         ? TiktokCampaignCubit.get(context)
                              //             .genders = [2]
                              //         : TiktokCampaignCubit.get(context)
                              //             .genders = [1, 2];
                            });
                            // widget.createAdCubit.adModel =
                            //     CreateAdCubit.get(context).adModel.copyWith(
                            //         genders: widget.createAdCubit.genders);
                            // ReachEstimateCubit.get(context).getReachEstimate(
                            //     context: context,
                            //     imagesFiles:
                            //     CreateAdCubit.get(context).adImages,
                            //     videosFiles:
                            //     CreateAdCubit.get(context).adVideo);
                          },
                          name: e,
                          index: genders.indexOf(e),
                        );
                      }).toList(),
                    ),
                    const SizedBox(
                      height: 30.0,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15.0),
                      child: Row(
                        children: [
                          CustomSvgWidget(
                            svg: AppAssets.lang,
                            width: 24.w,
                            height: 24.h,
                            //    color: Colors.white,
                          ),
                          SizedBox(width: 10.w),
                          CustomText(
                            text: 'Language'.tr,
                            fontSize: 18.sp,
                            color: Constants.primaryTextColor,
                            fontWeight: FontWeight.w700,
                            alignment: AlignmentDirectional.centerStart,
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    BlocBuilder<TiktokAdGroupCubit, TiktokAdGroupState>(
                      builder: (context, state) {
                        return TiktokAdGroupCubit.get(context)
                                    .tiktokLangs
                                    ?.result
                                    ?.isNotEmpty ==
                                true
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20.0),
                                child: Column(
                                  children: [
                                    // ListView.builder(
                                    //   shrinkWrap: true,
                                    //   physics:
                                    //       const NeverScrollableScrollPhysics(),
                                    //   padding: EdgeInsets.zero,
                                    //   itemBuilder: (ctx, index) {
                                    //     return InkWell(
                                    //       onTap: () {
                                    //         TiktokAdGroupCubit.get(context)
                                    //             .setSelectedLanguages(
                                    //                 TiktokAdGroupCubit.get(
                                    //                         context)
                                    //                     .languages[index]);
                                    //         setState(() {});
                                    //       },
                                    //       child: Column(
                                    //         children: [
                                    //           Row(
                                    //             mainAxisAlignment:
                                    //                 MainAxisAlignment
                                    //                     .spaceBetween,
                                    //             children: [
                                    //               CustomText(
                                    //                 text: TiktokAdGroupCubit
                                    //                             .get(context)
                                    //                         .languages[index]
                                    //                         .name ??
                                    //                     "",
                                    //                 color: Constants.darkColor,
                                    //                 fontSize: 14,
                                    //                 fontWeight: FontWeight.w400,
                                    //               ),
                                    //               Container(
                                    //                 height: 22,
                                    //                 width: 22,
                                    //                 decoration: BoxDecoration(
                                    //                   border: Border.all(
                                    //                     color:
                                    //                         Constants.darkColor,
                                    //                     width: 1.5,
                                    //                   ),
                                    //                   shape: BoxShape.circle,
                                    //                 ),
                                    //               ),
                                    //             ],
                                    //           ),
                                    //           const SizedBox(height: 5),
                                    //           const Divider(
                                    //               color: Constants.gray),
                                    //         ],
                                    //       ),
                                    //     );
                                    //   },
                                    //   itemCount: TiktokAdGroupCubit.get(context)
                                    //       .languages
                                    //       .length,
                                    // ),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      padding: EdgeInsets.zero,
                                      itemCount: TiktokAdGroupCubit.get(context)
                                          .tiktokLangs
                                          ?.result
                                          ?.length,
                                      itemBuilder: (item, index) {
                                        final lang =
                                            TiktokAdGroupCubit.get(context)
                                                .tiktokLangs
                                                ?.result?[index];
                                        final isSelected =
                                            TiktokAdGroupCubit.get(context)
                                                .selectedLanguages
                                                .any((l) => l == lang?.code);
                                        return !isSelected
                                            ? InkWell(
                                                onTap: () {
                                                  TiktokAdGroupCubit.get(
                                                          context)
                                                      .setSelectedLanguages(
                                                          TiktokAdGroupCubit
                                                                  .get(context)
                                                              .tiktokLangs!
                                                              .result![index]);
                                                  if (TiktokCampaignCubit.get(
                                                              context)
                                                          .demographicPercentage !=
                                                      0.99) {
                                                    TiktokCampaignCubit.get(
                                                                context)
                                                            .demographicPercentage =
                                                        TiktokCampaignCubit.get(
                                                                    context)
                                                                .demographicPercentage +
                                                            0.33;
                                                  }
                                                  setState(() {});
                                                },
                                                child: Column(
                                                  children: [
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        CustomText(
                                                          text: TiktokAdGroupCubit
                                                                      .get(
                                                                          context)
                                                                  .tiktokLangs
                                                                  ?.result?[
                                                                      index]
                                                                  .name ??
                                                              "",
                                                          color: Constants
                                                              .darkColor,
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                        ),
                                                        Container(
                                                          height: 22,
                                                          width: 22,
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                              color: Constants
                                                                  .darkColor,
                                                              width: 1.5,
                                                            ),
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    const SizedBox(height: 5),
                                                    const Divider(
                                                        color: Constants.gray),
                                                  ],
                                                ),
                                              )
                                            : Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 10.0, left: 8),
                                                child: InkWell(
                                                  onTap: () {
                                                    TiktokAdGroupCubit.get(
                                                            context)
                                                        .setSelectedLanguages(
                                                            TiktokAdGroupCubit
                                                                    .get(
                                                                        context)
                                                                .tiktokLangs!
                                                                .result![index]);
                                                    setState(() {});
                                                  },
                                                  child: Container(
                                                    alignment: Alignment.center,
                                                    padding: EdgeInsets.zero,
                                                    decoration: ShapeDecoration(
                                                      color: Colors.white,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8)),
                                                      shadows: const [
                                                        BoxShadow(
                                                          color:
                                                              Color(0x33000000),
                                                          blurRadius: 20,
                                                          offset: Offset(0, 0),
                                                          spreadRadius: -6,
                                                        )
                                                      ],
                                                    ),
                                                    child: Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 10.0,
                                                          vertical: 12),
                                                      child: Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Center(
                                                            child: ShaderMask(
                                                              shaderCallback:
                                                                  (Rect
                                                                      bounds) {
                                                                return const LinearGradient(
                                                                  colors: [
                                                                    Color(
                                                                        0xFFFF006F),
                                                                    Color(
                                                                        0xFFF6BA00),
                                                                  ],
                                                                ).createShader(
                                                                    bounds);
                                                              },
                                                              child: CustomText(
                                                                text: TiktokAdGroupCubit.get(
                                                                            context)
                                                                        .tiktokLangs
                                                                        ?.result?[
                                                                            index]
                                                                        .name ??
                                                                    "",
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                              ),
                                                            ),
                                                          ),
                                                          ShaderMask(
                                                            shaderCallback:
                                                                (Rect bounds) {
                                                              return const LinearGradient(
                                                                colors: [
                                                                  Color(
                                                                      0xFFFF006F),
                                                                  Color(
                                                                      0xFFF6BA00),
                                                                ],
                                                              ).createShader(
                                                                  bounds);
                                                            },
                                                            child: const Icon(
                                                              Icons
                                                                  .check_circle,
                                                              size: 22,
                                                              color:
                                                                  Colors.white,
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                      },
                                    ),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox();
                      },
                    ),
                    // BlocBuilder<TiktokAdGroupCubit, TiktokAdGroupState>(
                    //   builder: (context, state) {
                    //     return TiktokAdGroupCubit.get(context).languages.isEmpty
                    //         ? const SizedBox()
                    //         : Column(
                    //             children: [
                    //               CustomText(
                    //                 text: 'Selected Languages'.tr,
                    //                 fontSize: 14.sp,
                    //                 color: AppColors.iconBottomColor,
                    //                 fontWeight: FontWeight.w400,
                    //                 alignment: AlignmentDirectional.centerStart,
                    //               ),
                    //               SizedBox(
                    //                 height: 10.h,
                    //               ),
                    //               ListView.builder(
                    //                 shrinkWrap: true,
                    //                 physics:
                    //                     const NeverScrollableScrollPhysics(),
                    //                 padding: EdgeInsets.zero,
                    //                 itemCount: TiktokAdGroupCubit.get(context)
                    //                     .languages
                    //                     .length,
                    //                 itemBuilder: (item, index) {
                    //                   return Padding(
                    //                     padding: const EdgeInsets.only(
                    //                         bottom: 10.0, left: 8),
                    //                     child: InkWell(
                    //                       onTap: () {
                    //                         TiktokAdGroupCubit.get(context)
                    //                             .setSelectedLanguages(
                    //                                 TiktokAdGroupCubit.get(
                    //                                         context)
                    //                                     .languages[index]);
                    //
                    //                         // TiktokCampaignCubit.get(context).undoDemoProcess4();
                    //                         // TiktokCampaignCubit.get(context).updateAdSetProcess9();
                    //
                    //                         print("asdasda" +
                    //                             TiktokAdGroupCubit.get(context)
                    //                                 .languages
                    //                                 .toString());
                    //                       },
                    //                       child: Container(
                    //                         alignment: Alignment.center,
                    //                         padding: EdgeInsets.zero,
                    //                         decoration: ShapeDecoration(
                    //                           color: Colors.white,
                    //                           shape: RoundedRectangleBorder(
                    //                               borderRadius:
                    //                                   BorderRadius.circular(8)),
                    //                           shadows: const [
                    //                             BoxShadow(
                    //                               color: Color(0x33000000),
                    //                               blurRadius: 20,
                    //                               offset: Offset(0, 0),
                    //                               spreadRadius: -6,
                    //                             )
                    //                           ],
                    //                         ),
                    //                         child: Padding(
                    //                           padding:
                    //                               const EdgeInsets.symmetric(
                    //                                   horizontal: 10.0,
                    //                                   vertical: 12),
                    //                           child: Row(
                    //                             crossAxisAlignment:
                    //                                 CrossAxisAlignment.center,
                    //                             mainAxisAlignment:
                    //                                 MainAxisAlignment
                    //                                     .spaceBetween,
                    //                             children: [
                    //                               Center(
                    //                                 child: ShaderMask(
                    //                                   shaderCallback:
                    //                                       (Rect bounds) {
                    //                                     return const LinearGradient(
                    //                                       colors: [
                    //                                         Color(0xFFFF006F),
                    //                                         Color(0xFFF6BA00),
                    //                                       ],
                    //                                     ).createShader(bounds);
                    //                                   },
                    //                                   child: CustomText(
                    //                                     text: TiktokAdGroupCubit
                    //                                                 .get(
                    //                                                     context)
                    //                                             .languages[
                    //                                                 index]
                    //                                             .name ??
                    //                                         "",
                    //                                     color: Colors.white,
                    //                                     fontSize: 14,
                    //                                     fontWeight:
                    //                                         FontWeight.w400,
                    //                                   ),
                    //                                 ),
                    //                               ),
                    //                               ShaderMask(
                    //                                 shaderCallback:
                    //                                     (Rect bounds) {
                    //                                   return const LinearGradient(
                    //                                     colors: [
                    //                                       Color(0xFFFF006F),
                    //                                       Color(0xFFF6BA00),
                    //                                     ],
                    //                                   ).createShader(bounds);
                    //                                 },
                    //                                 child: const Icon(
                    //                                   Icons.check_circle,
                    //                                   size: 22,
                    //                                   color: Colors.white,
                    //                                 ),
                    //                               )
                    //                             ],
                    //                           ),
                    //                         ),
                    //                       ),
                    //                     ),
                    //                   );
                    //                 },
                    //               ),
                    //             ],
                    //           );
                    //   },
                    // ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 235.w,
              child: ButtonWidget(
                text: "Save".tr,
                onTap: () {
                  if (TiktokCampaignCubit.get(context)
                              .selectedAgeModelIds
                              ?.isNotEmpty ==
                          true &&
                      TiktokCampaignCubit.get(context).selectedIndex != null &&
                      TiktokAdGroupCubit.get(context)
                              .selectedLanguages
                              .isNotEmpty ==
                          true) {
                    TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage =
                        TiktokAdGroupCubit.get(context)
                                .tiktokAdGroupPercentage +
                            0.11111111111111;
                    print(
                        'tiktokCampaignPercentage ${TiktokCampaignCubit.get(context).tiktokCampaignPercentage}');
                    setState(() {});
                  } else {
                    TiktokCampaignCubit.get(context).tiktokCampaignPercentage =
                        TiktokCampaignCubit.get(context)
                                .tiktokCampaignPercentage -
                            0.11111111111111;
                    print(
                        'tiktokCampaignPercentage ${TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage}');
                    setState(() {});
                  }
                  // if (widget.createAdCubit.selectedMinAge == null) {
                  //   showErrorToast("please select min age".tr);
                  // } else if (widget.createAdCubit.selectedMaxAge ==
                  //     null) {
                  //   showErrorToast("please select max age".tr);
                  // } else if (widget.createAdCubit.selectedMaxAge! <
                  //     18 ||
                  //     widget.createAdCubit.selectedMinAge! < 18) {
                  //   showErrorToast("Valid age must be over or 18".tr);
                  // } else if (widget.createAdCubit.selectedMaxAge! <
                  //     widget.createAdCubit.selectedMinAge!) {
                  //   showErrorToast(
                  //       "Min age must be less than max age".tr);
                  // } else if (widget.createAdCubit.genders.isEmpty) {
                  //   showErrorToast("please select the gender".tr);
                  // } else if (widget.createAdCubit.languages.isEmpty) {
                  //   showErrorToast("please select the languages".tr);
                  // } else {
                  //   widget.createAdCubit.adModel =
                  //       CreateAdCubit.get(context).adModel.copyWith(
                  //           ageMin: widget.createAdCubit.selectedMinAge,
                  //           ageMax: widget.createAdCubit.selectedMaxAge,
                  //           languages: widget.createAdCubit.languages,
                  //           genders: widget.createAdCubit.genders);
                  //   print("testadasd" +
                  //       widget.createAdCubit.adModel
                  //           .toJson()
                  //           .toString());
                  Navigator.of(context).pop();
                  // }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

List<String> genders = ["Male".tr, "Female".tr, "All genders".tr];

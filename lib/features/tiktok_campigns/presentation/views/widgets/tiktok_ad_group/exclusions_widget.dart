import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';

class ExclusionsWidget extends StatelessWidget {
  final String name;
  final int index;
  final bool isSelected; // Determine if this ObjectiveWidget is selected
  final void Function(int) callback;
  const ExclusionsWidget({
    super.key,
    required this.name,
    required this.index,
    required this.isSelected,
    required this.callback,
  });
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        callback(index);
      },
      child: Container(
        width: 85.h,
        height: 70.h,
        decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(25.r),
            color: Colors.white,
            boxShadow: Constants.unSelectedShadow,
            gradient: isSelected ? Constants.defGradient : null
        ),

        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: CustomText(
            text: name,
            fontSize: 12.sp,
            maxLines: 2,
            fontWeight: FontWeight.w600,
            color: isSelected ?   AppColors.white:AppColors.iconBottomColor,
            textAlign: TextAlign.center,
            alignment: AlignmentDirectional.center,
          ),
        ),
      ),
    );
  }


}
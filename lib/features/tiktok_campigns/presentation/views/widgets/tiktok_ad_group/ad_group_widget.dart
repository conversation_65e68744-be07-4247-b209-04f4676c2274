import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_ad_group/tiktok_new_group_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';

class AdGroupWidget extends StatefulWidget {
  const AdGroupWidget({super.key});

  @override
  State<AdGroupWidget> createState() => _AdGroupWidgetState();
}

class _AdGroupWidgetState extends State<AdGroupWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TiktokAdGroupCubit(),
      child: BlocBuilder<TiktokAdGroupCubit, TiktokAdGroupState>(
        builder: (groupContext, groupState) {
          return ExpansionTileBorderItem(
            onExpansionChanged: (val) {
              TiktokAdGroupCubit.get(groupContext)
                  .setCampaignExpansionState(val);
            },
            expansionKey: Constants.tiktokAdGroupTileKey,
            childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
            iconColor: AppColors.secondColor,
            collapsedIconColor: AppColors.secondColor,
            expandedAlignment: Alignment.center,
            expandedCrossAxisAlignment: CrossAxisAlignment.center,
            leading:
                SvgPicture.asset(AppAssets.adset, color: AppColors.mainColor),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                BlocBuilder<TiktokAdGroupCubit, TiktokAdGroupState>(
                  builder: (context, state) {
                    return CircularPercentIndicator(
                      circularStrokeCap: CircularStrokeCap.round,
                      radius: 12.0,
                      lineWidth: 5.5,
                      percent: TiktokAdGroupCubit.get(context)
                          .tiktokAdGroupPercentage,
                      linearGradient: Constants.secGradient,
                      backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                      reverse: true,
                    );
                  },
                ),
                const Padding(
                  padding: EdgeInsets.only(left: 10.0),
                  child: CustomText(
                    text: "|",
                    color: AppColors.mainColor,
                    fontSize: 35,
                    fontWeight: FontWeight.w200,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 6.0),
                  child: ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return Constants.secGradient.createShader(bounds);
                    },
                    child: Icon(
                      TiktokAdGroupCubit.get(groupContext)
                              .isCampaignTileExpanded
                          ? Icons.expand_less
                          : Icons.expand_more,
                      size: 24.0,
                      color: Colors
                          .white, // This color will be replaced by the gradient
                    ),
                  ),
                )
              ],
            ),
            title: const CustomText(
                text: 'Ad Group',
                color: AppColors.mainColor,
                fontSize: 22,
                fontWeight: FontWeight.w700),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppColors.mainColor)),
            children: [
              const SizedBox(height: 20),
              Container(
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(40),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x3D000000),
                      blurRadius: 13.93,
                      offset: Offset(0, 0),
                      spreadRadius: -3.80,
                    )
                  ],
                ),
                child: const SizedBox(),
              ),
              TiktokNewGroupWidget(
                expansionTileKey: Constants.tiktokAdGroupTileKey,
              ),
              const SizedBox(height: 25),
            ],
          );
        },
      ),
    );
  }
}

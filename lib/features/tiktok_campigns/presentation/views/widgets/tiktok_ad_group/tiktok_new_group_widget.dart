import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/location_helper/location_helper.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/router/routes.dart';
import '../../../../../../utils/res/validations.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_switch.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import '../../../../../../widgets/text_field_widget.dart';
import '../../../../../../widgets/unfinished_target_widget.dart';
import '../../../controllers/tiktok_ad/tiktok_ad_cubit.dart';
import '../../../controllers/tiktok_campain/tiktok_campaign_cubit.dart';

class TiktokNewGroupWidget extends StatefulWidget {
  GlobalKey<ExpansionTileCustomState> expansionTileKey;

  TiktokNewGroupWidget({super.key, required this.expansionTileKey});

  @override
  State<TiktokNewGroupWidget> createState() => _TiktokNewGroupWidgetState();
}

class _TiktokNewGroupWidgetState extends State<TiktokNewGroupWidget> {
  @override
  void initState() {
    TiktokAdCubit.get(context).isAutoMaticVisible();
    // isAutoMaticVisible();
    // setState(() {});
    print(
        'ifStatements ${TiktokAdCubit
            .get(context)
            .isAutoMaticVisibleBool} ${TiktokAdCubit
            .get(context)
            .tiktokAdModel
            .objectiveType} ${TiktokAdCubit
            .get(context)
            .tiktokAdModel
            .optimizationGoal}');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokAdGroupCubit, TiktokAdGroupState>(
      builder: (ctx1, camState) {
        return Column(
          children: [
            const SizedBox(height: 20),
            Form(
              key: TiktokAdGroupCubit
                  .get(ctx1)
                  .groupFormKey,
              child: CustomTextFormField(
                label: "Ad Group Name",
                showIsReqiredFlag: true,
                textFontSize: 12,
                key: const ValueKey('Group_name'),
                hintText: "Group Name",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                controller: TiktokAdGroupCubit
                    .get(ctx1)
                    .groupNameController,
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (va) {
                  if (TiktokAdGroupCubit
                      .get(ctx1)
                      .groupNameController
                      .text
                      .isNotEmpty ==
                      true) {
                    TiktokAdGroupCubit
                        .get(ctx1)
                        .tiktokAdGroupPercentage =
                    0.11111111111111;
                    print(
                        'tiktokAdGroupPercentage ${TiktokAdGroupCubit
                            .get(context)
                            .tiktokAdGroupPercentage}');
                    setState(() {});
                  } else {
                    TiktokCampaignCubit
                        .get(ctx1)
                        .tiktokCampaignPercentage =
                    0.11111111111111;
                    print(
                        'tiktokAdGroupPercentage ${TiktokAdGroupCubit
                            .get(context)
                            .tiktokAdGroupPercentage}');
                    setState(() {});
                  }
                },
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: CustomText(
                text: 'Placements'.tr,
                color: Constants.primaryTextColor,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            BlocBuilder<TiktokAdCubit, TiktokAdState>(
              builder: (context, state) {
                return Visibility(
                  visible: TiktokAdCubit
                      .get(context)
                      .isAutoMaticVisibleBool,
                  child: Container(
                    width: double.infinity,
                    padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: const BorderSide(
                            width: 1, color: Color(0xFF0B0F26)),
                        borderRadius: BorderRadius.circular(50),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            // const Icon(
                            //   Icons.brightness_auto_rounded,
                            //   size: 50,
                            // ),
                            Image.asset(
                              "assets/images/Automatic.png",
                              height: 30,
                              width: 30,
                            ),
                            // CustomSvgWidget(
                            //   svg: AppAssets.fbFill,
                            //   height: 30.h,
                            //   width: 30.h,
                            // ),
                            10.horizontalSpace,
                            CustomText(
                              text: 'Automatic'.tr,
                              color: Constants.primaryTextColor,
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w700,
                            )
                          ],
                        ),
                        CustomSwitch(
                          value: TiktokAdGroupCubit
                              .get(context)
                              .isAutoActive,
                          onChanged: (newValue) {
                            TiktokAdGroupCubit.get(context)
                                .automaticPlacementsStatus(newValue);
                            if (TiktokAdGroupCubit
                                .get(ctx1)
                                .isManualActive ==
                                true) {
                              TiktokAdGroupCubit
                                  .get(ctx1)
                                  .tiktokAdGroupPercentage =
                                  TiktokAdGroupCubit
                                      .get(ctx1)
                                      .tiktokAdGroupPercentage +
                                      0.11111111111111;
                              print(
                                  'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                      .get(context)
                                      .tiktokAdGroupPercentage}');
                              setState(() {});
                            } else {
                              TiktokCampaignCubit
                                  .get(ctx1)
                                  .tiktokCampaignPercentage =
                                  TiktokCampaignCubit
                                      .get(ctx1)
                                      .tiktokCampaignPercentage -
                                      0.11111111111111;
                              print(
                                  'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                      .get(context)
                                      .tiktokAdGroupPercentage}');
                              setState(() {});
                            }
                            // print("kakfkajkl" +
                            //     TiktokAdGroupCubit.get(context)
                            //         .publisherPlatforms
                            //         .toString());
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                  side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                  borderRadius: BorderRadius.circular(50),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Image.asset(
                        "assets/images/Manual.png",
                        height: 30,
                        width: 30,
                      ),
                      // const Icon(
                      //   Icons.handshake,
                      //   size: 50,
                      // ),
                      // CustomSvgWidget(
                      //   svg: AppAssets.fbFill,
                      //   height: 30.h,
                      //   width: 30.h,
                      // ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Manual'.tr,
                        color: Constants.primaryTextColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w700,
                      )
                    ],
                  ),
                  CustomSwitch(
                    value: TiktokAdGroupCubit
                        .get(context)
                        .isManualActive,
                    onChanged: (newValue) {
                      TiktokAdGroupCubit.get(context)
                          .manualPlacemnetsStatus(newValue);
                      if (TiktokAdGroupCubit
                          .get(ctx1)
                          .isManualActive == true) {
                        TiktokAdGroupCubit
                            .get(ctx1)
                            .tiktokAdGroupPercentage =
                            TiktokAdGroupCubit
                                .get(ctx1)
                                .tiktokAdGroupPercentage +
                                0.11111111111111;
                        print(
                            'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                .get(context)
                                .tiktokAdGroupPercentage}');
                        setState(() {});
                      } else {
                        TiktokCampaignCubit
                            .get(ctx1)
                            .tiktokCampaignPercentage =
                            TiktokCampaignCubit
                                .get(ctx1)
                                .tiktokCampaignPercentage -
                                0.11111111111111;
                        print(
                            'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                .get(context)
                                .tiktokAdGroupPercentage}');
                        setState(() {});
                      }
                      // print("kakfkajkl" +
                      //     CreateAdCubit.get(context)
                      //         .publisherPlatforms
                      //         .toString());
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Visibility(
              visible: TiktokAdGroupCubit
                  .get(context)
                  .isManualActive,
              child: Column(
                children: [
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                    const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 4,
                      crossAxisSpacing: 12.0,
                      mainAxisSpacing: 12.0,
                    ),
                    itemBuilder: (item, index) {
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 1, color: Color(0xFF06398A)),
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(2.0),
                                child: SizedBox(
                                  width: 200,
                                  child: FittedBox(
                                    child: CustomText(
                                      text: TiktokAdGroupCubit
                                          .get(context)
                                          .tiktokPositions[index]
                                          .name
                                          ?.tr ??
                                          "",
                                      color: Constants.primaryTextColor,
                                      alignment: AlignmentDirectional.center,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Checkbox(
                              onChanged: (value) {
                                TiktokAdGroupCubit
                                    .get(context)
                                    .tiktokPositions[index]
                                    .isChecked =
                                value!; // Toggle the checkbox value

                                TiktokAdGroupCubit.get(context)
                                    .setSelectedFbPositions();
                                if (TiktokAdGroupCubit
                                    .get(ctx1)
                                    .tiktokSelectedPositions
                                    .isNotEmpty ==
                                    true) {
                                  TiktokAdGroupCubit
                                      .get(ctx1)
                                      .tiktokAdGroupPercentage =
                                      TiktokAdGroupCubit
                                          .get(ctx1)
                                          .tiktokAdGroupPercentage +
                                          0.11111111111111;
                                  print(
                                      'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                          .get(context)
                                          .tiktokAdGroupPercentage}');
                                  setState(() {});
                                } else {
                                  TiktokCampaignCubit
                                      .get(ctx1)
                                      .tiktokCampaignPercentage =
                                      TiktokCampaignCubit
                                          .get(ctx1)
                                          .tiktokCampaignPercentage -
                                          0.11111111111111;
                                  print(
                                      'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                          .get(context)
                                          .tiktokAdGroupPercentage}');
                                  setState(() {});
                                }
                                // print("fbPositions" +
                                //     CreateAdCubit.get(context)
                                //         .fbPositions
                                //         .toString());
                              },
                              value: TiktokAdGroupCubit
                                  .get(context)
                                  .tiktokPositions[index]
                                  .isChecked,
                              activeColor: Constants.primaryTextColor,
                              checkColor: Colors.white,
                              // Color of the checkmark
                              focusColor: Constants.primaryTextColor,
                              // Color of the border when focused
                              side: const BorderSide(
                                color: Constants.primaryTextColor,
                                width: 2,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    itemCount:
                    TiktokAdGroupCubit
                        .get(context)
                        .tiktokPositions
                        .length,
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
            const SizedBox(height: 20),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            const SizedBox(height: 20),
            BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
              builder: (context, state) {
                return InkWell(
                  onTap: () {
                    Future.delayed(const Duration(), () async {
                      // String? mylatLangs =
                      await LocationHelper.getAddressFromCurrentLocation(
                          context);
                      // print('homeLayoutewretgf ${mylatLangs}');
                      if (instance
                          .get<HiveHelper>().getMyCity() != "") {
                        await Navigator.pushNamed(
                          context,
                          Routes.tiktokLocations,
                        );
                      }
                    });
                  },
                  child: UnFinishedTargetWidget(
                    name: "Location".tr,
                    icon: AppAssets.target,
                    processPercentage: CircularPercentIndicator(
                      circularStrokeCap: CircularStrokeCap.round,
                      radius: 12.0,
                      lineWidth: 5.5,
                      percent:
                      TiktokCampaignCubit
                          .get(context)
                          .locationPercentage,
                      linearGradient: Constants.secGradient,
                      backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                      reverse: true,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
              builder: (context, state) {
                return InkWell(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      Routes.tiktokDemographics,
                    );
                  },
                  child: UnFinishedTargetWidget(
                    name: "Demographic".tr,
                    icon: AppAssets.target,
                    processPercentage: CircularPercentIndicator(
                      circularStrokeCap: CircularStrokeCap.round,
                      radius: 12.0,
                      lineWidth: 5.5,
                      percent: TiktokCampaignCubit
                          .get(context)
                          .demographicPercentage,
                      linearGradient: Constants.secGradient,
                      backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                      reverse: true,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
              builder: (context, state) {
                return InkWell(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      Routes.tiktokAudienceInterests,
                    );
                  },
                  child: UnFinishedTargetWidget(
                    name: "Audience Interests",
                    icon: AppAssets.target,
                    processPercentage: CircularPercentIndicator(
                      circularStrokeCap: CircularStrokeCap.round,
                      radius: 12.0,
                      lineWidth: 5.5,
                      percent:
                      TiktokCampaignCubit
                          .get(context)
                          .audiencePercentage,
                      linearGradient: Constants.secGradient,
                      backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                      reverse: true,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: CustomText(
                text: 'Budget Modes'.tr,
                color: Constants.primaryTextColor,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            buildBudgetModeRadio(),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  text: "Daily Budget",
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: Constants.primaryTextColor,
                ),
                Container(
                  width: 125.w,
                  height: 42.h,
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x33000000),
                        blurRadius: 20,
                        offset: Offset(0, 0),
                        spreadRadius: -4,
                      )
                    ],
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 80.w,
                        child: CustomTextField(
                          textInputType: TextInputType.number,
                          borderColor: Colors.transparent,
                          hintText: "00.00",
                          maxLength: 5,
                          hintStyle: const TextStyle(
                              fontSize: 16, color: Constants.gray),
                          controller: TiktokAdGroupCubit
                              .get(ctx1)
                              .dailyBudget,
                          onChanged: (val) {
                            if (TiktokAdGroupCubit
                                .get(ctx1)
                                .dailyBudget
                                .text
                                .isNotEmpty) {
                              if (TiktokAdGroupCubit
                                  .get(context)
                                  .dailyBudget
                                  .text
                                  .isNotEmpty ==
                                  true) {
                                TiktokAdGroupCubit
                                    .get(context)
                                    .tiktokAdGroupPercentage =
                                    TiktokAdGroupCubit
                                        .get(context)
                                        .tiktokAdGroupPercentage +
                                        0.11111111111111;
                                print(
                                    'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                        .get(context)
                                        .tiktokAdGroupPercentage}');
                                setState(() {});
                              } else {
                                TiktokCampaignCubit
                                    .get(context)
                                    .tiktokCampaignPercentage =
                                    TiktokCampaignCubit
                                        .get(context)
                                        .tiktokCampaignPercentage -
                                        0.11111111111111;
                                print(
                                    'tiktokCampaignPercentage ${TiktokAdGroupCubit
                                        .get(context)
                                        .tiktokAdGroupPercentage}');
                                setState(() {});
                              }
                            } else {}
                          },
                          validator: (value) =>
                              AppValidator.validateIdentity(value, context),
                        ),
                      ),
                      const VerticalDivider(
                        width: 2,
                        color: Constants.primaryTextColor,
                      ),
                      SizedBox(
                          width: 35.w,
                          child: FittedBox(
                            child: Padding(
                              padding: const EdgeInsets.all(6.0),
                              child: CustomText(
                                text: "AED",
                                fontSize: 14.sp,
                                alignment: AlignmentDirectional.center,
                                fontWeight: FontWeight.w400,
                                color: Constants.primaryTextColor,
                              ),
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            SizedBox(height: 15.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CustomText(
                      text: "Start Date",
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: Constants.primaryTextColor,
                    ),
                    10.horizontalSpace,
                    GestureDetector(
                      onTap: () {
                        _showDateTimePicker(context,
                            TiktokAdGroupCubit
                                .get(context)
                                .startDate, true);
                      },
                      child: AbsorbPointer(
                        child: Container(
                          width: 80.w,
                          height: 40.h,
                          decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            shadows: const [
                              BoxShadow(
                                color: Color(0x33000000),
                                blurRadius: 20,
                                offset: Offset(0, 0),
                                spreadRadius: -4,
                              )
                            ],
                          ),
                          child: CustomTextField(
                            onSaved: (val) {},
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                            textInputType: TextInputType.number,
                            borderColor: Colors.transparent,
                            hintText: "dd/mm/yy",
                            style: const TextStyle(
                                fontSize: 10, color: Colors.black),
                            hintStyle: const TextStyle(
                                fontSize: 12, color: Constants.gray),
                            controller:
                            TiktokAdGroupCubit
                                .get(context)
                                .startDate,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    CustomText(
                      text: "End Date",
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: Constants.primaryTextColor,
                    ),
                    10.horizontalSpace,
                    GestureDetector(
                      onTap: () {
                        _showDateTimePicker(context,
                            TiktokAdGroupCubit
                                .get(context)
                                .endDate, false);
                      },
                      child: AbsorbPointer(
                        child: Container(
                          width: 80.w,
                          height: 40.h,
                          decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            shadows: const [
                              BoxShadow(
                                color: Color(0x33000000),
                                blurRadius: 20,
                                offset: Offset(0, 0),
                                spreadRadius: -4,
                              )
                            ],
                          ),
                          child: CustomTextField(
                            textInputType: TextInputType.number,
                            borderColor: Colors.transparent,

                            hintText: "dd/mm/yy",
                            style: const TextStyle(
                                fontSize: 10, color: Colors.black),
                            hintStyle: const TextStyle(
                                fontSize: 12, color: Constants.gray),
                            controller: TiktokAdGroupCubit
                                .get(context)
                                .endDate,
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                            // TextField properties
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 15.h),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            SizedBox(height: 20.h),
            SizedBox(
              width: 235.w,
              child: ButtonWidget(
                text: "Save",
                onTap: () {
                  if (TiktokAdGroupCubit
                      .get(ctx1)
                      .groupFormKey
                      .currentState!
                      .validate()) {
                    print(
                        'valuesOnNull ${TiktokAdCubit
                            .get(context)
                            .tiktokAdModel
                            .campaignName}');
                    if (TiktokAdGroupCubit
                        .get(ctx1)
                        .endDate
                        .text == "" &&
                        TiktokAdGroupCubit
                            .get(ctx1)
                            .selectedBudgetMode ==
                            'BUDGET_MODE_TOTAL' ||
                        TiktokAdGroupCubit
                            .get(ctx1)
                            .selectedBudgetMode ==
                            'BUDGET_MODE_DYNAMIC_DAILY_BUDGET') {
                      print(
                          'whathappenedhere ${TiktokAdCubit
                              .get(context)
                              .tiktokAdModel
                              .dailyBudgetMode}');
                      return showErrorToast('should select end date');
                    }
                    if (TiktokCampaignCubit
                        .get(ctx1)
                        .selectedTiktokInterests
                        .isEmpty) {
                      showErrorToast("Please select your interests");
                      return;
                    }
                    widget.expansionTileKey.currentState?.collapse();
                    TiktokAdCubit
                        .get(ctx1)
                        .tiktokAdModel = TiktokAdCubit
                        .get(ctx1)
                        .tiktokAdModel
                        .copyWith(
                        adGroupName: TiktokAdGroupCubit
                            .get(ctx1)
                            .groupNameController
                            .text,
                        dailyBudget:
                        TiktokAdGroupCubit
                            .get(ctx1)
                            .dailyBudget
                            .text,
                        startDate:
                        DateTime.parse(TiktokAdGroupCubit
                            .get(ctx1)
                            .startDate
                            .text ?? "")
                            .format('yyyy-MM-dd HH:mm:ss'),
                        endDate: TiktokAdGroupCubit
                            .get(ctx1)
                            .endDate
                            .text != ""
                            ? DateTime.parse(TiktokAdGroupCubit
                            .get(ctx1)
                            .endDate
                            .text ?? "")
                            .format('yyyy-MM-dd HH:mm:ss')
                            : "",
                        location: TiktokCampaignCubit
                            .get(context)
                            .selectedTiktokLocationIds ??
                            [],
                        age: TiktokCampaignCubit
                            .get(context)
                            .selectedAgeModelIds ??
                            [],
                        gender: TiktokCampaignCubit
                            .get(context)
                            .gender,
                        selectedTiktokInterests: TiktokCampaignCubit
                            .get(context)
                            .selectedTiktokInterestsIds,
                        placementType: TiktokAdGroupCubit
                            .get(ctx1)
                            .placementType,
                        palcementsPositions: TiktokAdGroupCubit
                            .get(ctx1)
                            .tiktokSelectedPositions,
                        dailyBudgetMode: TiktokAdGroupCubit
                            .get(context)
                            .selectedBudgetMode);
                    TiktokAdCubit
                        .get(context)
                        .isTiktokAdGroup = true;
                    print(
                        'isTiktokAdGroup ${TiktokAdCubit
                            .get(context)
                            .isTiktokAdGroup}');
                    // TiktokAdCubit.get(context).createAD(
                    //     context: context,
                    //     imagesFiles: [],
                    //     videosFiles: [],
                    //     advertiserId:
                    //     instance.get<HiveHelper>().getAdvertiserId() ?? "",
                    //     campaignName: TiktokAdCubit
                    //         .get(context)
                    //         .tiktokAdModel
                    //         .campaignName ??
                    //         "",
                    //     objectiveType:
                    //     TiktokAdCubit
                    //         .get(context)
                    //         .tiktokAdModel
                    //         .objectiveType ??
                    //         "",
                    //     optimizationGoal:
                    //     TiktokAdCubit
                    //         .get(context)
                    //         .tiktokAdModel
                    //         .optimizationGoal ??
                    //         "",
                    //     adGroupName: TiktokAdGroupCubit
                    //         .get(context)
                    //         .groupNameController
                    //         .text,
                    //     location: TiktokCampaignCubit
                    //         .get(context)
                    //         .selectedTiktokLocation
                    //         ?.first
                    //         .geo
                    //         ?.parentId ??
                    //         "",
                    //     age: TiktokCampaignCubit
                    //         .get(context)
                    //         .selectedAgeModel
                    //         ?.ageId ??
                    //         "",
                    //     gender: TiktokCampaignCubit
                    //         .get(context)
                    //         .selectedIndex!,
                    //     languages:
                    //     // TiktokAdGroupCubit.get(context).languages.first.code ??
                    //     "ar",
                    //     dailyBudget:
                    //     TiktokAdGroupCubit
                    //         .get(ctx1)
                    //         .dailyBudget
                    //         .text,
                    //     startDate: DateTime.parse(TiktokAdGroupCubit
                    //         .get(ctx1)
                    //         .startDate
                    //         .text ?? "")
                    //         .format('yyyy-MM-dd HH:mm:ss'),
                    //     endDate: DateTime.parse(TiktokAdGroupCubit
                    //         .get(ctx1)
                    //         .endDate
                    //         .text ?? "")
                    //         .format('yyyy-MM-dd HH:mm:ss'),
                    //     selectedTiktokInterests:
                    //     TiktokAdCubit
                    //         .get(context)
                    //         .tiktokAdModel
                    //         .selectedTiktokInterests ?? []);
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDateTimePicker(BuildContext context,
      TextEditingController controller,
      bool isStartDate,) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Constants.primaryTextColor,
              onPrimary: Colors.white,
              onSurface: Constants.primaryTextColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Constants.primaryTextColor,
              ),
            ),
          ),
          child: child ?? const Text(""),
        );
      },
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      final DateTime now = DateTime.now();
      final bool isToday = pickedDate.year == now.year &&
          pickedDate.month == now.month &&
          pickedDate.day == now.day;

      TimeOfDay initialTime = isToday
          ? TimeOfDay.fromDateTime(now)
          : const TimeOfDay(hour: 0, minute: 0);

      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: initialTime,
        builder: (BuildContext context, Widget? child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: const ColorScheme.light(
                primary: Constants.primaryTextColor,
                onPrimary: Colors.white,
                onSurface: Constants.primaryTextColor,
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  foregroundColor: Constants.primaryTextColor,
                ),
              ),
            ),
            child: child ?? const Text(""),
          );
        },
      );

      if (pickedTime == null) {
        return; // User canceled time selection
      }

      final combinedDateTime = DateTime(
        pickedDate.year,
        pickedDate.month,
        pickedDate.day,
        pickedTime.hour,
        pickedTime.minute,
      );

      // Check if selected datetime is in the past
      if (combinedDateTime.isBefore(DateTime.now())) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text("Invalid Date/Time"),
              content:
              const Text("Selected date and time cannot be in the past."),
              actions: [
                TextButton(
                  onPressed: () {
                    TiktokAdGroupCubit
                        .get(context)
                        .tiktokAdGroupPercentage =
                        TiktokAdGroupCubit
                            .get(context)
                            .tiktokAdGroupPercentage +
                            0.11111111111111;
                    print(
                        'tiktokCampaignPercentage ${TiktokAdGroupCubit
                            .get(context)
                            .tiktokAdGroupPercentage}');
                    setState(() {});
                                      Navigator.pop(context);
                  },
                  child: const Text("OK"),
                ),
              ],
            );
          },
        );
        return;
      }

      // Validate against existing end or start date
      final cubit = TiktokAdGroupCubit.get(context);
      if (isStartDate) {
        final endDateText = cubit.endDate.text;
        if (endDateText.isNotEmpty) {
          try {
            final endDate = DateFormat("yyyy-MM-dd HH:mm").parse(endDateText);
            if (combinedDateTime.isAfter(endDate)) {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text("Invalid Date"),
                    content: const Text("Start date cannot be after end date."),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text("OK"),
                      ),
                    ],
                  );
                },
              );
              return;
            }
          } on FormatException {
            print('Error parsing end date');
          }
        }
      } else {
        final startDateText = cubit.startDate.text;
        if (startDateText.isNotEmpty) {
          try {
            final startDate =
            DateFormat("yyyy-MM-dd HH:mm").parse(startDateText);
            if (combinedDateTime.isBefore(startDate)) {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text("Invalid Date"),
                    content:
                    const Text("End date cannot be before start date."),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text("OK"),
                      ),
                    ],
                  );
                },
              );
              return;
            }
          } on FormatException {
            print('Error parsing start date');
          }
        }
      }

      // Update the controller with formatted datetime
      final formattedDate =
      DateFormat("yyyy-MM-dd HH:mm").format(combinedDateTime);
      controller.text = formattedDate;

      // Additional logic based on isStartDate if needed
      if (isStartDate) {
        // Handle start date specific actions
      } else {
        // Handle end date specific actions
      }
    }
  }

  Widget buildBudgetModeRadio() {
    const options = [
      'BUDGET_MODE_DAY',
      'BUDGET_MODE_TOTAL',
      'BUDGET_MODE_DYNAMIC_DAILY_BUDGET',
    ];

    return Column(
      children: options.map((mode) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: ListTile(
            dense: true,
            contentPadding: EdgeInsets.zero,
            leading: Radio<String>(
              value: mode,
              groupValue: TiktokAdGroupCubit
                  .get(context)
                  .selectedBudgetMode,
              onChanged: (String? value) {
                TiktokAdGroupCubit.get(context)
                    .setSelectedBudgetMode(value ?? "");
                // setState(() {
                //   _selectedBudgetMode = value;
                // });
              },
            ),
            title: Text(
              _getDisplayText(mode),
              style: TextStyle(
                fontSize: 16,
                color:
                TiktokAdGroupCubit
                    .get(context)
                    .selectedBudgetMode == mode
                    ? Colors.blue.shade700
                    : Colors.black87,
              ),
            ),
            onTap: () {
              TiktokAdGroupCubit.get(context).setSelectedBudgetMode(mode ?? "");
              if (TiktokAdGroupCubit
                  .get(context)
                  .selectedBudgetMode != null) {
                TiktokAdGroupCubit
                    .get(context)
                    .tiktokAdGroupPercentage =
                    TiktokAdGroupCubit
                        .get(context)
                        .tiktokAdGroupPercentage +
                        0.11111111111111;
                print(
                    'tiktokCampaignPercentage ${TiktokCampaignCubit
                        .get(context)
                        .tiktokCampaignPercentage}');
                setState(() {});
              }
              // else {
              //   TiktokCampaignCubit.get(context).tiktokCampaignPercentage =
              //       TiktokCampaignCubit.get(context).tiktokCampaignPercentage -
              //           0.11111111111111;
              //   print(
              //       'tiktokCampaignPercentage ${TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage}');
              //   setState(() {});
              // }
              // setState(() {
              //   _selectedBudgetMode = mode;
              // });
            },
          ),
        );
      }).toList(),
    );
  }

  String _getDisplayText(String mode) {
    switch (mode) {
      case 'BUDGET_MODE_DAY':
        return 'Daily Budget';
      case 'BUDGET_MODE_TOTAL':
        return 'Total Budget';
      case 'BUDGET_MODE_DYNAMIC_DAILY_BUDGET':
        return 'Dynamic Daily Budget';
      default:
        return '';
    }
  }

// void _showDatePicker(BuildContext context, TextEditingController controller,
//     bool isStartDate) async {
//   final DateTime? startDate = await showDatePicker(
//     context: context,
//     builder: (BuildContext context, Widget? child) {
//       return Theme(
//         data: Theme.of(context).copyWith(
//           colorScheme: const ColorScheme.light(
//             primary: Constants.primaryTextColor, // header background color
//             onPrimary: Colors.white, // header text color
//             onSurface: Constants.primaryTextColor, // body text color
//           ),
//           textButtonTheme: TextButtonThemeData(
//             style: TextButton.styleFrom(
//               foregroundColor:
//               Constants.primaryTextColor, // button text color
//             ),
//           ),
//         ),
//         child: child ?? const Text(""),
//       );
//     },
//     initialDate: DateTime.now(),
//     firstDate: DateTime.now(),
//     lastDate: DateTime(2100),
//   );
//
//   if (startDate != null) {
//     // Format the pickedDate as desired (e.g., "yyyy-MM-dd")
//     final formattedDate = DateFormat("yyyy-MM-dd").format(startDate);
//
//     // Check if the picked date is before the delivery date
//     if (TiktokAdGroupCubit
//         .get(context)
//         .endDate
//         .text
//         .isNotEmpty) {
//       final deliveryDate = DateFormat("yyyy-MM-dd")
//           .parse(TiktokAdGroupCubit
//           .get(context)
//           .endDate
//           .text);
//       if (startDate.isBefore(deliveryDate)) {
//         showDialog(
//           context: context,
//           builder: (BuildContext context) {
//             return AlertDialog(
//               title: const Text("Invalid Date"),
//               content: const Text("End Date can't be before start date"),
//               actions: [
//                 TextButton(
//                   child: const Text("OK"),
//                   onPressed: () {
//                     Navigator.of(context).pop();
//                   },
//                 ),
//               ],
//             );
//           },
//         );
//         return;
//       }
//     }
//
//     controller.text = formattedDate;
//     if (isStartDate) {} else {}
//   }
// }
}

import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../widgets/appbar.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../../../../widgets/text_field_widget.dart';
import '../../../controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';

class SelectLocationScreen extends StatefulWidget {
  const SelectLocationScreen({Key? key}) : super(key: key);

  @override
  State<SelectLocationScreen> createState() => _SelectLocationScreenState();
}

class _SelectLocationScreenState extends State<SelectLocationScreen> {
  bool isChecked = false;

  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      TiktokCampaignCubit.get(context).getTiktokLocations(
          context: context,
          advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "",
          searchKey: instance.get<HiveHelper>().getMyCity(),
          objectiveType: "REACH");
    });
    print(
        'tiktokLocationsList ${TiktokCampaignCubit.get(context).tiktokLocations?.result}');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Location".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
        builder: (context, state) {
          return Container(
            color: Colors.white10,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    child: Container(
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(37),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 20,
                            offset: Offset(0, 0),
                            spreadRadius: -4,
                          )
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              onChanged: (val) {},
                              borderColor: Colors.transparent,
                              hintText: "Search for ...".tr,
                              controller: TiktokCampaignCubit.get(context)
                                  .textController,
                              hintStyle: const TextStyle(fontSize: 14),
                              icon: ShaderMask(
                                shaderCallback: (Rect bounds) {
                                  return const LinearGradient(
                                    colors: [
                                      Color(0xFFFF006F),
                                      Color(0xFFF6BA00),
                                    ],
                                  ).createShader(bounds);
                                },
                                child: const Padding(
                                  padding: EdgeInsets.all(12.0),
                                  child: CustomSvgWidget(
                                      width: 13,
                                      height: 13,
                                      svg: AppAssets.search,
                                      color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              TiktokCampaignCubit.get(context)
                                      .textController
                                      .text
                                      .isEmpty
                                  ? null
                                  : TiktokCampaignCubit.get(context)
                                      .getTiktokLocations(
                                          context: context,
                                          advertiserId: instance
                                                  .get<HiveHelper>()
                                                  .getAdvertiserId() ??
                                              "",
                                          searchKey:
                                              TiktokCampaignCubit.get(context)
                                                  .textController
                                                  .text,
                                          objectiveType: "REACH");
                            },
                            child: Container(
                                width: 81.w,
                                padding: EdgeInsets.zero,
                                decoration: const ShapeDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment(-0.99, -0.10),
                                    end: Alignment(0.99, 0.1),
                                    colors: [
                                      Color(0xFF0B0F26),
                                      Color(0xFF1C4294)
                                    ],
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(100),
                                      bottomRight: Radius.circular(100),
                                    ),
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15.0, vertical: 15.0),
                                  child: CustomText(
                                      text: "Search".tr,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white),
                                )),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 40.0.h,
                  ),
                  if (TiktokCampaignCubit.get(context).selectedTiktokLocation !=
                          null ||
                      TiktokCampaignCubit.get(context)
                              .selectedTiktokLocation
                              ?.isEmpty ==
                          false)
                    ListView.builder(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20.0, vertical: 5.0),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: TiktokCampaignCubit.get(context)
                                .selectedTiktokLocation
                                ?.length ??
                            0,
                        itemBuilder: (context, int i) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 30.0, vertical: 5.0),
                            child: ListTile(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15.0),
                                  side: const BorderSide(color: Colors.black)),
                              leading: Text(TiktokCampaignCubit.get(context)
                                      .selectedTiktokLocation![i]
                                      .name ??
                                  ""),
                              trailing: IconButton(
                                icon: const Icon(Icons.delete),
                                onPressed: () {
                                  TiktokCampaignCubit.get(context)
                                      .tiktokLocations
                                      ?.result
                                      ?.firstWhere((loc) =>
                                          loc.geo?.geoId ==
                                          TiktokCampaignCubit.get(context)
                                              .selectedTiktokLocation![i]
                                              .geo
                                              ?.geoId)
                                      .isChecked = false;
                                  TiktokCampaignCubit.get(context)
                                      .removeLocation(
                                          TiktokCampaignCubit.get(context)
                                              .selectedTiktokLocation![i]);
                                  setState(() {});
                                },
                              ),
                            ),
                          );
                        }),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 30.0, vertical: 10.0),
                    child: Card(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15.0),
                          side: const BorderSide(color: Colors.black)),
                      child: ListView.builder(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20.0, vertical: 20.0),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: TiktokCampaignCubit.get(context)
                                  .tiktokLocations
                                  ?.result
                                  ?.length ??
                              0,
                          itemBuilder: (context, int i) {
                            return CheckboxListTile(
                                title: Text(TiktokCampaignCubit.get(context)
                                        .tiktokLocations
                                        ?.result![i]
                                        .name ??
                                    ""),
                                value: TiktokCampaignCubit.get(context)
                                    .tiktokLocations
                                    ?.result![i]
                                    .isChecked,
                                onChanged: (checked) {
                                  TiktokCampaignCubit.get(context)
                                      .tiktokLocations
                                      ?.result![i]
                                      .isChecked = checked!;
                                  TiktokCampaignCubit.get(context)
                                      .selectedLocation(
                                          TiktokCampaignCubit.get(context)
                                              .tiktokLocations
                                              ?.result![i]);
                                  setState(() {});
                                });
                          }),
                    ),
                  ),
                  SafeArea(
                    bottom: true,
                    child: SizedBox(
                      width: 235.w,
                      child: ButtonWidget(
                        text: "Save".tr,
                        onTap: () {
                          if (TiktokCampaignCubit.get(context)
                                  .selectedTiktokLocation
                                  ?.isNotEmpty ==
                              true) {
                            TiktokAdGroupCubit.get(context)
                                    .tiktokAdGroupPercentage =
                                TiktokAdGroupCubit.get(context)
                                        .tiktokAdGroupPercentage +
                                    0.11111111111111;
                            print(
                                'tiktokCampaignPercentage ${TiktokCampaignCubit.get(context).tiktokCampaignPercentage}');
                            setState(() {});
                          } else {
                            TiktokCampaignCubit.get(context)
                                    .tiktokCampaignPercentage =
                                TiktokCampaignCubit.get(context)
                                        .tiktokCampaignPercentage -
                                    0.11111111111111;
                            print(
                                'tiktokCampaignPercentage ${TiktokAdGroupCubit.get(context).tiktokAdGroupPercentage}');
                            setState(() {});
                          }
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

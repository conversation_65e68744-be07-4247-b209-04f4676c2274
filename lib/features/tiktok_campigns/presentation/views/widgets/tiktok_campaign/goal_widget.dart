import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';

import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/svg_widget.dart';

class GoalWidget extends StatelessWidget {
  final String name;
  final String icon;

  final int index;
  final bool isSelected; // Determine if this ObjectiveWidget is selected
  final void Function(int) callback;

  const GoalWidget({
    super.key,
    required this.name,
    required this.icon,
    required this.index,
    required this.isSelected,
    required this.callback,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        callback(index);
      },
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(25.r),
          color: Colors.white,
          boxShadow: Constants.unSelectedShadow,
          border: isSelected
              ? const GradientBoxBorder(
            gradient: Constants.secGradient,
            width: 2,
          )
              : null,
        ),
        //width: 80.h,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              isSelected?   ShaderMask(
                shaderCallback: (Rect bounds) {
                  return Constants.secGradient
                      .createShader(bounds);
                },
                child:CustomSvgWidget(
                svg: icon,
                height: 30,
                color: Colors.white,

              ),):CustomSvgWidget(
                svg: icon,
                height: 30,
                color: isSelected ? AppColors.mainColor : Constants.textColor,

              ),
              5.verticalSpace,
              isSelected?  ShaderMask(
                shaderCallback: (Rect bounds) {
                  return Constants.secGradient
                      .createShader(bounds);
                },
                child: CustomText(
                  text: name,
                  fontSize: 10.sp,
                  maxLines: 2,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: Colors.white,
                  textAlign: TextAlign.center,
                  alignment: AlignmentDirectional.center,
                ),
              ):CustomText(
                text: name,
                fontSize: 10.sp,
                maxLines: 2,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected ? AppColors.mainColor : Constants.textColor,
                textAlign: TextAlign.center,
                alignment: AlignmentDirectional.center,
              )
            ],
          ),
        ),
      ),
    );
  }
}

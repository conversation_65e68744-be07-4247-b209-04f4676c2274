
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_campaign/tiktok_objective_widget.dart';

import 'package:ads_dv/utils/res/validations.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../../controllers/get_objectives/get_tiktok_objectives_cubit.dart';
import '../../../controllers/get_tiktok_optimizations/get_tiktok_optimizations_cubit.dart';
import '../../../controllers/tiktok_ad/tiktok_ad_cubit.dart';

class TiktokNewCampaignWidget extends StatefulWidget {
  GlobalKey<ExpansionTileCustomState> expansionTileKey;

  TiktokNewCampaignWidget({super.key, required this.expansionTileKey});

  @override
  State<TiktokNewCampaignWidget> createState() =>
      _TiktokNewCampaignWidgetState();
}

class _TiktokNewCampaignWidgetState extends State<TiktokNewCampaignWidget> {
  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      await GetTiktokObjectivesCubit.get(context)
          .getObjectives(context: context);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
      builder: (ctx1, camState) {
        return BlocBuilder<GetTiktokOptimizationsCubit,
            GetTiktokOptimizationsState>(
          builder: (ctx2, optState) {
            return Column(
              children: [
                const SizedBox(height: 20),
                Form(
                  key: TiktokCampaignCubit.get(ctx1).campaignFormKey,
                  child: CustomTextFormField(
                    label: "Campaign Name",
                    showIsReqiredFlag: true,
                    textFontSize: 12,
                    key: const ValueKey('campaign_name'),
                    hintText: "Campaign Name",
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    controller:
                        TiktokCampaignCubit.get(ctx1).campaignNameController,
                    validator: (value) =>
                        AppValidator.validateIdentity(value, context),
                    onChanged: (va) {
                      if (TiktokCampaignCubit.get(ctx1)
                              .campaignNameController
                              .text
                              .isNotEmpty ==
                          true) {
                        TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage =
                            0.25;
                        print(
                            'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                        setState(() {});
                      } else {
                        TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage =
                            0.0;
                        print(
                            'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                        setState(() {});
                      }
                    },
                  ),
                ),
                const SizedBox(height: 20),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Divider(color: Constants.textColor),
                ),
                const SizedBox(height: 5),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: CustomText(
                    text: "Select Your Goal",
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    alignment: AlignmentDirectional.centerStart,
                    color: Constants.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 20),
                BlocBuilder<GetTiktokObjectivesCubit, GetTiktokObjectivesState>(
                  builder: (context, tiktokObjState) {
                    if (tiktokObjState is GetTiktokObjectivesStateLoaded) {
                      return AlignedGridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: 3,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        itemCount: tiktokObjState.data.length,
                        itemBuilder: (context, index) {
                          return TiktokObjectiveWidget(
                            // icon: TiktokCampaignCubit.get(ctx1).goals[index].icon,
                            isSelected: index ==
                                TiktokCampaignCubit.get(ctx1).selectedGoal,
                            callback: (_) {
                              TiktokCampaignCubit.get(ctx1).setSelectedGoal(
                                  index, tiktokObjState.data[index]);
                              GetTiktokOptimizationsCubit.get(context)
                                  .getOptimizations(
                                      context: context,
                                      objectiveActualName: tiktokObjState
                                          .data[index].actualName!);
                              if (TiktokCampaignCubit.get(ctx1).selectedGoal !=
                                      null &&
                                  TiktokCampaignCubit.get(ctx1)
                                          .isObjectiveUpdated ==
                                      false) {
                                TiktokCampaignCubit.get(ctx1)
                                    .isObjectiveUpdated = true;
                                TiktokCampaignCubit.get(ctx1)
                                        .tiktokCampaignPercentage =
                                    TiktokCampaignCubit.get(ctx1)
                                            .tiktokCampaignPercentage +
                                        0.50;
                                print(
                                    'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                                setState(() {});
                              }
                            },
                            name: tiktokObjState.data[index].showName ?? "",
                            index: index,
                          );
                        },
                      );
                    } else {
                      return const Center(child: CircularProgressIndicator());
                    }
                  },
                ),
                const SizedBox(height: 20),
                optState is GetTiktokOptimizationsStateLoading
                    ? const LoadingWidget(
                        isCircle: true,
                      )
                    : GetTiktokOptimizationsCubit.get(ctx2).opt.isEmpty
                        ? const SizedBox()
                        : Column(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  children: [
                                    CustomText(
                                      text: "Advertising Objective".tr,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.primaryTextColor,
                                    ),
                                    CustomText(
                                      text: "*",
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.redColor,
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              ExpansionTileItem(
                                expansionKey: Constants.optimizationKey,
                                onExpansionChanged: (val) {},
                                childrenPadding: EdgeInsets.zero,
                                iconColor: AppColors.secondColor,
                                collapsedIconColor: AppColors.secondColor,
                                expandedAlignment: Alignment.center,
                                expandedCrossAxisAlignment:
                                    CrossAxisAlignment.center,
                                trailing: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(left: 6.0),
                                      child: Icon(
                                        Icons.expand_more,
                                        size: 30.0,
                                        color: Constants.darkColor,
                                      ),
                                    )
                                  ],
                                ),
                                title: TiktokCampaignCubit.get(ctx1)
                                            .optimization !=
                                        null
                                    ? CustomText(
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                        text: TiktokCampaignCubit.get(ctx1)
                                                .optimization
                                                ?.showName ??
                                            "")
                                    : AccountHintText(
                                        isDefaultHint: false,
                                        hint: 'Advertising Objective'.tr,
                                      ),
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x3F000000),
                                      blurRadius: 40,
                                      offset: Offset(0, 0),
                                      spreadRadius: -10,
                                    )
                                  ],
                                ),
                                children: [
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount:
                                        GetTiktokOptimizationsCubit.get(ctx2)
                                            .opt
                                            .length,
                                    // 3 for the first text + 3 for the second text
                                    itemBuilder: (context, index) {
                                      // Check if the index is for the first section title
                                      // if (index == 0) {
                                      //   return Padding(
                                      //     padding: const EdgeInsets.all(16.0),
                                      //     child: CustomText(
                                      //       text: 'Drive More Visits Objectives'
                                      //           .tr,
                                      //       fontSize: 13.sp,
                                      //       fontWeight: FontWeight.w600,
                                      //     ),
                                      //   );
                                      // } else
                                      // if (index >=
                                      //       1 &&
                                      //   index <=
                                      //       3)
                                      // {
                                      // Return the first three items
                                      final optIndex =
                                          index; // Adjust index for the optimization list
                                      return InkWell(
                                        onTap: () {
                                          TiktokCampaignCubit.get(ctx1)
                                              .setSelectedOptimization(
                                                  GetTiktokOptimizationsCubit
                                                          .get(ctx2)
                                                      .opt[optIndex]);
                                          if (TiktokCampaignCubit.get(ctx1)
                                                      .optimization !=
                                                  null &&
                                              TiktokCampaignCubit.get(ctx1)
                                                      .isOptimizeUpdated ==
                                                  false) {
                                            TiktokCampaignCubit.get(ctx1)
                                                .isOptimizeUpdated = true;
                                            TiktokCampaignCubit.get(ctx1)
                                                    .tiktokCampaignPercentage =
                                                TiktokCampaignCubit.get(ctx1)
                                                        .tiktokCampaignPercentage +
                                                    0.25;
                                            print(
                                                'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                                            setState(() {});
                                          }
                                          // GetBillingEventsCubit
                                          //         .get(
                                          //             billContext)
                                          //     .getBillingEvents(
                                          //   context:
                                          //       context,
                                          //   optimizationId:
                                          //       GetOptimizationsCubit.get(optContext)
                                          //               .opt[optIndex]
                                          //               .id ??
                                          //           0,
                                          // );
                                          // CreateAdCubit.get(
                                          //         context)
                                          //     .billingEvent = null;
                                          Constants.optimizationKey.currentState
                                              ?.collapse();
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Constants.gray
                                                .withOpacity(0.15),
                                            border: Border.symmetric(
                                              horizontal: BorderSide(
                                                  color: Constants.gray
                                                      .withOpacity(0.3)),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 18, horizontal: 20),
                                            child: CustomText(
                                              maxLines: 3,
                                              fontSize: 12.sp,
                                              text: GetTiktokOptimizationsCubit
                                                          .get(ctx2)
                                                      .opt[optIndex]
                                                      .showName ??
                                                  "",
                                            ),
                                          ),
                                        ),
                                      );
                                      // }
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                // const SizedBox(height: 20),
                // const Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                //   child: Divider(color: Constants.textColor),
                // ),
                // const SizedBox(height: 20),
                // Padding(
                //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     crossAxisAlignment: CrossAxisAlignment.center,
                //     children: [
                //       CustomText(
                //         text: 'Create split test',
                //         fontSize: 12.sp,
                //         color: Constants.primaryTextColor,
                //         fontWeight: FontWeight.w400,
                //         alignment: AlignmentDirectional.centerStart,
                //       ),
                //       CustomSwitch(
                //         value: TiktokCampaignCubit.get(ctx1).isCreateActive,
                //         onChanged: (newValue) {
                //           TiktokCampaignCubit.get(ctx1)
                //               .changeCreateStatus(newValue);
                //         },
                //       ),
                //     ],
                //   ),
                // ),
                SizedBox(height: 50.h),
                SizedBox(
                  width: 235.w,
                  child: ButtonWidget(
                    text: "Save",
                    onTap: () {
                      if (TiktokCampaignCubit.get(ctx1)
                          .campaignFormKey
                          .currentState!
                          .validate()) {
                        widget.expansionTileKey.currentState?.collapse();
                        TiktokAdCubit.get(context).tiktokAdModel =
                            TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                                campaignName: TiktokCampaignCubit.get(ctx1)
                                    .campaignNameController
                                    .text,
                                objectiveType: TiktokCampaignCubit.get(ctx1)
                                    .objective
                                    ?.actualName,
                                optimizationGoal: TiktokCampaignCubit.get(ctx1)
                                    .optimization
                                    ?.actualName);
                        TiktokAdCubit.get(context).isTiktokCampaign = true;
                        // TiktokAdCubit.get(context).createAD(
                        //     context: context,
                        //     imagesFiles: [],
                        //     videosFiles: [],
                        //     advertiserId:
                        //         instance.get<HiveHelper>().getAdvertiserId() ??
                        //             "",
                        //     campaignName: TiktokCampaignCubit.get(ctx1)
                        //         .campaignNameController
                        //         .text,
                        //     objectiveType: TiktokCampaignCubit.get(ctx1)
                        //             .objective
                        //             ?.actualName ??
                        //         "",
                        //     optimizationGoal: TiktokCampaignCubit.get(ctx1)
                        //             .optimization
                        //             ?.actualName ??
                        //         "");
                      }
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

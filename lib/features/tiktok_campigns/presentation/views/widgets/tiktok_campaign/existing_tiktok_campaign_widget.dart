import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../../controllers/get_tiktok_optimizations/get_tiktok_optimizations_cubit.dart';
import '../../../controllers/tiktok_ad/tiktok_ad_cubit.dart';
import '../../../controllers/tiktok_campain/tiktok_campaign_cubit.dart';

class ExistingTiktokCampaignWidget extends StatefulWidget {
  GlobalKey<ExpansionTileCustomState> expansionTileKey;

  ExistingTiktokCampaignWidget({super.key, required this.expansionTileKey});

  @override
  State<ExistingTiktokCampaignWidget> createState() =>
      _ExistingTiktokCampaignWidgetState();
}

class _ExistingTiktokCampaignWidgetState
    extends State<ExistingTiktokCampaignWidget> {
  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      await TiktokCampaignCubit.get(context).getCampaigns(
          context: context,
          advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "");
      // print(
      //     'existingTiktokCampaign ${TiktokCampaignCubit.get(context).tiktokCampaigns?.data?.tiktokCampaigns}');
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokCampaignCubit, TiktokCampaignState>(
      builder: (context, state) {
        return BlocBuilder<GetTiktokOptimizationsCubit,
            GetTiktokOptimizationsState>(
          builder: (ctx1, optState) {
            return Column(
              children: [
                const SizedBox(height: 20),
                state is GetTiktokCampaignsStateLoading
                    ? const LoadingWidget(
                        isCircle: true,
                      )
                    : TiktokCampaignCubit.get(context)
                                .tiktokCampaigns
                                ?.result
                                ?.isEmpty ==
                            true
                        ? const SizedBox()
                        : Column(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  children: [
                                    CustomText(
                                      text: "select campaign".tr,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.primaryTextColor,
                                    ),
                                    CustomText(
                                      text: "*",
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.redColor,
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              ExpansionTileItem(
                                expansionKey:
                                    Constants.tiktokExistingCampaignKey,
                                onExpansionChanged: (val) {},
                                childrenPadding: EdgeInsets.zero,
                                iconColor: AppColors.secondColor,
                                collapsedIconColor: AppColors.secondColor,
                                expandedAlignment: Alignment.center,
                                expandedCrossAxisAlignment:
                                    CrossAxisAlignment.center,
                                trailing: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(left: 6.0),
                                      child: Icon(
                                        Icons.expand_more,
                                        size: 30.0,
                                        color: Constants.darkColor,
                                      ),
                                    )
                                  ],
                                ),
                                title: TiktokCampaignCubit.get(context)
                                            .selectedCampaign !=
                                        null
                                    ? CustomText(
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                        text: TiktokCampaignCubit.get(context)
                                                .selectedCampaign
                                                ?.campaignName ??
                                            "")
                                    : AccountHintText(
                                        isDefaultHint: false,
                                        hint: 'choose your campaign'.tr,
                                      ),
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x3F000000),
                                      blurRadius: 40,
                                      offset: Offset(0, 0),
                                      spreadRadius: -10,
                                    )
                                  ],
                                ),
                                children: [
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: TiktokCampaignCubit.get(context)
                                            .tiktokCampaigns
                                            ?.result
                                            ?.length ??
                                        0,
                                    // 3 for the first text + 3 for the second text
                                    itemBuilder: (context, index) {
                                      // Check if the index is for the first section title
                                      // if (index == 0) {
                                      //   return Padding(
                                      //     padding: const EdgeInsets.all(16.0),
                                      //     child: CustomText(
                                      //       text: 'Drive More Visits Objectives'.tr,
                                      //       fontSize: 13.sp,
                                      //       fontWeight: FontWeight.w600,
                                      //     ),
                                      //   );
                                      // } else
                                      // if (index >=
                                      //       1 &&
                                      //   index <=
                                      //       3)
                                      // {
                                      // Return the first three items
                                      final campIndex =
                                          index; // Adjust index for the optimization list
                                      return InkWell(
                                        onTap: () {
                                          TiktokCampaignCubit.get(context)
                                              .setSelectedCampaign(
                                                  TiktokCampaignCubit.get(
                                                          context)
                                                      .tiktokCampaigns
                                                      ?.result?[campIndex]);
                                          GetTiktokOptimizationsCubit.get(
                                                  context)
                                              .getOptimizations(
                                                  context: context,
                                                  objectiveActualName:
                                                      TiktokCampaignCubit.get(
                                                              context)
                                                          .tiktokCampaigns!
                                                          .result![campIndex]
                                                          .objectiveType!);
                                          // GetBillingEventsCubit
                                          //         .get(
                                          //             billContext)
                                          //     .getBillingEvents(
                                          //   context:
                                          //       context,
                                          //   optimizationId:
                                          //       GetOptimizationsCubit.get(optContext)
                                          //               .opt[optIndex]
                                          //               .id ??
                                          //           0,
                                          // );
                                          // CreateAdCubit.get(
                                          //         context)
                                          //     .billingEvent = null;
                                          Constants.tiktokExistingCampaignKey
                                              .currentState
                                              ?.collapse();
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Constants.gray
                                                .withOpacity(0.15),
                                            border: Border.symmetric(
                                              horizontal: BorderSide(
                                                  color: Constants.gray
                                                      .withOpacity(0.3)),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 18, horizontal: 20),
                                            child: CustomText(
                                              maxLines: 3,
                                              fontSize: 12.sp,
                                              text: TiktokCampaignCubit.get(
                                                          context)
                                                      .tiktokCampaigns
                                                      ?.result?[index]
                                                      .campaignName ??
                                                  "",
                                            ),
                                          ),
                                        ),
                                      );
                                      // }
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              optState is GetTiktokOptimizationsStateLoading
                                  ? const LoadingWidget(
                                      isCircle: true,
                                    )
                                  : GetTiktokOptimizationsCubit.get(ctx1)
                                          .opt
                                          .isEmpty
                                      ? const SizedBox()
                                      : Column(
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8.0),
                                              child: Row(
                                                children: [
                                                  CustomText(
                                                    text:
                                                        "Advertising Objective"
                                                            .tr,
                                                    fontSize: 12.sp,
                                                    fontWeight: FontWeight.w400,
                                                    alignment:
                                                        AlignmentDirectional
                                                            .centerStart,
                                                    color: Constants
                                                        .primaryTextColor,
                                                  ),
                                                  CustomText(
                                                    text: "*",
                                                    fontSize: 16.sp,
                                                    fontWeight: FontWeight.w400,
                                                    alignment:
                                                        AlignmentDirectional
                                                            .centerStart,
                                                    color: Constants.redColor,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            20.verticalSpace,
                                            ExpansionTileItem(
                                              expansionKey: Constants
                                                  .tiktokOptimizationKey,
                                              onExpansionChanged: (val) {},
                                              childrenPadding: EdgeInsets.zero,
                                              iconColor: AppColors.secondColor,
                                              collapsedIconColor:
                                                  AppColors.secondColor,
                                              expandedAlignment:
                                                  Alignment.center,
                                              expandedCrossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              trailing: const Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Padding(
                                                    padding: EdgeInsets.only(
                                                        left: 6.0),
                                                    child: Icon(
                                                      Icons.expand_more,
                                                      size: 30.0,
                                                      color:
                                                          Constants.darkColor,
                                                    ),
                                                  )
                                                ],
                                              ),
                                              title: TiktokCampaignCubit.get(
                                                              ctx1)
                                                          .optimization !=
                                                      null
                                                  ? CustomText(
                                                      fontSize: 12.sp,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      text: TiktokCampaignCubit
                                                                  .get(ctx1)
                                                              .optimization
                                                              ?.showName ??
                                                          "")
                                                  : AccountHintText(
                                                      isDefaultHint: false,
                                                      hint:
                                                          'Advertising Objective'
                                                              .tr,
                                                    ),
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x3F000000),
                                                    blurRadius: 40,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: -10,
                                                  )
                                                ],
                                              ),
                                              children: [
                                                ListView.builder(
                                                  shrinkWrap: true,
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  itemCount:
                                                      GetTiktokOptimizationsCubit
                                                              .get(ctx1)
                                                          .opt
                                                          .length,
                                                  // 3 for the first text + 3 for the second text
                                                  itemBuilder:
                                                      (context, index) {
                                                    // Check if the index is for the first section title
                                                    // if (index == 0) {
                                                    //   return Padding(
                                                    //     padding: const EdgeInsets.all(16.0),
                                                    //     child: CustomText(
                                                    //       text: 'Drive More Visits Objectives'
                                                    //           .tr,
                                                    //       fontSize: 13.sp,
                                                    //       fontWeight: FontWeight.w600,
                                                    //     ),
                                                    //   );
                                                    // } else
                                                    // if (index >=
                                                    //       1 &&
                                                    //   index <=
                                                    //       3)
                                                    // {
                                                    // Return the first three items
                                                    final optIndex =
                                                        index; // Adjust index for the optimization list
                                                    return InkWell(
                                                      onTap: () {
                                                        TiktokCampaignCubit.get(
                                                                ctx1)
                                                            .setSelectedOptimization(
                                                                GetTiktokOptimizationsCubit
                                                                        .get(
                                                                            ctx1)
                                                                    .opt[optIndex]);
                                                        if (TiktokCampaignCubit
                                                                        .get(
                                                                            ctx1)
                                                                    .optimization !=
                                                                null &&
                                                            TiktokCampaignCubit
                                                                        .get(
                                                                            ctx1)
                                                                    .isOptimizeUpdated ==
                                                                false) {
                                                          TiktokCampaignCubit
                                                                      .get(ctx1)
                                                                  .isOptimizeUpdated =
                                                              true;
                                                          TiktokCampaignCubit
                                                                      .get(ctx1)
                                                                  .tiktokCampaignPercentage =
                                                              TiktokCampaignCubit
                                                                          .get(
                                                                              ctx1)
                                                                      .tiktokCampaignPercentage +
                                                                  0.25;
                                                          print(
                                                              'tiktokCampaignPercentage ${TiktokCampaignCubit.get(ctx1).tiktokCampaignPercentage}');
                                                          setState(() {});
                                                        }
                                                        // GetBillingEventsCubit
                                                        //         .get(
                                                        //             billContext)
                                                        //     .getBillingEvents(
                                                        //   context:
                                                        //       context,
                                                        //   optimizationId:
                                                        //       GetOptimizationsCubit.get(optContext)
                                                        //               .opt[optIndex]
                                                        //               .id ??
                                                        //           0,
                                                        // );
                                                        // CreateAdCubit.get(
                                                        //         context)
                                                        //     .billingEvent = null;
                                                        Constants
                                                            .tiktokOptimizationKey
                                                            .currentState
                                                            ?.collapse();
                                                      },
                                                      child: Container(
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Constants.gray
                                                              .withOpacity(
                                                                  0.15),
                                                          border:
                                                              Border.symmetric(
                                                            horizontal: BorderSide(
                                                                color: Constants
                                                                    .gray
                                                                    .withOpacity(
                                                                        0.3)),
                                                          ),
                                                        ),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  vertical: 18,
                                                                  horizontal:
                                                                      20),
                                                          child: CustomText(
                                                            maxLines: 3,
                                                            fontSize: 12.sp,
                                                            text: GetTiktokOptimizationsCubit
                                                                        .get(
                                                                            ctx1)
                                                                    .opt[
                                                                        optIndex]
                                                                    .showName ??
                                                                "",
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                    // }
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                            ],
                          ),
                const SizedBox(height: 20),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Divider(color: Constants.textColor),
                ),
                SizedBox(
                  width: 235.w,
                  child: ButtonWidget(
                    text: "Save",
                    onTap: () {
                      if (TiktokCampaignCubit.get(context).selectedCampaign !=
                              null &&
                          TiktokCampaignCubit.get(ctx1).optimization != null) {
                        widget.expansionTileKey.currentState?.collapse();
                        TiktokAdCubit.get(context).tiktokAdModel =
                            TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                                existingCampaign:
                                    TiktokCampaignCubit.get(context)
                                        .selectedCampaign
                                        ?.campaignId
                                        .toInt(),
                                // campaignName: TiktokCampaignCubit.get(context)
                                //         .selectedCampaign
                                //         ?.campaignName ??
                                //     "",
                                // objectiveType: TiktokCampaignCubit.get(context)
                                //     .selectedCampaign
                                //     ?.objectiveType,
                                optimizationGoal:
                                    TiktokCampaignCubit.get(context)
                                        .optimization
                                        ?.actualName);
                        TiktokAdCubit.get(context).isTiktokCampaign = true;
                        // TiktokAdCubit.get(context).createAD(
                        //     context: context,
                        //     imagesFiles: [],
                        //     videosFiles: [],
                        //     advertiserId:
                        //         instance.get<HiveHelper>().getAdvertiserId() ??
                        //             "",
                        //     campaignName: TiktokCampaignCubit.get(ctx1)
                        //         .campaignNameController
                        //         .text,
                        //     objectiveType: TiktokCampaignCubit.get(ctx1)
                        //             .objective
                        //             ?.actualName ??
                        //         "",
                        //     optimizationGoal: TiktokCampaignCubit.get(ctx1)
                        //             .optimization
                        //             ?.actualName ??
                        //         "");
                      }
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

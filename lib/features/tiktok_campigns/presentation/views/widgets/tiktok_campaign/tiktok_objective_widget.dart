import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/svg_widget.dart';
import '../../../../../../utils/res/app_assets.dart';

class TiktokObjectiveWidget extends StatelessWidget {
  final String name;
  final int index;
  final bool isSelected; // Determine if this ObjectiveWidget is selected
  bool? isSnapChat; // Determine if this ObjectiveWidget is selected
  final void Function(int) callback;

  TiktokObjectiveWidget({
    super.key,
    required this.name,
    required this.index,
    required this.isSelected,
    this.isSnapChat = false,
    required this.callback,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        callback(index);
      },
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(25.r),
          color: Colors.white,
          boxShadow: Constants.unSelectedShadow,
          border: isSelected
              ? Border.all(
                  color: AppColors.mainColor,
                  width: 2.w,
                  style: BorderStyle.solid,
                )
              : null,
        ),
        //width: 80.h,
        child: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomSvgWidget(
                svg:
                    isSnapChat == false ? getIcon(name) : getSnapChatIcon(name),
                // height: 20.h,
                // width: 20.h,
                color: isSelected ? AppColors.mainColor : Constants.textColor,
              ),
              SizedBox(height: 8.h),
              FittedBox(
                child: CustomText(
                  text: capitalizeFirstLetter(name),
                  fontSize: 10.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.mainColor : Constants.textColor,
                  textAlign: TextAlign.center,
                  alignment: AlignmentDirectional.center,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  String getIcon(String name) {
    if (name == "Reach") {
      return AppAssets.tiktokObjective1;
    } else if (name == "Traffic") {
      return AppAssets.tiktokObjective2;
    } else if (name == "Video views") {
      return AppAssets.tiktokObjective3;
    } else if (name == "Lead generation") {
      return AppAssets.tiktokObjective4;
    } else if (name == "Community interaction") {
      return AppAssets.tiktokObjective5;
    } else if (name == "Website conversions") {
      return AppAssets.tiktokObjective5;
    } else {
      return 'assets/icons/Vector.svg';
    }
  }

  String getSnapChatIcon(String name) {
    if (name == "AWARENESS AND ENGAGEMENT") {
      return AppAssets.snapChatObjective1;
    } else if (name == "TRAFFIC") {
      return AppAssets.snapChatObjective2;
    } else if (name == "LEADS") {
      return AppAssets.snapChatObjective3;
    } else if (name == "APP PROMOTION") {
      return AppAssets.snapChatObjective4;
    } else if (name == "SALES") {
      return AppAssets.tiktokObjective5;
    } else if (name == "Website conversions") {
      return AppAssets.tiktokObjective5;
    } else {
      return 'assets/icons/Vector.svg';
    }
  }

  String capitalizeFirstLetter(String name) {
    if (name.isEmpty) return name;
    return name[0].toUpperCase() + name.substring(1).toLowerCase();
  }
}

import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_ad/ad_widget.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../widgets/custom_text.dart';

class TiktokAdWidget extends StatefulWidget {
  const TiktokAdWidget({super.key});

  @override
  State<TiktokAdWidget> createState() => _TiktokAdWidgetState();
}

class _TiktokAdWidgetState extends State<TiktokAdWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokAdCubit, TiktokAdState>(
      builder: (adContext, adState) {
        return ExpansionTileBorderItem(
          onExpansionChanged: (val) {
            TiktokAdCubit.get(adContext).setAdExpansionState(val);
          },
          expansionKey: Constants.tiktokAdTileKey,
          childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
          iconColor: AppColors.secondColor,
          collapsedIconColor: AppColors.secondColor,
          expandedAlignment: Alignment.center,
          expandedCrossAxisAlignment: CrossAxisAlignment.center,
          leading: SvgPicture.asset(AppAssets.ad, color: AppColors.mainColor),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocBuilder<TiktokAdCubit, TiktokAdState>(
                builder: (context, state) {
                  return CircularPercentIndicator(
                    circularStrokeCap: CircularStrokeCap.round,
                    radius: 12.0,
                    lineWidth: 5.5,
                    percent: TiktokAdCubit.get(context).tiktokAdPercentage,
                    linearGradient: Constants.secGradient,
                    backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                    reverse: true,
                  );
                },
              ),
              const Padding(
                padding: EdgeInsets.only(left: 10.0),
                child: CustomText(
                  text: "|",
                  color: AppColors.mainColor,
                  fontSize: 35,
                  fontWeight: FontWeight.w200,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 6.0),
                child: ShaderMask(
                  shaderCallback: (Rect bounds) {
                    return const LinearGradient(
                      colors: [
                        Color(0xFFFF006F),
                        Color(0xFFF6BA00),
                      ],
                    ).createShader(bounds);
                  },
                  child: Icon(
                    TiktokAdCubit.get(adContext).isAdTileExpanded
                        ? Icons.expand_less
                        : Icons.expand_more,
                    size: 24.0,
                    color: Colors
                        .white, // This color will be replaced by the gradient
                  ),
                ),
              )
            ],
          ),

          title: const CustomText(
              text: 'Ad',
              color: AppColors.mainColor,
              fontSize: 22,
              fontWeight: FontWeight.w700),

          // childrenPadding:  const EdgeInsets.symmetric(
          //     horizontal: 10, vertical:20),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: AppColors.mainColor)
              // color: AppColors.borderColor,
              ),
          children: [
            const SizedBox(height: 20),
            Container(
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(40),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x3D000000),
                    blurRadius: 13.93,
                    offset: Offset(0, 0),
                    spreadRadius: -3.80,
                  )
                ],
              ),
              child: const SizedBox(),
            ),
            AdWidget(
              expansionTileKey: Constants.tiktokAdTileKey,
            ),
            const SizedBox(height: 25),
          ],
        );
      },
    );
  }
}

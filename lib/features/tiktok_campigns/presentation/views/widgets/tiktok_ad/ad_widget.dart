import 'dart:io';
import 'package:image/image.dart' as img;

import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_campaign/new_campaign/tabs_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
// import 'package:mobkit_dashed_border/mobkit_dashed_border.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:video_compress/video_compress.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/validations.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../../../../widgets/video_player.dart';
import '../../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import 'package:country_code_picker/country_code_picker.dart';

class AdWidget extends StatefulWidget {
  GlobalKey<ExpansionTileCustomState> expansionTileKey;

  AdWidget({super.key, required this.expansionTileKey});

  @override
  State<AdWidget> createState() => _AdWidgetState();
}

class _AdWidgetState extends State<AdWidget> {
  bool _adNameValid = false;

  @override
  void initState() {
    TiktokAdCubit.get(context).getIdentities(
        context: context,
        advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "");

    super.initState();
  }

  File cropToAspectRatio(File imageFile, double targetAspectRatio) {
    final image = img.decodeImage(imageFile.readAsBytesSync())!;
    
    int newWidth = image.width;
    int newHeight = (image.width / targetAspectRatio).round();
    
    if (newHeight > image.height) {
      newHeight = image.height;
      newWidth = (image.height * targetAspectRatio).round();
    }
    
    final cropped = img.copyCrop(
      image,
      x: (image.width - newWidth) ~/ 2,
      y: (image.height - newHeight) ~/ 2,
      width: newWidth,
      height: newHeight,
    );
    
    // Create a temporary file with unique name
    final tempDir = Directory.systemTemp;
    final output = File('${tempDir.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.jpg')
      ..writeAsBytesSync(img.encodeJpg(cropped));
    return output;
  }

  /// Crops an image to one of the supported aspect ratios: 16:9, 9:16, or 1:1
  File cropToSupportedRatio(File imageFile, String aspectRatio) {
    switch (aspectRatio) {
      case '16:9':
        return cropToAspectRatio(imageFile, 16/9);
      case '9:16':
        return cropToAspectRatio(imageFile, 9/16);
      case '1:1':
        return cropToAspectRatio(imageFile, 1);
      default:
        // Default to 1:1 if invalid ratio provided
        return cropToAspectRatio(imageFile, 1);
    }
  }

  /// Detects the closest supported aspect ratio for an image
  String detectAspectRatio(File imageFile) {
    final bytes = imageFile.readAsBytesSync();
    final image = img.decodeImage(bytes);

    if (image == null) {
      // Return a default aspect ratio if image can't be decoded
      return '1:1';
    }

    final ratio = image.width / image.height;
    
    // Check which aspect ratio is closest
    if ((ratio - (16/9)).abs() < 0.1) {
      return '16:9';
    } else if ((ratio - (9/16)).abs() < 0.1) {
      return '9:16';
    } else {
      return '1:1';
    }
  }

  void showAspectRatioDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Choose aspect ratio"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("Select the aspect ratio for your image:"),
              SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  AspectRatioOption(
                    label: "16:9",
                    onTap: () {
                      Navigator.pop(context);
                      File croppedImage = cropToSupportedRatio(imageFile, '16:9');
                      setState(() {
                        TiktokAdCubit.get(context).identityImage = croppedImage;
                      });
                    },
                  ),
                  AspectRatioOption(
                    label: "9:16",
                    onTap: () {
                      Navigator.pop(context);
                      File croppedImage = cropToSupportedRatio(imageFile, '9:16');
                      setState(() {
                        TiktokAdCubit.get(context).identityImage = croppedImage;
                      });
                    },
                  ),
                  AspectRatioOption(
                    label: "1:1",
                    onTap: () {
                      Navigator.pop(context);
                      File croppedImage = cropToSupportedRatio(imageFile, '1:1');
                      setState(() {
                        TiktokAdCubit.get(context).identityImage = croppedImage;
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // Helper widget for aspect ratio selection

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokAdCubit, TiktokAdState>(
      bloc: TiktokAdCubit.get(context),
      builder: (ctx1, camState) {
        return Form(
          key: TiktokAdCubit
              .get(ctx1)
              .adFormKey,
          child: Column(
            children: [
              const SizedBox(height: 20),
              CustomTextFormField(
                label: "Ad Name",
                showIsReqiredFlag: true,
                textFontSize: 12,
                key: const ValueKey('Add_name'),
                hintText: "Add Name",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                controller: TiktokAdCubit
                    .get(ctx1)
                    .adNameController,
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (va) {
                  bool isValid = va.isNotEmpty;
                  if (isValid && !_adNameValid) {
                    TiktokAdCubit
                        .get(ctx1)
                        .tiktokAdPercentage += 0.02;
                    _adNameValid = true;
                  } else if (!isValid && _adNameValid) {
                    TiktokAdCubit
                        .get(ctx1)
                        .tiktokAdPercentage -= 0.02;
                    _adNameValid = false;
                  }
                },
              ),
              const SizedBox(height: 20),
              // const Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 8.0),
              //   child: Divider(color: Constants.textColor),
              // ),
              // const SizedBox(height: 10),
              // Padding(
              //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     crossAxisAlignment: CrossAxisAlignment.center,
              //     children: [
              //       CustomText(
              //         text: 'Use TikTok account',
              //         fontSize: 12.sp,
              //         color: Constants.primaryTextColor,
              //         fontWeight: FontWeight.w400,
              //         alignment: AlignmentDirectional.centerStart,
              //       ),
              //       CustomSwitch(
              //         value: TiktokAdCubit.get(ctx1).isTiktokAccountActive,
              //         onChanged: (newValue) {
              //           TiktokAdCubit.get(ctx1)
              //               .changeTiktokAccountStatus(newValue);
              //         },
              //       ),
              //     ],
              //   ),
              // ),
              // const SizedBox(height: 10),
              // const Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 8.0),
              //   child: Divider(color: Constants.textColor),
              // ),
              const SizedBox(height: 10),
              Row(
                children: [
                  CustomSvgWidget(
                    svg: AppAssets.behave,
                    width: 24.w,
                    height: 24.h,
                    //    color: Colors.white,
                  ),
                  SizedBox(width: 10.w),
                  CustomText(
                    text: 'Set Custom Identity',
                    fontSize: 18.sp,
                    color: Constants.primaryTextColor,
                    fontWeight: FontWeight.w700,
                    alignment: AlignmentDirectional.centerStart,
                  )
                ],
              ),
              const SizedBox(height: 30),
              TabsWidget(
                  selectedTab: TiktokAdCubit
                      .get(context)
                      .mainSelectedTab,
                  onTabChanged: (selected) {
                    TiktokAdCubit.get(context).mainSetSelectedTab(selected);
                    if (TiktokAdCubit
                        .get(context)
                        .mainSelectedTab == 1) {
                      TiktokAdCubit.get(context).getRalIdentities(
                          context: context,
                          advertiserId:
                          instance.get<HiveHelper>().getAdvertiserId() ??
                              "");
                      // if (TiktokAdCubit.get(context).mainSelectedTab == 1) {
                      //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                      //
                      //   0.02;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                      //   setState(() {});
                      // } else {
                      //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                      //   0.02;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                      //   setState(() {});
                      // }
                      // if (TiktokAdCubit.get(context).mainSelectedTab == 1) {
                      //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                      //       TiktokAdCubit.get(ctx1).tiktokAdPercentage -
                      //           0.02;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                      //   setState(() {});
                      // }
                    } else {
                      TiktokAdCubit.get(context).clearIdentities();
                      TiktokAdCubit.get(context).getIdentities(
                          context: context,
                          advertiserId:
                          instance.get<HiveHelper>().getAdvertiserId() ??
                              "");
                      // if (TiktokAdCubit.get(context).mainSelectedTab == 0) {
                      //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                      //
                      //   0.02;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                      //   setState(() {});
                      // } else {
                      //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                      //   0.02;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                      //   setState(() {});
                      // }
                      // if (TiktokAdCubit.get(context).mainSelectedTab == 0) {
                      //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                      //       TiktokAdCubit.get(ctx1).tiktokAdPercentage -
                      //           0.02;
                      //   print(
                      //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                      //   setState(() {});
                      // }
                    }
                  },
                  newObject: "dark".tr,
                  existObject: "real".tr),
              const SizedBox(height: 30),
              if (TiktokAdCubit
                  .get(context)
                  .mainSelectedTab == 1) ...[
                ExpansionTileBorderItem(
                  expansionKey: Constants.customIdentityTileKey,
                  onExpansionChanged: (val) {},
                  childrenPadding: EdgeInsets.zero,
                  iconColor: AppColors.secondColor,
                  collapsedIconColor: AppColors.secondColor,
                  expandedAlignment: Alignment.center,
                  expandedCrossAxisAlignment: CrossAxisAlignment.center,
                  trailing: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 6.0),
                        child: Icon(
                          Icons.expand_more,
                          size: 30.0,
                          color: Constants.darkColor,
                        ),
                      )
                    ],
                  ),
                  title: TiktokAdCubit
                      .get(ctx1)
                      .isAccountSelected
                      ? CustomText(
                    text: TiktokAdCubit
                        .get(ctx1)
                        .identity
                        ?.displayName ??
                        "",
                    color: Constants.primaryTextColor,
                  )
                      : AccountHintText(
                    isDefaultHint: false,
                    hint: 'Choose your Custom Identity ',
                  ),
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x3F000000),
                        blurRadius: 40,
                        offset: Offset(0, 0),
                        spreadRadius: -10,
                      )
                    ],
                  ),
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (item, index) {
                        return InkWell(
                          onTap: () {
                            TiktokAdCubit.get(ctx1).setSelectedAccount(true,
                                TiktokAdCubit
                                    .get(ctx1)
                                    .identities[index]);
                            if (TiktokAdCubit
                                .get(ctx1)
                                .identity != null) {
                              TiktokAdCubit
                                  .get(ctx1)
                                  .tiktokAdPercentage =
                                  TiktokAdCubit
                                      .get(ctx1)
                                      .tiktokAdPercentage +
                                      0.02;
                              print(
                                  'tiktokCampaignPercentage ${TiktokAdCubit
                                      .get(ctx1)
                                      .tiktokAdPercentage}');
                              setState(() {});
                            } else {
                              TiktokAdCubit
                                  .get(ctx1)
                                  .tiktokAdPercentage =
                                  TiktokAdCubit
                                      .get(ctx1)
                                      .tiktokAdPercentage -
                                      0.02;
                              print(
                                  'tiktokCampaignPercentage ${TiktokAdCubit
                                      .get(ctx1)
                                      .tiktokAdPercentage}');
                              setState(() {});
                            }
                            Constants.customIdentityTileKey.currentState
                                ?.collapse();
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Constants.gray.withOpacity(0.025),
                              border: Border.symmetric(
                                horizontal: BorderSide(
                                  color: Constants.gray.withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 18, horizontal: 20),
                              child: CustomText(
                                  maxLines: 3,
                                  text: TiktokAdCubit
                                      .get(ctx1)
                                      .identities[index]
                                      .displayName ??
                                      ""),
                            ),
                          ),
                        );
                      },
                      itemCount: TiktokAdCubit
                          .get(ctx1)
                          .identities
                          .length,
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 30),
              if (TiktokAdCubit
                  .get(context)
                  .mainSelectedTab == 0) ...[
                TabsWidget(
                    selectedTab: TiktokAdCubit
                        .get(context)
                        .selectedTab,
                    onTabChanged: (selected) {
                      TiktokAdCubit.get(context).setSelectedTab(selected);
                      if (TiktokAdCubit
                          .get(context)
                          .selectedTab == 0) {
                        TiktokAdCubit.get(context).getIdentities(
                            context: context,
                            advertiserId:
                            instance.get<HiveHelper>().getAdvertiserId() ??
                                "");
                        // if (TiktokAdCubit.get(context).selectedTab == 0) {
                        //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                        //       TiktokAdCubit.get(ctx1).tiktokAdPercentage -
                        //           0.02;
                        //   print(
                        //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                        //   setState(() {});
                        // }
                      } else {
                        TiktokAdCubit.get(context).clearIdentities();
                        // if (TiktokAdCubit.get(context).selectedTab == 1) {
                        //   TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                        //       TiktokAdCubit.get(ctx1).tiktokAdPercentage -
                        //           0.02;
                        //   print(
                        //       'tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                        //   setState(() {});
                        // }
                      }
                    },
                    newObject: "current".tr,
                    existObject: "new".tr),
                if (TiktokAdCubit
                    .get(context)
                    .selectedTab == 1) ...[
                  const SizedBox(height: 30),
                  CustomTextFormField(
                    label: "Display name",
                    showIsReqiredFlag: true,
                    textFontSize: 12,
                    key: const ValueKey('Dsiplay_name'),
                    hintText: "Display name",
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    controller: TiktokAdCubit
                        .get(ctx1)
                        .displayNameController,
                    validator: (value) =>
                        AppValidator.validateIdentity(value, context),
                    onChanged: (va) {},
                  ),
                  const SizedBox(height: 30),
                  InkWell(
                    onTap: () async {
                      showDialog(
                        context: context,
                        builder: (BuildContext ctx) {
                          return AlertDialog(
                            title: const Center(
                              child: Text("Image source"),
                            ),
                            content: const Text("Choose Image source"),
                            actionsAlignment: MainAxisAlignment.spaceBetween,
                            actions: [
                              TextButton(
                                onPressed: () async {
                                  Navigator.of(context).pop();

                                  // Check and request storage permission
                                  // var status = await Permission.videos.request();
                                  // print("asdasdfa" + status.isGranted.toString());
                                  // if (status.isGranted) {
                                  // Access the gallery
                                  FilePickerResult? result =
                                  await FilePicker.platform.pickFiles(
                                    type: FileType.image,
                                    allowMultiple: false,
                                    allowCompression: true,
                                  );

                                  if (result != null && result.files.isNotEmpty) {
                                    File imageFile = File(result.files.single.path!);
                                    
                                    // Detect the current aspect ratio
                                    String currentRatio = detectAspectRatio(imageFile);
                                    
                                    // You can either automatically crop to the detected ratio
                                    File croppedImage = cropToSupportedRatio(imageFile, currentRatio);
                                    
                                    // Or you can show a dialog to let the user choose which ratio they want
                                    showAspectRatioDialog(imageFile);
                                    
                                    setState(() {
                                      TiktokAdCubit
                                          .get(ctx1)
                                          .identityImage =
                                          croppedImage;
                                    });
                                    
                                    if (TiktokAdCubit
                                        .get(ctx1)
                                        .displayNameController
                                        .text
                                        .isNotEmpty ==
                                        true) {
                                      TiktokAdCubit
                                          .get(ctx1)
                                          .tiktokAdPercentage =
                                          TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage +
                                              0.02;
                                      print(
                                          'tiktokCampaignPercentage ${TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage}');
                                      setState(() {});
                                    } else {
                                      TiktokAdCubit
                                          .get(ctx1)
                                          .tiktokAdPercentage =
                                          TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage -
                                              0.02;
                                      print(
                                          'tiktokCampaignPercentage ${TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage}');
                                      setState(() {});
                                    }
                                    TiktokAdCubit.get(ctx1).updateStatus();
                                    // print("asda" +
                                    //     TiktokAdCubit.get(ctx1)
                                    //         .identityImage
                                    //         .path);
                                  }
                                  // } else {
                                  // If permission is denied, request access from the device settings
                                  // openAppSettings();
                                  // }
                                },
                                child: const Text("Gallery"),
                              ),
                              TextButton(
                                onPressed: () async {
                                  Navigator.of(context).pop();

                                  // Check and request camera permission
                                  var status =
                                  await Permission.camera.request();
                                  if (status.isGranted) {
                                    // Capture image from camera
                                    final XFile? capturedImage =
                                    await ImagePicker().pickImage(
                                      source: ImageSource.camera,
                                    );

                                    if (capturedImage != null) {
                                      setState(() {
                                        TiktokAdCubit
                                            .get(ctx1)
                                            .identityImage =
                                            File(capturedImage.path);
                                      });
                                    }
                                    if (TiktokAdCubit
                                        .get(ctx1)
                                        .displayNameController
                                        .text
                                        .isNotEmpty ==
                                        true) {
                                      TiktokAdCubit
                                          .get(ctx1)
                                          .tiktokAdPercentage =
                                          TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage +
                                              0.02;
                                      print(
                                          'tiktokCampaignPercentage ${TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage}');
                                      setState(() {});
                                    } else {
                                      TiktokAdCubit
                                          .get(ctx1)
                                          .tiktokAdPercentage =
                                          TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage -
                                              0.02;
                                      print(
                                          'tiktokCampaignPercentage ${TiktokAdCubit
                                              .get(ctx1)
                                              .tiktokAdPercentage}');
                                      setState(() {});
                                    }
                                  } else {
                                    // If permission is denied, request access from the device settings
                                    openAppSettings();
                                  }
                                },
                                child: const Text("Camera"),
                              ),
                            ],
                          );
                        },
                      );
                    },
                    child: TiktokAdCubit
                        .get(ctx1)
                        .identityImage == null ||
                        TiktokAdCubit
                            .get(ctx1)
                            .identityImage
                            ?.path
                            .isEmpty ==
                            true
                        ? Container(
                      height: 258.h,
                      decoration: BoxDecoration(
                        border: Border.all(
                            width: 1,
                            // dashLength: 5,
                            color: AppColors.mainColor),
                        borderRadius: const BorderRadius.all(
                          Radius.circular(10),
                        ),
                      ),
                      child: Center(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Lottie.asset(AppAssets.upload,
                                width: 60.h, height: 60.h),
                            const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomText(
                                  text: 'Upload Your image',
                                  color: AppColors.mainColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w700,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    )
                        : Stack(
                      children: [
                        Image.file(
                            TiktokAdCubit
                                .get(ctx1)
                                .identityImage!),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              onPressed: () {
                                setState(() {
                                  TiktokAdCubit
                                      .get(ctx1)
                                      .identityImage =
                                      File('');
                                });
                              },
                              icon: const Icon(
                                Icons.cancel_outlined,
                              ),
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),
                ],
                if (TiktokAdCubit
                    .get(context)
                    .selectedTab == 0) ...[
                  const SizedBox(height: 30),
                  ExpansionTileBorderItem(
                    expansionKey: Constants.customIdentityTileKey,
                    onExpansionChanged: (val) {},
                    childrenPadding: EdgeInsets.zero,
                    iconColor: AppColors.secondColor,
                    collapsedIconColor: AppColors.secondColor,
                    expandedAlignment: Alignment.center,
                    expandedCrossAxisAlignment: CrossAxisAlignment.center,
                    trailing: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 6.0),
                          child: Icon(
                            Icons.expand_more,
                            size: 30.0,
                            color: Constants.darkColor,
                          ),
                        )
                      ],
                    ),
                    title: TiktokAdCubit
                        .get(ctx1)
                        .isAccountSelected
                        ? CustomText(
                      text:
                      TiktokAdCubit
                          .get(ctx1)
                          .identity
                          ?.displayName ??
                          "",
                      color: Constants.primaryTextColor,
                    )
                        : AccountHintText(
                      isDefaultHint: false,
                      hint: 'Choose your Custom Identity ',
                    ),
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x3F000000),
                          blurRadius: 40,
                          offset: Offset(0, 0),
                          spreadRadius: -10,
                        )
                      ],
                    ),
                    children: [
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (item, index) {
                          return InkWell(
                            onTap: () {
                              TiktokAdCubit.get(ctx1).setSelectedAccount(true,
                                  TiktokAdCubit
                                      .get(ctx1)
                                      .identities[index]);
                              if (TiktokAdCubit
                                  .get(ctx1)
                                  .identity != null) {
                                TiktokAdCubit
                                    .get(ctx1)
                                    .tiktokAdPercentage =
                                    TiktokAdCubit
                                        .get(ctx1)
                                        .tiktokAdPercentage +
                                        0.02;
                                print(
                                    'tiktokCampaignPercentage ${TiktokAdCubit
                                        .get(ctx1)
                                        .tiktokAdPercentage}');
                                setState(() {});
                              } else {
                                TiktokAdCubit
                                    .get(ctx1)
                                    .tiktokAdPercentage =
                                    TiktokAdCubit
                                        .get(ctx1)
                                        .tiktokAdPercentage -
                                        0.02;
                                print(
                                    'tiktokCampaignPercentage ${TiktokAdCubit
                                        .get(ctx1)
                                        .tiktokAdPercentage}');
                                setState(() {});
                              }
                              Constants.customIdentityTileKey.currentState
                                  ?.collapse();
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Constants.gray.withOpacity(0.025),
                                border: Border.symmetric(
                                  horizontal: BorderSide(
                                    color: Constants.gray.withOpacity(0.3),
                                  ),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 18, horizontal: 20),
                                child: CustomText(
                                    maxLines: 3,
                                    text: TiktokAdCubit
                                        .get(ctx1)
                                        .identities[index]
                                        .displayName ??
                                        ""),
                              ),
                            ),
                          );
                        },
                        itemCount: TiktokAdCubit
                            .get(ctx1)
                            .identities
                            .length,
                      ),
                    ],
                  ),
                ],
              ],
              const SizedBox(height: 30),
              InkWell(
                onTap: () async {
                  showDialog(
                    context: context,
                    builder: (BuildContext ctx) {
                      return AlertDialog(
                        title: Center(
                          child: Text("Video source".tr),
                        ),
                        content: Text("Choose Video source".tr),
                        actionsAlignment: MainAxisAlignment.spaceBetween,
                        actions: [
                          TextButton(
                            onPressed: () async {
                              Navigator.of(context).pop(); // Close the dialog

                              // Pick a video file using FilePicker
                              FilePickerResult? result =
                              await FilePicker.platform.pickFiles(
                                type: FileType.video,
                                allowMultiple: false,
                                allowCompression: true,
                              );

                              Future<File?> convertPlatformFilesToFile(
                                  List<PlatformFile> platformFiles) async {
                                // Filter files by allowed extensions
                                final allowedExtensions = [
                                  '.mp4', '.mov', '.mpeg', '.3gp', '.avi',
                                ];

                                try {
                                  final filteredFiles = platformFiles.where((file) {
                                    final filePath = file.path;
                                    return filePath != null &&
                                        allowedExtensions.any((ext) => filePath.toLowerCase().endsWith(ext));
                                  }).toList();

                                  if (filteredFiles.isEmpty) {
                                    print('No files with allowed extensions found');
                                    showErrorToast('Please select a valid video file (mp4, mov, mpeg, 3gp, avi)');
                                    return null;
                                  }

                                  // Process the first valid file
                                  final platformFile = filteredFiles.first;
                                  final file = File(platformFile.path!);

                                  if (!file.existsSync()) {
                                    print('File does not exist: ${file.path}');
                                    showErrorToast('Selected file could not be accessed');
                                    return null;
                                  }

                                  print('Processing file: ${file.path}');

                                  // Instead of trying to detect aspect ratio for videos (which might not work),
                                  // just return the file directly
                                  // return file;

                                  // If you still want to try aspect ratio detection, uncomment this:

                                  String currentRatio;
                                  try {
                                    currentRatio = detectAspectRatio(file);
                                  } catch (e) {
                                    print('Error detecting aspect ratio: $e');
                                    currentRatio = '1:1'; // Default to 1:1 if detection fails
                                  }

                                  print('Detected aspect ratio: $currentRatio');

                                  // Just return the original file without cropping for now
                                  return file;

                                } catch (e, stackTrace) {
                                  print('Error in convertPlatformFilesToFile: $e');
                                  print('Stack trace: $stackTrace');
                                  showErrorToast('Error processing video file');
                                  return null;
                                }
                              }

                              if (result != null && result.files.isNotEmpty) {
                                // Try to convert the selected file
                                final convertedFile = await convertPlatformFilesToFile(result.files);

                                // Only proceed if we got a valid file
                                if (convertedFile != null) {
                                  TiktokAdCubit.get(context).adVideo = convertedFile;
                                  setState(() {});

                                  // Update percentage
                                  TiktokAdCubit.get(ctx1).tiktokAdPercentage =
                                      TiktokAdCubit.get(ctx1).tiktokAdPercentage + 0.02;
                                  print('tiktokCampaignPercentage ${TiktokAdCubit.get(ctx1).tiktokAdPercentage}');
                                  setState(() {});
                                } else {
                                  // Handle case where conversion failed
                                  print('Failed to process video file');
                                }
                              }
                            },
                            child: Text("Gallery".tr),
                          ),
                          TextButton(
                            onPressed: () async {
                              Navigator.of(context).pop();

                              // If file picker is not supported, capture image from camera
                              final XFile? capturedImage =
                              await ImagePicker().pickImage(
                                source: ImageSource.camera,
                              );

                              if (capturedImage != null) {
                                setState(() {
                                  TiktokAdCubit
                                      .get(context)
                                      .adVideo =
                                      File(capturedImage.path);
                                });
                                // CreateAdCubit.get(context).updateStatus();
                                // CreateAdCubit.get(context)
                                //     .updateAdCreativeProcess6();

                                TiktokAdCubit
                                    .get(ctx1)
                                    .tiktokAdPercentage =
                                    TiktokAdCubit
                                        .get(ctx1)
                                        .tiktokAdPercentage +
                                        0.02;
                                print(
                                    'tiktokCampaignPercentage ${TiktokAdCubit
                                        .get(ctx1)
                                        .tiktokAdPercentage}');
                                setState(() {});
                              
                                print("imagessasdas${TiktokAdCubit
                                        .get(context)
                                        .adVideo}");
                              }
                            },
                            child: Text("Camera".tr),
                          ),
                        ],
                      );
                    },
                  );
                },
                child: TiktokAdCubit
                    .get(context)
                    .adVideo
                    .path == ''
                    ? Container(
                  height: 258.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        width: 1,
                        // dashLength: 5,
                        color: AppColors.mainColor),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(10),
                    ),
                  ),
                  child: Center(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Lottie.asset(AppAssets.upload,
                            width: 60.h, height: 60.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomText(
                              text: 'Upload Your Video'.tr,
                              color: AppColors.mainColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )
                    : Stack(
                  children: [
                    CustomVideoPlayer.file(
                        file: TiktokAdCubit
                            .get(context)
                            .adVideo),
                    // if (_controller != null)
                    //   AspectRatio(
                    //     aspectRatio: _controller!.value.aspectRatio,
                    //     child: VideoPlayer(_controller!),
                    //   ),
                    // const CustomVideoPlayer.network(
                    //     videoUrl:
                    //         'https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/720/Big_Buck_Bunny_720_10s_1MB.mp4'),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: Align(
                        alignment: Alignment.topRight,
                        child: IconButton(
                          onPressed: () {
                            setState(() {
                              TiktokAdCubit
                                  .get(context)
                                  .adVideo =
                                  File('');
                            });
                            // CreateAdCubit.get(context)
                            //     .undoAdCreativeProcess6();
                          },
                          icon: const Icon(
                            Icons.cancel_outlined,
                          ),
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              CustomTextFormField(
                label: "Ad Text",
                showIsReqiredFlag: true,
                textFontSize: 12,
                key: const ValueKey('ad text'),
                hintText: "Ad Text",
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                controller: TiktokAdCubit
                    .get(ctx1)
                    .adText,
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (va) {
                  bool isValid = va.isNotEmpty;
                  if (isValid && !_adNameValid) {
                    TiktokAdCubit
                        .get(ctx1)
                        .tiktokAdPercentage += 0.02;
                    _adNameValid = true;
                  } else if (!isValid && _adNameValid) {
                    TiktokAdCubit
                        .get(ctx1)
                        .tiktokAdPercentage -= 0.02;
                    _adNameValid = false;
                  }
                },
              ),
              const SizedBox(height: 10),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Divider(color: Constants.textColor),
              ),
              const SizedBox(height: 10),
              if (TiktokAdCubit
                  .get(context)
                  .tiktokAdModel
                  .objectiveType ==
                  "LEAD_GENERATION" &&
                  TiktokAdCubit
                      .get(context)
                      .tiktokAdModel
                      .optimizationGoal ==
                      "CLICK") ...[
                CustomTextFormField(
                  label: "Phone",
                  showIsReqiredFlag: true,
                  textFontSize: 12,
                  key: const ValueKey('Phone'),
                  hintText: "Phone",
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.phone,
                  controller: TiktokAdCubit
                      .get(ctx1)
                      .phoneController,
                  validator: (value) =>
                      AppValidator.validateIdentity(value, context),
                  onChanged: (va) {
                    bool isValid = va.isNotEmpty;
                    if (isValid && !_adNameValid) {
                      TiktokAdCubit
                          .get(ctx1)
                          .tiktokAdPercentage += 0.02;
                      _adNameValid = true;
                    } else if (!isValid && _adNameValid) {
                      TiktokAdCubit
                          .get(ctx1)
                          .tiktokAdPercentage -= 0.02;
                      _adNameValid = false;
                    }
                  },
                  prefixIcon: CountryCodePicker(
                    onChanged: (countryCode) {
                      TiktokAdCubit
                          .get(context)
                          .countryCode = countryCode.code;
                      TiktokAdCubit
                          .get(context)
                          .countryCallingCode =
                          countryCode.dialCode;
                      // TiktokAdCubit.get(context).phoneNumber =
                      //     TiktokAdCubit.get(ctx1).phoneController.text;
                      // TiktokAdCubit.get(context).tiktokAdModel.copyWith(
                      //     countryCode: countryCode.code,
                      //     countryCallingCode: countryCode.dialCode,TiktokAdCubit.get(ctx1).phoneController);
                    },
                    onInit: (countryCode) {
                      TiktokAdCubit
                          .get(context)
                          .countryCode =
                          countryCode?.code;
                      TiktokAdCubit
                          .get(context)
                          .countryCallingCode =
                          countryCode?.dialCode;
                    },
                    // Initial selection and favorite can be one of code ('IT') OR dial_code('+39')
                    initialSelection: 'EG',
                    // favorite: ['+39', 'FR'],
                    // optional. Shows only country name and flag
                    showCountryOnly: false,
                    // optional. Shows only country name and flag when popup is closed.
                    showOnlyCountryWhenClosed: false,
                    // optional. aligns the flag and the Text left
                    alignLeft: false,
                  ),
                ),
                // const SizedBox(height: 20),
                const SizedBox(height: 10),
              ] else
                if (TiktokAdCubit
                    .get(context)
                    .tiktokAdModel
                    .objectiveType ==
                    "LEAD_GENERATION" &&
                    TiktokAdCubit
                        .get(context)
                        .tiktokAdModel
                        .optimizationGoal ==
                        "CONVERSATION")
                  ...[]
                else
                  if (TiktokAdCubit
                      .get(context)
                      .tiktokAdModel
                      .objectiveType !=
                      "ENGAGEMENT") ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CustomText(
                            text: 'Call to action',
                            fontSize: 12.sp,
                            color: Constants.primaryTextColor,
                            fontWeight: FontWeight.w400,
                            alignment: AlignmentDirectional.centerStart,
                          ),
                          const SizedBox(height: 25),
                          BlocBuilder<TiktokAdCubit, TiktokAdState>(
                            builder: (context, state) {
                              return ExpansionTileItem(
                                expansionKey: Constants.callToActionKey,
                                onExpansionChanged: (val) {},
                                childrenPadding:
                                const EdgeInsets.symmetric(vertical: 8),
                                iconColor: AppColors.secondColor,
                                collapsedIconColor: AppColors.secondColor,
                                expandedAlignment: Alignment.center,
                                expandedCrossAxisAlignment:
                                CrossAxisAlignment.center,
                                trailing: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(left: 6.0),
                                      child: Icon(
                                        Icons.expand_more,
                                        size: 30.0,
                                        color: Constants.darkColor,
                                      ),
                                    )
                                  ],
                                ),
                                title: TiktokAdCubit
                                    .get(context)
                                    .typeName != null
                                    ? CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: TiktokAdCubit
                                        .get(context)
                                        .typeName ??
                                        "")
                                    : CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: 'callToAction'.tr),
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x3F000000),
                                      blurRadius: 40,
                                      offset: Offset(0, 0),
                                      spreadRadius: -10,
                                    )
                                  ],
                                ),
                                children: [
                                  ListView.separated(
                                    shrinkWrap: true,
                                    physics: const BouncingScrollPhysics(
                                        parent: NeverScrollableScrollPhysics()),
                                    scrollDirection: Axis.vertical,
                                    itemCount: TiktokAdCubit
                                        .get(context)
                                        .callToAction
                                        .length,
                                    clipBehavior: Clip.none,
                                    separatorBuilder: (context, index) =>
                                        SizedBox(height: 10.h),
                                    itemBuilder: (context, index) {
                                      return GestureDetector(
                                        onTap: () {
                                          TiktokAdCubit.get(context)
                                              .setCallToAction(
                                              TiktokAdCubit
                                                  .get(context)
                                                  .callToAction[index]);
                                          if (TiktokAdCubit
                                              .get(context)
                                              .typeName !=
                                              null) {
                                            TiktokAdCubit
                                                .get(ctx1)
                                                .tiktokAdPercentage =
                                                TiktokAdCubit
                                                    .get(ctx1)
                                                    .tiktokAdPercentage +
                                                    0.02;
                                            print(
                                                'tiktokCampaignPercentage ${TiktokAdCubit
                                                    .get(ctx1)
                                                    .tiktokAdPercentage}');
                                            setState(() {});
                                          } else {
                                            TiktokAdCubit
                                                .get(ctx1)
                                                .tiktokAdPercentage =
                                                TiktokAdCubit
                                                    .get(ctx1)
                                                    .tiktokAdPercentage -
                                                    0.02;
                                            print(
                                                'tiktokCampaignPercentage ${TiktokAdCubit
                                                    .get(ctx1)
                                                    .tiktokAdPercentage}');
                                            setState(() {});
                                          }
                                          Constants.callToActionKey.currentState
                                              ?.collapse();
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8),
                                          child: Container(
                                            height: 40.h,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                              BorderRadius.circular(25.r),
                                              color:
                                              TiktokAdCubit
                                                  .get(context)
                                                  .type ==
                                                  TiktokAdCubit
                                                      .get(context)
                                                      .callToAction[index]
                                                      .value
                                                  ? AppColors.mainColor
                                                  : Colors.white,
                                              gradient:
                                              TiktokAdCubit
                                                  .get(context)
                                                  .type ==
                                                  TiktokAdCubit
                                                      .get(context)
                                                      .callToAction[index]
                                                      .value
                                                  ? Constants.defGradient
                                                  : null,
                                              boxShadow: Constants
                                                  .unSelectedShadow,
                                              border: null,
                                            ),
                                            width: 80.h,
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                  8.0),
                                              child: CustomText(
                                                text: TiktokAdCubit
                                                    .get(context)
                                                    .callToAction[index]
                                                    .name ??
                                                    "",
                                                fontSize: 12.sp,
                                                fontWeight: TiktokAdCubit
                                                    .get(
                                                    context)
                                                    .type ==
                                                    TiktokAdCubit
                                                        .get(context)
                                                        .callToAction[index]
                                                        .value
                                                    ? FontWeight.w600
                                                    : FontWeight.w400,
                                                color: TiktokAdCubit
                                                    .get(context)
                                                    .type ==
                                                    TiktokAdCubit
                                                        .get(context)
                                                        .callToAction[index]
                                                        .value
                                                    ? AppColors.white
                                                    : Constants.textColor,
                                                textAlign: TextAlign.center,
                                                alignment:
                                                AlignmentDirectional.center,
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  )
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Divider(color: Constants.textColor),
                    ),
                    const SizedBox(height: 10),
                    // if (TiktokAdCubit.get(context).tiktokAdModel.objectiveType !=
                    //         "LEAD_GENERATION"
                    //     // &&
                    //     // TiktokAdCubit.get(context).tiktokAdModel.optimizationGoal !=
                    //     //     "CLICK"
                    //     )
                    CustomTextFormField(
                      label: "Website URL",
                      showIsReqiredFlag: true,
                      textFontSize: 12,
                      key: const ValueKey('Website URL'),
                      hintText: "Website URL",
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.text,
                      controller: TiktokAdCubit
                          .get(ctx1)
                          .url,
                      validator: (value) =>
                          AppValidator.validateUrl(value, context),
                      onChanged: (va) {
                        bool isValid = va.isNotEmpty;
                        if (isValid && !_adNameValid) {
                          TiktokAdCubit
                              .get(ctx1)
                              .tiktokAdPercentage += 0.02;
                          _adNameValid = true;
                        } else if (!isValid && _adNameValid) {
                          TiktokAdCubit
                              .get(ctx1)
                              .tiktokAdPercentage -= 0.02;
                          _adNameValid = false;
                        }
                        // if (!va.startsWith('https://')) {
                        //   showErrorToast('it should start with https://');
                        // }
                      },
                    ),
                    SizedBox(height: 50.h),
                  ],
              SizedBox(
                width: 235.w,
                child: ButtonWidget(
                  text: "Save",
                  onTap: () {
                    if (TiktokAdCubit
                        .get(ctx1)
                        .adFormKey
                        .currentState!
                        .validate()) {
                      // TiktokAdCubit.get(context)
                      //     .createIdentity(context: context);
                      TiktokAdCubit
                          .get(ctx1)
                          .tiktokAdModel =
                          TiktokAdCubit
                              .get(ctx1)
                              .tiktokAdModel
                              .copyWith(
                              adName:
                              TiktokAdCubit
                                  .get(ctx1)
                                  .adNameController
                                  .text,
                              adText: TiktokAdCubit
                                  .get(ctx1)
                                  .adText
                                  .text,
                              websiteUrl: TiktokAdCubit
                                  .get(ctx1)
                                  .url
                                  .text,
                              adVideo: TiktokAdCubit
                                  .get(ctx1)
                                  .adVideo,
                              callToAction: TiktokAdCubit
                                  .get(ctx1)
                                  .type,
                              identity:
                              TiktokAdCubit
                                  .get(ctx1)
                                  .identity
                                  ?.identityId);
                      TiktokAdCubit
                          .get(context)
                          .isTiktokAd = true;
                      widget.expansionTileKey.currentState?.collapse();
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class AspectRatioOption extends StatelessWidget {
  final String label;
  final VoidCallback onTap;

  const AspectRatioOption({
    Key? key,
    required this.label,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.mainColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(label),
      ),
    );
  }
}

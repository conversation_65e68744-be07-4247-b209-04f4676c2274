import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../controllers/tiktok_ad/tiktok_ad_cubit.dart';

class TiktokAdGroupReviewWidget extends StatelessWidget {
  const TiktokAdGroupReviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokAdCubit, TiktokAdState>(
      bloc: TiktokAdCubit.get(context),
      builder: (ctx, state) {
        return DecoratedContainer(
          width: double.infinity,
          color: Colors.white,
          boxShadow: const BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 150,
            offset: Offset(0, 4),
            spreadRadius: 0,
          ),
          radius: 20.sp,
          widget: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.sp, horizontal: 30.sp),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset(
                          height: 24.h,
                          AppAssets.adset,
                          color: AppColors.mainColor,
                        ),
                        10.horizontalSpace,
                        CustomText(
                            text: 'Advertising Type'.tr,
                            color: AppColors.mainColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w700),
                      ],
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();

                        Constants.adSetExpansionTileKey.currentState?.expand();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 3),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 1, color: Color(0xFF0B0F26)),
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 2.0),
                              child: CustomText(
                                text: 'Change'.tr,
                                color: Constants.primaryTextColor,
                                fontSize: 10.sp,
                                alignment: AlignmentDirectional.center,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                15.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Ad Set Name".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    CustomText(
                      text:
                          // CreateAdCubit.get(context).existingAdSet?.name ??
                          TiktokAdCubit.get(context)
                                  .tiktokAdModel
                                  .adGroupName ??
                              "",
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                8.verticalSpace,
                // (CreateAdCubit.get(context).existingAdSet != null)
                //     ? const SizedBox()
                //     :
                Column(
                  children: [
                    const Divider(
                      color: Constants.darkColor,
                      thickness: 0.5,
                    ),
                    8.verticalSpace,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Age".tr,
                          color: const Color(0xFF848484),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        Row(
                          children: [
                            ...?TiktokAdCubit.get(context)
                                .tiktokAdModel
                                .age
                                ?.map(
                                  (e) => CustomText(
                                    text: e,
                                    color: Constants.primaryTextColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),

                            // const CustomText(
                            //   text: " - ",
                            //   color: Constants.primaryTextColor,
                            //   fontSize: 12,
                            //   fontWeight: FontWeight.w400,
                            // ),
                            // CustomText(
                            //   text: CreateAdCubit.get(context)
                            //       .selectedMaxAge
                            //       .toString(),
                            //   color: Constants.primaryTextColor,
                            //   fontSize: 12,
                            //   fontWeight: FontWeight.w400,
                            // ),
                          ],
                        ),
                      ],
                    ),

                    // CreateAdCubit.get(context).genders.isNotEmpty
                    //     ?
                    Column(
                      children: [
                        8.verticalSpace,
                        const Divider(
                          color: Constants.darkColor,
                          thickness: 0.5,
                        ),
                        8.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: "Gender".tr,
                              color: const Color(0xFF848484),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            CustomText(
                              text:
                                  TiktokCampaignCubit.get(context).gender ?? "",
                              color: Constants.primaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ],
                        ),
                      ],
                    ),
                    // : SizedBox(),
                    8.verticalSpace,

                    const Divider(
                      color: Constants.darkColor,
                      thickness: 0.5,
                    ),
                    8.verticalSpace,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Languages".tr,
                          color: const Color(0xFF848484),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        const Column(children: [
                          CustomText(
                            text: "ar",
                            color: Constants.primaryTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ]
                            // CreateAdCubit.get(context)
                            //     .languages
                            //     .map((e) {
                            //   return Padding(
                            //     padding: const EdgeInsets.symmetric(
                            //         vertical: 1.0),
                            //     child: CustomText(
                            //       text: e.name ?? "",
                            //       color: Constants.primaryTextColor,
                            //       fontSize: 12,
                            //       fontWeight: FontWeight.w400,
                            //     ),
                            //   );
                            // }).toList(),
                            ),
                      ],
                    ),
                    8.verticalSpace,
                    const Divider(
                      color: Constants.darkColor,
                      thickness: 0.5,
                    ),
                    8.verticalSpace,

                    FittedBox(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: "Interests".tr,
                            color: const Color(0xFF848484),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          Column(
                            children: TiktokCampaignCubit.get(context)
                                .selectedTiktokInterests
                                .map((e) {
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 1.0),
                                child: CustomText(
                                  text: e.keyword ?? "",
                                  color: Constants.primaryTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                    // 8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment:
                    //   MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     const CustomText(
                    //       text: "Behaviours",
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children: CreateAdCubit.get(context).behaviours.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment:
                    //   MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     const CustomText(
                    //       text: "Schools/Universities",
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children: CreateAdCubit.get(context).selectedEducationalSchool.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment:
                    //   MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     const CustomText(
                    //       text: "Education Majors",
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children: CreateAdCubit.get(context).majors.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment:
                    //   MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     const CustomText(
                    //       text: "Education Statuses",
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children: CreateAdCubit.get(context).educationStatuses.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment:
                    //   MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     const CustomText(
                    //       text: "Life Events",
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children: CreateAdCubit.get(context).lifeEvents.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment:
                    //   MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     const CustomText(
                    //       text: "User Device",
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children: CreateAdCubit.get(context).userDevices.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,
                    //
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     CustomText(
                    //       text: "User Os".tr,
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     Column(
                    //       children:
                    //       TiktokAdCubit.get(context).tiktokAdModel.userOs.map((e) {
                    //         return Padding(
                    //           padding: const EdgeInsets.symmetric(
                    //               vertical: 1.0),
                    //           child: CustomText(
                    //             text: e.name ?? "",
                    //             color: Constants.primaryTextColor,
                    //             fontSize: 12,
                    //             fontWeight: FontWeight.w400,
                    //           ),
                    //         );
                    //       }).toList(),
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Locations".tr,
                          color: const Color(0xFF848484),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        // CreateAdCubit.get(context).geoLocations.isNotEmpty
                        //     ?
                        Column(
                          children: [
                            Row(
                              children: [
                                if (TiktokCampaignCubit.get(context)
                                        .selectedTiktokLocation
                                        ?.isNotEmpty ==
                                    true)
                                  CustomText(
                                    text: TiktokCampaignCubit.get(context)
                                            .selectedTiktokLocation
                                            ?.first
                                            .geo
                                            ?.description ??
                                        "",
                                    color: Constants.primaryTextColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                // 5.horizontalSpace,
                                // CustomText(
                                //   text: e.radius.toString() ?? "",
                                //   color:
                                //   Constants.primaryTextColor,
                                //   fontSize: 12,
                                //   fontWeight: FontWeight.w400,
                                // ),
                                // 5.horizontalSpace,
                                // CustomText(
                                //   text: e.distanceUnit ==
                                //       DistanceUnit.kilometer
                                //       ? "K"
                                //       : "M",
                                //   color:
                                //   Constants.primaryTextColor,
                                //   fontSize: 12,
                                //   fontWeight: FontWeight.w400,
                                // ),
                              ],
                            ),
                          ],
                          //     CreateAdCubit.get(context)
                          //         .geoLocations
                          //         .first
                          //         .customLocations!
                          //         .map((e) {
                          //       return Padding(
                          //         padding: const EdgeInsets.symmetric(
                          //             vertical: 1.0),
                          //         child: Row(
                          //           children: [
                          //             CustomText(
                          //               text: e.addressString ?? "",
                          //               color:
                          //                   Constants.primaryTextColor,
                          //               fontSize: 12,
                          //               fontWeight: FontWeight.w400,
                          //             ),
                          //             5.horizontalSpace,
                          //             CustomText(
                          //               text: e.radius.toString() ?? "",
                          //               color:
                          //                   Constants.primaryTextColor,
                          //               fontSize: 12,
                          //               fontWeight: FontWeight.w400,
                          //             ),
                          //             5.horizontalSpace,
                          //             CustomText(
                          //               text: e.distanceUnit ==
                          //                       DistanceUnit.kilometer
                          //                   ? "K"
                          //                   : "M",
                          //               color:
                          //                   Constants.primaryTextColor,
                          //               fontSize: 12,
                          //               fontWeight: FontWeight.w400,
                          //             ),
                          //           ],
                          //         ),
                          //       );
                          //     }).toList(),
                          //   )
                          // : const SizedBox(),
                        )
                      ],
                    ),
                    8.verticalSpace,
                    // const Divider(
                    //   color: Constants.darkColor,
                    //   thickness: 0.5,
                    // ),
                    // 8.verticalSpace,

                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     CustomText(
                    //       text: "post-ad action".tr,
                    //       color: Color(0xFF848484),
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w500,
                    //     ),
                    //     CustomText(
                    //       text: CreateAdCubit.get(context)
                    //               .destinationType ??
                    //           "",
                    //       color: Constants.primaryTextColor,
                    //       fontSize: 12,
                    //       fontWeight: FontWeight.w400,
                    //     ),
                    //   ],
                    // ),
                    // 8.verticalSpace,
                    const Divider(
                      color: Constants.darkColor,
                      thickness: 0.5,
                    ),
                    8.verticalSpace,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Daily Budget".tr,
                          color: const Color(0xFF848484),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        Row(
                          children: [
                            CustomText(
                              text: TiktokAdCubit.get(context)
                                      .tiktokAdModel
                                      .dailyBudget ??
                                  "",
                              color: Constants.primaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                            5.horizontalSpace,
                            const CustomText(
                              text: "AED",
                              color: Constants.primaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ],
                        ),
                      ],
                    ),
                    8.verticalSpace,
                    const Divider(
                      color: Constants.darkColor,
                      thickness: 0.5,
                    ),
                    8.verticalSpace,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Start Date".tr,
                          color: const Color(0xFF848484),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        CustomText(
                          text: TiktokAdCubit.get(context)
                                  .tiktokAdModel
                                  .startDate ??
                              "",
                          color: Constants.primaryTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ],
                    ),
                    8.verticalSpace,
                    const Divider(
                      color: Constants.darkColor,
                      thickness: 0.5,
                    ),
                    8.verticalSpace,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "End Date".tr,
                          color: const Color(0xFF848484),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        CustomText(
                          text: TiktokAdCubit.get(context)
                                  .tiktokAdModel
                                  .endDate ??
                              "",
                          color: Constants.primaryTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

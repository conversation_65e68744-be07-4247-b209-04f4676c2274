import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';

class TiktokCampaignReviewWidget extends StatelessWidget {
  const TiktokCampaignReviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokAdCubit, TiktokAdState>(
      bloc: TiktokAdCubit.get(context),
      builder: (ctx, state) {
        return DecoratedContainer(
          width: double.infinity,
          color: Colors.white,
          boxShadow: const BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 150,
            offset: Offset(0, 4),
            spreadRadius: 0,
          ),
          radius: 20.sp,
          widget: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.sp, horizontal: 30.sp),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset(
                          height: 24.h,
                          AppAssets.goal,
                          color: AppColors.mainColor,
                        ),
                        10.horizontalSpace,
                        CustomText(
                            text: 'Goal'.tr,
                            color: AppColors.mainColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w700),
                      ],
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        Constants.expansionTileKey.currentState?.expand();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 3),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 1, color: Color(0xFF0B0F26)),
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 2.0),
                              child: CustomText(
                                text: 'Change'.tr,
                                color: Constants.primaryTextColor,
                                fontSize: 10.sp,
                                alignment: AlignmentDirectional.center,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                15.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CustomText(
                        text: "Ad Set Name".tr,
                        color: const Color(0xFF848484),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: CustomText(
                        text:
                            // CreateAdCubit.get(context).existingCampaign?.name ??
                            TiktokAdCubit.get(context)
                                    .tiktokAdModel
                                    .campaignName ??
                                "",
                        // "",
                        color: Constants.primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Status".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    CustomText(
                      text:
                          // CreateAdCubit.get(context).existingCampaign?.status ??
                          TiktokCampaignCubit.get(context)
                                  .isCreateActive
                                  .toString() ??
                              "",
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Goal".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    CustomText(
                      text: TiktokAdCubit.get(context)
                              .tiktokAdModel
                              .optimizationGoal ??
                          "",
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Advertising Objective".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    CustomText(
                      text: TiktokAdCubit.get(context)
                              .tiktokAdModel
                              .objectiveType ??
                          "",
                      // CreateAdCubit.get(context).existingCampaign!=
                      //     null
                      //     ? CreateAdCubit.get(context)
                      //     .existingCampaign
                      //     ?.objective ==
                      //     "OUTCOME_AWARENESS"
                      //     ? "Awareness"
                      //     : CreateAdCubit.get(context)
                      //     .existingCampaign
                      //     ?.objective ==
                      //     "OUTCOME_SALES"
                      //     ? "Sales"
                      //     : CreateAdCubit.get(context)
                      //     .existingCampaign
                      //     ?.objective ==
                      //     "OUTCOME_LEADS"
                      //     ? "Leads"
                      //     : "Engagements"
                      //     : CreateAdCubit.get(context).objective ==
                      //     "OUTCOME_AWARENESS"
                      //     ? "Awareness"
                      //     : CreateAdCubit.get(context).objective ==
                      //     "OUTCOME_SALES"
                      //     ? "Sales"
                      //     : CreateAdCubit.get(context).objective ==
                      //     "OUTCOME_LEADS"
                      //     ? 'Leads'
                      //     : "Engagements",
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "billing".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    const CustomText(
                      text: 'CPC',
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

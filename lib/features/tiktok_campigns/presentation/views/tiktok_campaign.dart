import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_ad_group/ad_group_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_ad/tiktok_ad_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_campaign/tiktok_campaign_widget.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_review/tiktok_review_widget.dart';
import 'package:ads_dv/widgets/stepper/bottom_nav.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../widgets/appbar.dart';
import '../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';

class CreateTiktokCampaignScreen extends StatelessWidget {
  const CreateTiktokCampaignScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: const CustomBottomNavBar(
        isReview: true,
        isTiktok: true,
      ),
      appBar: const CustomAppBar(
        title: "Tiktok Campaign",
        showBackButton: true,
        hasDrawer: true,
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              if (instance<HiveHelper>().getAdvertiserId() != null)
                Column(
                  children: [
                    5.verticalSpace,
                    AccountHintText(
                      isDefaultHint: true,
                      hint:
                          "${instance<HiveHelper>().getTiktokPageName() ?? ""}'s Ad Account",
                    ),
                    20.verticalSpace,
                  ],
                )
              else
                const SizedBox(),
              const TiktokCampaignWidget(),
              SizedBox(height: 20.h),
              const AdGroupWidget(),
              SizedBox(height: 20.h),
              const TiktokAdWidget(),
              SizedBox(height: 20.h),
              const TiktokAdReviewWidget(),
            ],
          ),
        ),
      ),
    );
  }
}

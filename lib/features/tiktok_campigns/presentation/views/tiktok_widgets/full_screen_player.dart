import 'dart:io';

import 'package:ads_dv/features/tiktok_campigns/presentation/views/tiktok_widgets/video_caption_background.dart';
import 'package:ads_dv/widgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../../../../../utils/res/colors.dart';

class FullScreenPlayer extends StatefulWidget {
  final File videoUrl;
  final String caption;
  final String websiteLink;
  final String callToAction;

  const FullScreenPlayer(
      {super.key,
      required this.videoUrl,
      required this.caption,
      required this.websiteLink,
      required this.callToAction});

  @override
  State<FullScreenPlayer> createState() => _FullScreenPlayerState();
}

class _FullScreenPlayerState extends State<FullScreenPlayer> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();

    // Create and store the VideoPlayerController. The VideoPlayerController
    // offers several different constructors to play videos from assets, files,
    // or the internet.
    _controller = VideoPlayerController.file(
      widget.videoUrl,
    )..setVolume(10);
  }

  @override
  void dispose() {
    // Ensure disposing of the VideoPlayerController to free up resources.
    _controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _controller.initialize(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          // If the VideoPlayerController has finished initialization, use
          // the data it provides to limit the aspect ratio of the video.
          return GestureDetector(
            onTap: () {
              if (!_controller.value.isPlaying) {
                _controller.play();
              } else {
                // If the video is paused, play it.
                _controller.pause();
              }
            },
            child: AspectRatio(
              aspectRatio: _controller.value.aspectRatio,
              // Use the VideoPlayer widget to display the video.
              child: Stack(children: [
                VideoPlayer(_controller),
                const VideoCaptionBackground(),
                Positioned(
                    bottom: 20,
                    left: 20,
                    child: _VideoCaption(
                      caption: widget.caption,
                      websiteLink: widget.websiteLink,
                      callToAction: widget.callToAction,
                    ))
              ]),
            ),
          );
        } else {
          // If the VideoPlayerController is still initializing, show a
          // loading spinner.
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
      },
    );
  }
}

class _VideoCaption extends StatelessWidget {
  final String caption;
  final String websiteLink;
  final String callToAction;

  const _VideoCaption(
      {required this.caption,
      required this.websiteLink,
      required this.callToAction});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    const titleStyle = TextStyle(color: AppColors.white);

    return SizedBox(
      width: size.width * 0.6,
      child: Column(
        children: [
          Text(
            caption,
            maxLines: 2,
            style: titleStyle,
          ),
          Text(
            websiteLink,
            maxLines: 1,
            style: titleStyle.copyWith(overflow: TextOverflow.ellipsis),
          ),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 40.0, vertical: 5.0),
            child: CustomButton(
              text: callToAction, onPressed: () {},
              // child: Text(
              //   callToAction,
              //   maxLines: 2,
              //   style: titleStyle,
              // ),
            ),
          ),
        ],
      ),
    );
  }
}

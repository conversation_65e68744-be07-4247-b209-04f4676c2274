import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';


class VideoButtons extends StatelessWidget {
  bool? isSnapChat = false;

  VideoButtons({super.key, this.isSnapChat});

  @override
  Widget build(BuildContext context) {
    if (isSnapChat == false) {
      return Column(
        children: [
          const _CustomIconButton(
            value: 1200,
            color: Colors.red,
            iconData: Icons.favorite,
          ),
          const _CustomIconButton(
            value: 25000,
            color: Colors.white,
            iconData: Icons.remove_red_eye_outlined,
          ),
          SpinPerfect(
              infinite: true,
              duration: const Duration(seconds: 5),
              child: const _CustomIconButton(
                value: 0,
                color: Colors.white,
                iconData: Icons.play_circle_outline,
              ))
        ],
      );
    } else {
      return const Column(
        children: [
          _CustomIconButton(
            value: 1200,
            color: Colors.white,
            iconData: Icons.share,
          ),
          _CustomIconButton(
            value: 25000,
            color: Colors.white,
            iconData: Icons.menu,
          ),
        ],
      );
    }
  }
}

class _CustomIconButton extends StatelessWidget {
  final int value;
  final IconData iconData;
  final Color color;

  const _CustomIconButton(
      {required this.value, required this.iconData, required this.color});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        IconButton(
            onPressed: () {},
            icon: Icon(
              iconData,
              color: color,
              size: 30,
            )),
        // if (value > 0)
        //   Text(
        //     HumanFormat.humanReadableNumber(value.toDouble()),
        //     style: const TextStyle(color: AppColors.whiteColor),
        //   )
      ],
    );
  }
}

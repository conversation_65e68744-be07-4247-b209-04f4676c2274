import 'dart:io';

import 'package:ads_dv/features/tiktok_campigns/presentation/views/tiktok_widgets/video_buttons.dart';
import 'package:flutter/material.dart';

import 'full_screen_player.dart';

class VideoCreollableView extends StatelessWidget {
  final File videos;
  final String? caption;
  final String? websiteLink;
  final String? callToAction;

  const VideoCreollableView(
      {super.key,
      required this.videos,
      required this.caption,
      required this.websiteLink,
      required this.callToAction});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: SizedBox(
        height: 500,
        child: Stack(
          children: [
            SizedBox.expand(
                child: FullScreenPlayer(
                    videoUrl: videos,
                    caption: caption ?? "",
                    websiteLink: websiteLink ?? "",
                    callToAction: callToAction ?? "")),
            Positioned(
                bottom: 20,
                right: 20,
                child: VideoButtons(
                  isSnapChat: false,
                ))
          ],
        ),
      ),
    );
  }
}

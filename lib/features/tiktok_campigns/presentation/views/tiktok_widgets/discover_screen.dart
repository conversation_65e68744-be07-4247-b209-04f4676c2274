import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/tiktok_widgets/video_scrollable_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DiscoverScreen extends StatelessWidget {
  const DiscoverScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TiktokAdCubit, TiktokAdState>(
      builder: (adContext, state) {
        return
            // TiktokAdCubit.get(adContext).initialLoading
            //   ? const Center(child: CircularProgressIndicator(strokeWidth: 2))
            //   :
            VideoCreollableView(
          videos: TiktokAdCubit.get(adContext).adVideo,
          caption: TiktokAdCubit.get(adContext).adText.text,
          websiteLink: TiktokAdCubit.get(adContext).url.text,
          callToAction: TiktokAdCubit.get(adContext).typeName ?? "",
        );
      },
    );
  }
}

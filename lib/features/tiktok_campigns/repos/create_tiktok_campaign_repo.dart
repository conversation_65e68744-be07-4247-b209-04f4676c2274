import 'dart:io';

import 'package:dartz/dartz.dart';

import '../../../../utils/network/connection/network_info.dart';
import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/network/failure_helper.dart';
import '../../create_campaigns/data/models/objectives.dart';
import '../../create_campaigns/data/models/optimization.dart';
import '../data/data_source/create_tiktok_campaign_data_source.dart';
import '../data/models/identity_model.dart';
import '../data/models/tiktok_campaigns_response_model.dart';
import '../data/models/tiktok_interests_response_model.dart';
import '../data/models/tiktok_lang_model.dart';
import '../data/models/tiktok_location_response_model.dart';

class CreateTikTokCampaignRepo {
  NetworkInfo networkInfo;
  CreateTikTokCampaignDataSource createTikTokCampaignDataSource;

  CreateTikTokCampaignRepo(
      {required this.networkInfo,
      required this.createTikTokCampaignDataSource});

  Future<Either<Failure, TiktokCampaignsResponse>> getTiktokCampaigns(
      {required String advertiserId}) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource
            .getTiktokCampaigns(advertiserId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> createIdentity(
      {required String advertiserId,
      required File image,
      required String displayName}) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.createIdentity(
            advertiserId, image, displayName);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, TiktokLocationResponseModel>> getTiktokLocations(
      {required String advertiserId,
      String? searchKey,
      String? objectiveType}) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.getTiktokLocations(
            advertiserId, searchKey, objectiveType);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, TiktokInterestsResponseModel>> getTiktokInterests({
    required String advertiserId,
    String? searchKey,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.getTiktokInterests(
            advertiserId, searchKey);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, TiktokLanguagesResponseModel>> getTiktokLanguages({
    required String advertiserId,
    String? searchKey,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource
            .getTiktokLanguages(advertiserId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<Objective>>> getTiktokObjectives() {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.getTiktokObjectives();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<Optimizations>>> getTiktokOptimizations(
      {required String objectiveActualName}) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.getTiktokOptimizations(
            objectiveActualName: objectiveActualName);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<IdentityListModel>>> getTiktokIdentities(
      {required String advertiserId}) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.getTiktokIdentities(
            advertiserId: advertiserId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<IdentityListModel>>> getTiktokRealIdentities(
      {required String advertiserId}) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.getTiktokRealIdentities(
            advertiserId: advertiserId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> createAD({
    required List<File> imagesFiles,
    required List<File> videosFiles,
    required String advertiserId,
    required String campaignName,
    required String objectiveType,
    required String optimizationGoal,
    required String adGroupName,
    required List<String> location,
    required List<String> age,
    required String gender,
    required List<String> languages,
    required String dailyBudget,
    required String startDate,
    required String endDate,
    required List<String> selectedTiktokInterests,
    required String adName,
    required String identityId,
    required String adText,
    required String websiteUrl,
    required String callToAction,
    required File adVideo,
    required String placementType,
    required List<String> selectedTiktokPositions,
    File? image,
    String? countryCode,
    String? countryCallingCode,
    String? phone,
    int? existingCampaign,
    required String displayName,
    required String dailyBudgetMode,
    required String identityType,
    // required List<File> thumbFiles
  }) {
    return FailureHelper.instance(
      method: () async {
        return await createTikTokCampaignDataSource.createAD(
            imagesFiles: imagesFiles,
            videosFiles: videosFiles,
            advertiserId: advertiserId,
            campaignName: campaignName,
            objectiveType: objectiveType,
            optimizationGoal: optimizationGoal,
            adGroupName: adGroupName,
            location: location,
            age: age,
            gender: gender,
            languages: languages,
            dailyBudget: dailyBudget,
            startDate: startDate,
            endDate: endDate,
            selectedTiktokInterests: selectedTiktokInterests,
            adName: adName,
            adText: adText,
            websiteUrl: websiteUrl,
            callToAction: callToAction,
            adVideo: adVideo,
            placementType: placementType,
            selectedTiktokPositions: selectedTiktokPositions,
            identityId: identityId,
            image: image,
            displayName: displayName,
            dailyBudgetMode: dailyBudgetMode,
            identityType: identityType,
            countryCode: countryCode,
            countryCallingCode: countryCallingCode,
            phone: phone,
            existingCampaign: existingCampaign
            // thumbFiles: thumbFiles
            );
      },
      networkInfo: networkInfo,
    );
  }
}

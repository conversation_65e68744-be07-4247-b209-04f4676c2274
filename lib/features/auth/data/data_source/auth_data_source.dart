import 'package:dio/dio.dart';
// import 'package:flutter_web_auth/flutter_web_auth.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../models/user.dart';

class AuthDataSource {
  Future<UserData?> login({
    required String email,
    required String password,
  }) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
          EndPoints.login,
          params: FormData.fromMap({'email': email, 'password': password}),
          options: Options(method: Method.post.name));
      print('myUserDatadsfasdcdzx $response');
      return UserData.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  // Future<String> tiktokGetCode() async {
  //   try {
  //     final encodedRedirect = Uri.encodeComponent(Constants.redirectUri);
  //     // 1. Construct the proper authorization URL
  //     final String authUrl = 'https://www.tiktok.com/v2/auth/authorize/?'
  //         'client_key=${Constants.clientKey}&'
  //         'scope=${Constants.scope}&'
  //         'response_type=code&'
  //         'redirect_uri=$encodedRedirect&'
  //         'state=SOME_UNIQUE_STATE';
  //
  //     // 2. Use Web Auth flow (user must interact with TikTok's login page)
  //     // final result = await FlutterWebAuth.authenticate(
  //     //   url: authUrl,
  //     //   callbackUrlScheme: Uri.parse(Constants.redirectUri).scheme,
  //     //   preferEphemeral: false,
  //     // );
  //
  //     // 3. Extract code from the redirect URL
  //     final code = Uri.parse(result).queryParameters['code'];
  //
  //     if (code == null) {
  //       throw Exception('Authorization failed: No code received');
  //     }
  //
  //     return code;
  //   } catch (error) {
  //     rethrow;
  //   }
  // }

  Future<String?> checkEmailValidation({
    required String email,
    required String phone,
  }) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
          EndPoints.checkEmailValidation,
          params: FormData.fromMap({'email': email, 'phone': phone}),
          options: Options(method: Method.post.name));
      return response['message'];
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> storeFcmToken({
    required String token,
    String? userId,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.storeFcmToken,
              params: FormData.fromMap({
                'token': token,
                if (userId != null) 'user_id': userId,
              }),
              options: Options(method: Method.post.name));
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> facebookDisconnect({
    required String userId,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.facebookDisconnect,
              params: FormData.fromMap({
                'user_id': userId,
              }),
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<UserData?> updateToken({
    required String accessToken,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.updateToken,
              params: FormData.fromMap({
                'access_token': accessToken,
              }),
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return UserData.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<UserData?> register({
    required String name,
    required String email,
    required String phone,
    String? accessToken,
    String? password,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.register,
              params: FormData.fromMap(
                {
                  'name': name,
                  'email': email,
                  'phone': phone,
                  if (accessToken != null) 'access_token': accessToken,
                  if (password != null) 'password': password,
                },
              ),
              options: Options(method: Method.post.name));
      return UserData.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<UserData?> socialRegister({
    required String? type,
    required String name,
    required String email,
    required String phone,
    String? accessToken,
    String? password,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.socialRegister,
              params: FormData.fromMap(
                {
                  'type': type ?? 'facebook',
                  'name': name,
                  'email': email,
                  'phone': phone,
                  if (accessToken != null) 'access_token': accessToken,
                  if (password != null) 'password': password,
                },
              ),
              options: Options(method: Method.post.name));
      return UserData.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> forgetPassword({
    required String email,
  }) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
          EndPoints.forgetPassword,
          params: FormData.fromMap({'email': email}),
          options: Options(method: Method.post.name));
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> deactivateAccount({required bool isDelete}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        isDelete ? EndPoints.deleteAccount : EndPoints.deactivateAccount,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> validateOTP({
    required String email,
    required String otp,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.validateOtp,
              params: FormData.fromMap({
                'email': email,
                "otp": otp,
              }),
              options: Options(method: Method.post.name));
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> resetPassword({
    required String email,
    required String otp,
    required String password,
  }) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.resetPassword,
              params: FormData.fromMap({
                'email': email,
                "otp": otp,
                "password": password,
              }),
              options: Options(method: Method.post.name));
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<String> logout() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.logoutUrl,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return response['message'];
    } catch (error) {
      rethrow;
    }
  }
}


import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/network/connection/network_info.dart';
import 'package:ads_dv/utils/network/errors/failures.dart';
import 'package:ads_dv/utils/network/failure_helper.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../../utils/res/constants.dart';
import '../../../../utils/res/custom_widgets.dart';
import '../data_source/auth_data_source.dart';
import '../models/user.dart';

extension FacebookAuthCredential on AccessToken {
  OAuthCredential get credential => FacebookAuthProvider.credential(token);
}

extension FirebaseUserConverter on UserCredential? {
  UserData get userModel => UserData(
        accessToken: this?.credential?.accessToken ?? "",
        email: this?.user?.email ?? "",
        name: this?.user?.displayName ?? "",
        phone: this?.user?.phoneNumber ?? "",
      );
}

const _permission = [
  'email',
  'public_profile',
  'catalog_management',
  'business_management',
  'instagram_basic',
  'pages_manage_ads',
  'ads_read',
  'pages_read_engagement',
  'pages_show_list',
  'ads_management',
  'pages_manage_posts',
  'instagram_content_publish',

  ///no need now
  // 'instagram_manage_messages',
  // 'whatsapp_business_management',
  ///rejected from fb becuase did not used
  // 'pages_manage_cta',
  // 'read_insights',
  // 'pages_messaging',
  // 'pages_manage_engagement',
];

class AuthRepo {
  final FacebookAuth fbAuth;
  final FirebaseAuth auth;

  NetworkInfo networkInfo;
  AuthDataSource authDataSource;

  AuthRepo(
      {required this.networkInfo,
      required this.authDataSource,
      required this.fbAuth,
      required this.auth});

  Future<Either<Failure, UserData?>> login(
      {required String email, required String password}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.login(email: email, password: password);
        },
        networkInfo: networkInfo);
  }

  // Future<Either<Failure, String>> tiktokGetCode() {
  //   return FailureHelper.instance(
  //       method: () async {
  //         return await authDataSource.tiktokGetCode();
  //       },
  //       networkInfo: networkInfo);
  // }

  Future<Either<Failure, String?>> checkEmailValidation(
      {required String email, required String phone}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.checkEmailValidation(
              email: email, phone: phone);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, bool>> storeFcmToken(
      {required String token, String? userId}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.storeFcmToken(
              token: token, userId: userId);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, bool>> deactivateAccount({required bool isDelete}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.deactivateAccount(isDelete: isDelete);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, bool>> facebookDisconnect({required String userId}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.facebookDisconnect(userId: userId);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, UserData?>> updateToken(
      {required String accessToken}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.updateToken(accessToken: accessToken);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, UserData?>> register({
    required String name,
    required String email,
    required String phone,
    String? accessToken,
    String? password,
  }) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.register(
            name: name,
            email: email,
            phone: phone,
            accessToken: accessToken,
            password: password,
          );
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, UserData?>> socialRegister({
    required String? type,
    required String name,
    required String email,
    required String phone,
    String? accessToken,
    String? password,
  }) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.socialRegister(
            type: type,
            name: name,
            email: email,
            phone: phone,
            accessToken: accessToken,
            password: password,
          );
        },
        networkInfo: networkInfo);
  }

  FutureEither<User?> signUp({
    required String userEmail,
    required String password,
  }) async {
    try {
      // Perform sign up with Firebase Authentication
      UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: userEmail, password: password);

      // Return the user on successful sign up
      return Right(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      // Handle FirebaseAuth exceptions
      if (e.code == 'weak-password') {
        return const Left(
            ServerFailure(message: 'The password provided is too weak.'));
      } else if (e.code == 'email-already-in-use') {
        return const Left(ServerFailure(
            message: 'The account already exists for that email.'));
      }
      // Return a generic server failure if the exception is not handled
      return Left(ServerFailure(
          message: e.message ?? 'An error occurred during sign up.'));
    } catch (e, stackTrace) {
      // Handle other exceptions
      stackTrace
          .toString()
          .printLog(color: ANSICOLOR.red, name: e.runtimeType.toString());
      return Left(ServerFailure(message: e.toString()));
    }
  }

  FutureEither<AccessToken> signInFacebookAccount() async {
    try {
      final LoginResult loginResult =
          await fbAuth.login(permissions: _permission);

      if (loginResult.status != LoginStatus.success) {
        return const Left(ServerFailure(message: 'Facebook login failed'));
      }
      if (loginResult.accessToken == null) {
        return const Left(
            ServerFailure(message: 'Something Error with you facebook token'));
      }
      return Right(loginResult.accessToken!);
    } catch (e, stackTrace) {
      stackTrace
          .toString()
          .printLog(color: ANSICOLOR.red, name: e.runtimeType.toString());

      return Left(ServerFailure(message: e.toString()));
    }
  }

  Future<Either<ServerFailure, AuthorizationCredentialAppleID>>
      signInAppleAccount() async {
    try {
      final AuthorizationCredentialAppleID loginResult =
          await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      print('Login Result: $loginResult');

      return Right(loginResult);
    } catch (e, stackTrace) {
      stackTrace
          .toString()
          .printLog(color: ANSICOLOR.red, name: e.runtimeType.toString());
      return Left(ServerFailure(message: e.toString()));
    }
  }

  Future<Either<Failure, bool>> forgetPassword({required String email}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.forgetPassword(email: email);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, bool>> validateOTP(
      {required String email, required String otp}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.validateOTP(email: email, otp: otp);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, bool>> resetPassword(
      {required String email, required String otp, required String password}) {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.resetPassword(
              email: email, otp: otp, password: password);
        },
        networkInfo: networkInfo);
  }

  Future<UserCredential?> authInWithCredential(
      AuthCredential credential) async {
    try {
      final response = await auth.signInWithCredential(credential);
      if (response.user == null) return null;
      return response;
    } catch (e) {
      showErrorToast(e.toString());
      e.toString().printLog();
      return null;
    }
  }

  Future<void> logOutFacebook() async => await fbAuth.logOut();

  Future<Either<Failure, String>> logout() async {
    return FailureHelper.instance(
        method: () async {
          return await authDataSource.logout();
        },
        networkInfo: networkInfo);
    // try {
    //   final response = await authDataSource.logout();
    //   return response;
    // } catch (e) {
    //   showErrorToast(e.toString());
    //   e.toString().printLog();
    //   return e.toString();
    // }
  }
}

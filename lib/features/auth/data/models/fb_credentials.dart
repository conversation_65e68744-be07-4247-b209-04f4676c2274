class FbCredentials {
  final String name;
  final String email;
  final String pictureUrl;
  final String id;

  FbCredentials({
    required this.name,
    required this.email,
    required this.pictureUrl,
    required this.id,
  });

  // Factory constructor to create an instance of FbCredentials from JSON
  factory FbCredentials.fromJson(Map<String, dynamic> json) {
    return FbCredentials(
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      pictureUrl: json['picture'] != null
          ? json['picture']['data']['url'] ?? ''
          : '',
      id: json['id'] ?? '',
    );
  }

  // Method to convert FbCredentials instance to JSON (for sending to API, etc.)
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'picture': {'data': {'url': pictureUrl}},
      'id': id,
    };
  }
}
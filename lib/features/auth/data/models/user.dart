import 'dart:convert';

UserData userModelFromJson(String str) => UserData.fromJson(json.decode(str));

String userModelToJson(UserData data) => json.encode(data.toJson());

class UserData {
  int? id;
  String? userId;
  String? name;
  String? email;
  String? phone;
  String? photo;
  String? pageName;
  String? pagePic;
  String? emailVerifiedAt;
  String? token;
  String? accessToken;
  String? createdAt;
  String? updatedAt;
  String? defaultAccountId;
  String? defaultPageId;
  String? defaultPageAccessToken;
  String? defaultAccountName;
  String? pageUserName;
  String? tiktokToken;
  String? snapChatToken;
  String? instUserName;
  String? instAccId;
  String? instUserId;
  String? whatsNumber;

  UserData({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.photo,
    this.pageName,
    this.pagePic,
    this.emailVerifiedAt,
    this.token,
    this.accessToken,
    this.userId,
    this.createdAt,
    this.updatedAt,
    this.defaultAccountId,
    this.defaultPageId,
    this.defaultPageAccessToken,
    this.defaultAccountName,
    this.pageUserName,
    this.tiktokToken,
    this.snapChatToken,
    this.instUserName,
    this.instAccId,
    this.instUserId,
    this.whatsNumber,
  });

  UserData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'] ?? "";
    email = json['email'] ?? "";
    phone = json['phone'] ?? "";
    photo = json['photo'] ?? "";
    emailVerifiedAt = json['email_verified_at'] ?? "";
    token = json['token'] ?? "";
    accessToken = json['access_token'] ?? "";
    createdAt = json['created_at'] ?? "";
    updatedAt = json['updated_at'] ?? "";
    defaultAccountId = json['default_account_id'];
    defaultPageId = json['default_page_id'];
    defaultPageAccessToken = json['default_page_access_token'];
    defaultAccountName = json['default_account_name'];
    pageName = json['page_name'];
    pagePic = json['page_pic'];
    pageUserName = json['default_page_username'].toString();
    instUserName = json['default_insta_username'].toString();
    instAccId = json['default_insta_id'].toString();
    instUserId = json['instagram_user_id'].toString();
    whatsNumber = json['default_whats_number'].toString();
    tiktokToken = json['tiktok_token'];
    snapChatToken = json['snapchat_token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id ?? 0;
    data['name'] = name ?? "";
    data['email'] = email ?? "";
    data['phone'] = phone ?? "";
    data['photo'] = photo ?? "";
    data['email_verified_at'] = emailVerifiedAt ?? "";
    data['token'] = token ?? "";
    data['access_token'] = accessToken ?? "";
    data['created_at'] = createdAt ?? "";
    data['updated_at'] = updatedAt ?? "";
    data['access_token'] = accessToken ?? "";
    data['default_account_id'] = defaultAccountId;
    data['default_page_id'] = defaultPageId;
    data['default_page_access_token'] = defaultPageAccessToken;
    data['default_account_name'] = defaultAccountName;
    data['page_name'] = pageName;
    data['page_pic'] = pagePic;
    data['default_page_username'] = pageUserName;
    data['default_insta_username'] = instUserName;
    data['default_insta_id'] = instAccId;
    data['instagram_user_id'] = instUserId;
    data['default_whats_number'] = whatsNumber;
    data['tiktok_token'] = tiktokToken;
    data['snapchat_token'] = snapChatToken;

    return data;
  }

  UserData copyWith({
    int? id,
    String? userId,
    String? instUserId,
    String? name,
    String? email,
    String? phone,
    String? photo,
    String? pageName,
    String? pagePic,
    String? emailVerifiedAt,
    String? token,
    String? accessToken,
    String? createdAt,
    String? updatedAt,
    String? defaultAccountId,
    String? tiktokToken,
    String? snapChatToken,
    String? defaultPageId,
    String? defaultPageAccessToken,
    String? defaultAccountName,
    String? pageUserName,
    String? instUserName,
    String? instAccId,
    String? whatsNumber,
  }) {
    return UserData(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      photo: photo ?? this.photo,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      token: token ?? this.token,
      accessToken: accessToken ?? this.accessToken,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      defaultAccountId: defaultAccountId ?? this.defaultAccountId,
      defaultPageId: defaultPageId ?? this.defaultPageId,
      defaultPageAccessToken:
          defaultPageAccessToken ?? this.defaultPageAccessToken,
      defaultAccountName: defaultAccountName ?? this.defaultAccountName,
      pageName: pageName ?? this.pageName,
      pagePic: pagePic ?? this.pagePic,
      pageUserName: pageUserName ?? this.pageUserName,
      instUserName: instUserName ?? this.instUserName,
      instAccId: instAccId ?? this.instAccId,
      whatsNumber: whatsNumber ?? this.whatsNumber,
      instUserId: instUserId ?? this.instUserId,
      tiktokToken: tiktokToken ?? this.tiktokToken,
      snapChatToken: snapChatToken ?? this.snapChatToken,
    );
  }
}

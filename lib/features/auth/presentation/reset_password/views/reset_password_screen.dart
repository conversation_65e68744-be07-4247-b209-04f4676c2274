import 'package:ads_dv/features/auth/presentation/reset_password/controllers/reset_password_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/custom_widgets.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/custom_button.dart';
import '../../../../../widgets/custom_text_field.dart';

class ResetPasswordScreen extends StatefulWidget {
  String email;
  String otp;

  ResetPasswordScreen({super.key, required this.email, required this.otp});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _passwordConfirmController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _passwordController.dispose();
    _passwordConfirmController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ResetPasswordCubit(),
      child: BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
        builder: (context, state) {
          return Scaffold(
            body: Container(
              height: SizeConfig.screenHeight(context),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(AppAssets.forgetPassword),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(22),
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 55.h,
                        ),
                        CachedImageWidget(
                          assetsImage: AppAssets.logoColored,
                          height: 40.h,
                          width: 150.h,
                        ),
                        SizedBox(
                          height: 80.h,
                        ),
                        CustomText(
                          text: "setNewPassword".tr,
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(
                          height: 16.h,
                        ),
                        CustomText(
                          text:
                              "Enter your new password below and check the hint while setting it."
                                  .tr,
                          fontSize: 16.sp,
                          maxLines: 2,
                          fontWeight: FontWeight.w400,
                          color: Constants.gray,
                        ),
                        SizedBox(
                          height: 90.h,
                        ),
                        Column(
                          children: [
                            CustomTextFormField.withPrefixSVG(
                              svgURL: AppAssets.lock,
                              hintText: "Password".tr,
                              keyboardType: TextInputType.visiblePassword,
                              textInputAction: TextInputAction.done,
                              obscureText: !ResetPasswordCubit.get(context)
                                  .isPasswordVisible,
                              suffixIcon: buildPasswordVisibilityBtn(
                                  isVisible: ResetPasswordCubit.get(context)
                                      .isPasswordVisible,
                                  onPressed: ResetPasswordCubit.get(context)
                                      .chanePasswordVisibility),
                              controller: _passwordController,
                              validator: (value) =>
                                  AppValidator.validPassword(value),
                            ),
                            SizedBox(height: 40.h),
                            CustomTextFormField.withPrefixSVG(
                              svgURL: AppAssets.lock,
                              hintText: "Confirm Password".tr,
                              keyboardType: TextInputType.visiblePassword,
                              textInputAction: TextInputAction.done,
                              obscureText: !ResetPasswordCubit.get(context)
                                  .isPasswordConfirmVisible,
                              suffixIcon: buildPasswordVisibilityBtn(
                                isVisible: ResetPasswordCubit.get(context)
                                    .isPasswordConfirmVisible,
                                onPressed: ResetPasswordCubit.get(context)
                                    .chanePasswordConfirmVisibility,
                              ),
                              controller: _passwordConfirmController,
                              validator: (value) =>
                                  AppValidator.validateConfirmPassword(
                                      value, _passwordController.text),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 50.h,
                        ),
                        state is ResetPasswordLoading
                            ? const LoadingWidget(
                                isCircle: true,
                              )
                            : SizedBox(
                                width: 252.w,
                                height: 60.h,
                                child: GestureDetector(
                                  onTap: () {
                                    if (_formKey.currentState!.validate()) {
                                      ResetPasswordCubit.get(context)
                                          .resetPassword(
                                              email: widget.email,
                                              otp: widget.otp,
                                              password:
                                                  _passwordController.text,
                                              context: context);
                                    }
                                  },
                                  child: Stack(
                                    children: [
                                      ShaderMask(
                                        shaderCallback: (Rect bounds) {
                                          return Constants.defGradient
                                              .createShader(bounds);
                                        },
                                        child: CustomButton(
                                          text: '',
                                          textColor: Colors.white,
                                          onPressed: () {},
                                          //    onPressed: controller.signInWithEmailAndPassword,
                                        ),
                                      ),
                                      Positioned(
                                        top: 15.h,
                                        right: 15.w,
                                        left: 15.w,
                                        child: CustomText(
                                          text: 'Confirm'.tr,
                                          fontSize: 16.sp,
                                          color: AppColors.white,
                                          alignment:
                                              AlignmentDirectional.center,
                                          textAlign: TextAlign.center,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

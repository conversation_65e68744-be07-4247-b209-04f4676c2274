part of 'reset_password_cubit.dart';

@immutable
abstract class ResetPasswordState {
  const ResetPasswordState();
  List<Object?> get props => [];
}

class ResetPasswordInitial extends ResetPasswordState {}

class ResetPasswordLoading extends ResetPasswordState {}

class ResetPasswordLoaded extends ResetPasswordState {
}

class ResetPasswordError extends ResetPasswordState {
  final Failure message;

  const ResetPasswordError(this.message);

  @override
  List<Object?> get props => [message];
}

class ChangePasswordVisibilityState extends ResetPasswordState {}

class ChangePasswordConfirmVisibilityState extends ResetPasswordState {}
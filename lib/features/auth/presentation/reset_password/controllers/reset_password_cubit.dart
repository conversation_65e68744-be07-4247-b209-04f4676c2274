import 'package:ads_dv/features/auth/data/repos/auth_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/common_utils.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/cached__image.dart';
import '../../../../../widgets/custom_text.dart';

part 'reset_password_state.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  ResetPasswordCubit() : super(ResetPasswordInitial());

  static ResetPasswordCubit get(context) => BlocProvider.of(context);

  bool isPasswordVisible = false;
  bool isPasswordConfirmVisible = false;

  resetPassword(
      {required String email,
      required String otp,
      required String password,
      required BuildContext context}) async {
    emit(ResetPasswordLoading());
    instance<AuthRepo>()
        .resetPassword(email: email, otp: otp, password: password)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ResetPasswordError(l));
      }, (r) async {
        CommonUtils.showBottomDialog(
            context,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 34.sp),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CachedImageWidget(
                    assetsImage: AppAssets.pass,
                    height: 120.h,
                  ),
                  20.verticalSpace,
                  CustomText(
                    text: "Password Recovery Successful",
                    fontSize: 26.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  CustomText(
                    text: "Return to the login screen to enter the application",
                    fontSize: 16.sp,
                    color: const Color(0xFF808080),
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 50.sp),
                    child: ButtonWidget(
                      text: "Return to login",
                      fontSize: 16.sp,
                      padding: 16.sp,
                      onTap: () {
                        Navigator.of(context).pushNamedAndRemoveUntil(
                          Routes.login,
                          (Route<dynamic> route) => false,
                        );
                      },
                    ),
                  )
                ],
              ),
            ));
      });
    });
  }

  void chanePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
    emit(ChangePasswordVisibilityState());
  }

  void chanePasswordConfirmVisibility() {
    isPasswordConfirmVisible = !isPasswordConfirmVisible;
    emit(ChangePasswordConfirmVisibilityState());
  }
}

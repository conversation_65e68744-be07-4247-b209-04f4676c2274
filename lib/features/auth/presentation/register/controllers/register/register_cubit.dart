import 'dart:async';

import 'package:ads_dv/features/auth/data/repos/auth_repo.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../utils/res/router/routes.dart';

part 'register_state.dart';

class RegisterCubit extends Cubit<RegisterState> {
  RegisterCubit() : super(RegisterInitial());

  static RegisterCubit get(context) => BlocProvider.of(context);

  bool isEmailVerified = false;
  bool isPasswordVisible = false;
  bool isPasswordConfirmVisible = false;
  bool isAgreedToTermsAndPolices = false;
  Timer? timer;
  FirebaseAuth? auth;

  void sendEmailVerification(
      {required String name,
      required String email,
      required String phone,
      String? accessToken,
      required String password,
      required BuildContext context}) {
    FirebaseAuth.instance.currentUser?.sendEmailVerification();
    timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      checkEmailVerified(
          name: name,
          email: email,
          phone: phone,
          password: password,
          context: context);
    });
  }

  checkEmailVerified(
      {required String name,
      required String email,
      required String phone,
      String? accessToken,
      required String password,
      required BuildContext context}) async {
    await FirebaseAuth.instance.currentUser?.reload();

    isEmailVerified = FirebaseAuth.instance.currentUser!.emailVerified;

    if (isEmailVerified) {
      showSuccessToast("Email Successfully Verified");
      register(
          name: name,
          email: email,
          phone: phone,
          password: password,
          context: context);
      timer?.cancel();
    }
  }

  bool emailIsValid = false;

  Future<void> checkEmailValidation(
      {required String phone,
      required String email,
      required BuildContext context}) async {
    await instance<AuthRepo>()
        .checkEmailValidation(email: email, phone: phone)
        .then((value) => value.fold(
            (l) => showErrorToast(l.message),
            (r) =>
                r == "Success" ? emailIsValid = true : emailIsValid = false));
    // if (isEmailVerified) {
    //   showSuccessToast("Email Successfully Verified");

    // }
    print('emailIsValid $emailIsValid');
  }

  storeFcmToken(
      {required String token,
      required String userId,
      required BuildContext context}) async {
    instance<AuthRepo>().storeFcmToken(token: token, userId: userId);
  }

  register(
      {required String name,
      required String email,
      String? phone,
      // String? accessToken,
      required String password,
      required BuildContext context}) async {
    emit(RegisterLoading());
    instance<AuthRepo>()
        .register(
      name: name,
      email: email,
      phone: phone ?? "",
      // accessToken: accessToken,
      password: password,
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(RegisterError(l));
      }, (r) async {
        if (r != null) {
          await instance<HiveHelper>().setUserModel(r);
          await instance<HiveHelper>().setToken(r.token ?? "");
          storeFcmToken(
              token: instance<HiveHelper>().getFcmToken().toString(),
              userId: r.id.toString(),
              context: context);
        }
        Navigator.of(context).pushNamedAndRemoveUntil(
          Routes.home,
          (Route<dynamic> route) => false,
        );
      });
    });
  }

  Future<void> singInWithEmail(
      {required String email,
      required String phone,
      required String name,
      required String password,
      required BuildContext context}) async {
    if (!isAgreedToTermsAndPolices) {
      showErrorToast(
          "You must agree to terms and conditions and privacy policy");
      return;
    }
    await checkEmailValidation(phone: phone, email: email, context: context);
    if (emailIsValid == false) return;
    emit(SignUpLoading());

    final fbAuth =
        await instance<AuthRepo>().signUp(userEmail: email, password: password);

    fbAuth.fold((l) {
      showErrorToast(l.message);
      emit(SignUpError());
    }, (user) async {
      if (user != null) {
        Navigator.pushNamedAndRemoveUntil(
            context, Routes.verifyEmail, (route) => false,
            arguments: {
              "name": name,
              "email": email,
              "phone": phone,
              "password": password,
            });
        emit(SignUpSuccess());
      }
    });
  }

  void chanePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
    emit(ChangePasswordVisibilityState());
  }

  void chanePasswordConfirmVisibility() {
    isPasswordConfirmVisible = !isPasswordConfirmVisible;
    emit(ChangePasswordConfirmVisibilityState());
  }

  void changeEmailVerificationStatus() {
    isEmailVerified = FirebaseAuth.instance.currentUser!.emailVerified;
    emit(ChangeEmailVerificationStatus());
  }

  void chaneAgreedTerms() {
    isAgreedToTermsAndPolices = !isAgreedToTermsAndPolices;
    emit(ChangeAgreedTermsState());
  }
}

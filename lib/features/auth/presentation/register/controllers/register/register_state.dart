part of 'register_cubit.dart';

@immutable
abstract class RegisterState {
  const RegisterState();
  List<Object?> get props => [];
}

class RegisterInitial extends RegisterState {}

class RegisterLoading extends RegisterState {}

class RegisterLoaded extends RegisterState {
}

class RegisterError extends RegisterState {
  final Failure message;

  const RegisterError(this.message);

  @override
  List<Object?> get props => [message];
}

class ChangePasswordVisibilityState extends RegisterState {}

class ChangePasswordConfirmVisibilityState extends RegisterState {}

class ChangeAgreedTermsState extends RegisterState {}
class ChangeEmailVerificationStatus extends RegisterState {}



class SignUpLoading extends RegisterState {}

class SignUpError extends RegisterState {}

class SignUpSuccess extends RegisterState {}


class StoreTokenLoading extends RegisterState {}

class StoreTokenLoaded extends RegisterState {
}

class StoreTokenError extends RegisterState {
  final Failure message;

  const StoreTokenError(this.message);

  @override
  List<Object?> get props => [message];
}
import 'package:ads_dv/features/auth/presentation/register/controllers/register/register_cubit.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:flutter/material.dart';

import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../widgets/custom_text_field.dart';

class RegisterForm extends StatefulWidget {
  final formKey;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController passwordController;
  final TextEditingController passwordConfirmController;

  RegisterCubit registerCubit;

  RegisterForm(
      {super.key,
      required this.registerCubit,
      required this.formKey,
      required this.nameController,
      required this.emailController,
      required this.phoneController,
      required this.passwordController,
      required this.passwordConfirmController});

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  @override
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics:
          const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
      child: Form(
        key: widget.formKey,
        child: Column(
          children: [
            CustomTextFormField.withPrefixSVG(
              controller: widget.nameController,
              svgURL: AppAssets.user,
              hintText: "Name",
              validator: (value) =>
                  AppValidator.validateIdentity(value, context),
            ),
            16.hSpace,
            CustomTextFormField.withPrefixSVG(
              controller: widget.emailController,
              svgURL: AppAssets.mail,
              hintText: "E-Mail",
              validator: (value) => AppValidator.validateEmail(value),
            ),
            16.hSpace,
            CustomTextFormField.withPrefixSVG(
              hintText: "Phone",
              svgURL: AppAssets.phone,
              controller: widget.phoneController,
              // validator: (value) =>
              //     AppValidator.validateIdentity(value, context),
            ),
            16.hSpace,
            CustomTextFormField.withPrefixSVG(
              controller: widget.passwordController,
              svgURL: AppAssets.password,
              hintText: "Password",
              keyboardType: TextInputType.visiblePassword,
              textInputAction: TextInputAction.done,
              obscureText: !widget.registerCubit.isPasswordVisible,
              suffixIcon: buildPasswordVisibilityBtn(
                  isVisible: widget.registerCubit.isPasswordVisible,
                  onPressed: widget.registerCubit.chanePasswordVisibility),
              validator: (value) => AppValidator.validPassword(value),
            ),
            16.hSpace,
            CustomTextFormField.withPrefixSVG(
              svgURL: AppAssets.password,
              hintText: "Confirm Password",
              controller: widget.passwordConfirmController,
              keyboardType: TextInputType.visiblePassword,
              textInputAction: TextInputAction.done,
              obscureText: !widget.registerCubit.isPasswordConfirmVisible,
              suffixIcon: buildPasswordVisibilityBtn(
                isVisible: widget.registerCubit.isPasswordConfirmVisible,
                onPressed: widget.registerCubit.chanePasswordConfirmVisibility,
              ),
              validator: (value) => AppValidator.validateConfirmPassword(
                  value, widget.passwordController.text),
            ),
          ],
        ),
      ),
    );
  }
}

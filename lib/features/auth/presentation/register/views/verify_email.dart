import 'package:ads_dv/features/auth/presentation/register/controllers/register/register_cubit.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/media_query_config.dart';
import '../../../../../widgets/custom_button.dart';
import '../../../../../widgets/custom_text.dart';

class VerifyEmailScreen extends StatefulWidget {
  String name;
  String email;
  String phone;
  String password;

  VerifyEmailScreen({
    super.key,
    required this.name,
    required this.email,
    required this.phone,
    required this.password,
  });

  @override
  State<VerifyEmailScreen> createState() => _VerifyEmailScreenState();
}

class _VerifyEmailScreenState extends State<VerifyEmailScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RegisterCubit()
        ..sendEmailVerification(
            name: widget.name,
            email: widget.email,
            phone: widget.phone,
            password: widget.password,
            context: context),
      child: BlocBuilder<RegisterCubit, RegisterState>(
        builder: (context, state) {
          return Scaffold(
            body: Container(
              height: SizeConfig.screenHeight(context),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(AppAssets.forgetPassword),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(22),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Lottie.asset(AppAssets.verify, width: 190.h, height: 190.h),
                    SizedBox(
                      height: 50.h,
                    ),
                    CustomText(
                      text: "checkMail".tr,
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.blackColor,
                      alignment: AlignmentDirectional.center,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    CustomText(
                      text: "We have sent you a mail on your email".tr,
                      fontSize: 16.sp,
                      alignment: AlignmentDirectional.center,
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      fontWeight: FontWeight.w400,
                      color: Constants.gray,
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    CustomText(
                      text: widget.email,
                      fontSize: 16.sp,
                      alignment: AlignmentDirectional.center,
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      fontWeight: FontWeight.w600,
                      color: Constants.gray,
                    ),
                    SizedBox(
                      height: 50.h,
                    ),
                    SizedBox(
                      width: 252.w,
                      height: 60.h,
                      child: GestureDetector(
                        onTap: () async {
                          try {
                            await FirebaseAuth.instance.currentUser
                                ?.sendEmailVerification();
                            showSuccessToast("Email sent successfully".tr);
                          } catch (e) {
                            showErrorToast("Failed to send email".tr);
                          }
                        },
                        child: Stack(
                          children: [
                            ShaderMask(
                              shaderCallback: (Rect bounds) {
                                return Constants.defGradient
                                    .createShader(bounds);
                              },
                              child: CustomButton(
                                text: '',
                                textColor: Colors.white,
                                onPressed: () {},
                                //    onPressed: controller.signInWithEmailAndPassword,
                              ),
                            ),
                            Positioned(
                              top: 15.h,
                              right: 15.w,
                              left: 15.w,
                              child: CustomText(
                                text: "resend".tr,
                                fontSize: 16.sp,
                                color: AppColors.white,
                                alignment: AlignmentDirectional.center,
                                textAlign: TextAlign.center,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

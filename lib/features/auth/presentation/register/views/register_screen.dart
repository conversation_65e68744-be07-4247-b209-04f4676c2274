import 'package:ads_dv/features/auth/presentation/register/controllers/register/register_cubit.dart';
import 'package:ads_dv/features/auth/presentation/register/views/widgets/register_form.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/dv_logo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../widgets/custom_button.dart';
import '../../../../../widgets/custom_checkbox.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/loading_widget.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _passwordConfirmController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RegisterCubit(),
      child: BlocBuilder<RegisterCubit, RegisterState>(
        builder: (context, state) {
          return Scaffold(
            body: Stack(
              children: [
                Container(
                  decoration:
                      const BoxDecoration(gradient: Constants.defGradient),
                  height: 200.h,
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.white,
                    height: 0.5.sh,
                  ),
                ),
                Column(
                  children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    const SafeArea(
                      child: DVLogo(),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24).w,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(50.r),
                          ),
                        ),
                        child: Column(
                          children: [
                            40.hSpace,
                            CustomText(
                              text: "Sign Up".tr,
                              fontSize: 22.sp,
                              color: AppColors.blackColor,
                              alignment: AlignmentDirectional.center,
                              textAlign: TextAlign.center,
                              fontWeight: FontWeight.w700,
                            ),
                            40.hSpace,
                            RegisterForm(
                              formKey: _formKey,
                              registerCubit: RegisterCubit.get(context),
                              nameController: _nameController,
                              emailController: _emailController,
                              phoneController: _phoneController,
                              passwordController: _passwordController,
                              passwordConfirmController:
                                  _passwordConfirmController,
                            ).expanded,
                            12.hSpace,
                            CustomCheckbox(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              intialValue: RegisterCubit.get(context)
                                  .isAgreedToTermsAndPolices,
                              onChanged: (value) {
                                if (value == null) return;
                                RegisterCubit.get(context).chaneAgreedTerms();
                              },
                              child: Wrap(
                                alignment: WrapAlignment.start,
                                runSpacing: 0,
                                spacing: 0,
                                crossAxisAlignment: WrapCrossAlignment.center,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Expanded(
                                        child: CustomText(
                                          maxLines: 2,
                                          text:
                                              'I agree to the terms and conditions and privacy policy'
                                                  .tr
                                                  .tr,
                                          fontWeight: FontWeight.w400,
                                          fontSize: 12.sp,
                                          alignment:
                                              AlignmentDirectional.centerStart,
                                          color: Constants.gray,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            12.hSpace,
                            state is SignUpLoading
                                ? const LoadingWidget(isCircle: true)
                                : GestureDetector(
                                    onTap: () {
                                      if (_formKey.currentState!.validate()) {
                                        RegisterCubit.get(context)
                                            .singInWithEmail(
                                                password:
                                                    _passwordController.text,
                                                email: _emailController.text,
                                                name: _nameController.text,
                                                phone: _phoneController.text,
                                                context: context);
                                      }
                                    },
                                    child: Stack(
                                      children: [
                                        ShaderMask(
                                          shaderCallback: (Rect bounds) {
                                            return Constants.defGradient
                                                .createShader(bounds);
                                          },
                                          child: CustomButton(
                                            text: '',
                                            textColor: Colors.white,
                                            onPressed: () {
                                              if (_formKey.currentState!
                                                  .validate()) {
                                                RegisterCubit.get(context)
                                                    .register(
                                                        password:
                                                            _passwordController
                                                                .text,
                                                        email: _emailController
                                                            .text,
                                                        name: _nameController
                                                            .text,
                                                        phone: _phoneController
                                                            .text,
                                                        context: context);
                                              }
                                            },
                                            //    onPressed: controller.signInWithEmailAndPassword,
                                          ),
                                        ),
                                        Positioned(
                                          top: 3.h,
                                          bottom: 3.h,
                                          right: 15.w,
                                          left: 15.w,
                                          child: CustomText(
                                            text: "signup".tr,
                                            fontSize: 14.sp,
                                            color: AppColors.white,
                                            alignment:
                                                AlignmentDirectional.center,
                                            textAlign: TextAlign.center,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    ),
                    SafeArea(
                      top: false,
                      bottom: true,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomText(
                            text: "alreadyMember".tr,
                            color: Constants.gray,
                            fontSize: 14.sp,
                          ),
                          CustomButton(
                            text: "signInHint".tr,
                            onPressed: () {
                              Navigator.pushNamedAndRemoveUntil(
                                  context, Routes.login, (route) => false);
                            },
                            color: Colors.transparent,
                            textColor: AppColors.blackColor,
                            hoveColor: Constants.mainColor,
                            textWeight: FontWeight.normal,
                            textSize: 16.sp,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

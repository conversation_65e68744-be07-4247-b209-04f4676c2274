import 'dart:io';

import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/colors.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../widgets/custom_button.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import '../../../../../../widgets/dv_logo.dart';
import '../../../../../utils/res/router/routes.dart';
import '../controllers/login/login_cubit.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({
    super.key,
  });

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _emailController = TextEditingController();

  final TextEditingController _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: BlocBuilder<LoginCubit, LoginState>(
        builder: (context, state) {
          return Scaffold(
            body: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: AlignmentDirectional.centerStart,
                      end: AlignmentDirectional.centerEnd,
                      colors: Constants.defGradient.colors,
                      stops: Constants.defGradient.stops,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.white,
                    height: 60.h,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Spacer(flex: 1),
                    const DVLogo(),
                    const Spacer(),
                    Expanded(
                      flex: 6,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 24.w, vertical: 14.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(50.r),
                          ),
                        ),
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(
                              parent: AlwaysScrollableScrollPhysics()),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              children: [
                                SizedBox(height: 20.h),
                                CustomText(
                                  text: 'Sign In'.tr,
                                  fontSize: 18.sp,
                                  color: AppColors.blackColor,
                                  alignment: AlignmentDirectional.center,
                                  textAlign: TextAlign.center,
                                  fontWeight: FontWeight.w700,
                                ),
                                SizedBox(height: 40.h),
                                Column(
                                  children: [
                                    CustomTextFormField.withPrefixSVG(
                                      svgURL: AppAssets.mail,
                                      hintText: "E-Mail or Phone number".tr,
                                      controller: _emailController,
                                      // validator: (value) =>
                                      //     AppValidator.validateEmail(value),
                                    ),
                                    SizedBox(height: 16.h),
                                    CustomTextFormField.withPrefixSVG(
                                      svgURL: AppAssets.lock,
                                      hintText: "Password".tr,
                                      keyboardType:
                                          TextInputType.visiblePassword,
                                      textInputAction: TextInputAction.done,
                                      obscureText: !LoginCubit.get(context)
                                          .isPasswordVisible,
                                      suffixIcon: buildPasswordVisibilityBtn(
                                          isVisible: LoginCubit.get(context)
                                              .isPasswordVisible,
                                          onPressed: LoginCubit.get(context)
                                              .chanePasswordVisibility),
                                      controller: _passwordController,
                                      validator: (value) =>
                                          AppValidator.validPassword(value),
                                    )
                                  ],
                                ),
                                32.hSpace,

                                state is LoginLoading
                                    ? const LoadingWidget(isCircle: true)
                                    : GestureDetector(
                                        onTap: () {
                                          if (_formKey.currentState!
                                              .validate()) {
                                            LoginCubit.get(context).login(
                                                password:
                                                    _passwordController.text,
                                                email: _emailController.text,
                                                context: context);
                                          }
                                        },
                                        child: Stack(
                                          children: [
                                            ShaderMask(
                                              shaderCallback: (Rect bounds) {
                                                return Constants.defGradient
                                                    .createShader(bounds);
                                              },
                                              child: CustomButton(
                                                text: '',
                                                textColor: Colors.white,
                                                onPressed: () {
                                                  if (_formKey.currentState!
                                                      .validate()) {
                                                    LoginCubit.get(context).login(
                                                        password:
                                                            _passwordController
                                                                .text,
                                                        email: _emailController
                                                            .text,
                                                        context: context);
                                                  }
                                                },
                                                //    onPressed: controller.signInWithEmailAndPassword,
                                              ),
                                            ),
                                            Positioned(
                                              top: 3.h,
                                              bottom: 3.h,
                                              right: 15.w,
                                              left: 15.w,
                                              child: CustomText(
                                                text: 'Sign In'.tr,
                                                fontSize: 14.sp,
                                                color: AppColors.white,
                                                alignment:
                                                    AlignmentDirectional.center,
                                                textAlign: TextAlign.center,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                SizedBox(height: 24.h),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomText(
                                      text: "${'Forget Your Password'.tr}?",
                                      color: Constants.gray,
                                      fontSize: 13.sp,
                                    ),
                                    CustomButton(
                                      text: "resetPassword".tr,
                                      onPressed: () {
                                        Navigator.pushNamed(
                                            context, Routes.forgetPassword);
                                        // Get.toNamed(Routes.MAIN);
                                      },
                                      color: Colors.transparent,
                                      textColor: AppColors.blackColor,
                                      hoveColor: Constants.mainColor,
                                      textWeight: FontWeight.normal,
                                      textSize: 13.sp,
                                    ),
                                  ],
                                ),
                                // const Spa cer(),
                                // spaceH(54),
                                12.hSpace,
                                CustomText(
                                  text: 'OR'.tr,
                                  color: Constants.gray,
                                  fontSize: 14.sp,
                                  alignment: AlignmentDirectional.center,
                                  textAlign: TextAlign.center,
                                ),
                                24.hSpace,
                                // spaceH(20),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    state is FbSignInLoading
                                        ? const LoadingWidget(
                                            isCircle: true,
                                          )
                                        : SizedBox(
                                            height: 44.h,
                                            width: 44.h,
                                            child: Material(
                                              elevation: 4,
                                              color: Colors.white,
                                              clipBehavior: Clip.antiAlias,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(50.r),
                                              ),
                                              child: InkWell(
                                                onTap: () async {
                                                  LoginCubit.get(context)
                                                      .signInWithFacebook(
                                                    isConnect: false,
                                                    context: context,
                                                  );
                                                },
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(6),
                                                  child: CustomSvgWidget(
                                                    svg: AppAssets.fb,
                                                    width: 44.h,
                                                    height: 44.h,
                                                    color: null,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                    Visibility(
                                        visible: (Platform.isIOS),
                                        child: 24.horizontalSpace),
                                    Visibility(
                                      visible: (Platform.isIOS),
                                      child: SizedBox(
                                        height: 44.h,
                                        width: 44.h,
                                        child: Material(
                                          elevation: 4,
                                          color: Colors.white,
                                          clipBehavior: Clip.antiAlias,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(50.r),
                                          ),
                                          child: state is AppleSignInLoading
                                              ? const LoadingWidget(
                                                  isCircle: true,
                                                )
                                              : InkWell(
                                                  onTap: () async {
                                                    await LoginCubit.get(
                                                            context)
                                                        .signInWithApple(
                                                      context: context,
                                                    );
                                                  },
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10),
                                                    child: CustomSvgWidget(
                                                      svg:
                                                          "assets/icons/apple.svg",
                                                      width: 44.h,
                                                      height: 44.h,
                                                    ),
                                                  ),
                                                ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomText(
                          text: "newMember".tr,
                          color: Constants.gray,
                          fontSize: 13.sp,
                        ),
                        CustomButton(
                          text: "signupHint".tr,
                          onPressed: () {
                            Navigator.pushNamedAndRemoveUntil(
                                context, Routes.register, (route) => false);
                            //   Get.offNamedUntil(Routes.SIGN_UP, ModalRoute.withName(Routes.SIGN_UP));
                            // Get.offNamedUntil(page, (route) => false)
                            // Get.toNamed(Routes.SIGN_UP);
                          },
                          color: Colors.transparent,
                          textColor: AppColors.blackColor,
                          hoveColor: Constants.mainColor,
                          textWeight: FontWeight.normal,
                          textSize: 13.sp,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

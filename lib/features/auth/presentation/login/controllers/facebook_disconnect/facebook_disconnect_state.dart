part of 'facebook_disconnect_cubit.dart';

@immutable
abstract class FacebookDisconnectState {
  const FacebookDisconnectState();
  List<Object?> get props => [];
}

class FacebookDisconnectInitial extends FacebookDisconnectState {}

class FacebookDisconnectLoading extends Facebook<PERSON>isconnectState {}

class FacebookDisconnectLoaded extends FacebookDisconnectState {
}

class FacebookDisconnectError extends FacebookDisconnectState {
  final Failure message;

  const FacebookDisconnectError(this.message);

  @override
  List<Object?> get props => [message];
}

class FacebookDisconnected extends FacebookDisconnectState {
}

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../data/models/user.dart';
import '../../../../data/repos/auth_repo.dart';

part 'facebook_disconnect_state.dart';

class FacebookDisconnectCubit extends Cubit<FacebookDisconnectState> {
  FacebookDisconnectCubit() : super(FacebookDisconnectInitial());

  static FacebookDisconnectCubit get(context) => BlocProvider.of(context);

  facebookDisconnect({
    required String userId,
    required BuildContext context,
  }) async {
    emit(FacebookDisconnectLoading());

    final user = instance<HiveHelper>().getUser(); // Fetch user once

    instance<AuthRepo>().facebookDisconnect(userId: userId).then((value) {
      value.fold(
        (l) {
          FailureHelper.instance.handleFailures(l, context);
          emit(FacebookDisconnectError(l));
        },
        (r) async {
          // Create an updated userData model with default values
          UserData userData = UserData().copyWith(
            accessToken: null,
            id: user?.id ?? 0,
            name: user?.name ?? '',
            email: user?.email ?? '',
            userId: user?.userId ?? '',
            createdAt: user?.createdAt ?? '',
            updatedAt: user?.updatedAt ?? '',
            phone: user?.phone ?? '',
            photo: user?.photo ?? '',
            emailVerifiedAt: user?.emailVerifiedAt ?? '',
            token: user?.token ?? '',
            defaultAccountName: user?.defaultAccountName ?? '',
            defaultPageId: user?.defaultPageId ?? '0',
            defaultPageAccessToken: user?.defaultPageAccessToken ?? '',
            defaultAccountId: user?.defaultAccountId ?? '',
            instUserId: user?.instUserId ?? "",
            snapChatToken: user?.snapChatToken,
            tiktokToken: user?.tiktokToken,
          );

          await instance<HiveHelper>().setUserModel(userData);

          // Clear AdAccount and MetaPages
          await instance<HiveHelper>().deleteAdAccount();
          await instance<HiveHelper>().deleteMetaPages();

          // await updateStatus();
        },
      );
    });
  }

  Future<void> updateStatus() async {
    emit(FacebookDisconnected());
  }
}

part of 'deactivate_account_cubit.dart';

@immutable
abstract class DeactivateAccountState {
  const DeactivateAccountState();
  List<Object?> get props => [];
}

class DeactivateAccountInitial extends DeactivateAccountState {}

class DeactivateAccountLoading extends DeactivateAccountState {}

class DeactivateAccountLoaded extends DeactivateAccountState {
}

class DeactivateAccountError extends DeactivateAccountState {
  final Failure message;

  const DeactivateAccountError(this.message);

  @override
  List<Object?> get props => [message];
}


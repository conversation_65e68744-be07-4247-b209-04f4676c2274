import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../data/repos/auth_repo.dart';

part 'deactivate_account_state.dart';

class DeactivateAccountCubit extends Cubit<DeactivateAccountState> {
  DeactivateAccountCubit() : super(DeactivateAccountInitial());

  static DeactivateAccountCubit get(context) => BlocProvider.of(context);

  deactivateAccount({required BuildContext context,required bool isDelete}) async {
    emit(DeactivateAccountLoading());
    instance<AuthRepo>().deactivateAccount(isDelete: isDelete).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(DeactivateAccountError(l));
      }, (r) async {
        instance<HiveHelper>().logout(context);
        emit(DeactivateAccountLoaded());
      });
    });
  }
}

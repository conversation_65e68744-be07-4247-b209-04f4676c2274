import 'dart:async';

import 'package:ads_dv/features/auth/data/models/user.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../../../../../utils/di/injection.dart';
import '../../../../../../../utils/network/errors/failures.dart';
import '../../../../../../../utils/network/failure_helper.dart';
import '../../../../../../../utils/res/router/routes.dart';
import '../../../../../../utils/ext.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../data/models/fb_credentials.dart';
import '../../../../data/repos/auth_repo.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(LoginInitial());

  static LoginCubit get(context) => BlocProvider.of(context);

  bool isPasswordVisible = false;

  storeFcmToken(
      {required String token,
      String? userId,
      required BuildContext context}) async {
    emit(StoreFCMTokenLoading());

    instance<AuthRepo>()
        .storeFcmToken(token: token, userId: userId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(StoreFCMTokenError(l));
      }, (r) {});
    });
  }

  login(
      {required String password,
      required String email,
      required BuildContext context}) async {
    emit(LoginLoading());
    instance<AuthRepo>()
        .login(
      email: email,
      password: password,
    )
        .then((value) {
      value.fold((l) {
        // print('loginErrorcxcv ${l.message}');
        FailureHelper.instance.handleFailures(l, context);
        emit(LoginError(l));
      }, (r) async {
        if (r != null) {
          await instance<HiveHelper>().setUserModel(r);
          await instance<HiveHelper>().setToken(r.token ?? "");
          storeFcmToken(
              token: instance<HiveHelper>().getFcmToken().toString(),
              userId: r.id.toString(),
              context: context);
        }
        print('myUserDatadsfasdcdzx ${r?.snapChatToken}');
        Navigator.of(context).pushNamedAndRemoveUntil(
            Routes.home, (Route<dynamic> route) => false);
      });
    });
  }

  Future<UserData> updateToken(
      {required String accessToken, required BuildContext context}) async {
    emit(UpdateTokenLoading());
    instance<AuthRepo>()
        .updateToken(
      accessToken: accessToken,
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(UpdateTokenError(l));
      }, (r) async {
        if (r != null) {
          instance<HiveHelper>().setUserModel(
            UserData(
              id: instance<HiveHelper>().getUser()?.id,
              name: instance<HiveHelper>().getUser()?.name,
              email: instance<HiveHelper>().getUser()?.email,
              phone: instance<HiveHelper>().getUser()?.phone,
              photo: instance<HiveHelper>().getUser()?.photo,
              pageName: instance<HiveHelper>().getUser()?.pageName,
              pagePic: instance<HiveHelper>().getUser()?.pagePic,
              emailVerifiedAt:
                  instance<HiveHelper>().getUser()?.emailVerifiedAt,
              token: instance<HiveHelper>().getUser()?.token,
              accessToken: r.accessToken,
              userId: instance<HiveHelper>().getUser()?.userId,
              createdAt: instance<HiveHelper>().getUser()?.createdAt,
              updatedAt: instance<HiveHelper>().getUser()?.updatedAt,
              defaultAccountId:
                  instance<HiveHelper>().getUser()?.defaultAccountId,
              defaultPageId: instance<HiveHelper>().getUser()?.defaultPageId,
              defaultPageAccessToken:
                  instance<HiveHelper>().getUser()?.defaultPageAccessToken,
              defaultAccountName:
                  instance<HiveHelper>().getUser()?.defaultAccountName,
              pageUserName: instance<HiveHelper>().getUser()?.pageUserName,
              instUserName: instance<HiveHelper>().getUser()?.instUserName,
              instAccId: instance<HiveHelper>().getUser()?.instAccId,
              whatsNumber: instance<HiveHelper>().getUser()?.whatsNumber,
              instUserId: instance<HiveHelper>().getUser()?.instUserId,
              snapChatToken: instance<HiveHelper>().getUser()?.snapChatToken,
              tiktokToken: instance<HiveHelper>().getUser()?.tiktokToken,
            ),
          );
        }

        emit(UpdateTokenLoaded());
      });
    });
    return UserData();
  }

  void chanePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
    emit(ChangePasswordVisibilityState());
  }

  Future<void> updateStatus() async {
    emit(FbConnected());
  }

  Future<void> signInWithFacebook(
      {required BuildContext context, required bool isConnect}) async {
    emit(FbSignInLoading());
    final fbAuthResponse = await instance<AuthRepo>().signInFacebookAccount();

    fbAuthResponse.fold(
      (l) {
        emit(FbSignInError());
        showErrorToast(l.message);
      },
      (token) async {
        print("fb email bug");
        final responseUser = await instance<AuthRepo>().fbAuth.getUserData();
        // await instance<AuthRepo>().authInWithCredential(token.credential);
        await instance<HiveHelper>().setUserId(token.userId ?? "");

        if (responseUser == {}) {
          return;
        }
        final user = FbCredentials.fromJson(responseUser);
        final accessToken = token.token;

        print('refreshToken&accessToken ${user.email} $accessToken');

        if (isConnect) {
          // ignore: use_build_context_synchronously
          await updateToken(accessToken: accessToken, context: context);
          await updateStatus();
        } else {
          instance<AuthRepo>()
              .socialRegister(
                  type: "facebook",
                  name: user.name ?? "",
                  email: user.email ?? "",
                  phone: "",
                  accessToken: accessToken)
              .then(
            (userResponseFromServer) {
              userResponseFromServer.fold(
                (l) {
                  showErrorToast(l.message);
                },
                (dbUser) async {
                  if (dbUser != null) {
                    await instance<HiveHelper>().setUserModel(dbUser);
                    print('userModelvczxcvzx ${dbUser.toJson()}');

                    await instance<HiveHelper>().setToken(dbUser.token ?? "");
                    storeFcmToken(
                        token: instance<HiveHelper>().getFcmToken().toString(),
                        userId: dbUser.id.toString(),
                        context: context);
                  }

                  responseUser.printLog(
                      name: 'Facebook Login Response', color: ANSICOLOR.cyan);
                  responseUser.printLog(
                      name: 'User Id ${instance<HiveHelper>().getUserId()}',
                      color: ANSICOLOR.cyan);
                  dbUser.toString().printLog(
                      name: 'Server Login Response', color: ANSICOLOR.red);
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      Routes.home, (Route<dynamic> route) => false);
                },
              );
            },
          );
        }

        emit(FbSignInLoaded());
      },
    );
  }

  Future<void> signInWithApple({required BuildContext context}) async {
    emit(AppleSignInLoading());

    final appleResponse = await instance<AuthRepo>().signInAppleAccount();

    print('faiApple1 $appleResponse');

    appleResponse.fold(
      (failure) {
        emit(AppleSignInError());
        print('faiApple ${failure.message}');
        showErrorToast(failure.message);
      },
      (user) {
        _handleUserRegistration(user, context);
      },
    );
  }

  Future<void> _handleUserRegistration(
      AuthorizationCredentialAppleID user, BuildContext context) async {
    final fullName = "${user.givenName} ${user.familyName}";
    final email = user.email ?? "";

    print('appleEmail $email');

    instance<AuthRepo>()
        .socialRegister(
            type: "apple",
            name: fullName ?? "",
            email: email ?? "",
            phone: "",
            accessToken: user.identityToken)
        .then(
      (userResponseFromServer) {
        userResponseFromServer.fold(
          (l) {
            showErrorToast(l.message);
          },
          (dbUser) async {
            if (dbUser != null) {
              await instance<HiveHelper>().setUserModel(dbUser);
              print('userModelvczxcvzx ${dbUser.toJson()}');

              await instance<HiveHelper>().setToken(dbUser.token ?? "");
              storeFcmToken(
                  token: instance<HiveHelper>().getFcmToken().toString(),
                  userId: dbUser.id.toString(),
                  context: context);
            }

            // responseUser.printLog(
            //     name: 'Facebook Login Response', color: ANSICOLOR.cyan);
            // responseUser.printLog(
            //     name: 'User Id ${instance<HiveHelper>().getUserId()}',
            //     color: ANSICOLOR.cyan);
            // dbUser.toString().printLog(
            //     name: 'Server Login Response', color: ANSICOLOR.red);
            Navigator.of(context).pushNamedAndRemoveUntil(
                Routes.home, (Route<dynamic> route) => false);
          },
        );
      },
    );

    // final registrationResponse = await instance<AuthRepo>().register(
    //   name: fullName,
    //   email: email,
    //   phone: "",
    // );

    // registrationResponse.fold(
    //   (failure) {
    //     showErrorToast(failure.message);
    //   },
    //   (dbUser) async {
    //     if (dbUser != null) {
    //       await _storeUserData(dbUser, context);
    //
    //       user.printLog(name: 'Apple Login Response', color: ANSICOLOR.cyan);
    //       Navigator.of(context).pushNamedAndRemoveUntil(
    //           Routes.home, (Route<dynamic> route) => false);
    //     }
    //   },
    // );

    emit(AppleSignInLoaded());
  }

  Future<void> _storeUserData(UserData dbUser, BuildContext context) async {
    await instance<HiveHelper>().setUserModel(dbUser);
    await instance<HiveHelper>().setToken(dbUser.token ?? "");

    storeFcmToken(
      token: instance<HiveHelper>().getFcmToken().toString(),
      userId: dbUser.id.toString(),
      context: context,
    );
  }

  Future<void> logout() async {
    // final logoutResponse =
    await instance<AuthRepo>().logout().then(
          (value) => value.fold(
            (l) {},
            (r) async {
              await instance<HiveHelper>().setToken("");
              await Navigator.of(Constants.navigatorKey.currentContext!)
                  .pushNamedAndRemoveUntil(
                Routes.login,
                (Route<dynamic> route) => false,
              );
            },
          ),
        );

    // logoutResponse.fold(
    //   (failure) {
    //     showErrorToast(failure.message);
    //   },
    //   (s) async {
    //     await instance<HiveHelper>().setToken("");
    //     await Navigator.of(Constants.navigatorKey.currentContext!)
    //         .pushNamedAndRemoveUntil(
    //       Routes.login,
    //       (Route<dynamic> route) => false,
    //     );
    //     print('logoutRes ${instance<HiveHelper>().getUser()}');
    //   },
    // );
  }
}

part of 'login_cubit.dart';

@immutable
abstract class LoginState {
  const LoginState();
  List<Object?> get props => [];
}

class LoginInitial extends LoginState {}

class LoginLoading extends LoginState {}

class LoginLoaded extends LoginState {
}

class LoginError extends LoginState {
  final Failure message;

  const LoginError(this.message);

  @override
  List<Object?> get props => [message];
}

class ChangePasswordVisibilityState extends LoginState {}


class ChangeAgreedTermsState extends LoginState {}


class FbSignInLoading extends LoginState {}

class FbSignInLoaded extends LoginState {
}

class FbSignInError extends LoginState {
}

class AppleSignInLoading extends LoginState {}

class AppleSignInLoaded extends LoginState {
}

class AppleSignInError extends LoginState {
}

class FbConnected extends LoginState {
}


class UpdateTokenLoading extends LoginState {}

class UpdateTokenLoaded extends LoginState {
}

class UpdateTokenError extends LoginState {
  final Failure message;

  const UpdateTokenError(this.message);

  @override
  List<Object?> get props => [message];
}

class StoreFCMTokenLoading extends LoginState {}

class StoreFCMTokenLoaded extends LoginState {
}

class StoreFCMTokenError extends LoginState {
  final Failure message;

  const StoreFCMTokenError(this.message);

  @override
  List<Object?> get props => [message];
}
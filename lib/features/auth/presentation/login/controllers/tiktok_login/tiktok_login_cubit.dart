import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';

part 'tiktok_login_state.dart';

class TiktokLoginCubit extends Cubit<TiktokLoginState> {
  TiktokLoginCubit() : super(TiktokLoginInitial());

  tiktokGetCode({required BuildContext context}) async {
    launchUrl(Uri.parse(
        "https://business-api.tiktok.com/portal/auth?app_id=7412831026029789200&state=${instance
            .get<HiveHelper>()
            .getToken()}&redirect_uri=https://dvadsstage.devdigitalvibes.com/public/api/tiktok/access_token"));
    // await FlutterWebAuth.authenticate(
    //   url: authUrl,
    //   callbackUrlScheme: Uri.parse(Constants.redirectUri).scheme,
    //   preferEphemeral: false,
    // );
    // emit(LoginLoading());
    // instance<AuthRepo>().tiktokGetCode().then((value) {
    //   value.fold((l) {
    //     print('tikTokLogincxc ${l.message}');
    //     FailureHelper.instance.handleFailures(l, context);
    //     // emit(LoginError(l));
    //   }, (r) async {
    //     print('tikTokLogincxc ${r}');
    //     // if (r != null) {
    //     // await instance<HiveHelper>().setUserModel(r);
    //     // await instance<HiveHelper>().setToken(r.token ?? "");
    //     // storeFcmToken(
    //     //     token: instance<HiveHelper>().getFcmToken().toString(),
    //     //     userId: r.id.toString(),
    //     //     context: context);
    //     // }
    //
    //     // Navigator.of(context).pushNamedAndRemoveUntil(
    //     //     Routes.home, (Route<dynamic> route) => false);
    //   });
    // });
  }
}

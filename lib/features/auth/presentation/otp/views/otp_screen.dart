import 'package:ads_dv/features/auth/presentation/otp/controllers/validate_otp_cubit.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pin_input_text_field/pin_input_text_field.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../widgets/cached__image.dart';
import '../../../../../widgets/custom_button.dart';
import '../../../../../widgets/custom_text.dart';

class OtpScreen extends StatefulWidget {
  String email;

  OtpScreen({super.key, required this.email});

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  final TextEditingController _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ValidateOtpCubit(),
      child: BlocBuilder<ValidateOtpCubit, ValidateOtpState>(
        builder: (context, state) {
          return Scaffold(
            body: Container(
              height: SizeConfig.screenHeight(context),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(AppAssets.forgetPassword),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(22),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 55.h,
                      ),
                      CachedImageWidget(
                        assetsImage: AppAssets.logoColored,
                        height: 40.h,
                        width: 150.h,
                      ),
                      SizedBox(
                        height: 80.h,
                      ),
                      CustomText(
                        text: "authyVerification".tr,
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w700,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      CustomText(
                        text:
                            "Copy the verification code in your authy application to verify this account recovery."
                                .tr,
                        fontSize: 16.sp,
                        maxLines: 3,
                        fontWeight: FontWeight.w400,
                        color: Constants.gray,
                      ),
                      SizedBox(
                        height: 90.h,
                      ),
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            Padding(
                                padding: EdgeInsets.symmetric(horizontal: 40.w),
                                child: SizedBox(
                                  height: 55.h,
                                  child: PinInputTextField(
                                    pinLength: 4,
                                    decoration: BoxLooseDecoration(
                                        strokeColorBuilder:
                                            PinListenColorBuilder(
                                                Constants.mainColor,
                                                Constants.darkColor)),
                                    autoFocus: true,
                                    controller: _controller,
                                    textInputAction: TextInputAction.done,
                                  ),
                                )),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 120.h,
                      ),
                      state is ValidateOtpLoading
                          ? const LoadingWidget(
                              isCircle: true,
                            )
                          : SizedBox(
                              width: 252.w,
                              height: 60.h,
                              child: GestureDetector(
                                onTap: () {
                                  if (_controller.text.isEmpty ||
                                      _controller.text == "") {
                                    showErrorToast("Please enter the code".tr);
                                  } else {
                                    ValidateOtpCubit.get(context).validateOTP(
                                        email: widget.email,
                                        otp: _controller.text,
                                        context: context);
                                  }
                                },
                                child: Stack(
                                  children: [
                                    ShaderMask(
                                      shaderCallback: (Rect bounds) {
                                        return Constants.defGradient
                                            .createShader(bounds);
                                      },
                                      child: CustomButton(
                                        text: '',
                                        textColor: Colors.white,
                                        onPressed: () {},
                                        //    onPressed: controller.signInWithEmailAndPassword,
                                      ),
                                    ),
                                    Positioned(
                                      top: 15.h,
                                      right: 15.w,
                                      left: 15.w,
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 40.0),
                                        child: CustomText(
                                          text: "SubmitVerification".tr,
                                          fontSize: 16.sp,
                                          color: AppColors.white,
                                          alignment:
                                              AlignmentDirectional.center,
                                          textAlign: TextAlign.center,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

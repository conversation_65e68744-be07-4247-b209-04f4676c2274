import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../data/repos/auth_repo.dart';

part 'validate_otp_state.dart';

class ValidateOtpCubit extends Cubit<ValidateOtpState> {
  ValidateOtpCubit() : super(ValidateOtpInitial());

  static ValidateOtpCubit get(context) => BlocProvider.of(context);

  validateOTP({required String email,required String otp, required BuildContext context}) async {
    emit(ValidateOtpLoading());
    instance<AuthRepo>()
        .validateOTP(
      email: email,
      otp: otp
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ValidateOtpError(l));
      }, (r) async {
        Navigator.of(context).pushNamedAndRemoveUntil(
            Routes.resetPassword, (Route<dynamic> route) => false, arguments: {
          "email": email,
          "otp": otp,
        });
      });
    });
  }
}

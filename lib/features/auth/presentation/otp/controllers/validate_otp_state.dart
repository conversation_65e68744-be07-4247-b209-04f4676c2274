part of 'validate_otp_cubit.dart';

@immutable
abstract class ValidateOtpState {
  const ValidateOtpState();
  List<Object?> get props => [];
}

class ValidateOtpInitial extends ValidateOtpState {}

class ValidateOtpLoading extends ValidateOtpState {}

class ValidateOtpLoaded extends ValidateOtpState {
}

class ValidateOtpError extends ValidateOtpState {
  final Failure message;

  const ValidateOtpError(this.message);

  @override
  List<Object?> get props => [message];
}
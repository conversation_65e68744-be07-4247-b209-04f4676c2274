import 'package:ads_dv/features/auth/presentation/forget_password/controllers/forget_password_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/custom_button.dart';
import '../../../../../widgets/custom_text_field.dart';

class ForgetPasswordScreen extends StatefulWidget {
  const ForgetPasswordScreen({super.key});

  @override
  State<ForgetPasswordScreen> createState() => _ForgetPasswordScreenState();
}

class _ForgetPasswordScreenState extends State<ForgetPasswordScreen> {
  final TextEditingController _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgetPasswordCubit(),
      child: BlocBuilder<ForgetPasswordCubit, ForgetPasswordState>(
        builder: (context, state) {
          return Scaffold(
            body: Container(
              height: SizeConfig.screenHeight(context),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(AppAssets.forgetPassword),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(22),
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 55.h,
                        ),
                        CachedImageWidget(
                          assetsImage: AppAssets.logoColored,
                          height: 40.h,
                          width: 150.h,
                        ),
                        SizedBox(
                          height: 80.h,
                        ),
                        CustomText(
                          text: "Forget Password".tr,
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(
                          height: 16.h,
                        ),
                        CustomText(
                          text:
                              "Opps. It happens to the best of us. Input your email address to fix the issue."
                                  .tr
                                  .tr,
                          fontSize: 16.sp,
                          maxLines: 2,
                          fontWeight: FontWeight.w400,
                          color: Constants.gray,
                        ),
                        SizedBox(
                          height: 90.h,
                        ),
                        CustomTextFormField.withPrefixSVG(
                          svgURL: AppAssets.mail,
                          hintText: "E-Mail".tr,
                          controller: _emailController,
                          validator: (value) =>
                              AppValidator.validateEmail(value),
                        ),
                        SizedBox(
                          height: 120.h,
                        ),
                        state is ForgetPasswordLoading
                            ? const LoadingWidget(
                                isCircle: true,
                              )
                            : SizedBox(
                                width: 252.w,
                                height: 60.h,
                                child: GestureDetector(
                                  onTap: () {
                                    if (_formKey.currentState!.validate()) {
                                      ForgetPasswordCubit.get(context)
                                          .forgetPassword(
                                              email: _emailController.text,
                                              context: context);
                                    }
                                  },
                                  child: Stack(
                                    children: [
                                      ShaderMask(
                                        shaderCallback: (Rect bounds) {
                                          return Constants.defGradient
                                              .createShader(bounds);
                                        },
                                        child: CustomButton(
                                          text: '',
                                          textColor: Colors.white,
                                          onPressed: () {},
                                          //    onPressed: controller.signInWithEmailAndPassword,
                                        ),
                                      ),
                                      Positioned(
                                        top: 15.h,
                                        right: 15.w,
                                        left: 15.w,
                                        child: CustomText(
                                          text: 'Confirm'.tr,
                                          fontSize: 16.sp,
                                          color: AppColors.white,
                                          alignment:
                                              AlignmentDirectional.center,
                                          textAlign: TextAlign.center,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

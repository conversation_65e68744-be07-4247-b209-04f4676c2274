part of 'forget_password_cubit.dart';

@immutable
abstract class ForgetPasswordState {
  const ForgetPasswordState();
  List<Object?> get props => [];
}

class ForgetPasswordInitial extends ForgetPasswordState {}

class ForgetPasswordLoading extends ForgetPasswordState {}

class ForgetPasswordLoaded extends ForgetPasswordState {
}

class ForgetPasswordError extends ForgetPasswordState {
  final Failure message;

  const ForgetPasswordError(this.message);

  @override
  List<Object?> get props => [message];
}
import 'package:ads_dv/utils/network/failure_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../data/repos/auth_repo.dart';

part 'forget_password_state.dart';

class ForgetPasswordCubit extends Cubit<ForgetPasswordState> {
  ForgetPasswordCubit() : super(ForgetPasswordInitial());

  static ForgetPasswordCubit get(context) => BlocProvider.of(context);

  forgetPassword({required String email, required BuildContext context}) async {
    emit(ForgetPasswordLoading());
    instance<AuthRepo>()
        .forgetPassword(
      email: email,
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ForgetPasswordError(l));
      }, (r) async {
        Navigator.of(context).pushNamedAndRemoveUntil(
            Routes.otp, (Route<dynamic> route) => false,arguments: email);
      });
    });
  }
}

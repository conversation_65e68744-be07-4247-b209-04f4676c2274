class AdReachEstimatedModel {
  int? usersLowerBound;
  int? usersUpperBound;
  bool? estimateReady;

  AdReachEstimatedModel(
      {this.usersLowerBound, this.usersUpperBound, this.estimateReady});

  AdReachEstimatedModel.fromJson(Map<String, dynamic> json) {
    usersLowerBound = json['users_lower_bound'];
    usersUpperBound = json['users_upper_bound'];
    estimateReady = json['estimate_ready'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['users_lower_bound'] = usersLowerBound;
    data['users_upper_bound'] = usersUpperBound;
    data['estimate_ready'] = estimateReady;
    return data;
  }
}
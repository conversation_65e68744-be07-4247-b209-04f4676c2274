import 'package:equatable/equatable.dart';

import '../../../../utils/res/meta_constants.dart';

class AdSetGeoLocations {
  List<CustomLocations>? customLocations;
  List<String?>? countries;
  List<Regions?>? regions;
  List<Cities?>? cities;

  AdSetGeoLocations({
    this.customLocations,
    this.countries,
    this.regions,
    this.cities,
  });

  AdSetGeoLocations.fromJson(Map<String, dynamic> json) {
    if (json['custom_locations'] != null && (json['custom_locations'] is List)) {
      final v = json['custom_locations'];
      final arr0 = <CustomLocations>[];
      v.forEach((v) {
        arr0.add(CustomLocations.fromJson(v));
      });
      customLocations = arr0;
    }
    if (json['countries'] != null && (json['countries'] is List)) {
      final v = json['countries'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      countries = arr0;
    }
    if (json['regions'] != null && (json['regions'] is List)) {
      final v = json['regions'];
      final arr0 = <Regions>[];
      v.forEach((v) {
        arr0.add(Regions.fromJson(v));
      });
      regions = arr0;
    }
    if (json['cities'] != null && (json['cities'] is List)) {
      final v = json['cities'];
      final arr0 = <Cities>[];
      v.forEach((v) {
        arr0.add(Cities.fromJson(v));
      });
      cities = arr0;
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (customLocations != null) {
      final v = customLocations;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v.toJson());
      }
      data['custom_locations'] = arr0;
    }
    if (countries != null) {
      final v = countries;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['countries'] = arr0;
    }
    if (regions != null) {
      final v = regions;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['regions'] = arr0;
    }
    if (cities != null) {
      final v = cities;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['cities'] = arr0;
    }
    return data;
  }
}

class CustomLocations extends Equatable {
  double? latitude;
  double? longitude;
  int? radius;
  DistanceUnit? distanceUnit;
  String? addressString;

  CustomLocations({
    this.latitude,
    this.longitude,
    this.radius,
    this.distanceUnit,
    this.addressString,
  });

  CustomLocations.fromJson(Map<String, dynamic> json) {
    latitude = double.tryParse(json['latitude']?.toString() ?? '');
    longitude = double.tryParse(json['longitude']?.toString() ?? '');
    radius = int.tryParse(json['radius']?.toString() ?? '');
    distanceUnit = DistanceUnitExtension.fromJSON(json['distance_unit']?.toString() ?? '');
    // addressString = json['address_string']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['radius'] = radius ?? "10";
    if (distanceUnit != null) {
      data['distance_unit'] = distanceUnit!.name;
    }

    // if (addressString != null) data['address_string'] = addressString;
    return data;
  }

  @override
  List<Object?> get props => [
    latitude,
    longitude,
    radius,
    distanceUnit,
    addressString,
  ];
}

class Regions {
  String? key;

  Regions({
    this.key,
  });

  Regions.fromJson(Map<String, dynamic> json) {
    key = json['key']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['key'] = key;
    return data;
  }
}

class Cities {
  String? key;
  String? radius;
  DistanceUnit? distanceUnit;

  Cities({
    this.key,
    this.radius,
    this.distanceUnit,
  });

  Cities.fromJson(Map<String, dynamic> json) {
    key = json['key']?.toString();
    radius = json['radius']?.toString();
    distanceUnit = DistanceUnitExtension.fromJSON(json['distance_unit']?.toString() ?? '');
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['key'] = key;
    data['radius'] = radius;
    if (distanceUnit != null) {
      data['distance_unit'] = distanceUnit!.toJSON();
    }
    return data;
  }
}
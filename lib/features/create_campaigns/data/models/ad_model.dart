import 'dart:io';

import 'package:ads_dv/features/create_campaigns/data/models/adset_location.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_result.dart';
import 'package:dio/dio.dart';


class AdModel {
  String? campaignName;
  bool? isInstaPost;
  String? x;
  String? instaUserId;
  String? adAccountId;
  String? objective;
  String? status;
  List<String>? specialAdCategories;
  String? existCampaign;
  String? existAdSet;
  String? pageAccessToken;
  String? adSetName;
  String? optimizationGoal;
  String? billingEven;
  int? dailyBudget;
  String? startDate;
  String? endDate;
  List<int>? genders;
  int? ageMin;
  int? ageMax;
  List<SearchResult>? interests;
  List<String>? publisherPlatforms;
  List<String>? facebookPositions;
  List<String>? instagramPositions;
  List<String>? messengerPositions;
  List<AdSetGeoLocations>? geoLocations;
  List<SearchResult>? languages;
  List<SearchResult>? educationSchools;
  String? destinationType;
  String? type;
  String? phone;
  int? bidAmount;
  List<File>? images;
  List<File>? video;
  List<File>? thumb;
  int? pageId;
  String? adCreativeName;
  String? webSiteLinkMain;
  String? message;
  String? objectStoryId;
  String? videoId;
  String? linkDescription;
  String? description;
  String? adName;
  String? adStatus;
  String? title;
  String? link;
  String? formName;
  String? formLocale;
  String? formLinkText;
  String? formUrl;
  String? formTitle;
  String? formBody;
  String? formButtonType;
  String? formButtonText;
  String? formWebsiteUrl;
  List<String>? questions;
  String? formId;
  String? instAccId;

  AdModel(
      {this.campaignName,
      this.isInstaPost,
      this.x,
      this.instaUserId,
      this.adAccountId,
      this.objective,
      this.status,
      this.specialAdCategories,
      this.pageAccessToken,
      this.existCampaign,
      this.existAdSet,
      this.adSetName,
      this.optimizationGoal,
      this.billingEven,
      this.dailyBudget,
      this.startDate,
      this.endDate,
      this.genders,
      this.ageMax,
      this.phone,
      this.ageMin,
      this.interests,
      this.publisherPlatforms,
      this.facebookPositions,
      this.instagramPositions,
      this.messengerPositions,
      this.geoLocations,
      this.languages,
      this.educationSchools,
      this.destinationType,
      this.bidAmount,
      this.type,
      this.images,
      this.pageId,
      this.video,
      this.thumb,
      this.adCreativeName,
      this.webSiteLinkMain,
      this.message,
      this.objectStoryId,
      this.videoId,
      this.linkDescription,
      this.description,
      this.adName,
      this.adStatus,
      this.title,
      this.link,
      this.formName,
      this.formLocale,
      this.questions,
      this.formLinkText,
      this.formUrl,
      this.formTitle,
      this.formBody,
      this.formButtonType,
      this.formButtonText,
      this.formWebsiteUrl,
      this.formId,
      this.instAccId});

  AdModel.fromJson(Map<String, dynamic> json) {
    campaignName = json['campaign_name'] ?? "";
    adAccountId = json['ad_account_id'] ?? "";
    isInstaPost = json['is_instagram_post'] ?? false;
    instaUserId = json['instagram_user_id'] ?? "";
    objective = json['objective'] ?? "";
    phone = json['phone_number'] ?? "";
    status = json['status'] ?? "";
    if (json['special_ad_categories'] != null &&
        json['special_ad_categories'] is List) {
      final v = json['special_ad_categories'] as List;
      specialAdCategories = v.map((e) => e.toString()).toList();
    }
    pageAccessToken = json['page_access_token'] ?? "";
    adSetName = json['ad_set_name'] ?? "";
    existCampaign = json['exist_campaign'] ?? "";
    existAdSet = json['exist_adset'] ?? "";
    optimizationGoal = json['optimization_goal'] ?? "";
    billingEven = json['billing_event'] ?? "";
    dailyBudget = json['daily_budget'] ?? 0;
    startDate = json['start_time'] ?? "";
    endDate = json['end_time'] ?? "";
    type = json['type'] ?? "";
    if (json['genders'] != null && json['genders'] is List) {
      final v = json['genders'] as List;
      genders = [];
      for (var element in v) {
        genders?.add(int.parse(element));
      }
    }
    ageMin = json['age_min'] ?? 0;
    ageMax = json['age_max'] ?? 0;
    if (json['interests'] != null && json['interests'] is List) {
      final v = json['interests'] as List;
      interests = v.map<SearchResult>((e) => SearchResult.fromJson(e)).toList();
    }
    if (json['publisher_platforms'] != null &&
        json['publisher_platforms'] is List) {
      final v = json['publisher_platforms'] as List;
      publisherPlatforms = v.map((e) => e.toString()).toList();
    }
    if (json['facebook_positions'] != null &&
        json['facebook_positions'] is List) {
      final v = json['facebook_positions'] as List;
      facebookPositions = v.map((e) => e.toString()).toList();
    }
    if (json['instagram_positions'] != null &&
        json['instagram_positions'] is List) {
      final v = json['instagram_positions'] as List;
      instagramPositions = v.map((e) => e.toString()).toList();
    }
    if (json['messenger_positions'] != null &&
        json['messenger_positions'] is List) {
      final v = json['messenger_positions'] as List;
      messengerPositions = v.map((e) => e.toString()).toList();
    }
    if (json['geo_locations'] != null && json['geo_locations'] is List) {
      final v = json['geo_locations'] as List;
      geoLocations = v
          .map<AdSetGeoLocations>((e) => AdSetGeoLocations.fromJson(e))
          .toList();
    }
    if (json['lang'] != null && json['lang'] is List) {
      final v = json['lang'] as List;
      languages = v.map<SearchResult>((e) => SearchResult.fromJson(e)).toList();
    }

    if (json['education_schools'] != null &&
        json['education_schools'] is List) {
      final v = json['education_schools'] as List;
      educationSchools =
          v.map<SearchResult>((e) => SearchResult.fromJson(e)).toList();
    }
    destinationType = json['destination_type'] ?? '';
    video = json['video '] ?? File('');
    thumb = json['thumb '] ?? File('');
    bidAmount = json['bid_amount'] ?? 0;
    if (json['images'] != null) {
      List<String> imagesPaths = List<String>.from(json['images']);
      images = imagesPaths.map((path) => File(path)).toList();
    }
    pageId = json['page_id'] ?? 0;
    adCreativeName = json['ad_crative_name'] ?? "";
    webSiteLinkMain = json['website_link_main'] ?? "";
    message = json['message'] ?? "";
    objectStoryId = json['object_story_id'] ?? "";
    videoId = json['video_id'] ?? "";
    linkDescription = json['link_description'] ?? "";
    description = json['description'] ?? "";
    adName = json['ad_name'] ?? "";
    adStatus = json['ad_status'] ?? "";
    title = json['title'] ?? "";
    link = json['link'] ?? "";
    formName = json['form_name'] ?? "";
    formLocale = json['form_locale'] ?? "";
    formLinkText = json['form_link_text'] ?? "";
    formUrl = json['form_url'] ?? "";
    formTitle = json['form_title'] ?? "";
    formBody = json['form_body'] ?? "";
    formButtonType = json['form_button_type'] ?? "";
    formButtonText = json['form_button_text'] ?? "";
    formWebsiteUrl = json['form_website_url'] ?? "";
    if (json['questions'] != null && json['questions'] is List) {
      final v = json['questions'] as List;
      questions = v.map((e) => e.toString()).toList();
    }
    formId = json['lead_gen_form_id'] ?? "";
    instAccId = json['instagram_actor_id'] ?? "";
  }

  Future<Map<String, dynamic>> toJson() async {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['campaign_name'] = campaignName;
    data['phone_number'] = phone;
    data['is_instagram_post'] = isInstaPost ?? false;
    data['instagram_user_id'] = instaUserId ?? "";
    data['x'] = x ?? "";
    data['ad_account_id'] = adAccountId;
    data['objective'] = objective;
    data['status'] = status;
    if (specialAdCategories != null) {
      data['special_ad_categories'] =
          specialAdCategories!.map((e) => e).toList();
    }
    data['page_access_token'] = pageAccessToken;
    data['ad_set_name'] = adSetName;
    data['exist_campaign'] = existCampaign;
    data['exist_adset'] = existAdSet;
    data['optimization_goal'] = optimizationGoal;
    data['billing_event'] = billingEven;
    data['daily_budget'] = dailyBudget;
    data['start_time'] = startDate;
    data['end_time'] = endDate;
    if (genders != null) {
      data['genders'] = genders;
    }
    data['age_min'] = ageMin;
    data['age_max'] = ageMax;
    data['type'] = type;
    if (interests != null) {
      for (int i = 0; i < interests!.length; i++) {
        data['interests[$i]'] =
            interests![i].toJson(); // Serialize each interest
      }
    }

    if (publisherPlatforms != null) {
      data['publisher_platforms'] = publisherPlatforms!.map((e) => e).toList();
    }
    if (facebookPositions != null) {
      data['facebook_positions'] = facebookPositions!.map((e) => e).toList();
    }
    if (instagramPositions != null) {
      data['instagram_positions'] = instagramPositions!.map((e) => e).toList();
    }
    if (messengerPositions != null) {
      data['messenger_positions'] = messengerPositions!.map((e) => e).toList();
    }
    if (geoLocations != null) {
      data['geo_locations'] = geoLocations!.map((e) => e).toList();
    }
    if (languages != null) {
      data['lang'] = languages!.map((e) => e.key).toList();
    }
    if (educationSchools != null) {
      data['education_schools'] =
          educationSchools!.map((e) => e.toJson()).toList();
    }
    data['destination_type'] = destinationType;
    data['bid_amount'] = bidAmount;
    if (images != null) {
      data["images]"] = images!.map((file) => file.path).toList();
    }
    data['page_id'] = pageId;
    if (video != null) {
      data["video"] = video!.map((file) async {
        await MultipartFile.fromFile(
          file.path ?? "",
        );
      }).toList();
    }
    if (thumb != null) {
      data["thumb"] = thumb!.map((file) async {
        await MultipartFile.fromFile(
          file.path ?? "",
        );
      }).toList();
    }
    // if (video?.existsSync() ?? false) {
    //   data['video'] = await MultipartFile.fromFile(
    //     video?.path ?? "",
    //   );
    // }
    // if (thumb?.existsSync() ?? false) {
    //   data['thumb'] = await MultipartFile.fromFile(
    //     thumb?.path ?? "",
    //   );
    // }
    data['ad_crative_name'] = adCreativeName;
    data['website_link_main'] = webSiteLinkMain;
    data['message'] = message;
    data['object_story_id'] = objectStoryId;
    data['video_id'] = videoId;
    data['link_description'] = linkDescription;
    data['description'] = description;
    data['ad_name'] = adName;
    data['ad_status'] = adStatus;
    data['title'] = title;
    data['link'] = link;
    data['form_name'] = formName;
    data['form_locale'] = formLocale;
    data['form_link_text'] = formLinkText;
    data['form_url'] = formUrl;
    data['form_title'] = formTitle;
    data['form_body'] = formBody;
    data['form_button_type'] = formButtonType;
    data['form_button_text'] = formButtonText;
    data['form_website_url'] = formWebsiteUrl;
    if (questions != null) {
      data['questions'] = questions!.map((e) => e).toList();
    }
    data['lead_gen_form_id'] = formId;
    data['instagram_actor_id'] = instAccId;

    return data;
  }

  AdModel copyWith(
      {String? campaignName,
      bool? isInstaPost,
      String? instaUserId,
      String? x,
      String? adAccountId,
      String? objective,
      String? status,
      String? existCampaign,
      String? existAdSet,
      List<String>? specialAdCategories,
      String? pageAccessToken,
      String? adSetName,
      String? optimizationGoal,
      String? billingEven,
      int? dailyBudget,
      String? startDate,
      String? endDate,
      List<int>? genders,
      int? ageMin,
      int? ageMax,
      List<SearchResult>? interests,
      List<String>? publisherPlatforms,
      List<String>? facebookPositions,
      List<String>? instagramPositions,
      List<String>? messengerPositions,
      List<AdSetGeoLocations>? geoLocations,
      List<SearchResult>? languages,
      List<SearchResult>? educationSchools,
      String? destinationType,
      String? type,
      String? phone,
      int? bidAmount,
      List<File>? images,
      List<File>? video,
      List<File>? thumb,
      int? pageId,
      String? description,
      String? adCreativeName,
      String? webSiteLinkMain,
      String? message,
      String? objectStoryId,
      String? videoId,
      String? linkDescription,
      String? adName,
      String? adStatus,
      String? title,
      String? link,
      String? formName,
      String? formLocale,
      String? formLinkText,
      String? formUrl,
      String? formTitle,
      String? formBody,
      String? formButtonType,
      String? formButtonText,
      String? formWebsiteUrl,
      List<String>? questions,
      String? formId,
      String? instAccId}) {
    return AdModel(
        campaignName: campaignName ?? this.campaignName,
        isInstaPost: isInstaPost ?? this.isInstaPost,
        x: x ?? this.x,
        instaUserId: instaUserId ?? this.instaUserId,
        adAccountId: adAccountId ?? this.adAccountId,
        objective: objective ?? this.objective,
        phone: phone ?? this.phone,
        video: video ?? this.video,
        thumb: thumb ?? this.thumb,
        status: status ?? this.status,
        existAdSet: existAdSet ?? this.existAdSet,
        existCampaign: existCampaign ?? this.existCampaign,
        specialAdCategories: specialAdCategories ?? this.specialAdCategories,
        pageAccessToken: pageAccessToken ?? this.pageAccessToken,
        adSetName: adSetName ?? this.adSetName,
        optimizationGoal: optimizationGoal ?? this.optimizationGoal,
        billingEven: billingEven ?? this.billingEven,
        dailyBudget: dailyBudget ?? this.dailyBudget,
        startDate: startDate ?? this.startDate,
        endDate: endDate ?? this.endDate,
        genders: genders ?? this.genders,
        ageMax: ageMax ?? this.ageMax,
        ageMin: ageMin ?? this.ageMin,
        interests: interests ?? [...?this.interests],
        publisherPlatforms: publisherPlatforms ?? this.publisherPlatforms,
        facebookPositions: facebookPositions ?? this.facebookPositions,
        instagramPositions: instagramPositions ?? this.instagramPositions,
        messengerPositions: messengerPositions ?? this.messengerPositions,
        geoLocations: geoLocations ?? this.geoLocations,
        languages: languages ?? [...?this.languages],
        educationSchools: educationSchools ?? [...?this.educationSchools],
        destinationType: destinationType ?? this.destinationType,
        type: type ?? this.type,
        bidAmount: bidAmount ?? this.bidAmount,
        images: images ?? [...?this.images],
        pageId: pageId ?? this.pageId,
        adCreativeName: adCreativeName ?? this.adCreativeName,
        webSiteLinkMain: webSiteLinkMain ?? this.webSiteLinkMain,
        message: message ?? this.message,
        objectStoryId: objectStoryId ?? this.objectStoryId,
        videoId: videoId ?? this.videoId,
        linkDescription: linkDescription ?? this.linkDescription,
        description: description ?? this.description,
        adName: adName ?? this.adName,
        adStatus: adStatus ?? this.adStatus,
        title: title ?? this.title,
        link: link ?? this.link,
        formName: formName ?? this.formName,
        formLocale: formLocale ?? this.formLocale,
        formLinkText: formLinkText ?? this.formLinkText,
        formUrl: formUrl ?? this.formUrl,
        formTitle: formTitle ?? this.formTitle,
        formBody: formBody ?? this.formBody,
        formButtonType: formButtonType ?? this.formButtonType,
        formButtonText: formButtonText ?? this.formButtonText,
        formWebsiteUrl: formWebsiteUrl ?? this.formWebsiteUrl,
        questions: questions ?? this.questions,
        formId: formId ?? this.formId,
        instAccId: instAccId ?? this.instAccId);
  }
}

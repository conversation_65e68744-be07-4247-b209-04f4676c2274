class SearchClass {
  String? id;
  String? name;
  String? platform;

  SearchClass({this.id, this.name,this.platform});

  SearchClass.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    name = json['name'];
    platform = json['platform'];

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['platform'] = name;

    return data;
  }
}
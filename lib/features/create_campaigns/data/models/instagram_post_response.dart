class InstagramPostsResponse {
  bool? success;
  String? message;
  Result? result;

  InstagramPostsResponse({this.success, this.message, this.result});

  InstagramPostsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    result =
        json['result'] != null ? Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.toJson();
    }
    return data;
  }
}

class Result {
  List<InstaPosts>? data;
  String? next;
  String? previous;

  Result({this.data, this.next, this.previous});

  Result.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <InstaPosts>[];
      json['data'].forEach((v) {
        data!.add(InstaPosts.fromJson(v));
      });
    }
    next = json['next'];
    previous = json['previous'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    data['previous'] = previous;
    return data;
  }
}

class InstaPosts {
  String? mediaUrl;
  String? mediaType;
  String? caption;
  String? mediaProductType;
  String? id;
  String? thumbnailUrl;

  InstaPosts(
      {this.mediaUrl,
      this.mediaType,
      this.caption,
      this.mediaProductType,
      this.id,
      this.thumbnailUrl});

  InstaPosts.fromJson(Map<String, dynamic> json) {
    mediaUrl = json['media_url'];
    mediaType = json['media_type'];
    caption = json['caption'];
    mediaProductType = json['media_product_type'];
    id = json['id'];
    thumbnailUrl = json['thumbnail_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['media_url'] = mediaUrl;
    data['media_type'] = mediaType;
    data['caption'] = caption;
    data['media_product_type'] = mediaProductType;
    data['id'] = id;
    data['thumbnail_url'] = thumbnailUrl;
    return data;
  }
}

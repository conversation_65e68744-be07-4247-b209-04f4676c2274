class AdSet {
  String? name;
  String? startTime;
  String? dailyBudget;
  String? lifetimeBudget;
  String? id;

  AdSet(
      {this.name,
      this.startTime,
      this.dailyBudget,
      this.lifetimeBudget,
      this.id});

  AdSet.fromJson(Map<String, dynamic> json) {
    name = json['name'] ?? "";
    startTime = json['start_time'] ?? "";
    dailyBudget = json['daily_budget'] ?? "";
    lifetimeBudget = json['lifetime_budget'] ?? "";
    id = json['id'] ?? "";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name ?? "";
    data['start_time'] = startTime ?? "";
    data['daily_budget'] = dailyBudget ?? "";
    data['lifetime_budget'] = lifetimeBudget ?? "";
    data['id'] = id;
    return data;
  }
}

class GetAdSetsResponse {
  bool? success;
  String? message;
  Result? result;

  GetAdSetsResponse({this.success, this.message, this.result});

  GetAdSetsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    result =
        json['result'] != null ? Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.toJson();
    }
    return data;
  }
}

class Result {
  List<AdSet>? data;
  String? next;
  String? previous;

  Result({this.data, this.next, this.previous});

  Result.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <AdSet>[];
      json['data'].forEach((v) {
        data!.add(AdSet.fromJson(v));
      });
    }
    next = json['next'];
    previous = json['previous'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    data['previous'] = previous;
    return data;
  }
}

import 'optimization.dart';

class Objective {
  int? id;
  String? showName;
  String? actualName;
  String? createdAt;
  String? updatedAt;
  List<Optimizations>? opimizations;

  Objective(
      {this.id,
        this.showName,
        this.actualName,
        this.createdAt,
        this.updatedAt,
        this.opimizations});

  Objective.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    showName = json['show_name']??"";
    actualName = json['actual_name']??"";
    createdAt = json['created_at']??"";
    updatedAt = json['updated_at']??"";
    if (json['opimizations'] != null) {
      opimizations = <Optimizations>[];
      json['opimizations'].forEach((v) {
        opimizations!.add(Optimizations.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id ?? 0;
    data['show_name'] = showName??"";
    data['actual_name'] = actualName??"";
    data['created_at'] = createdAt??"";
    data['updated_at'] = updatedAt??"";
    if (opimizations != null) {
      data['opimizations'] = opimizations!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
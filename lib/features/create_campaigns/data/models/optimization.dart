class Optimizations {
  int? id;
  int? objectiveId;
  String? showName;
  String? actualName;
  String? createdAt;
  String? updatedAt;

  Optimizations(
      {this.id,
        this.objectiveId,
        this.showName,
        this.actualName,
        this.createdAt,
        this.updatedAt});

  Optimizations.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    objectiveId = json['objective_id']??0;
    showName = json['show_name']??"";
    actualName = json['actual_name']??"";
    createdAt = json['created_at']??"";
    updatedAt = json['updated_at']??"";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id ?? 0;
    data['objective_id'] = objectiveId??0;
    data['show_name'] = showName??"";
    data['actual_name'] = actualName??"";
    data['created_at'] = createdAt??"";
    data['updated_at'] = updatedAt??"";
    return data;
  }
}
class PostResponse {
  bool? success;
  String? message;
  Result? result;

  PostResponse({this.success, this.message, this.result});

  PostResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    result =
        json['result'] != null ? Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.toJson();
    }
    return data;
  }
}

class Result {
  List<Post>? data;
  String? next;
  String? previous;

  Result({this.data, this.next, this.previous});

  Result.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Post>[];
      json['data'].forEach((v) {
        data!.add(Post.fromJson(v));
      });
    }
    next = json['next'];
    previous = json['previous'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    data['previous'] = previous;
    return data;
  }
}

class Post {
  String? mediaUrl;
  String? mediaType;
  String? caption;
  String? id;

  Post({this.mediaUrl, this.mediaType, this.caption, this.id});

  Post.fromJson(Map<String, dynamic> json) {
    mediaUrl = json['media_url'];
    mediaType = json['media_type'];
    caption = json['caption'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['media_url'] = mediaUrl;
    data['media_type'] = mediaType;
    data['caption'] = caption;
    data['id'] = id;
    return data;
  }
}

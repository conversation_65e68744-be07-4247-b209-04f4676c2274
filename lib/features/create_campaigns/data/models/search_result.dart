class SearchResult {
  int? key;
  String? id;
  String? name;

  SearchResult({this.key, this.name, this.id});

  SearchResult.fromJson(Map<String, dynamic> json) {
    key = json['key'] ?? 0;
    name = (json['name'] ?? "").replaceAll(RegExp(r'[\s&]+'), "");
    id = json['id'] ?? "";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    (id != "") ? data['id'] = id : data['id'] = key;
    if (name != "") data['name'] = name;
    return data;
  }
}
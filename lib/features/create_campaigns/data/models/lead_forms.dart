class FormsResponse {
  List<LeadForm>? data;
  String? next;
  String? previous;

  FormsResponse({this.data, this.next, this.previous});

  FormsResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <LeadForm>[];
      json['data'].forEach((v) {
        data!.add(LeadForm.fromJson(v));
      });
    }
    next = json['next'];
    previous = json['previous'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    data['previous'] = previous;
    return data;
  }
}



class LeadForm {
  String? id;
  String? name;
  String? status;
  String? createdTime;
  List<Questions>? questions;

  LeadForm({this.id, this.name, this.status, this.createdTime, this.questions});

  LeadForm.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    status = json['status'];
    createdTime = json['created_time'];
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions!.add(Questions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    data['created_time'] = createdTime;
    if (questions != null) {
      data['questions'] = questions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Questions {
  String? key;
  String? id;

  Questions({this.key, this.id});

  Questions.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['id'] = id;
    return data;
  }
}
import 'dart:io';

import 'package:ads_dv/features/create_campaigns/data/models/ad_model.dart';
import 'package:ads_dv/features/create_campaigns/data/models/adset.dart';
import 'package:ads_dv/features/create_campaigns/data/models/existing_campagin.dart';
import 'package:ads_dv/features/create_campaigns/data/models/optimization.dart';
import 'package:ads_dv/features/create_campaigns/data/models/post_response.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_class.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_result.dart';
import 'package:dartz/dartz.dart';

import '../../../../utils/network/connection/network_info.dart';
import '../../../../utils/network/errors/failures.dart';
import '../../../../utils/network/failure_helper.dart';
import '../data_sources/create_campagin_data_source.dart';
import '../models/ad_reach_estimated_model.dart';
import '../models/instagram_post_response.dart';
import '../models/lead_forms.dart';
import '../models/objectives.dart';

class CreateCampaignRepo {
  NetworkInfo networkInfo;
  CreateCampaignDataSource createCampaignDataSource;

  CreateCampaignRepo(
      {required this.networkInfo, required this.createCampaignDataSource});

  Future<Either<Failure, GetAllCampaignesResponse>> getCampaigns(
      {required String accountId}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getCampaigns(accountId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, GetAllCampaignesResponse>> loadMoreCampaigns(
      {required String url}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.loadMoreCampaigns(url: url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<Objective>>> getObjectives() {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getObjectives();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, Objective>> getCurrentObjective(
      {required String campaignId}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getCurrentCampaignObjective(
            campaignId: campaignId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, PostResponse>> getFbPosts(
      {required String pageId, required String pageAccessToken}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getFbPosts(
            pageId: pageId, pageAccessToken: pageAccessToken);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, InstagramPostsResponse>> getInstaPosts(
      {required String pageId,
      required String pageAccessToken,
      required String instaUserId}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getInstaPosts(
            pageId: pageId,
            pageAccessToken: pageAccessToken,
            instaUserId: instaUserId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, InstagramPostsResponse>> loadMoreInstaPosts({
    String? url,
    required String pageAccessToken,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.loadMoreInstaPosts(
            url: url, pageAccessToken: pageAccessToken);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, PostResponse>> loadMoreFbPosts({String? url}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.loadMoreFbPosts(url: url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, FormsResponse>> getLeadForms(
      {required String pageId, required String pageAccessToken}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getLeadForms(
            pageId: pageId, pageAccessToken: pageAccessToken);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, FormsResponse>> loadMoreLeadsForms({String? url}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.loadMoreLeadsForms(url: url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<Optimizations>>> getOptimizations(
      {required int objectiveId}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getOptimizations(
            objectiveId: objectiveId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<Optimizations>>> getBillingEvents(
      {required int optimizationId}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getBillingEvents(
            optimizationId: optimizationId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, GetAdSetsResponse>> getAdSets(
      {required String campaignId, required String pageAccessToken}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getAdSets(
            campaignId, pageAccessToken);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, GetAdSetsResponse>> loadMoreAdSets(
      {required String url}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.loadMoreAdSets(url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<SearchResult>>> search(
      {required String type, required String keyword}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.search(type, keyword);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<SearchClass>>> searchForClass(
      {required String type, required String searchClass}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.searchForClass(type, searchClass);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, AdReachEstimatedModel>> getReachEstimate(
      {required AdModel adModel,
      required List<File> imagesFiles,
      required List<File> videosFiles}) {
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.getReachEstimate(
            adModel: adModel,
            imagesFiles: imagesFiles,
            videosFiles: videosFiles);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> createAD({
    required AdModel adModel,
    required List<File> imagesFiles,
    required List<File> videosFiles,
    // required List<File> thumbFiles
  }) {
    adModel.toJson().then((value) => print('creationAdxx2 $value'));
    return FailureHelper.instance(
      method: () async {
        return await createCampaignDataSource.createAD(
          adModel: adModel,
          imagesFiles: imagesFiles,
          videosFiles: videosFiles,
          // thumbFiles: thumbFiles
        );
      },
      networkInfo: networkInfo,
    );
  }
}

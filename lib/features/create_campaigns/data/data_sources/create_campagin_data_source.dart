import 'dart:io';

import 'package:ads_dv/features/create_campaigns/data/models/ad_model.dart';
import 'package:ads_dv/features/create_campaigns/data/models/existing_campagin.dart';
import 'package:ads_dv/features/create_campaigns/data/models/objectives.dart';
import 'package:ads_dv/features/create_campaigns/data/models/optimization.dart';
import 'package:ads_dv/features/create_campaigns/data/models/post_response.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_class.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_result.dart';
import 'package:dio/dio.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../utils/network/dio/enum.dart';
import '../../../../utils/network/dio/network_call.dart';
import '../../../../utils/network/urls/end_points.dart';
import '../models/ad_reach_estimated_model.dart';
import '../models/adset.dart';
import '../models/instagram_post_response.dart';
import '../models/lead_forms.dart';

class CreateCampaignDataSource {
  Future<List<Objective>> getObjectives() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getObjectives,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['data'];
      List<Objective> objective =
          data.map((objective) => Objective.fromJson(objective)).toList();
      return objective;
    } catch (error) {
      rethrow;
    }
  }

  Future<Objective> getCurrentCampaignObjective(
      {required String campaignId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getCurrentObjective,
        params: {
          "campaign_id": campaignId,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      // dynamic data = response['data'];
      Objective objective = Objective.fromJson(response['result']);
      // data((objective) => Objective.fromJson(objective));
      return objective;
    } catch (error) {
      rethrow;
    }
  }

  Future<GetAllCampaignesResponse> loadMoreCampaigns({String? url}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.campaignsPagination,
        queryParameters: {
          if (url != null) "link": url,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return GetAllCampaignesResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<List<Optimizations>> getOptimizations(
      {required int objectiveId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getOptimizations(objectiveId),
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['data'];
      List<Optimizations> optimizations = data
          .map((optimizations) => Optimizations.fromJson(optimizations))
          .toList();
      return optimizations;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<Optimizations>> getBillingEvents(
      {required int optimizationId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getBillingEvent(optimizationId),
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['data'];
      List<Optimizations> events =
          data.map((events) => Optimizations.fromJson(events)).toList();
      return events;
    } catch (error) {
      rethrow;
    }
  }

  Future<PostResponse> getFbPosts(
      {required String pageId, required String pageAccessToken}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getPosts,
        queryParameters: {
          "page_access_token": pageAccessToken,
          "page_id": pageId,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return PostResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<InstagramPostsResponse> getInstaPosts(
      {required String pageId,
      required String pageAccessToken,
      required String instaUserId}) async {
    try {
      print('instaPayload $pageId $pageAccessToken $instaUserId');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getInstaPosts,
        queryParameters: {
          "page_access_token": pageAccessToken,
          "page_id": pageId,
          'instagram_user_id': instaUserId,
          // "17841457812335333",
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return InstagramPostsResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<InstagramPostsResponse> loadMoreInstaPosts({
    String? url,
    required String pageAccessToken,
  }) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.instaPostsPagination,
        queryParameters: {
          if (url != null) "link": url,
          "page_access_token": pageAccessToken,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return InstagramPostsResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<PostResponse> loadMoreFbPosts({String? url}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.postsPagination,
        queryParameters: {
          if (url != null) "link": url,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return PostResponse.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<FormsResponse> getLeadForms(
      {required String pageId, required String pageAccessToken}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getLeadsForms,
        queryParameters: {
          "page_access_token": pageAccessToken,
          "page_id": pageId,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return FormsResponse.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<FormsResponse> loadMoreLeadsForms({String? url}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.leadsFormsPagination,
        queryParameters: {
          if (url != null) "link": url,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return FormsResponse.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<GetAllCampaignesResponse> getCampaigns(String accountId) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllCampaigns,
        queryParameters: {
          "ad_account_id": accountId,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      GetAllCampaignesResponse cam = GetAllCampaignesResponse.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<GetAdSetsResponse> getAdSets(
      String campaignId, String pageAccessToken) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAdSets,
        queryParameters: {
          "campaign_id": campaignId,
          "page_access_token": pageAccessToken,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      GetAdSetsResponse adSet = GetAdSetsResponse.fromJson(data);
      return adSet;
    } catch (error) {
      rethrow;
    }
  }

  Future<GetAdSetsResponse> loadMoreAdSets(String url) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAdSetsPagination,
        queryParameters: {
          "link": url,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      GetAdSetsResponse adSet = GetAdSetsResponse.fromJson(data);
      return adSet;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<SearchResult>> search(String type, String keyword) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.searchWithTextUrl,
        queryParameters: {
          "type": type,
          "key_word": keyword,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<SearchResult> search =
          data.map((search) => SearchResult.fromJson(search)).toList();
      return search;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<SearchClass>> searchForClass(
      String type, String searchClass) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.searchWithClassUrl,
        queryParameters: {
          "type": type,
          "class": searchClass,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['result'];
      List<SearchClass> cl =
          data.map((cl) => SearchClass.fromJson(cl)).toList();
      return cl;
    } catch (error) {
      rethrow;
    }
  }

  Future<AdReachEstimatedModel> getReachEstimate(
      {required AdModel adModel,
      required List<File> imagesFiles,
      required List<File> videosFiles}) async {
    try {
      List<bool> isImagesValid =
          imagesFiles.map((e) => e.existsSync()).toList();

      var body = (imagesFiles.isNotEmpty || adModel.objectStoryId != null)
          ? FormData.fromMap({
              "ad_account_id": adModel.adAccountId,
              "page_id": adModel.pageId,
              "page_access_token": adModel.pageAccessToken,
              "instagram_actor_id": adModel.instAccId,
              "lead_gen_form_id": adModel.formId,
              "form_name": adModel.formName,
              "form_locale": adModel.formLocale,
              for (int i = 0; i < (adModel.questions?.length ?? 0); i++)
                "questions[$i][type]": adModel.questions?[i],
              "form_link_text": adModel.formLinkText,
              "form_url": adModel.formUrl,
              "form_title": adModel.formTitle,
              "form_body": adModel.formBody,
              "form_button_type": adModel.formButtonType,
              "form_button_text": adModel.formButtonText,
              "form_website_url": adModel.formWebsiteUrl,
              "type": adModel.type,
              "ad_crative_name": adModel.adCreativeName,
              "object_story_id": adModel.objectStoryId,
              "link": adModel.link,
              "description": adModel.description,
              "website_link_main": adModel.webSiteLinkMain,
              "message": adModel.message,
              "link_description": adModel.linkDescription,
              "title": adModel.title,
              "destination_type": adModel.destinationType,
              "ad_set_name": adModel.adSetName,
              "exist_campaign": adModel.existCampaign,
              "exist_adset": adModel.existAdSet,
              "optimization_goal": adModel.optimizationGoal,
              "billing_event": adModel.billingEven,
              "daily_budget": adModel.dailyBudget,
              "start_time": adModel.startDate,
              "end_time": adModel.endDate,
              for (int i = 0; i < (adModel.genders?.length ?? 0); i++)
                "genders[$i]": adModel.genders?[i],
              "age_min": adModel.ageMin,
              "age_max": adModel.ageMax,
              for (int i = 0; i < (adModel.interests?.length ?? 0); i++)
                "interests[$i]": adModel.interests?[i].toJson(),
              for (int i = 0;
                  i < (adModel.publisherPlatforms?.length ?? 0);
                  i++)
                "publisher_platforms[$i]": adModel.publisherPlatforms?[i],
              for (int i = 0; i < (adModel.facebookPositions?.length ?? 0); i++)
                "facebook_positions[$i]": adModel.facebookPositions?[i],
              for (int i = 0;
                  i < (adModel.instagramPositions?.length ?? 0);
                  i++)
                "instagram_positions[$i]": adModel.instagramPositions?[i],
              for (int i = 0;
                  i < (adModel.messengerPositions?.length ?? 0);
                  i++)
                "messenger_positions[$i]": adModel.messengerPositions?[i],
              for (int i = 0; i < (adModel.languages?.length ?? 0); i++)
                "lang[$i]": adModel.languages?[i].key,
              for (int i = 0; i < (adModel.educationSchools?.length ?? 0); i++)
                "education_schools[$i]": adModel.educationSchools?[i].toJson(),
              "bid_amount": adModel.bidAmount,
              for (int i = 0; i < (adModel.geoLocations?.length ?? 0); i++)
                "geo_locations": adModel.geoLocations?[i].toJson(),
              "ad_name": adModel.adName,
              "ad_status": adModel.adStatus,
              for (int i = 0; i < (imagesFiles.length); i++)
                "images[$i]": imagesFiles[i].existsSync()
                    ? await MultipartFile.fromFile(imagesFiles[i].path)
                    : null,
              // for (int i = 0; i < (files.length); i++)
              //   "video[$i]": files[i].existsSync()
              //       ? await MultipartFile.fromFile(files[i].path)
              //       : null,
              // for (int i = 0; i < (files.length); i++)
              //   "thumb[$i]": files[i].existsSync()
              //       ? await MultipartFile.fromFile(files[i].path)
              //       : null,
              "campaign_name": adModel.campaignName,
              "objective": adModel.objective,
              "status": adModel.status,
              for (int i = 0;
                  i < (adModel.specialAdCategories?.length ?? 0);
                  i++)
                "special_ad_categories[$i]": adModel.specialAdCategories?[i],
            })
          : FormData.fromMap({
              "instagram_actor_id": adModel.instAccId,

              "lead_gen_form_id": adModel.formId,
              "form_name": adModel.formName,
              "form_locale": adModel.formLocale,
              for (int i = 0; i < (adModel.questions?.length ?? 0); i++)
                "questions[$i][type]": adModel.questions?[i],
              "form_link_text": adModel.formLinkText,
              "form_url": adModel.formUrl,
              "form_title": adModel.formTitle,
              "form_body": adModel.formBody,
              "form_button_type": adModel.formButtonType,
              "form_button_text": adModel.formButtonText,
              "form_website_url": adModel.formWebsiteUrl,
              "object_story_id": adModel.objectStoryId,
              "page_id": adModel.pageId,
              "page_access_token": adModel.pageAccessToken,
              "ad_account_id": adModel.adAccountId,
              "exist_campaign": adModel.existCampaign,
              "exist_adset": adModel.existAdSet,
              "ad_crative_name": adModel.adCreativeName,
              "link": adModel.link,
              "website_link_main": adModel.webSiteLinkMain,
              "message": adModel.message,
              "link_description": adModel.linkDescription,
              "title": adModel.title,
              "description": adModel.description,
              "ad_set_name": adModel.adSetName,
              "optimization_goal": adModel.optimizationGoal,
              "billing_event": adModel.billingEven,
              "daily_budget": adModel.dailyBudget,
              "start_time": adModel.startDate,
              "type": adModel.type,
              "end_time": adModel.endDate,
              for (int i = 0; i < (adModel.genders?.length ?? 0); i++)
                "genders[$i]": adModel.genders?[i],
              "age_min": adModel.ageMin,
              "age_max": adModel.ageMax,
              for (int i = 0; i < (adModel.interests?.length ?? 0); i++)
                "interests[$i]": adModel.interests?[i].toJson(),
              for (int i = 0;
                  i < (adModel.publisherPlatforms?.length ?? 0);
                  i++)
                "publisher_platforms[$i]": adModel.publisherPlatforms?[i],
              for (int i = 0; i < (adModel.languages?.length ?? 0); i++)
                "lang[$i]": adModel.languages?[i].key,
              for (int i = 0; i < (adModel.educationSchools?.length ?? 0); i++)
                "education_schools[$i]": adModel.educationSchools?[i].toJson(),
              "destination_type": adModel.destinationType,
              "bid_amount": adModel.bidAmount,
              for (int i = 0; i < (adModel.geoLocations?.length ?? 0); i++)
                "geo_locations": adModel.geoLocations?[i].toJson(),
              // for (int i = 0; i < (videosFiles.length); i++)
              //   "video[$i]": videosFiles[i].existsSync()
              //       ? await MultipartFile.fromFile(videosFiles[i].path)
              //       : null,
              // for (int i = 0; i < (videosFiles.length); i++)
              //   "thumb[$i]": videosFiles[i].existsSync()
              //       ? await MultipartFile.fromFile(videosFiles[i].path)
              //       : null,
              //'video': (adModel.video != null && adModel.video!.existsSync())
              //     ? await MultipartFile.fromFile(adModel.video!.path)
              //     : null,
              // 'thumb': (adModel.thumb != null && adModel.thumb!.existsSync())
              //     ? await MultipartFile.fromFile(adModel.thumb!.path)
              //     : null,
              "ad_name": adModel.adName,
              "ad_status": adModel.adStatus,
              "campaign_name": adModel.campaignName,
              "objective": adModel.objective,
              "status": adModel.status,
              for (int i = 0;
                  i < (adModel.specialAdCategories?.length ?? 0);
                  i++)
                "special_ad_categories[$i]": adModel.specialAdCategories?[i],
            });

      var response =
          await instance<NetworkCall>().request(EndPoints.getCampaignsStatus,
              params: isImagesValid.every((element) => element) ? body : null,
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      AdReachEstimatedModel reachEstimatedModel =
          AdReachEstimatedModel.fromJson(response['result']);
      return reachEstimatedModel;
      // return response['result'];
    } catch (error) {
      rethrow;
    }
  }

  Future<String> createAD({
    required AdModel adModel,
    required List<File> imagesFiles,
    required List<File> videosFiles,
    // required List<File> thumbFiles
  }) async {
    try {
      List<bool> isImagesValid =
          imagesFiles.map((e) => e.existsSync()).toList();

      var body = (imagesFiles.isNotEmpty || adModel.objectStoryId != null)
          ? FormData.fromMap({
              "ad_account_id": adModel.adAccountId,
              "page_id": adModel.pageId,
              "phone_number": adModel.phone,
              "is_instagram_post": adModel.isInstaPost,
              "instagram_user_id": adModel.instaUserId,
              "page_access_token": adModel.pageAccessToken,
              "instagram_actor_id": adModel.instAccId,
              "lead_gen_form_id": adModel.formId,
              "form_name": adModel.formName,
              "form_locale": adModel.formLocale,
              for (int i = 0; i < (adModel.questions?.length ?? 0); i++)
                "questions[$i][type]": adModel.questions?[i],
              "form_link_text": adModel.formLinkText,
              "form_url": adModel.formUrl,
              "form_title": adModel.formTitle,
              "form_body": adModel.formBody,
              "form_button_type": adModel.formButtonType,
              "form_button_text": adModel.formButtonText,
              "form_website_url": adModel.formWebsiteUrl,
              "type": adModel.phone != null ? "CALL_NOW" : adModel.type,
              "ad_crative_name": adModel.adCreativeName,
              "object_story_id": adModel.objectStoryId,
              "link": adModel.link,
              "description": adModel.description,
              "website_link_main": adModel.phone != null ? null : adModel.webSiteLinkMain,
              "message": adModel.message,
              "link_description": adModel.linkDescription,
              "title": adModel.title,
              "destination_type": adModel.phone != null ? "PHONE_CALL" : adModel.destinationType,
              "ad_set_name": adModel.adSetName,
              "exist_campaign": adModel.existCampaign,
              "exist_adset": adModel.existAdSet,
              "optimization_goal": adModel.optimizationGoal,
              "billing_event": adModel.billingEven,
              "daily_budget": adModel.dailyBudget,
              "start_time": adModel.startDate,
              "end_time": adModel.endDate,
              for (int i = 0; i < (adModel.genders?.length ?? 0); i++)
                "genders[$i]": adModel.genders?[i],
              "age_min": adModel.ageMin,
              "age_max": adModel.ageMax,
              for (int i = 0; i < (adModel.interests?.length ?? 0); i++)
                "interests[$i]": adModel.interests?[i].toJson(),
              for (int i = 0;
                  i < (adModel.publisherPlatforms?.length ?? 0);
                  i++)
                "publisher_platforms[$i]": adModel.publisherPlatforms?[i],
              for (int i = 0; i < (adModel.facebookPositions?.length ?? 0); i++)
                "facebook_positions[$i]": adModel.facebookPositions?[i],
              for (int i = 0;
                  i < (adModel.instagramPositions?.length ?? 0);
                  i++)
                "instagram_positions[$i]": adModel.instagramPositions?[i],
              for (int i = 0;
                  i < (adModel.messengerPositions?.length ?? 0);
                  i++)
                "messenger_positions[$i]": adModel.messengerPositions?[i],
              for (int i = 0; i < (adModel.languages?.length ?? 0); i++)
                "lang[$i]": adModel.languages?[i].key,
              for (int i = 0; i < (adModel.educationSchools?.length ?? 0); i++)
                "education_schools[$i]": adModel.educationSchools?[i].toJson(),
              "bid_amount": adModel.bidAmount,
              for (int i = 0; i < (adModel.geoLocations?.length ?? 0); i++)
                "geo_locations": adModel.geoLocations?[i].toJson(),
              "ad_name": adModel.adName,
              "ad_status": adModel.adStatus,
              for (int i = 0; i < (imagesFiles.length); i++)
                "images[$i]": imagesFiles[i].existsSync()
                    ? await MultipartFile.fromFile(imagesFiles[i].path)
                    : null,
              // for (int i = 0; i < (files.length); i++)
              //   "video[$i]": files[i].existsSync()
              //       ? await MultipartFile.fromFile(files[i].path)
              //       : null,
              // for (int i = 0; i < (files.length); i++)
              //   "thumb[$i]": files[i].existsSync()
              //       ? await MultipartFile.fromFile(files[i].path)
              //       : null,
              "campaign_name": adModel.campaignName,
              "objective": adModel.objective,
              "status": adModel.status,
              for (int i = 0;
                  i < (adModel.specialAdCategories?.length ?? 0);
                  i++)
                "special_ad_categories[$i]": adModel.specialAdCategories?[i],
            })
          : FormData.fromMap({
              "instagram_actor_id": adModel.instAccId,
              "phone_number": adModel.phone,
              "lead_gen_form_id": adModel.formId,
              "form_name": adModel.formName,
              "form_locale": adModel.formLocale,
              for (int i = 0; i < (adModel.questions?.length ?? 0); i++)
                "questions[$i][type]": adModel.questions?[i],
              "form_link_text": adModel.formLinkText,
              "form_url": adModel.formUrl,
              "form_title": adModel.formTitle,
              "form_body": adModel.formBody,
              "form_button_type": adModel.formButtonType,
              "form_button_text": adModel.formButtonText,
              "form_website_url": adModel.formWebsiteUrl,
              "object_story_id": adModel.objectStoryId,
              "page_id": adModel.pageId,
              "page_access_token": adModel.pageAccessToken,
              "ad_account_id": adModel.adAccountId,
              "exist_campaign": adModel.existCampaign,
              "exist_adset": adModel.existAdSet,
              "ad_crative_name": adModel.adCreativeName,
              "link": adModel.link,
              "website_link_main": adModel.phone != null ? null : adModel.webSiteLinkMain,
              "message": adModel.message,
              "link_description": adModel.linkDescription,
              "title": adModel.title,
              "description": adModel.description,
              "ad_set_name": adModel.adSetName,
              "optimization_goal": adModel.optimizationGoal,
              "billing_event": adModel.billingEven,
              "daily_budget": adModel.dailyBudget,
              "start_time": adModel.startDate,
              "type": adModel.phone != null ? "CALL_NOW" : adModel.type,
              "end_time": adModel.endDate,
              for (int i = 0; i < (adModel.genders?.length ?? 0); i++)
                "genders[$i]": adModel.genders?[i],
              "age_min": adModel.ageMin,
              "age_max": adModel.ageMax,
              for (int i = 0; i < (adModel.interests?.length ?? 0); i++)
                "interests[$i]": adModel.interests?[i].toJson(),
              for (int i = 0;
                  i < (adModel.publisherPlatforms?.length ?? 0);
                  i++)
                "publisher_platforms[$i]": adModel.publisherPlatforms?[i],
              for (int i = 0; i < (adModel.languages?.length ?? 0); i++)
                "lang[$i]": adModel.languages?[i].key,
              for (int i = 0; i < (adModel.educationSchools?.length ?? 0); i++)
                "education_schools[$i]": adModel.educationSchools?[i].toJson(),
              "destination_type": adModel.phone != null ? "PHONE_CALL" :  adModel.destinationType,
              "bid_amount": adModel.bidAmount,
              for (int i = 0; i < (adModel.geoLocations?.length ?? 0); i++)
                "geo_locations": adModel.geoLocations?[i].toJson(),
              for (int i = 0; i < (videosFiles.length); i++)
                "video[$i]": videosFiles[i].existsSync()
                    ? await MultipartFile.fromFile(videosFiles[i].path)
                    : null,
              // for (int i = 0; i < (thumbFiles.length); i++)
              //   "thumb[$i]": thumbFiles[i].existsSync()
              //       ? await MultipartFile.fromFile(thumbFiles[i].path)
              //       : null,
              // 'video': (adModel.video != null && adModel.video!.existsSync())
              //     ? await MultipartFile.fromFile(adModel.video!.path)
              //     : null,
              // 'thumb': (adModel.thumb != null && adModel.thumb!.existsSync())
              //     ? await MultipartFile.fromFile(adModel.thumb!.path)
              //     : null,
              "ad_name": adModel.adName,
              "ad_status": adModel.adStatus,
              "campaign_name": adModel.campaignName,
              "objective": adModel.objective,
              "status": adModel.status,
              for (int i = 0;
                  i < (adModel.specialAdCategories?.length ?? 0);
                  i++)
                "special_ad_categories[$i]": adModel.specialAdCategories?[i],
            });

      print('jasdoasfdklf ${body.fields}');

      var response = await instance<NetworkCall>().request(EndPoints.fullCycle,
          params: isImagesValid.every((element) => element) ? body : null,
          options: Options(
            method: Method.post.name,
            headers: {
              "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
            },
          ));
      return response['result'];
    } catch (error) {
      rethrow;
    }
  }
}

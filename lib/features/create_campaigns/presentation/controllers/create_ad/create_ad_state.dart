part of 'create_ad_cubit.dart';

@immutable
abstract class CreateAdState {}

class CreateAdInitial extends CreateAdState {}

class SetSelectedInterests extends CreateAdState {}

class ChangeCampaignTab extends CreateAdState {}
class ChangeFormTab extends CreateAdState {}

class ChangeAdSetTab extends CreateAdState {}

class SelectExistingCampaign extends CreateAdState {}

class SetSelectedGoal extends CreateAdState {}

class SetSelectedOptimization extends CreateAdState {}

class ChangeCampaignStatus extends CreateAdState {}

class ChangeAdSetStatus extends CreateAdState {}

class RemoveFromLanguageState extends CreateAdState {}

class AdToLanguageState extends CreateAdState {}

class RemoveFromMajorsState extends CreateAdState {}

class AdToMajorsState extends CreateAdState {}

class RemoveFromEventsState extends CreateAdState {}

class AdToEventsState extends CreateAdState {}


class RemoveFromUserOsState extends CreateAdState {}

class AdToUserOsState extends CreateAdState {}

class RemoveFromDevicesState extends CreateAdState {}

class AdToDevicesState extends CreateAdState {}

class RemoveFromBehavioursState extends CreateAdState {}

class AdToBehavioursState extends CreateAdState {}

class RemoveFromStatusesState extends CreateAdState {}

class AdToStatusesState extends CreateAdState {}
class RemoveFromSchoolsState extends CreateAdState {}

class AdToSchoolState extends CreateAdState {}


class RemoveFromInterestsState extends CreateAdState {}

class AdToInterestsState extends CreateAdState {}

class CreateADInitial extends CreateAdState {}

class CreateADStateLoading extends CreateAdState {}

class CreateADStateLoaded extends CreateAdState {
  final String? data;
  final AdReachEstimatedModel? reachEstimatedModel;

  CreateADStateLoaded({this.data
    ,this.reachEstimatedModel,
  });

  @override
  List<Object?> get props => [data,reachEstimatedModel];

  CreateADStateLoaded copyWith({
    String? data,
    AdReachEstimatedModel? reachEstimatedModel,
  }) {
    return CreateADStateLoaded(
      data: data ?? this.data,
      reachEstimatedModel: reachEstimatedModel ?? this.reachEstimatedModel,
    );
  }
}

class CreateADStateError extends CreateAdState {
  final Failure message;

  CreateADStateError(this.message);

  @override
  List<Object?> get props => [message];
}


class RemoveImageState extends CreateAdState {}
class AddImageState extends CreateAdState {}
class AddImagesState extends CreateAdState {}
final class UpdateStates extends CreateAdState {}

final class UpdateProcessPercentage extends CreateAdState {}
final class SetSelectedLocations extends CreateAdState {}

final class SetAdExpansionState extends CreateAdState {}

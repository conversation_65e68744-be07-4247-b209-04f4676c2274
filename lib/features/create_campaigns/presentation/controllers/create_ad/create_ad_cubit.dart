import 'dart:io';

import 'package:ads_dv/features/create_campaigns/data/models/ad_model.dart';
import 'package:ads_dv/features/create_campaigns/data/models/adset.dart';
import 'package:ads_dv/features/create_campaigns/data/models/call_to_action.dart';
import 'package:ads_dv/features/create_campaigns/data/models/language.dart';
import 'package:ads_dv/features/create_campaigns/data/models/post_response.dart';
import 'package:ads_dv/features/create_campaigns/data/models/question.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_class.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/ad_account.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/status.dart';
import 'package:ads_dv/main.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/utils/res/meta_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/common_utils.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/cached__image.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../sidebar/ad_accounts/data/models/meta_page.dart';
import '../../../../sidebar/ad_accounts/data/models/page.dart';
import '../../../data/models/ad_reach_estimated_model.dart';
import '../../../data/models/adset_location.dart';
import '../../../data/models/existing_campagin.dart';
import '../../../data/models/optimization.dart';
import '../../../data/models/position.dart';
import '../../../data/models/search_result.dart';
import '../../../data/repos/create_campaign_repo.dart';
import '../get_objectives/get_objectives_cubit.dart';

part 'create_ad_state.dart';

class CreateAdCubit extends Cubit<CreateAdState> {
  CreateAdCubit() : super(CreateAdInitial());

  static CreateAdCubit get(context) => BlocProvider.of(context);

  ExistingCampaign? existingCampaign;
  AdSet? existingAdSet;

  bool? isCampaignCreated = false;
  bool? isAdSetCreated = false;
  bool? isAdCreativeCreated = false;
  bool? isAdReviewCreated = false;

  List<Positions> facebookPositions = [
    Positions(name: 'Home Feed', value: 'feed', isChecked: false),
    Positions(name: 'Marketplace', value: 'marketplace', isChecked: false),
    Positions(name: 'Video Feeds', value: 'video_feeds', isChecked: false),
    Positions(name: 'Story', value: 'story', isChecked: false),
    Positions(name: 'Search', value: 'search', isChecked: false),
    Positions(
        name: 'Facebook Reels', value: 'facebook_reels', isChecked: false),
    Positions(name: 'Profile Feed', value: 'profile_feed', isChecked: false),
  ];

  List<Positions> instagramPositions = [
    //   Positions(name: 'Feed', value: 'feed', isChecked: false),
    Positions(name: 'Stream', value: 'stream', isChecked: false),
    Positions(name: 'Story', value: 'story', isChecked: false),
    Positions(name: 'Explore', value: 'explore', isChecked: false),
    Positions(name: 'home page', value: 'explore_home', isChecked: false),
    Positions(name: 'Reels', value: 'reels', isChecked: false),
    Positions(name: 'Profile Reels', value: 'profile_reels', isChecked: false),
    Positions(name: 'Profile Feed', value: 'profile_feed', isChecked: false),
  ];

  List<Positions> messengerPositions = [
    Positions(
        name: 'Messenger Inbox', value: 'messenger_home', isChecked: false),
    Positions(
        name: 'Sponsored Message',
        value: 'sponsored_messages',
        isChecked: false),
    Positions(name: 'Story', value: 'story', isChecked: false),
  ];
  List<AdSetGeoLocations> geoLocations = [
    // AdSetGeoLocations(customLocations: [
    //   CustomLocations(
    //       latitude: 30.0444196,
    //       longitude: 31.2357116,
    //       radius: 49,
    //       addressString: "re",
    //       distanceUnit: DistanceUnit.kilometer)
    // ])
    // // AdSetGeoLocations(customLocations: [
    // //   CustomLocations(
    // //       latitude: 30.0444196,
    // //       longitude: 31.2357116,
    // //       radius: 49,
    // //       addressString: "re",
    // //       distanceUnit: DistanceUnit.kilometer)
    // // ])
  ];

  void setSelectedLocation(List<AdSetGeoLocations> adLocations) {
    if (geoLocations.isEmpty) {
      geoLocations = adLocations;
      isAddNewLocation = false;
    } else {
      geoLocations.first.customLocations!
          .addAll(adLocations.first.customLocations!.map((e) => e).toList());
      isAddNewLocation = false;
    }
    emit(UpdateStates());
  }

  void removeLocation(List<AdSetGeoLocations> adLocations, int indexToRemove) {
    if (geoLocations.isEmpty) {
      geoLocations = adLocations;

      isAddNewLocation = true;
    } else {
      // Check if the indexToRemove is valid
      if (indexToRemove >= 0 &&
          indexToRemove < geoLocations.first.customLocations!.length) {
        // Remove the location at the specified index
        geoLocations.first.customLocations!.removeAt(indexToRemove);
        if (geoLocations.first.customLocations!.isEmpty) {
          geoLocations = adLocations;
          isAddNewLocation = true;
          locationPercentage = 0.0;
        }
      }
    }
    emit(UpdateStates());
  }

/////////////////////////

  final leadsFormKey = GlobalKey<FormState>();
  final TextEditingController leadHeadlineController = TextEditingController();
  final TextEditingController linkController = TextEditingController();
  final TextEditingController linkTextController = TextEditingController();
  final TextEditingController websiteLinkController = TextEditingController();
  final TextEditingController ctaController = TextEditingController();

  final TextEditingController leadMessage = TextEditingController();
  final TextEditingController leadDesc = TextEditingController();

  ///////////////////////////
  final campaignFormKey = GlobalKey<FormState>();
  final TextEditingController campaignNameController = TextEditingController();

  final adSetFormKey = GlobalKey<FormState>();

  final TextEditingController adSetNameController = TextEditingController();
  final TextEditingController dailyBudget = TextEditingController();

  var startDate = TextEditingController(text: "");
  var endDate = TextEditingController(text: "");

  var minAge = TextEditingController();
  var maxAge = TextEditingController();

  final adFormKey = GlobalKey<FormState>();

  TextEditingController adName = TextEditingController();

  TextEditingController adCreativeName = TextEditingController();
  TextEditingController headline = TextEditingController();
  TextEditingController message = TextEditingController();
  TextEditingController webSiteLink = TextEditingController();
  TextEditingController linkDesc = TextEditingController();
  TextEditingController phoneNumber = TextEditingController();
  String? phoneCode;

  final adCreativeFormKey = GlobalKey<FormState>();

  final adCreativeVideoFormKey = GlobalKey<FormState>();
  double campaignProcessPercentage = 0.0;
  bool isCampaignProcess1Updated = false;
  bool isCampaignProcess2Updated = false;
  bool isCampaignProcess3Updated = false;
  bool isCampaignProcess4Updated = false;
  bool isAddCreated = false;

  bool isAdSetTileExpanded = false;
  bool isAdCreativeTileExpanded = false;
  bool isReviewTileExpanded = false;

  bool isAddNewLocation = false;

  void setAddNewLocationStatus() {
    isAddNewLocation = true;
    emit(UpdateStates());
  }

  void setAdSetExpansionState(bool isClosed) {
    isAdSetTileExpanded = isClosed;
    emit(SetAdExpansionState());
  }

  void setAdCreativeExpansionState(bool isClosed) {
    isAdCreativeTileExpanded = isClosed;
    emit(SetAdExpansionState());
  }

  void setReviewExpansionState(bool isClosed) {
    isReviewTileExpanded = isClosed;
    emit(SetAdExpansionState());
  }

  void undoCampaignProcess1() {
    if (isCampaignProcess1Updated) {
      campaignProcessPercentage -= 0.25;
      isCampaignProcess1Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateCampaignProcess1() {
    if (!isCampaignProcess1Updated) {
      campaignProcessPercentage += 0.25;
      isCampaignProcess1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateCampaignProcess2() {
    if (!isCampaignProcess2Updated) {
      campaignProcessPercentage += 0.25;
      isCampaignProcess2Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateCampaignProcess3() {
    if (!isCampaignProcess3Updated) {
      campaignProcessPercentage += 0.25;
      isCampaignProcess3Updated = true;
    } else if (isCampaignProcess4Updated) {
      campaignProcessPercentage -= 0.25;
      isCampaignProcess4Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateCampaignProcess4() {
    if (!isCampaignProcess4Updated) {
      campaignProcessPercentage += 0.25;
      isCampaignProcess4Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

//////////////////////////////
  double demoPercentage = 0.0;
  bool isDemoProcess1Updated = false;
  bool isDemoProcess2Updated = false;
  bool isDemoProcess3Updated = false;
  bool isDemoProcess4Updated = false;

  void updateDemoProcess1() {
    if (!isDemoProcess1Updated) {
      demoPercentage += 0.25;
      isDemoProcess1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoDemoProcess1() {
    if (isDemoProcess1Updated) {
      demoPercentage -= 0.25;
      isDemoProcess1Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateDemoProcess2() {
    if (!isDemoProcess2Updated) {
      demoPercentage += 0.25;
      isDemoProcess2Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoDemoProcess2() {
    if (isDemoProcess2Updated) {
      demoPercentage -= 0.25;
      isDemoProcess2Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateDemoProcess3() {
    if (!isDemoProcess3Updated) {
      demoPercentage += 0.25;
      isDemoProcess3Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateDemoProcess4() {
    if (!isDemoProcess4Updated && languages.length == 1) {
      demoPercentage += 0.25;
      isDemoProcess4Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoDemoProcess4() {
    if (languages.isEmpty) {
      demoPercentage -= 0.25;
      isDemoProcess4Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  ///////////////////////////
  double locationPercentage = 0.0;
  bool isLocationProcess1Updated = false;
  bool isLocationProcess2Updated = false;
  bool isLocationProcess3Updated = false;

  void updateLocationProcess1() {
    if (!isLocationProcess1Updated) {
      locationPercentage += 0.35;
      isLocationProcess1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoLocationProcess1() {
    if (isLocationProcess1Updated) {
      locationPercentage -= 0.35;
      isLocationProcess1Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateLocationProcess2() {
    if (!isLocationProcess2Updated) {
      locationPercentage += 0.35;
      isLocationProcess2Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoLocationProcess2() {
    if (isLocationProcess2Updated) {
      locationPercentage -= 0.35;
      isLocationProcess2Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateLocationProcess3() {
    if (!isLocationProcess3Updated) {
      locationPercentage += 0.3;
      isLocationProcess3Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  ////////////////////////

  double targetingPercentage = 0.0;

  bool isTargeting1Updated = false;
  bool isTargeting2Updated = false;
  bool isTargeting3Updated = false;
  bool isTargeting4Updated = false;
  bool isTargeting5Updated = false;
  bool isTargeting6Updated = false;
  bool isTargeting7Updated = false;
  bool isTargeting8Updated = false;

  void updateTargetingProcess1() {
    if (!isTargeting1Updated) {
      targetingPercentage += 0.125;
      isTargeting1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess1() {
    targetingPercentage -= 0.125;

    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess2() {
    if (!isTargeting2Updated && behaviours.length == 1) {
      targetingPercentage += 0.125;
      isTargeting2Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess2() {
    if (behaviours.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting2Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess3() {
    if (!isTargeting3Updated && selectedEducationalSchool.length == 1) {
      targetingPercentage += 0.125;
      isTargeting3Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess3() {
    if (selectedEducationalSchool.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting3Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess4() {
    if (!isTargeting4Updated && majors.length == 1) {
      targetingPercentage += 0.125;
      isTargeting4Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess4() {
    if (majors.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting4Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess5() {
    if (!isTargeting5Updated && educationStatuses.length == 1) {
      targetingPercentage += 0.125;
      isTargeting5Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess5() {
    if (educationStatuses.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting5Updated = false;
    }
    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess6() {
    if (!isTargeting6Updated && lifeEvents.length == 1) {
      targetingPercentage += 0.125;
      isTargeting6Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess6() {
    if (lifeEvents.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting6Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess7() {
    if (!isTargeting7Updated && userDevices.length == 1) {
      targetingPercentage += 0.125;
      isTargeting7Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess7() {
    if (userDevices.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting7Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateTargetingProcess8() {
    if (!isTargeting8Updated && userOs.length == 1) {
      targetingPercentage += 0.125;
      isTargeting8Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoTargetingProcess8() {
    if (userOs.isEmpty) {
      targetingPercentage -= 0.125;
      isTargeting8Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  //////////////////////////////
  double adPercentage = 0.0;
  double interestsPercentage = 0.0;
  bool isProcess1Updated = false;

  void undoProcess1() {
    if (isProcess1Updated) {
      adPercentage -= 1;
      print('addpercenv2 $adPercentage');
      isProcess1Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateProcess1() {
    if (!isProcess1Updated) {
      adPercentage += 1;
      print('addpercenv $adPercentage');
      isProcess1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

///////////////////

  double adSetPercentage = 0.0;

  bool isAdSetProcess1Updated = false;
  bool isAdSetProcess2Updated = false;
  bool isAdSetProcess3Updated = false;
  bool isAdSetProcess4Updated = false;
  bool isAdSetProcess5Updated = false;
  bool isAdSetProcess6Updated = false;
  bool isAdSetProcess7Updated = false;
  bool isAdSetProcess8Updated = false;
  bool isAdSetProcess9Updated = false;

  void updateAdSetProcess1() {
    if (!isAdSetProcess1Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdSetProcess1() {
    if (isAdSetProcess1Updated) {
      adSetPercentage -= 0.1;
      isAdSetProcess1Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess2() {
    if (!isAdSetProcess2Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess2Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess3() {
    if (!isAdSetProcess3Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess3Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess4() {
    if (!isAdSetProcess4Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess4Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdSetProcess4() {
    if (isAdSetProcess4Updated) {
      adSetPercentage -= 0.1;
      isAdSetProcess4Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess5() {
    if (!isAdSetProcess5Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess5Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess6() {
    if (!isAdSetProcess6Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess6Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess7() {
    if (targetingPercentage != 0.0 && !isAdSetProcess7Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess7Updated = true;
    } else {
      adSetPercentage -= 0.1;
      isAdSetProcess7Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess8() {
    if (locationPercentage != 0.0 && !isAdSetProcess8Updated) {
      adSetPercentage += 0.2;
      isAdSetProcess8Updated = true;
    } else if (geoLocations.isEmpty) {
      adSetPercentage -= 0.2;
      isAdSetProcess8Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdSetProcess9() {
    if (demoPercentage != 0.0 && !isAdSetProcess9Updated) {
      adSetPercentage += 0.1;
      isAdSetProcess9Updated = true;
    } else {
      adSetPercentage -= 0.1;
      isAdSetProcess9Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  /////////////////
  double adCreativePercentage = 0.0;
  bool isAdCreativeProcess1Updated = false;
  bool isAdCreativeProcess2Updated = false;
  bool isAdCreativeProcess3Updated = false;
  bool isAdCreativeProcess4Updated = false;
  bool isAdCreativeProcess5Updated = false;
  bool isAdCreativeProcess6Updated = false;
  bool isAdCreativeProcess7Updated = false;

  void updateAdCreativeProcess1() {
    if (!isAdCreativeProcess1Updated) {
      adCreativePercentage += 0.2;
      isAdCreativeProcess1Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdCreativeProcess1() {
    if (isAdCreativeProcess1Updated) {
      adCreativePercentage -= 0.2;
      isAdCreativeProcess1Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdCreativeProcess2() {
    if (!isAdCreativeProcess2Updated) {
      adCreativePercentage += 0.2;
      isAdCreativeProcess2Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdCreativeProcess2() {
    if (isAdCreativeProcess2Updated) {
      adCreativePercentage -= 0.2;
      isAdCreativeProcess2Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdCreativeProcess3() {
    if (!isAdCreativeProcess3Updated) {
      adCreativePercentage += 0.2;
      isAdCreativeProcess3Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdCreativeProcess3() {
    if (isAdCreativeProcess3Updated) {
      adCreativePercentage -= 0.2;
      isAdCreativeProcess3Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdCreativeProcess4() {
    if (!isAdCreativeProcess4Updated) {
      adCreativePercentage += 0.2;
      isAdCreativeProcess4Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdCreativeProcess4() {
    if (isAdCreativeProcess4Updated) {
      adCreativePercentage -= 0.2;
      isAdCreativeProcess4Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdCreativeProcess5(bool isImage) {
    if (!isAdCreativeProcess5Updated) {
      adCreativePercentage += 0.1;
      print('adCreativePercentage55 $adCreativePercentage');
      isAdCreativeProcess5Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdCreativeProcess6() {
    if (!isAdCreativeProcess6Updated) {
      adCreativePercentage += 0.1;
      print('adCreativePercentage66 $adCreativePercentage');
      isAdCreativeProcess6Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void updateAdCreativeProcess7() {
    if (!isAdCreativeProcess7Updated) {
      adCreativePercentage += 0.5;
      print('adCreativePercentage77 $adCreativePercentage');
      isAdCreativeProcess7Updated = true;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdCreativeProcess5() {
    if (isAdCreativeProcess5Updated) {
      adCreativePercentage -= 0.1;
      isAdCreativeProcess5Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  void undoAdCreativeProcess6() {
    if (isAdCreativeProcess6Updated) {
      adCreativePercentage -= 0.1;
      isAdCreativeProcess6Updated = false;
    }

    emit(UpdateProcessPercentage());
  }

  //////////////////
  List<File> adImages = [];

  // List<File> videoImage = [];

  List<File> adVideo = [];
  int? selectedDestinationIndex;

  AdModel adModel = AdModel();
  int? selectedExistingCampaign;
  int? selectedExistingAdSet;
  int selectFormTab = 0;

  int selectCampaignTab = 0;
  int? selectSocialPostsTab = 0;
  int selectAdSetTab = 0;
  int? selectedPost;
  int? postIndex;

  String? postId;
  Post? post;
  int? selectedForm;
  int? formIndex;

  String? formId;

  int? selectedLang;
  int? langIndex;

  String? langValue;
  int? selectedAction;
  int? actionIndex;

  String? actionValue;

  int? selectedGoal;
  List<Optimizations> selectedOptimization = [];
  List<SearchResult> languages = [];

  List<SearchResult> majors = [];
  List<SearchClass> lifeEvents = [];
  List<SearchClass> userOs = [];

  List<SearchClass> userDevices = [];
  List<SearchClass> behaviours = [];
  List<EducationStatuses> educationStatuses = [];

  //int? selectOptimizationIndex;
  String? type;
  String? typeName;

  String? objective;
  List<Language> lang = [
    Language(name: 'Arabic', value: 'AR_AR'),
    Language(name: 'English', value: 'EN_US'),
  ];

  List<Language> actions = [
    Language(name: 'Go to website', value: 'VIEW_WEBSITE'),
    Language(name: 'Call business', value: 'CALL_BUSINESS'),
    Language(name: 'View file', value: 'DOWNLOAD'),
    Language(name: 'Redeem promo code', value: 'PROMO_CODE'),
  ];

  List<Question> questions = [
    Question(name: 'What is your city?', value: 'CITY', isChecked: false),
    Question(
        name: 'What is your company name?',
        value: 'COMPANY_NAME',
        isChecked: false),
    Question(
        name: 'What is your country name?', value: 'COUNTRY', isChecked: false),
    Question(name: 'What is your gender?', value: 'GENDER', isChecked: false),
    Question(
        name: 'What is your first name?',
        value: 'FIRST_NAME',
        isChecked: false),
    Question(
        name: 'What is your full name?', value: 'FULL_NAME', isChecked: false),
    Question(
        name: 'What is your job title?', value: 'JOB_TITLE', isChecked: false),
    Question(
        name: 'What is your date of birth?', value: 'DOB', isChecked: false),
    Question(name: 'What is your email?', value: 'EMAIL', isChecked: false),
    Question(
        name: 'What is your last name?', value: 'LAST_NAME', isChecked: false),
    Question(
        name: 'What is your marital status?',
        value: 'MARITIAL_STATUS',
        isChecked: false),
    Question(name: 'What is your phone?', value: 'PHONE', isChecked: false),
    Question(name: 'What is your state?', value: 'STATE', isChecked: false),
    Question(
        name: 'What is your street address?',
        value: 'STREET_ADDRESS',
        isChecked: false),
    // Question(name: 'What is your city?', value: 'CITY', isChecked: false),
    // Question(
    //     name: 'What is your company name?',
    //     value: 'COMPANY_NAME',
    //     isChecked: false),
    // Question(
    //     name: 'What is your country name?', value: 'COUNTRY', isChecked: false),
    // Question(name: 'What is your gender?', value: 'GENDER', isChecked: false),
    // Question(
    //     name: 'What is your first name?',
    //     value: 'FIRST_NAME',
    //     isChecked: false),
    // Question(
    //     name: 'What is your full name?', value: 'FULL_NAME', isChecked: false),
    // Question(
    //     name: 'What is your job title?', value: 'JOB_TITLE', isChecked: false),
  ];
  List<CallToAction> awarenessCallToAction = [
    ///awareness
    CallToAction(name: 'Apply Now'.tr, value: 'APPLY_NOW'),
    CallToAction(name: 'Book Now'.tr, value: 'BOOK_NOW'),
    CallToAction(name: 'Call Now'.tr, value: 'CALL_NOW'),
    CallToAction(name: 'Contact Us'.tr, value: 'CONTACT_US'),
    CallToAction(name: 'Download'.tr, value: 'DOWNLOAD'),
    CallToAction(name: 'Get Directions'.tr, value: 'GET_DIRECTIONS'),

    CallToAction(name: 'Get Quota'.tr, value: 'GET_QUOTE'),
    CallToAction(name: 'Learn More'.tr, value: 'LEARN_MORE'),
    CallToAction(name: 'Order Now'.tr, value: 'ORDER_NOW'),
    CallToAction(name: 'Save'.tr, value: 'SAVE'),
    CallToAction(name: 'See Menu'.tr, value: 'SEE_MENU'),

    CallToAction(name: 'Send Message'.tr, value: 'SEND_MESSAGE'),
    CallToAction(name: 'Whatsapp Message'.tr, value: 'WHATSAPP_MESSAGE'),

    CallToAction(name: 'Shop Now'.tr, value: 'SHOP_NOW'),
    CallToAction(name: 'Subscribe'.tr, value: 'SUBSCRIBE'),
    CallToAction(name: 'Sign up'.tr, value: 'SIGN_UP'),
    CallToAction(name: 'Watch More'.tr, value: 'WATCH_MORE'),
    CallToAction(name: 'Donate Now'.tr, value: 'DONATE_NOW'),
    CallToAction(name: 'Get Promotions'.tr, value: 'GET_PROMOTIONS'),
    CallToAction(name: 'Get Updates'.tr, value: 'GET_UPDATES'),
    CallToAction(name: 'Listen Now'.tr, value: 'LISTEN_NOW'),
    CallToAction(name: 'Open Link'.tr, value: 'OPEN_LINK'),
  ];
  List<CallToAction> engagementCallToAction = [
    ///engagement
    CallToAction(name: 'Apply Now'.tr, value: 'APPLY_NOW'),
    CallToAction(name: 'Book Now'.tr, value: 'BOOK_NOW'),
    CallToAction(name: 'Contact Us'.tr, value: 'CONTACT_US'),
    CallToAction(name: 'Get Quota'.tr, value: 'GET_QUOTE'),
    CallToAction(name: 'Learn More'.tr, value: 'LEARN_MORE'),
    CallToAction(name: 'Order Now'.tr, value: 'ORDER_NOW'),
    CallToAction(name: 'Send Message'.tr, value: 'SEND_MESSAGE'),
    CallToAction(name: 'Whatsapp Message'.tr, value: 'WHATSAPP_MESSAGE'),
    CallToAction(name: 'Shop Now'.tr, value: 'SHOP_NOW'),
    CallToAction(name: 'Subscribe'.tr, value: 'SUBSCRIBE'),
    CallToAction(name: 'Sign up'.tr, value: 'SIGN_UP'),
    CallToAction(name: 'Donate Now'.tr, value: 'DONATE_NOW'),
    CallToAction(name: 'Get Promotions'.tr, value: 'GET_PROMOTIONS'),
    CallToAction(name: 'Get Updates'.tr, value: 'GET_UPDATES'),
  ];
  List<CallToAction> salesCallToAction = [
    //sales
    CallToAction(name: 'Book Now'.tr, value: 'BOOK_NOW'),
    CallToAction(name: 'Contact Us'.tr, value: 'CONTACT_US'),
    CallToAction(name: 'Download'.tr, value: 'DOWNLOAD'),
    CallToAction(name: 'Learn More'.tr, value: 'LEARN_MORE'),
    CallToAction(name: 'Order Now'.tr, value: 'ORDER_NOW'),
    CallToAction(name: 'See Menu'.tr, value: 'SEE_MENU'),
    CallToAction(name: 'Shop Now'.tr, value: 'SHOP_NOW'),
    CallToAction(name: 'Subscribe'.tr, value: 'SUBSCRIBE'),
    CallToAction(name: 'Sign up'.tr, value: 'SIGN_UP'),
    CallToAction(name: 'Send Message'.tr, value: 'SEND_MESSAGE'),
    CallToAction(name: 'Whatsapp Message'.tr, value: 'WHATSAPP_MESSAGE'),
    CallToAction(name: 'Watch More'.tr, value: 'WATCH_MORE'),
    CallToAction(name: 'Donate Now'.tr, value: 'DONATE_NOW'),
    CallToAction(name: 'Listen Now'.tr, value: 'LISTEN_NOW'),
    CallToAction(name: 'Open Link'.tr, value: 'OPEN_LINK'),
  ];

  List<Status> statuses = [
    Status(name: 'Active', value: 'ACTIVE'),
    Status(name: 'Paused', value: 'PAUSED'),
    Status(name: 'Deleted', value: 'DELETED'),
    Status(name: 'Archived', value: 'ARCHIVED'),
  ];
  Optimizations? optimization;

  Optimizations? billingEvent;

  List<int> genders = [];
  String statusType = "PAUSED";

  String destinationType = "";

  String adSetStatusType = "PAUSED";
  String adStatusType = "PAUSED";

  // String? pageAccessToken;
  // String? pageId;
  //
  // String? pageName;
  // String? accountName;
  // String? addAccountId;
  MetaPages? metaPages;
  Pages? accessedMetaPages;

  AdAccount? adAccount;
  bool isCampaignActive = false;
  bool isAdSetActive = false;
  bool isAdActive = false;

  List<String> publisherPlatforms = [];
  List<String> addedQuestions = [];

  List<String> fbPositions = [];
  List<String> igPositions = [];
  List<String> mPositions = [];

  bool fbPlatformActive = false;
  bool instagramPlatformActive = false;
  bool messengerPlatformActive = false;

  List<SearchResult> selectedInterests = [];
  List<SearchResult> selectedEducationalSchool = [];

  int? destinationIndex;
  bool? isSelectedDestination;

  var textController = TextEditingController();

  int? selectedMinAge;
  int? selectedMaxAge;

  int? selectedIndex;

  // List<AdSetGeoLocations> geoLocations = [
  //   AdSetGeoLocations(customLocations: [
  //     CustomLocations(
  //         latitude: 30.0444196,
  //         longitude: 31.2357116,
  //         radius: 49,
  //         addressString: "re",
  //         distanceUnit: DistanceUnit.kilometer)
  //   ])
  // ];
  List<SearchResult> defaultInterests = [
    SearchResult(id: "6002839660079", name: "Cosmetics"),
    SearchResult(id: "6002866718622", name: "Science"),
    SearchResult(id: "6002867432822", name: "Beauty"),
    SearchResult(id: "6002868021822", name: "Adventure travel"),
    SearchResult(id: "6002868910910", name: "Organic food"),
    SearchResult(id: "6002884511422", name: "Small business"),
    SearchResult(id: "6002920953955", name: "Interior design"),
    SearchResult(id: "6002925538921", name: "Acting"),
    SearchResult(id: "6002926108721", name: "Vacations"),
    SearchResult(id: "6002929380259", name: "Volleyball"),
    SearchResult(id: "6002936693259", name: "Soft drinks"),
    SearchResult(id: "6002951587955", name: "Classical music"),
    SearchResult(id: "6002957026250", name: "Theatre"),
    SearchResult(id: "6002960574320", name: "Tablet computers"),
    SearchResult(id: "6002963523717", name: "Aviation"),
    SearchResult(id: "6002964239317", name: "Mexican cuisine"),
    SearchResult(id: "6002964500317", name: "Word games"),
    SearchResult(id: "6002970406974", name: "Concerts"),
    SearchResult(id: "6002971085794", name: "Mobile phones"),
    SearchResult(id: "6002971095994", name: "Action games"),
  ];

  void setSelectedGoal(
    int index,
    String obj,
    /*List<Optimizations> optimization*/
  ) {
    selectedGoal = index;
    objective = obj;
    //selectOptimizationIndex = null;
    // selectedOptimization = optimization;
    emit(SetSelectedGoal());
  }

  void setSelectedOptimization(Optimizations obt) {
    // selectOptimizationIndex = index;
    optimization = obt;
    emit(SetSelectedOptimization());
  }

  void setSelectedBillingEvent(Optimizations bill) {
    billingEvent = bill;
    emit(SetSelectedOptimization());
  }

  void changeCampaignTabIndex(int index) {
    selectCampaignTab = index;
    emit(ChangeCampaignTab());
  }

  void changeSocialPostsTabIndex(int? index) {
    // print(
    //     'sdxzfvgcxbcvdsf ${instance<HiveHelper>().getUser()!.instUserId} ${index}');
    selectSocialPostsTab = index;
    emit(ChangeCampaignTab());
  }

  void changeFormTabIndex(int index) {
    selectFormTab = index;
    emit(ChangeCampaignTab());
  }

  void changeAdSetTabIndex(int index) {
    selectAdSetTab = index;
    emit(ChangeAdSetTab());
  }

  void changePostValue(int? index) {
    selectedPost = index;
    post = null;
    postIndex = null;
    postId = null;
    adCreativePercentage = 0.0;
    isAdCreativeProcess7Updated = false;
    adModel.objectStoryId = null;
    adImages = [];
    adVideo.clear();
    // videoImage.clear();
    headline.clear();
    // linkDesc.clear();
    emit(ChangeAdSetTab());
  }

  void setSelectedPost(String? selectedPostId, int index, Post exPost) {
    postId = selectedPostId;
    postIndex = index;
    post = exPost;
    emit(ChangeAdSetTab());
  }

  void changeFormValue(int? index) {
    selectedForm = index;
    emit(UpdateStates());
  }

  void setSelectedForm(String? selectedFormId, int index) {
    formId = selectedFormId;
    formIndex = index;
    emit(UpdateStates());
  }

  void changeLangValue(int? index) {
    selectedLang = index;
    emit(ChangeAdSetTab());
  }

  void setSelectedLang(String? selectedLangValue, int index) {
    langValue = selectedLangValue;
    langIndex = index;
    emit(ChangeAdSetTab());
  }

  void changeActionValue(int? index) {
    selectedAction = index;
    emit(ChangeAdSetTab());
  }

  void setSelectedAction(String? selectedActionValue, int index) {
    actionValue = selectedActionValue;
    actionIndex = index;
    emit(ChangeAdSetTab());
  }

  void selectExistingCampaign(int index, ExistingCampaign campaign) {
    selectedExistingCampaign = index;
    existingCampaign = campaign;
    emit(SelectExistingCampaign());
  }

  void selectExistingAdSet(int index, AdSet adset) {
    selectedExistingAdSet = index;
    existingAdSet = adset;
    emit(SelectExistingCampaign());
  }

  void changeCampaignStatus(bool status) {
    isCampaignActive = status;
    isCampaignActive ? statusType = "ACTIVE" : statusType = "PAUSED";
    emit(ChangeCampaignStatus());
  }

  void changeAdSetStatus(bool status) {
    isAdSetActive = status;
    isAdSetActive ? adSetStatusType = "ACTIVE" : adSetStatusType = "PAUSED";

    emit(ChangeAdSetStatus());
  }

  void changeAdStatus(bool status) {
    isAdActive = status;
    isAdActive ? adStatusType = "ACTIVE" : adStatusType = "PAUSED";

    emit(ChangeAdSetStatus());
  }

  void fbPlatformStatusStatus(bool status) {
    fbPlatformActive = status;
    if (fbPlatformActive) {
      publisherPlatforms.add('facebook');
    } else {
      publisherPlatforms.remove('facebook');
    }

    emit(ChangeAdSetStatus());
  }

  void instagramPlatformStatusStatus(bool status) {
    instagramPlatformActive = status;
    if (instagramPlatformActive) {
      publisherPlatforms.add('instagram');
    } else {
      publisherPlatforms.remove('instagram');
    }

    emit(ChangeAdSetStatus());
  }

  void messengerPlatformStatusStatus(bool status) {
    messengerPlatformActive = status;
    if (messengerPlatformActive) {
      publisherPlatforms.add('messenger');
    } else {
      publisherPlatforms.remove('messenger');
    }

    emit(ChangeAdSetStatus());
  }

  void setSelectedFbPositions() {
    for (int i = 0; i < facebookPositions.length; i++) {
      if (!fbPositions.contains(facebookPositions[i].value)) {
        if (facebookPositions[i].isChecked) {
          fbPositions.add(facebookPositions[i].value ?? "");
        }
      } else {
        if (!facebookPositions[i].isChecked) {
          fbPositions.remove(facebookPositions[i].value ?? "");
        }
      }
    }

    emit(ChangeAdSetStatus());
  }

  void setSelectedQuestion() {
    // Use a Set to prevent duplicates
    Set<String> addedQuestionsSet = Set<String>.from(addedQuestions);

    for (int i = 0; i < questions.length; i++) {
      String? value = questions[i].value;

      if (questions[i].isChecked) {
        // Add to set if it's checked
        addedQuestionsSet.add(value ?? "");
      } else {
        // Remove from set if it's unchecked
        addedQuestionsSet.remove(value ?? "");
      }
    }

    // Convert back to List if needed
    addedQuestions = addedQuestionsSet.toList();

    emit(ChangeAdSetStatus());

    // Debugging output
    print("Added Questions: $addedQuestions");
  }

  void setSelectedInstagramPositions() {
    for (int i = 0; i < instagramPositions.length; i++) {
      if (!igPositions.contains(instagramPositions[i].value)) {
        if (instagramPositions[i].isChecked) {
          igPositions.add(instagramPositions[i].value ?? "");
        }
      } else {
        if (!instagramPositions[i].isChecked) {
          igPositions.remove(instagramPositions[i].value ?? "");
        }
      }
    }

    emit(ChangeAdSetStatus());
  }

  void setSelectedMessengerPositions() {
    for (int i = 0; i < messengerPositions.length; i++) {
      if (!mPositions.contains(messengerPositions[i].value)) {
        if (messengerPositions[i].isChecked) {
          mPositions.add(messengerPositions[i].value ?? "");
        }
      } else {
        if (!messengerPositions[i].isChecked) {
          mPositions.remove(messengerPositions[i].value ?? "");
        }
      }
    }

    emit(ChangeAdSetStatus());
  }

  // void addToInterests(SearchResult e) {
  //   if (selectedInterests.where((element) => element.name == e.name).isNotEmpty) {
  //     selectedInterests.removeWhere((element) => e.name == element.name);
  //     emit(RemoveFromInterestsState());
  //
  //   } else {
  //     selectedInterests.add(e);
  //     emit(AdToInterestsState());
  //
  //   }
  // }

  addToInterests(SearchResult e) {
    if (selectedInterests
        .where((element) => element.name == e.name)
        .isNotEmpty) {
      showErrorToast("You already selected this interest");
    } else {
      selectedInterests.add(e);
      if (interestsPercentage < 1.0) {
        interestsPercentage += 1.0;
      }
      emit(AdToInterestsState());
    }
  }

  void removeFromInterests(SearchResult e) {
    if (selectedInterests
        .where((element) => element.name == e.name)
        .isNotEmpty) {
      selectedInterests.removeWhere((element) => e.name == element.name);
      // interestsPercentage -= 1.0;
      emit(RemoveFromInterestsState());
    }
  }

  void setSelectedLanguages(SearchResult language) {
    if (languages.where((element) => element.key == language.key).isNotEmpty) {
      languages.removeWhere((element) => language.key == element.key);
      emit(RemoveFromLanguageState());
    } else {
      languages.add(language);
      emit(AdToLanguageState());
    }
  }

  void setSelectedMajors(SearchResult major) {
    if (majors.where((element) => element.id == major.id).isNotEmpty) {
      majors.removeWhere((element) => major.id == element.id);
      emit(RemoveFromMajorsState());
    } else {
      majors.add(major);
      emit(AdToMajorsState());
    }
  }

  void setSelectedLifeEvents(SearchClass event) {
    if (lifeEvents.where((element) => element.id == event.id).isNotEmpty) {
      lifeEvents.removeWhere((element) => event.id == element.id);
      emit(RemoveFromEventsState());
    } else {
      lifeEvents.add(event);
      emit(AdToEventsState());
    }
  }

  void setSelectedUserOs(SearchClass os) {
    if (userOs.where((element) => element.platform == os.platform).isNotEmpty) {
      userOs.removeWhere((element) => os.platform == element.platform);
      emit(RemoveFromUserOsState());
    } else {
      userOs.add(os);
      emit(AdToUserOsState());
    }
  }

  void setSelectedUserDevices(SearchClass userDevice) {
    if (userDevices
        .where((element) => element.name == userDevice.name)
        .isNotEmpty) {
      userDevices.removeWhere((element) => userDevice.name == element.name);
      emit(RemoveFromDevicesState());
    } else {
      userDevices.add(userDevice);
      emit(AdToDevicesState());
    }
  }

  void setSelectedBehaviours(SearchClass behaviour) {
    if (behaviours.where((element) => element.id == behaviour.id).isNotEmpty) {
      behaviours.removeWhere((element) => behaviour.id == element.id);
      emit(RemoveFromBehavioursState());
    } else {
      behaviours.add(behaviour);
      emit(AdToBehavioursState());
    }
  }

  void setSelectedStatuses(EducationStatuses status) {
    if (educationStatuses
        .where((element) => element.id == status.id)
        .isNotEmpty) {
      educationStatuses.removeWhere((element) => status.id == element.id);
      emit(RemoveFromStatusesState());
    } else {
      educationStatuses.add(status);
      emit(AdToStatusesState());
    }
  }

  void setSelectedEducationalSchools(SearchResult school) {
    if (selectedEducationalSchool
        .where((element) => element.id == school.id)
        .isNotEmpty) {
      selectedEducationalSchool
          .removeWhere((element) => school.id == element.id);
      emit(RemoveFromSchoolsState());
    } else {
      selectedEducationalSchool.add(school);
      emit(AdToSchoolState());
    }
  }

  void removeImage(int index, bool isImage) {
    adImages.removeAt(index);
    if (adImages.isEmpty) {
      isImage ? adCreativePercentage -= 0.2 : adCreativePercentage -= 0.1;
      isAdCreativeProcess5Updated = false;
    }
    emit(RemoveImageState());
  }

  void addImage(File file) {
    adImages.add(file);
    emit(AddImageState());
  }

  void addImages(List<File> files) {
    adImages = files;
    emit(AddImageState());
  }

  Future<void> setSelectedPage(MetaPages selectedPage) async {
    metaPages = selectedPage;
    await instance.get<HiveHelper>().setMetaPages(metaPages!);
    emit(UpdateStates());
  }

  Future<void> setSelectedAccessedPage(Pages selectedPage) async {
    accessedMetaPages = selectedPage;
    emit(UpdateStates());
  }

  Future<void> setSelectedAccount(AdAccount selectedAccount) async {
    adAccount = selectedAccount;
    await instance.get<HiveHelper>().setAdAccount(adAccount!);
    emit(UpdateStates());
  }

  Future<void> removeAccount() async {
    adAccount = null;
    accessedMetaPages = null;
    await instance.get<HiveHelper>().setAdAccount(adAccount!);
    emit(UpdateStates());
  }

  void setCallToAction(CallToAction callToAction) {
    if (destinationType == "WHATSAPP" && callToAction.name == "Send Message") {
      callToAction.value = "WHATSAPP_MESSAGE";
    } else if (destinationType == "INSTAGRAM_DIRECT" &&
        callToAction.name == "Send Message") {
      callToAction.value = "INSTAGRAM_MESSAGE";
    } else if (destinationType == "MESSENGER" &&
        callToAction.name == "Send Message") {
      callToAction.value = "MESSAGE_PAGE";
    }
    type = callToAction.value;
    typeName = callToAction.name;
    emit(UpdateStates());
  }

  // void setAccountId(String id,String adAccount) {
  //   addAccountId = id;
  //   accountName = adAccount;
  //   emit(UpdateStates());
  // }

  createAD({
    required BuildContext context,
    required List<File> imagesFiles,
    required List<File> videosFiles,
    // required List<File> thumbFiles
  }) async {
    emit(CreateADStateLoading());
    // adModel.adCreativeName = adCreativeName.text;
    // adModel.type = type;
    // adModel.copyWith(link: CreateAdCubit.get(context).adModel.link ?? linkTextController.text);
    // = CreateAdCubit.get(context).adModel.link ?? linkTextController.text;
    // adModel.description = linkDesc.text;
    // adModel.message = message.text;
    // adModel.destinationType = destinationType;
    // adModel.adName = adName.text;
    // adModel.adStatus = adStatusType;
    adModel.title = headline.text;
    CreateAdCubit.get(context)
        .adModel
        .toJson()
        .then((value) => print('creationAdxx $value'));
    print('creationAdxx ${adModel.toJson()}');
    // adModel.toJson().then((value) =>
    //     print('adModel ${value['destination_type']} ${value['link']}'));
    // print('adModel ${adModel.destinationType} ${adModel.link}');
    instance<CreateCampaignRepo>()
        .createAD(
      adModel: adModel,
      imagesFiles: imagesFiles,
      videosFiles: videosFiles,
      // thumbFiles: thumbFiles
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(CreateADStateError(l));
      }, (r) {
        isAddCreated = true;
        CommonUtils.showBottomDialog(
            navigatorKey.currentState?.context ?? context,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 34.sp),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomText(
                    text: "Congratulations",
                    fontSize: 26.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  CachedImageWidget(
                    assetsImage: AppAssets.adSuccess,
                    height: 120.h,
                  ),
                  20.verticalSpace,
                  CustomText(
                    text:
                        "Congratulations! Your ad has been successfully published",
                    fontSize: 16.sp,
                    color: const Color(0xFF808080),
                    maxLines: 2,
                    alignment: AlignmentDirectional.center,
                    textAlign: TextAlign.center,
                  ),
                  20.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 50.sp),
                    child: ButtonWidget(
                      text: "Return To Home",
                      fontSize: 16.sp,
                      padding: 16.sp,
                      onTap: () async {
                        await Navigator.pushNamedAndRemoveUntil(
                            context, Routes.splash, (route) => false);
                      },
                    ),
                  )
                ],
              ),
            ));

        emit(CreateADStateLoaded(data: r));
        clearCampaignData(context);
      });
    });
  }

  void clearCampaignData(BuildContext context) {
    campaignNameController.text = "";
    CreateAdCubit.get(context).adModel = AdModel();
    CreateAdCubit.get(context).geoLocations = [];
    isAddNewLocation = false;
    CreateAdCubit.get(context).selectCampaignTab = 0;
    CreateAdCubit.get(context).selectSocialPostsTab = 0;
    GetObjectivesCubit.get(context).clearObjectives();
    CreateAdCubit.get(context).selectAdSetTab = 0;
    //CreateAdCubit.get(context).selectOptimizationIndex = null;
    CreateAdCubit.get(context).objective = null;
    CreateAdCubit.get(context).optimization = null;
    CreateAdCubit.get(context).billingEvent = null;

    CreateAdCubit.get(context).selectedGoal = null;
    CreateAdCubit.get(context).statusType = "PAUSED";

    CreateAdCubit.get(context).selectedOptimization = [];
    adImages = [];
    adSetNameController.text = "";
    dailyBudget.text = "";
    existingAdSet = null;
    existingCampaign = null;
    type = null;
    typeName = null;
    minAge.text = "";
    maxAge.text = "";
    startDate.text = "";
    endDate.text = "";
    adName.text = "";
    adCreativeName.text = "";
    webSiteLink.text = "";
    headline.text = "";
    message.text = "";
    linkDesc.text = "";
    // CreateAdCubit.get(context).videoImage = [];

    CreateAdCubit.get(context).adVideo = [];

    CreateAdCubit.get(context).languages = [];
    CreateAdCubit.get(context).majors = [];
    CreateAdCubit.get(context).lifeEvents = [];
    CreateAdCubit.get(context).userOs = [];

    CreateAdCubit.get(context).userDevices = [];
    CreateAdCubit.get(context).behaviours = [];
    CreateAdCubit.get(context).educationStatuses = [];

    CreateAdCubit.get(context).genders = [];

    CreateAdCubit.get(context).destinationType = "";

    CreateAdCubit.get(context).adSetStatusType = "PAUSED";

    CreateAdCubit.get(context).isCampaignActive = false;
    CreateAdCubit.get(context).isAdSetActive = false;
    CreateAdCubit.get(context).selectedInterests = [];
    CreateAdCubit.get(context).selectedEducationalSchool = [];
    CreateAdCubit.get(context).campaignProcessPercentage = 0.0;
    CreateAdCubit.get(context).isCampaignProcess1Updated = false;
    CreateAdCubit.get(context).isCampaignProcess2Updated = false;
    CreateAdCubit.get(context).isCampaignProcess3Updated = false;
    CreateAdCubit.get(context).isCampaignProcess4Updated = false;

    CreateAdCubit.get(context).isAdSetTileExpanded = false;
    CreateAdCubit.get(context).isAdCreativeTileExpanded = false;
    CreateAdCubit.get(context).isReviewTileExpanded = false;

    CreateAdCubit.get(context).isAddNewLocation = false;
    CreateAdCubit.get(context).adSetPercentage = 0.0;
    CreateAdCubit.get(context).isAdSetProcess1Updated = false;
    CreateAdCubit.get(context).isAdSetProcess2Updated = false;
    CreateAdCubit.get(context).isAdSetProcess3Updated = false;
    CreateAdCubit.get(context).isAdSetProcess4Updated = false;
    CreateAdCubit.get(context).isAdSetProcess5Updated = false;
    CreateAdCubit.get(context).isAdSetProcess6Updated = false;
    CreateAdCubit.get(context).isAdSetProcess7Updated = false;
    CreateAdCubit.get(context).isAdSetProcess8Updated = false;
    CreateAdCubit.get(context).isAdSetProcess9Updated = false;

    CreateAdCubit.get(context).demoPercentage = 0.0;
    CreateAdCubit.get(context).isDemoProcess1Updated = false;
    CreateAdCubit.get(context).isDemoProcess2Updated = false;
    CreateAdCubit.get(context).isDemoProcess3Updated = false;
    CreateAdCubit.get(context).isDemoProcess4Updated = false;

    CreateAdCubit.get(context).targetingPercentage = 0.0;
    CreateAdCubit.get(context).isTargeting1Updated = false;
    CreateAdCubit.get(context).isTargeting2Updated = false;
    CreateAdCubit.get(context).isTargeting3Updated = false;
    CreateAdCubit.get(context).isTargeting4Updated = false;
    CreateAdCubit.get(context).isTargeting5Updated = false;
    CreateAdCubit.get(context).isTargeting6Updated = false;
    CreateAdCubit.get(context).isTargeting7Updated = false;
    CreateAdCubit.get(context).isTargeting8Updated = false;

    CreateAdCubit.get(context).locationPercentage = 0.0;

    CreateAdCubit.get(context).isLocationProcess1Updated = false;
    CreateAdCubit.get(context).isLocationProcess2Updated = false;
    CreateAdCubit.get(context).isLocationProcess3Updated = false;

    CreateAdCubit.get(context).adCreativePercentage = 0.0;
    CreateAdCubit.get(context).isAdCreativeProcess1Updated = false;
    CreateAdCubit.get(context).isAdCreativeProcess2Updated = false;
    CreateAdCubit.get(context).isAdCreativeProcess3Updated = false;
    CreateAdCubit.get(context).isAdCreativeProcess4Updated = false;
    CreateAdCubit.get(context).isAdCreativeProcess5Updated = false;
    CreateAdCubit.get(context).isAdCreativeProcess6Updated = false;

    CreateAdCubit.get(context).adPercentage = 0.0;
    CreateAdCubit.get(context).interestsPercentage = 0.0;
    CreateAdCubit.get(context).isProcess1Updated = false;

    CreateAdCubit.get(context).publisherPlatforms = [];
    CreateAdCubit.get(context).fbPositions = [];
    CreateAdCubit.get(context).igPositions = [];
    CreateAdCubit.get(context).mPositions = [];
    CreateAdCubit.get(context).fbPlatformActive = false;
    CreateAdCubit.get(context).instagramPlatformActive = false;
    CreateAdCubit.get(context).messengerPlatformActive = false;
    CreateAdCubit.get(context).facebookPositions = [
      Positions(name: 'Feed', value: 'feed', isChecked: false),
      Positions(name: 'Marketplace', value: 'marketplace', isChecked: false),
      Positions(name: 'Video Feeds', value: 'video_feeds', isChecked: false),
      Positions(name: 'Story', value: 'story', isChecked: false),
      Positions(name: 'Search', value: 'search', isChecked: false),
      Positions(
          name: 'Facebook Reels', value: 'facebook_reels', isChecked: false),
      Positions(name: 'Profile Feed', value: 'profile_feed', isChecked: false),
    ];

    CreateAdCubit.get(context).instagramPositions = [
      Positions(name: 'Stream', value: 'stream', isChecked: false),
      Positions(name: 'Story', value: 'story', isChecked: false),
      Positions(name: 'Explore', value: 'explore', isChecked: false),
      Positions(name: 'Explore Home', value: 'explore_home', isChecked: false),
      Positions(name: 'Reels', value: 'reels', isChecked: false),
      Positions(
          name: 'Profile Reels', value: 'profile_reels', isChecked: false),
      Positions(name: 'Profile Feed', value: 'profile_feed', isChecked: false),
    ];
    CreateAdCubit.get(context).textController.text = "";

    CreateAdCubit.get(context).destinationIndex = null;
    CreateAdCubit.get(context).isSelectedDestination = null;
    CreateAdCubit.get(context).selectedMinAge = null;
    CreateAdCubit.get(context).selectedMaxAge = null;
    CreateAdCubit.get(context).selectedDestinationIndex = null;

    CreateAdCubit.get(context).selectedIndex = null;
    CreateAdCubit.get(context).messengerPositions = [
      Positions(
          name: 'Messenger Home', value: 'messenger_home', isChecked: false),
      Positions(
          name: 'Sponsored Message',
          value: 'sponsored_messages',
          isChecked: false),
      Positions(name: 'Story', value: 'story', isChecked: false),
    ];
    CreateAdCubit.get(context).leadDesc.text = "";
    CreateAdCubit.get(context).leadMessage.text = "";
    CreateAdCubit.get(context).ctaController.text = "";
    CreateAdCubit.get(context).websiteLinkController.text = "";
    CreateAdCubit.get(context).linkTextController.text = "";
    CreateAdCubit.get(context).linkController.text = "";
    CreateAdCubit.get(context).leadHeadlineController.text = "";
    CreateAdCubit.get(context).questions = [
      Question(name: 'What is your city?', value: 'CITY', isChecked: false),
      Question(
          name: 'What is your company name?',
          value: 'COMPANY_NAME',
          isChecked: false),
      Question(
          name: 'What is your country name?',
          value: 'COUNTRY',
          isChecked: false),
      Question(name: 'What is your gender?', value: 'GENDER', isChecked: false),
      Question(
          name: 'What is your first name?',
          value: 'FIRST_NAME',
          isChecked: false),
      Question(
          name: 'What is your full name?',
          value: 'FULL_NAME',
          isChecked: false),
      Question(
          name: 'What is your job title?',
          value: 'JOB_TITLE',
          isChecked: false),
    ];

    CreateAdCubit.get(context).lang = [
      Language(name: 'Arabic', value: 'AR_AR'),
      Language(name: 'English', value: 'EN_US'),
    ];

    CreateAdCubit.get(context).addedQuestions = [];
    CreateAdCubit.get(context).langValue = null;
    CreateAdCubit.get(context).langIndex = null;
    CreateAdCubit.get(context).postId = null;
    CreateAdCubit.get(context).postIndex = null;
    CreateAdCubit.get(context).formId = null;
    CreateAdCubit.get(context).formIndex = null;
    emit(UpdateStates());
  }

  void updateStatus() {
    emit(UpdateStates());
  }
}

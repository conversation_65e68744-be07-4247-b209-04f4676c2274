part of 'reach_estimate_cubit.dart';

@immutable
sealed class ReachEstimateState {}

final class ReachEstimateInitial extends ReachEstimateState {}

class ReachStateLoaded extends ReachEstimateState {
  final AdReachEstimatedModel? reachEstimatedModel;

  ReachStateLoaded({this.reachEstimatedModel});

  @override
  List<Object?> get props => [reachEstimatedModel];

  ReachStateLoaded copyWith({
    AdReachEstimatedModel? reachEstimatedModel,
  }) {
    return ReachStateLoaded(
      reachEstimatedModel: reachEstimatedModel ?? this.reachEstimatedModel,
    );
  }
}

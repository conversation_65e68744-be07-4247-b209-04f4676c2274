import 'dart:io';

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/models/ad_model.dart';
import '../../../data/models/ad_reach_estimated_model.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'reach_estimate_state.dart';

class ReachEstimateCubit extends Cubit<ReachEstimateState> {
  ReachEstimateCubit() : super(ReachEstimateInitial());

  static ReachEstimateCubit get(context) => BlocProvider.of(context);

  AdReachEstimatedModel? reachEstimatedModel;

  getReachEstimate(
      {required BuildContext context,
      required List<File> imagesFiles,
      required List<File> videosFiles}) async {
    // emit(CreateADStateLoading());
    AdModel model = CreateAdCubit.get(context).adModel;
    model
        .toJson()
        .then((value) => print('reachEstimatedErroradModel $value'));
    // print(
    //     'reachEstimatedErroradModel ${model.toJson().then((value) => value)}');
    await instance<CreateCampaignRepo>()
        .getReachEstimate(
            adModel: CreateAdCubit.get(context).adModel,
            imagesFiles: imagesFiles,
            videosFiles: videosFiles)
        .then((value) {
      value.fold((l) {
        print('reachEstimatedError ${l.message}');
        FailureHelper.instance.handleFailures(l, context);
        // emit(CreateADStateError(l));
      }, (r) {
        // isAddCreated = true;
        reachEstimatedModel = r;
        emit(ReachStateLoaded(reachEstimatedModel: r));
        print('adModelasdcxzcsd ${r.toJson()}');
      });
    });
  }

  clearReachEstimate() {
    reachEstimatedModel = AdReachEstimatedModel();
    emit(ReachStateLoaded(reachEstimatedModel: reachEstimatedModel));
  }
}

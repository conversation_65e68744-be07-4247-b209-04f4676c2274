
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'create_campaign_state.dart';

class CreateCampaignCubit extends Cubit<CreateCampaignState> {
  CreateCampaignCubit() : super(CreateCampaignInitial());

  static CreateCampaignCubit get(context) => BlocProvider.of(context);
  // bool isExpanded = false;
  // void closeExpansionState() {
  //   isExpanded = false;
  //   emit(SetExpansionState());
  //   print("heee");
  // }
  // void openExpansionState() {
  //   isExpanded = true;
  //   emit(SetExpansionState());
  //   print("asdadasd");
  // }

  bool isGoalTileExpanded = false;



  bool isTextEmpty = false;

  void closeKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  void setGoalExpansionState(bool isClosed) {
    isGoalTileExpanded = isClosed;
    emit(SetExpansionState());
  }


}

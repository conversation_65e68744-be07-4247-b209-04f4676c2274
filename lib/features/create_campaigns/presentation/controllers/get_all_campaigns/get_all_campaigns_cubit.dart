import 'package:ads_dv/features/create_campaigns/data/models/existing_campagin.dart';
import 'package:ads_dv/features/create_campaigns/data/repos/create_campaign_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';

part 'get_all_campaigns_state.dart';

class GetCampaignsCubit extends Cubit<GetCampaignsState> {
  GetCampaignsCubit() : super(GetCampaignsInitial());

  static GetCampaignsCubit get(context) => BlocProvider.of(context);

  List<ExistingCampaign>? campaigns = [];

  getCampaigns(
      {required BuildContext context, required String accountId}) async {
    campaigns?.clear();
    emit(GetCampaignsStateLoading());
    instance<CreateCampaignRepo>()
        .getCampaigns(accountId: accountId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetCampaignsStateError(l));
      }, (r) {
        campaigns = r.result?.data;
        emit(GetCampaignsStateLoaded(r));
      });
    });
  }

  loadMoreCampaigns(
      {required BuildContext context, required String url}) async {
    emit(GetCampaignsStateLoading());
    instance<CreateCampaignRepo>().loadMoreCampaigns(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetCampaignsStateError(l));
      }, (r) {
        campaigns?.addAll(r.result?.data?.map((e) => e).toList() ?? []);
        // campaigns?.addAll(r.result?.data ?? []);
        emit(GetCampaignsStateLoaded(r));
      });
    });
  }
}

part of 'get_all_campaigns_cubit.dart';

@immutable
abstract class GetCampaignsState {
  const GetCampaignsState();

  List<Object?> get props => [];
}

class GetCampaignsInitial extends GetCampaignsState {}

class GetCampaignsStateLoading extends GetCampaignsState {}

class GetCampaignsStateLoaded extends GetCampaignsState {
  final GetAllCampaignesResponse data;

  const GetCampaignsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetCampaignsStateLoaded copyWith({
    GetAllCampaignesResponse? data,
  }) {
    return GetCampaignsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetCampaignsStateError extends GetCampaignsState {
  final Failure message;

  const GetCampaignsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

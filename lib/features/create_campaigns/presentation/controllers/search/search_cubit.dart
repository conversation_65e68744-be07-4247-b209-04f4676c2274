import 'package:ads_dv/features/create_campaigns/data/models/search_class.dart';
import 'package:ads_dv/features/create_campaigns/data/models/search_result.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'search_state.dart';

class SearchCubit extends Cubit<SearchState> {
  SearchCubit() : super(SearchInitial());

  static SearchCubit get(context) => BlocProvider.of(context);

  List<SearchClass> behaviors = [];
  List<SearchClass> originalList = [];

  List<SearchClass> filteredList = [];


  search(
      {required BuildContext context,
      required String type,
      required String keyword}) async {
    emit(SearchStateLoading());
    instance<CreateCampaignRepo>()
        .search(type: type, keyword: keyword)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(SearchStateError(l));
      }, (r) {
        print("adasd1");
        emit(SearchStateLoaded(r));
        print("adasd2");
      });
    });
  }

  searchBehaviors(String searchQuery) {
    final currentState = state;
    if (currentState is SearchClassStateLoaded) {
      final filteredData = currentState.filterByName(searchQuery);
      if (searchQuery.isEmpty) {
        emit(currentState.copyWith(data: behaviors));
      } else {
        emit(currentState.copyWith(data: filteredData));
      }
    }
  }

  void localSearch(String searchText) {

    if (searchText.isEmpty) {
      behaviors = originalList;
    }
    filteredList = behaviors
        .where((behavior) =>
            behavior.name!.toLowerCase().contains(searchText.toLowerCase()))
        .toList();

    behaviors = filteredList;
    emit(SearchClassStateLoaded(filteredList));
  }

  searchForClass(
      {required BuildContext context,
      required String type,
      required String searchClass}) async {
    emit(SearchClassStateLoading());
    instance<CreateCampaignRepo>()
        .searchForClass(type: type, searchClass: searchClass)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(SearchClassStateError(l));
      }, (r) {
        behaviors = r;
        originalList = r;
        emit(SearchClassStateLoaded(behaviors));
      });
    });
  }

  // void filterStatuses(String query) {
  //   query = query.toLowerCase();
  //   if (query.isEmpty) {
  //     emit(List.of(educationStatuses));
  //   } else {
  //     final filteredStatuses = educationStatuses!
  //         .where((status) => status.displayName.contains(query))
  //         .toList();
  //     emit(filteredStatuses);
  //   }
  // }
}

part of 'search_cubit.dart';

@immutable
abstract class SearchState {
  const SearchState();
  List<Object?> get props => [];
}

class SearchInitial extends SearchState {}

class SearchStateLoading extends SearchState {}

class SearchStateLoaded extends SearchState {
  final List<SearchResult> data;

  const SearchStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  SearchStateLoaded copyWith({
    List<SearchResult>? data,
  }) {
    return SearchStateLoaded(
      data ?? this.data,
    );
  }
}

class SearchStateError extends SearchState {
  final Failure message;

  const SearchStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class SearchClassStateLoading extends SearchState {}


class SearchClassStateLoaded extends SearchState {
  final List<SearchClass> data;

  const SearchClassStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  List<SearchClass> filterByName(String searchQuery) {
    if (searchQuery.isEmpty) {
      return data;
    } else {
      List<SearchClass> temp = data;
      return temp
          .where((searchClass) =>
      (searchClass.name
          ?.toLowerCase()
          .contains(searchQuery.toLowerCase()) ??
          false) ||
          (searchClass.platform
              ?.toLowerCase()
              .contains(searchQuery.toLowerCase()) ??
              false))
          .toList();
    }
  }

  SearchClassStateLoaded copyWith({
    List<SearchClass>? data,
  }) {
    return SearchClassStateLoaded(
      data ?? this.data,
    );
  }
}

class SearchClassStateError extends SearchState {
  final Failure message;

  const SearchClassStateError(this.message);

  @override
  List<Object?> get props => [message];
}
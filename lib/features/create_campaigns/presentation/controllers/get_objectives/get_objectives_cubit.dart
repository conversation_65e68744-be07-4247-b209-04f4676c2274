import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/colors.dart';
import '../../../data/models/objectives.dart';
import '../../../data/models/question.dart';
import '../../../data/repos/create_campaign_repo.dart';
import '../../views/widgets/create_campaign/new_campaign/new_campaign_widget.dart';
import '../create_ad/create_ad_cubit.dart';
import '../get_optimizations/get_optimizations_cubit.dart';

part 'get_objectives_state.dart';

class GetObjectivesCubit extends Cubit<GetObjectivesState> {
  GetObjectivesCubit() : super(GetObjectivesInitial());

  static GetObjectivesCubit get(context) => BlocProvider.of(context);

  getObjectives({
    required BuildContext context,
  }) async {
    emit(GetObjectivesStateLoading());
    instance<CreateCampaignRepo>().getObjectives().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetObjectivesStateError(l));
      }, (r) {
        print("objectiveszxcxc $r");
        emit(GetObjectivesStateLoaded(r));
      });
    });
  }

  getCurrentCampaignObjective({
    required BuildContext context,
    required String campaignId,
  }) async {
    emit(GetObjectivesStateLoading());
    instance<CreateCampaignRepo>()
        .getCurrentObjective(campaignId: campaignId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetObjectivesStateError(l));
      }, (r) {
        print("getCurrent campaign $r");
        emit(GetCurrentObjectiveStateLoaded(r));
        GetOptimizationsCubit.get(context)
            .getOptimizations(context: context, objectiveId: r.id!);
        r.actualName == "OUTCOME_LEADS"
            ? _createLeadFormDialog(context, CreateAdCubit.get(context))
            : null;
        if (r.actualName != "OUTCOME_LEADS") {
          CreateAdCubit.get(context).leadDesc.text = "";
          CreateAdCubit.get(context).leadMessage.text = "";
          CreateAdCubit.get(context).ctaController.text = "";
          CreateAdCubit.get(context).websiteLinkController.text = "";
          CreateAdCubit.get(context).linkTextController.text = "";
          CreateAdCubit.get(context).linkController.text = "";
          CreateAdCubit.get(context).leadHeadlineController.text = "";
          // CreateAdCubit.get(context).addedQuestions=[];
          CreateAdCubit.get(context).langValue = null;
          CreateAdCubit.get(context).langIndex = null;

          CreateAdCubit.get(context).formId = null;
          CreateAdCubit.get(context).formIndex = null;
          CreateAdCubit.get(context).addedQuestions.clear();
          CreateAdCubit.get(context).questions.clear();
          CreateAdCubit.get(context).questions = [
            Question(
                name: 'What is your city?', value: 'CITY', isChecked: false),
            Question(
                name: 'What is your company name?',
                value: 'COMPANY_NAME',
                isChecked: false),
            Question(
                name: 'What is your country name?',
                value: 'COUNTRY',
                isChecked: false),
            Question(
                name: 'What is your gender?',
                value: 'GENDER',
                isChecked: false),
            Question(
                name: 'What is your first name?',
                value: 'FIRST_NAME',
                isChecked: false),
            Question(
                name: 'What is your full name?',
                value: 'FULL_NAME',
                isChecked: false),
            Question(
                name: 'What is your job title?',
                value: 'JOB_TITLE',
                isChecked: false),
          ];
        }
      });
    });
  }

  clearObjectives() {
    emit(const GetObjectivesStateLoaded([]));
  }
}

void _createLeadFormDialog(BuildContext context, CreateAdCubit createAdCubit) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return Dialog(
        backgroundColor: AppColors.whiteColor,
        child: PageViewDialog(
          createAdCubit: createAdCubit,
        ),
      );
    },
  );
}

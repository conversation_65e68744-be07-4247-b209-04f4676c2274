part of 'get_objectives_cubit.dart';

@immutable
abstract class GetObjectivesState {
  const GetObjectivesState();

  List<Object?> get props => [];
}

class GetObjectivesInitial extends GetObjectivesState {}

class GetObjectivesStateLoading extends GetObjectivesState {}

class GetObjectivesStateLoaded extends GetObjectivesState {
  final List<Objective> data;

  const GetObjectivesStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetObjectivesStateLoaded copyWith({
    List<Objective>? data,
  }) {
    return GetObjectivesStateLoaded(
      data ?? this.data,
    );
  }
}

class GetCurrentObjectiveStateLoaded extends GetObjectivesState {
  final Objective? objective;

  const GetCurrentObjectiveStateLoaded(this.objective);

  @override
  List<Object?> get props => [objective];

  GetCurrentObjectiveStateLoaded copyWith({
    Objective? objective,
  }) {
    return GetCurrentObjectiveStateLoaded(
      objective ?? this.objective,
    );
  }
}

class GetObjectivesStateError extends GetObjectivesState {
  final Failure message;

  const GetObjectivesStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class SetOptimizationList extends GetObjectivesState {}

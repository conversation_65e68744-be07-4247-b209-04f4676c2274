import 'package:ads_dv/features/create_campaigns/data/models/post_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/models/instagram_post_response.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'get_posts_state.dart';

class GetPostsCubit extends Cubit<GetFbPostsState> {
  GetPostsCubit() : super(GetFbPostsInitial());

  static GetPostsCubit get(context) => BlocProvider.of(context);

  List<Post> posts = [];
  List<InstaPosts> instaPosts = [];
  String? url;

  setSelectedUrl(String? selectedUrl) {
    url = selectedUrl;
    emit(UpdateStatus());
  }

  getFbPosts(
      {required BuildContext context,
      required String pageId,
      required String pageAccessToken}) async {
    emit(GetFbPostsStateLoading());
    instance<CreateCampaignRepo>()
        .getFbPosts(pageAccessToken: pageAccessToken, pageId: pageId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        print('fbpostsError ${l.message}');
        emit(GetFbPostsStateError(l));
      }, (r) {
        posts = r.result?.data ?? [];
        print('fbPostsnmbcx ${posts.length} ${r.result?.data}');
        setSelectedUrl(r.result?.next);

        emit(GetFbPostsStateLoaded(r));
      });
    });
  }

  getInstaPosts(
      {required BuildContext context,
      required String pageId,
      required String pageAccessToken,
      required String instaUserId}) async {
    emit(GetFbPostsStateLoading());
    instance<CreateCampaignRepo>()
        .getInstaPosts(
            pageAccessToken: pageAccessToken,
            pageId: pageId,
            instaUserId: instaUserId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetFbPostsStateError(l));
      }, (r) {
        instaPosts = r.result?.data ?? [];
        print('instapostsdafd $instaPosts');
        // setSelectedUrl(r.next);

        emit(GetInstaPostsStateLoaded(r));
      });
    });
  }

  loadMoreInstaPosts({
    required BuildContext context,
    required String url,
    required String pageAccessToken,
  }) async {
    emit(GetFbPostsStateLoading());
    instance<CreateCampaignRepo>()
        .loadMoreInstaPosts(url: url, pageAccessToken: pageAccessToken)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetFbPostsStateError(l));
      }, (r) {
        instaPosts.addAll(r.result?.data?.map((e) => e).toList() ?? []);
        // instaPosts = r.result?.data ?? [];
        // setSelectedUrl(r.next);

        emit(GetInstaPostsStateLoaded(r));
      });
    });
  }

  loadMoreFbPosts({required BuildContext context, String? url}) async {
    emit(GetMorePostsStateLoading());
    instance<CreateCampaignRepo>().loadMoreFbPosts(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetFbPostsStateError(l));
      }, (r) {
        posts.addAll(r.result?.data?.map((e) => e).toList() ?? []);
        setSelectedUrl(r.result?.next);

        emit(GetFbPostsStateLoaded(r));
      });
    });
  }
}

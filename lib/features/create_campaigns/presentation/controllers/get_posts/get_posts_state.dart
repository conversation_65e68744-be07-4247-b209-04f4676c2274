part of 'get_posts_cubit.dart';

@immutable
abstract class GetFbPostsState {
  const GetFbPostsState();

  List<Object?> get props => [];
}

class GetFbPostsInitial extends GetFbPostsState {}

class GetFbPostsStateLoading extends GetFbPostsState {}

class GetInstaPostsStateLoading extends GetFbPostsState {}

class GetInstaPostsStateLoaded extends GetFbPostsState {
  final InstagramPostsResponse data;

  const GetInstaPostsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetInstaPostsStateLoaded copyWith({
    InstagramPostsResponse? data,
  }) {
    return GetInstaPostsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetFbPostsStateLoaded extends GetFbPostsState {
  final PostResponse data;

  const GetFbPostsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetFbPostsStateLoaded copyWith({
    PostResponse? data,
  }) {
    return GetFbPostsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetMorePostsStateLoading extends GetFbPostsState {}

class GetFbPostsStateError extends GetFbPostsState {
  final Failure message;

  const GetFbPostsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class UpdateStatus extends GetFbPostsState {}

import 'package:ads_dv/features/create_campaigns/data/models/adset.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'get_adsets_state.dart';

class GetAdSetCubit extends Cubit<GetAdSetState> {
  GetAdSetCubit() : super(GetAdSetInitial());

  static GetAdSetCubit get(context) => BlocProvider.of(context);

  List<AdSet>? adSets = [];

  getAdSet(
      {required BuildContext context,
      required String campaignId,
      required String pageAccessToken}) async {
    emit(GetAdSetStateLoading());
    instance<CreateCampaignRepo>()
        .getAdSets(campaignId: campaignId, pageAccessToken: pageAccessToken)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAdSetStateError(l));
      }, (r) {
        adSets = r.result?.data;
        emit(GetAdSetStateLoaded(r));
      });
    });
  }

  loadMoreAdSet({
    required BuildContext context,
    required String url,
  }) async {
    emit(GetAdSetStateLoading());
    instance<CreateCampaignRepo>().loadMoreAdSets(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAdSetStateError(l));
      }, (r) {
        adSets?.addAll(r.result?.data?.map((e) => e).toList() ?? []);
        // adSets?.addAll(r.result?.data ?? []);
        emit(GetAdSetStateLoaded(r));
      });
    });
  }
}

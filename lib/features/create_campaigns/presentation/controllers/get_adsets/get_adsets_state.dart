part of 'get_adsets_cubit.dart';

@immutable
abstract class GetAdSetState {
  const GetAdSetState();

  List<Object?> get props => [];
}

class GetAdSetInitial extends GetAdSetState {}

class GetAdSetStateLoading extends GetAdSetState {}

class GetAdSetStateLoaded extends GetAdSetState {
  final GetAdSetsResponse data;

  const GetAdSetStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetAdSetStateLoaded copyWith({
    GetAdSetsResponse? data,
  }) {
    return GetAdSetStateLoaded(
      data ?? this.data,
    );
  }
}

class GetAdSetStateError extends GetAdSetState {
  final Failure message;

  const GetAdSetStateError(this.message);

  @override
  List<Object?> get props => [message];
}

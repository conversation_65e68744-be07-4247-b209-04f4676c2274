import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';

import '../../../../../utils/res/meta_constants.dart';

part 'education_statuses_state.dart';

class EducationStatusesCubit extends Cubit<List<EducationStatuses>> {
  EducationStatusesCubit() : super(EducationStatuses.values.toList());

  static EducationStatusesCubit get(context) => BlocProvider.of(context);
  void filterSearchResults(String query) {
    List<EducationStatuses> dummySearchList = EducationStatuses.values.toList();
    if (query.isNotEmpty) {
      List<EducationStatuses> dummyListData = [];
      for (var status in dummySearchList) {
        if (status.toString().toLowerCase().contains(query.toLowerCase())) {
          dummyListData.add(status);
        }
      }
      emit(dummyListData);
      return;
    } else {
      emit(dummySearchList);
    }
  }
}

part of 'get_billing_events_cubit.dart';

@immutable
abstract class GetBillingEventsState {
  const GetBillingEventsState();
  List<Object?> get props => [];
}

class GetBillingEventsInitial extends GetBillingEventsState {}

class GetBillingEventsStateLoading extends GetBillingEventsState {}

class GetBillingEventsStateLoaded extends GetBillingEventsState {
  final List<Optimizations> data;

  const GetBillingEventsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetBillingEventsStateLoaded copyWith({
    List<Optimizations>? data,
  }) {
    return GetBillingEventsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetBillingEventsStateError extends GetBillingEventsState {
  final Failure message;

  const GetBillingEventsStateError(this.message);

  @override
  List<Object?> get props => [message];
}
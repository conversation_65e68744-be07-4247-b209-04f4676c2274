import 'package:ads_dv/features/create_campaigns/data/models/optimization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'get_billing_events_state.dart';

class GetBillingEventsCubit extends Cubit<GetBillingEventsState> {
  GetBillingEventsCubit() : super(GetBillingEventsInitial());
  static GetBillingEventsCubit get(context) => BlocProvider.of(context);

  List<Optimizations> events = [];
  getBillingEvents({
    required BuildContext context,
    required int optimizationId
  }) async {
    emit(GetBillingEventsStateLoading());
    instance<CreateCampaignRepo>().getBillingEvents(optimizationId:optimizationId ).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetBillingEventsStateError(l));
      }, (r) {
        events = r;
        emit(GetBillingEventsStateLoaded(r));
      });
    });
  }
}

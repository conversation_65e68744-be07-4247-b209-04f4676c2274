part of 'get_forms_cubit.dart';

@immutable
abstract class GetLeadsFormsState {
  const GetLeadsFormsState();
  List<Object?> get props => [];
}

class GetLeadsFormsInitial extends GetLeadsFormsState {}

class GetLeadsFormsStateLoading extends GetL<PERSON>sFormsState {}

class GetLeadsFormsStateLoaded extends GetLeadsFormsState {
  final FormsResponse data;

  const GetLeadsFormsStateLoaded(this.data);
  @override
  List<Object?> get props => [data];

  GetLeadsFormsStateLoaded copyWith({
    FormsResponse? data,
  }) {
    return GetLeadsFormsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetMoreFormsStateLoading extends GetLeadsFormsState {}

class GetLeadsFormsStateError extends GetLeadsFormsState {
  final Failure message;

  const GetLeadsFormsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class UpdateStatus extends GetLeadsFormsState {}
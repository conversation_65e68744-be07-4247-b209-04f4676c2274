import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/models/lead_forms.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'get_forms_state.dart';

class GetFormsCubit extends Cubit<GetLeadsFormsState> {
  GetFormsCubit() : super(GetLeadsFormsInitial());

  static GetFormsCubit get(context) => BlocProvider.of(context);

  List<LeadForm> forms = [];
  String? url;

  setSelectedUrl(String? selectedUrl){
    url = selectedUrl;
    emit(UpdateStatus());
  }
  getLeadForms(
      {required BuildContext context,
        required String pageId,
        required String pageAccessToken}) async {
    emit(GetLeadsFormsStateLoading());
    instance<CreateCampaignRepo>()
        .getLeadForms(pageAccessToken: pageAccessToken,pageId: pageId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetLeadsFormsStateError(l));
      }, (r) {
        forms = r.data ?? [];
        setSelectedUrl(r.next);

        emit(GetLeadsFormsStateLoaded(r));
      });
    });
  }

  loadMoreLeadsForms({required BuildContext context, String? url}) async {
    emit(GetMoreFormsStateLoading());
    instance<CreateCampaignRepo>().loadMoreLeadsForms(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetLeadsFormsStateError(l));
      }, (r) {
        forms.addAll(r.data?.map((e) => e).toList() ?? []);
        setSelectedUrl(r.next);

        emit(GetLeadsFormsStateLoaded(r));
      });
    });
  }
}

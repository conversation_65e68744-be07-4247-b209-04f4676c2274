part of 'get_optimizations_cubit.dart';

@immutable
abstract class GetOptimizationsState {
  const GetOptimizationsState();
  List<Object?> get props => [];
}

class GetOptimizationsInitial extends GetOptimizationsState {}

class GetOptimizationsStateLoading extends GetOptimizationsState {}

class GetOptimizationsStateLoaded extends GetOptimizationsState {
  final List<Optimizations> data;

  const GetOptimizationsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetOptimizationsStateLoaded copyWith({
    List<Optimizations>? data,
  }) {
    return GetOptimizationsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetOptimizationsStateError extends GetOptimizationsState {
  final Failure message;

  const GetOptimizationsStateError(this.message);

  @override
  List<Object?> get props => [message];
}


import 'package:ads_dv/features/create_campaigns/data/models/optimization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/create_campaign_repo.dart';

part 'get_optimizations_state.dart';

class GetOptimizationsCubit extends Cubit<GetOptimizationsState> {
  GetOptimizationsCubit() : super(GetOptimizationsInitial());
  static GetOptimizationsCubit get(context) => BlocProvider.of(context);

  List<Optimizations> opt = [];
  getOptimizations({
    required BuildContext context,
    required int objectiveId
  }) async {
    emit(GetOptimizationsStateLoading());
    instance<CreateCampaignRepo>().getOptimizations(objectiveId:objectiveId ).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetOptimizationsStateError(l));
      }, (r) {
        opt = r;
        emit(GetOptimizationsStateLoaded(r));
      });
    });
  }
}

import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_review/ad_creative_review_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_review/ad_review_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_review/adset_review_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_review/campaign_review_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../widgets/appbar.dart';
import '../../../../widgets/stepper/bottom_nav.dart';
import '../../../review_screen/presentation/review_screen.dart';
import '../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../controllers/create_ad/create_ad_cubit.dart';

class ReviewCampaignScreen extends StatelessWidget {
  const ReviewCampaignScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return state is CreateADStateLoading
            ? ReviewScreen(
                tiktok: false,
                snapChat: false,
              )
            : Scaffold(
                bottomNavigationBar: const CustomBottomNavBar(
                  isReview: false,
                  isTiktok: false,
                ),
                appBar: const CustomAppBar(
                  title: "Campaign Summary",
                  showBackButton: true,
                  hasDrawer: true,
                ),
                body: CreateAdCubit.get(context).isAddCreated
                    ? const SizedBox()
                    : SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            children: [
                              if (instance<HiveHelper>()
                                      .getUser()
                                      ?.defaultAccountName !=
                                  null)
                                Column(
                                  children: [
                                    5.verticalSpace,
                                    AccountHintText(
                                      isDefaultHint: true,
                                      hint:
                                          "${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}'s Ad Account",
                                    ),
                                    20.verticalSpace,
                                  ],
                                )
                              else
                                const SizedBox(),
                              const CampaignReviewWidget(),
                              20.verticalSpace,
                              const AdSetReviewWidget(),
                              20.verticalSpace,
                              const AdCreativeReviewWidget(),
                              20.verticalSpace,
                              const ReviewAdWidget(),
                            ],
                          ),
                        ),
                      ),
              );
      },
    );
  }
}

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_creative/create_adcreative_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_review/review_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/create_adset_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_campaign/create_campaign_widget.dart';
import 'package:ads_dv/widgets/stepper/bottom_nav.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../utils/di/injection.dart';
import '../../../../utils/hive_helper/hive_helper.dart';
import '../../../../widgets/appbar.dart';
import '../../../review_screen/presentation/review_screen.dart';
import '../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';

class CreateCampaignScreen extends StatelessWidget {
  const CreateCampaignScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return state is CreateADStateLoading
            ? ReviewScreen(
                tiktok: false,
                snapChat: false,
              )
            : SafeArea(
                child: Scaffold(
                  bottomNavigationBar: const CustomBottomNavBar(
                    isReview: true,
                    isTiktok: false,
                  ),
                  appBar: CustomAppBar(
                    title: "meta Advertising".tr,
                    showBackButton: true,
                    hasDrawer: true,
                  ),
                  body: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          if (instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultAccountName !=
                              null)
                            Column(
                              children: [
                                5.verticalSpace,
                                AccountHintText(
                                  isDefaultHint: true,
                                  hint:
                                      "${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}",
                                ),
                                20.verticalSpace,
                              ],
                            )
                          else
                            const SizedBox(),
                          CreateCampaignWidget(
                            createAdCubit: CreateAdCubit.get(context),
                          ),
                          SizedBox(height: 20.h),
                          const CreateAdSetWidget(),
                          SizedBox(height: 20.h),
                          CreateAdCreativeWidget(
                            adCubit: CreateAdCubit.get(context),
                          ),
                          SizedBox(height: 20.h),
                          const AdReviewWidget(),
                        ],
                      ),
                    ),
                  ),
                ),
              );
      },
    );
  }
}

import 'package:ads_dv/features/create_campaigns/data/models/search_result.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../../widgets/loading_widget.dart';
import '../../../../../../../widgets/svg_widget.dart';
import '../../../../../../../widgets/text_field_widget.dart';
import '../../../../controllers/get_reach_estimate/reach_estimate_cubit.dart';
import '../../../../controllers/search/search_cubit.dart';

class DetailedTargeting extends StatefulWidget {
  const DetailedTargeting({super.key});

  @override
  State<DetailedTargeting> createState() => _DetailedTargetingState();
}

class _DetailedTargetingState extends State<DetailedTargeting> {
  var searchInterestsController = TextEditingController();
  var searchBehaveController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => SearchCubit(),
        ),
      ],
      child: BlocBuilder<CreateAdCubit, CreateAdState>(
        builder: (context2, createAdState) {
          final pattern = RegExp(r'\(.*?\)');

          return Scaffold(
            appBar: CustomAppBar(
              title: "Detailed Targeting".tr,
              showBackButton: true,
              hasDrawer: true,
            ),
            body: BlocBuilder<SearchCubit, SearchState>(
              builder: (context, classState) {
                if (classState is SearchClassStateLoading) {
                  return const Center(
                    child: LoadingWidget(isCircle: true),
                  );
                } else if (classState is SearchClassStateError) {
                  return HandleErrorWidget(
                      fun: () {
                        SearchCubit.get(context).searchForClass(
                            context: context,
                            type: "adTargetingCategory",
                            searchClass: "behaviors");
                      },
                      failure: classState.message);
                }
                return BlocBuilder<SearchCubit, SearchState>(
                  builder: (ctx, searchState) {
                    return Padding(
                      padding: const EdgeInsets.only(
                          top: 10.0, left: 10.0, right: 10.0, bottom: 70.0),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Row(
                              children: [
                                CustomSvgWidget(
                                  svg: AppAssets.interests,
                                  width: 24.w,
                                  height: 24.h,
                                ),
                                SizedBox(width: 10.w),
                                CustomText(
                                  text: 'Interests'.tr,
                                  fontSize: 18.sp,
                                  color: Constants.primaryTextColor,
                                  fontWeight: FontWeight.w700,
                                  alignment: AlignmentDirectional.centerStart,
                                )
                              ],
                            ),
                            SizedBox(
                              height: 30.h,
                            ),
                            Container(
                              //    width: 125.w,
                              height: 46.h,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(37),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 20,
                                    offset: Offset(0, 0),
                                    spreadRadius: -4,
                                  )
                                ],
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: CustomTextField(
                                      borderColor: Colors.white,
                                      radius: 37,
                                      radiusOnly: true,
                                      hintText: "Search".tr,
                                      controller: searchInterestsController,
                                      hintStyle: const TextStyle(fontSize: 14),
                                      icon: ShaderMask(
                                        shaderCallback: (Rect bounds) {
                                          return const LinearGradient(
                                            colors: [
                                              Color(0xFFFF006F),
                                              Color(0xFFF6BA00),
                                            ],
                                          ).createShader(bounds);
                                        },
                                        child: const Padding(
                                          padding: EdgeInsets.all(12.0),
                                          child: CustomSvgWidget(
                                              width: 13,
                                              height: 13,
                                              svg: AppAssets.search,
                                              color: Colors.white),
                                        ),
                                      ),
                                      // TextField properties
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      searchInterestsController.text.isEmpty
                                          ? null
                                          : SearchCubit.get(ctx).search(
                                              context: context,
                                              type: "adinterest",
                                              keyword: searchInterestsController
                                                  .text);
                                    },
                                    child: Container(
                                        width: 81.w,
                                        padding: EdgeInsets.zero,
                                        decoration: const ShapeDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment(-0.99, -0.10),
                                            end: Alignment(0.99, 0.1),
                                            colors: [
                                              Color(0xFF0B0F26),
                                              Color(0xFF1C4294)
                                            ],
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.only(
                                              topRight: Radius.circular(100),
                                              bottomRight: Radius.circular(100),
                                            ),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 15.0, vertical: 15.0),
                                          child: CustomText(
                                              text: "Search".tr,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.white),
                                        )),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 30.h,
                            ),
                            BlocBuilder<SearchCubit, SearchState>(
                              builder: (context1, interState) {
                                return (interState is SearchStateLoading)
                                    ? const LoadingWidget(
                                        isCircle: true,
                                      )
                                    : (interState is SearchStateError)
                                        ? HandleErrorWidget(
                                            fun: () {
                                              SearchCubit.get(context)
                                                  .searchForClass(
                                                      context: context,
                                                      type:
                                                          "adTargetingCategory",
                                                      searchClass:
                                                          "adinterest");
                                            },
                                            failure: interState.message)
                                        : (interState is SearchStateLoaded)
                                            ? Column(
                                                children: [
                                                  CreateAdCubit.get(context2)
                                                          .selectedInterests
                                                          .isEmpty
                                                      ? const SizedBox()
                                                      : Column(
                                                          children: [
                                                            GridView.builder(
                                                              shrinkWrap: true,
                                                              // To wrap content without scrolling issues
                                                              physics:
                                                                  const NeverScrollableScrollPhysics(),
                                                              // Prevents nested scrolling
                                                              gridDelegate:
                                                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                                                crossAxisCount:
                                                                    3,
                                                                // Items per row
                                                                crossAxisSpacing:
                                                                    4.0,
                                                                mainAxisSpacing:
                                                                    8.0,
                                                                childAspectRatio:
                                                                    2.3, // Adjust to control width to height ratio
                                                              ),
                                                              itemCount: CreateAdCubit
                                                                      .get(
                                                                          context2)
                                                                  .selectedInterests
                                                                  .length,
                                                              itemBuilder:
                                                                  (context,
                                                                      index) {
                                                                final interest =
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .selectedInterests[index];

                                                                return InkWell(
                                                                  onTap: () {
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .removeFromInterests(
                                                                            interest);
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .undoTargetingProcess1();
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .updateAdSetProcess7();
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    decoration:
                                                                        ShapeDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        begin: Alignment(
                                                                            -0.87,
                                                                            -0.50),
                                                                        end: Alignment(
                                                                            0.87,
                                                                            0.5),
                                                                        colors: [
                                                                          Color(
                                                                              0xFFF6BA00),
                                                                          Color(
                                                                              0xFFFF006F)
                                                                        ],
                                                                      ),
                                                                      shape:
                                                                          RoundedRectangleBorder(
                                                                        borderRadius:
                                                                            BorderRadius.circular(36.59),
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Padding(
                                                                      padding: const EdgeInsets
                                                                          .symmetric(
                                                                          horizontal:
                                                                              8.0,
                                                                          vertical:
                                                                              8.0),
                                                                      child:
                                                                          Row(
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.center,
                                                                        children: [
                                                                          FittedBox(
                                                                            child:
                                                                                CustomText(
                                                                              text: interest.name?.replaceAll(pattern, "") ?? "",
                                                                              fontWeight: FontWeight.w700,
                                                                              color: AppColors.white,
                                                                              fontSize: 10.sp,
                                                                              // overflow: TextOverflow.ellipsis,
                                                                            ),
                                                                          ),
                                                                          const FittedBox(
                                                                            child:
                                                                                Icon(
                                                                              Icons.check,
                                                                              color: AppColors.white,
                                                                              size: 16.0,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                );
                                                              },
                                                            ),
                                                            SizedBox(
                                                              height: 20.h,
                                                            ),
                                                            const Padding(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          8.0),
                                                              child: Divider(
                                                                  color: Constants
                                                                      .textColor,
                                                                  thickness: 2),
                                                            ),
                                                            SizedBox(
                                                              height: 20.h,
                                                            ),
                                                          ],
                                                        ),
                                                  GridView.builder(
                                                    shrinkWrap: true,
                                                    // To wrap content without scrolling issues
                                                    physics:
                                                        const NeverScrollableScrollPhysics(),
                                                    // Prevents nested scrolling
                                                    gridDelegate:
                                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                                      crossAxisCount:
                                                          3, // Items per row
                                                      crossAxisSpacing: 4.0,
                                                      mainAxisSpacing: 8.0,
                                                      childAspectRatio:
                                                          2.3, // Adjust to control width to height ratio
                                                    ),
                                                    itemCount:
                                                        interState.data.length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final interest =
                                                          interState
                                                              .data[index];

                                                      return InkWell(
                                                        onTap: () {
                                                          final createAdCubit =
                                                              CreateAdCubit.get(
                                                                  context);
                                                          createAdCubit
                                                              .addToInterests(
                                                                  interest);
                                                          createAdCubit
                                                              .updateTargetingProcess1();
                                                          createAdCubit
                                                              .updateAdSetProcess7();

                                                          print(
                                                              "Selected Interests: ${createAdCubit.selectedInterests}");
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              ShapeDecoration(
                                                            color: const Color(
                                                                    0xFFFB533E)
                                                                .withOpacity(
                                                                    0.1),
                                                            shape:
                                                                RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          36.59),
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        8.0,
                                                                    vertical:
                                                                        8.0),
                                                            child: Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .center,
                                                              children: [
                                                                // Wrap the text with Flexible to avoid overflow
                                                                Flexible(
                                                                  child:
                                                                      CustomText(
                                                                    text: interest.name?.replaceAll(
                                                                            pattern,
                                                                            "") ??
                                                                        "",
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w700,
                                                                    color: Constants
                                                                        .primaryTextColor,
                                                                    fontSize:
                                                                        10.sp,
                                                                    textOverflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                    maxLines: 2,
                                                                  ),
                                                                ),
                                                                const SizedBox(
                                                                    width: 4),
                                                                // spacing between text and icon
                                                                const Icon(
                                                                  Icons.add,
                                                                  color: Constants
                                                                      .primaryTextColor,
                                                                  size: 16.0,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              )
                                            : Column(
                                                children: [
                                                  CreateAdCubit.get(context2)
                                                          .selectedInterests
                                                          .isEmpty
                                                      ? const SizedBox()
                                                      : Column(
                                                          children: [
                                                            GridView.builder(
                                                              shrinkWrap: true,
                                                              // Wrap content without scrolling issues
                                                              physics:
                                                                  const NeverScrollableScrollPhysics(),
                                                              // Prevents nested scrolling
                                                              gridDelegate:
                                                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                                                crossAxisCount:
                                                                    3,
                                                                // Number of items per row
                                                                crossAxisSpacing:
                                                                    4.0,
                                                                mainAxisSpacing:
                                                                    8.0,
                                                                childAspectRatio:
                                                                    2.3, // Adjust to control width to height ratio
                                                              ),
                                                              itemCount: CreateAdCubit
                                                                      .get(
                                                                          context2)
                                                                  .selectedInterests
                                                                  .length,
                                                              itemBuilder:
                                                                  (context,
                                                                      index) {
                                                                final interest =
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .selectedInterests[index];

                                                                return InkWell(
                                                                  onTap: () {
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .removeFromInterests(
                                                                            interest);
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .undoTargetingProcess1();
                                                                    CreateAdCubit.get(
                                                                            context2)
                                                                        .updateAdSetProcess7();
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    decoration:
                                                                        ShapeDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        begin: Alignment(
                                                                            -0.87,
                                                                            -0.50),
                                                                        end: Alignment(
                                                                            0.87,
                                                                            0.5),
                                                                        colors: [
                                                                          Color(
                                                                              0xFFF6BA00),
                                                                          Color(
                                                                              0xFFFF006F),
                                                                        ],
                                                                      ),
                                                                      shape:
                                                                          RoundedRectangleBorder(
                                                                        borderRadius:
                                                                            BorderRadius.circular(36.59),
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Padding(
                                                                      padding: const EdgeInsets
                                                                          .symmetric(
                                                                          horizontal:
                                                                              8.0,
                                                                          vertical:
                                                                              2),
                                                                      child:
                                                                          Row(
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.spaceBetween,
                                                                        children: [
                                                                          FittedBox(
                                                                            child:
                                                                                CustomText(
                                                                              text: interest.name?.replaceAll(pattern, "") ?? "",
                                                                              fontWeight: FontWeight.w700,
                                                                              color: AppColors.white,
                                                                              fontSize: 10.sp,
                                                                              // overflow: TextOverflow.ellipsis,
                                                                            ),
                                                                          ),
                                                                          const FittedBox(
                                                                            child:
                                                                                Icon(
                                                                              Icons.check,
                                                                              color: AppColors.white,
                                                                              size: 16.0,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                );
                                                              },
                                                            ),
                                                            SizedBox(
                                                              height: 20.h,
                                                            ),
                                                            const Padding(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          8.0),
                                                              child: Divider(
                                                                  color: Constants
                                                                      .textColor,
                                                                  thickness: 2),
                                                            ),
                                                            SizedBox(
                                                              height: 20.h,
                                                            ),
                                                          ],
                                                        ),
                                                  GridView.builder(
                                                    shrinkWrap: true,
                                                    // Important to wrap content without scrolling issues
                                                    physics:
                                                        const NeverScrollableScrollPhysics(),
                                                    // If used inside another scrollable widget
                                                    gridDelegate:
                                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                                      crossAxisCount: 3,
                                                      // Number of items per row
                                                      crossAxisSpacing: 4.0,
                                                      mainAxisSpacing: 8.0,
                                                      childAspectRatio:
                                                          2.8, // Adjusts the width to height ratio
                                                    ),
                                                    itemCount:
                                                        CreateAdCubit.get(
                                                                context2)
                                                            .defaultInterests
                                                            .length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final interest =
                                                          CreateAdCubit.get(
                                                                      context2)
                                                                  .defaultInterests[
                                                              index];

                                                      return InkWell(
                                                        onTap: () {
                                                          CreateAdCubit.get(
                                                                  context2)
                                                              .updateTargetingProcess1();
                                                          CreateAdCubit.get(
                                                                  context2)
                                                              .updateAdSetProcess7();
                                                          CreateAdCubit.get(
                                                                  context2)
                                                              .addToInterests(
                                                                  interest);
                                                          print(
                                                              "Selected Interests: ${CreateAdCubit.get(context2).selectedInterests}");
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              ShapeDecoration(
                                                            color: const Color(
                                                                    0xFFFB533E)
                                                                .withOpacity(
                                                                    0.1),
                                                            shape:
                                                                RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          36.59),
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        0.0,
                                                                    vertical:
                                                                        8.0),
                                                            child: Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .center,
                                                              children: [
                                                                FittedBox(
                                                                  child:
                                                                      CustomText(
                                                                    text: interest.name?.replaceAll(
                                                                            pattern,
                                                                            "") ??
                                                                        "",
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w700,
                                                                    color: Constants
                                                                        .primaryTextColor,
                                                                    fontSize:
                                                                        10.sp,
                                                                    // overflow: TextOverflow.ellipsis,
                                                                  ),
                                                                ),
                                                                const FittedBox(
                                                                  child: Icon(
                                                                    Icons.add,
                                                                    color: Constants
                                                                        .primaryTextColor,
                                                                    size: 16.0,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              );
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
            floatingActionButton: SafeArea(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 8.0, top: 40.0),
                child: SizedBox(
                  width: 235.w,
                  child: ButtonWidget(
                    text: "Save",
                    onTap: () {
                      // Retrieve the current instance of CreateAdCubit
                      final cubit = CreateAdCubit.get(context2);
                      // Assuming cubit.selectedInterests is a List<SearchResult>
                      List<SearchResult> interests =
                          cubit.selectedInterests.map((e) {
                        // Create a new SearchResult with the cleaned name
                        return SearchResult(
                          key: e.key,
                          id: e.id,
                          name: e.name?.replaceAll(RegExp(r'[\s&]+'), "") ?? "",
                        );
                      }).toList();
                      // Check if selected interests are empty
                      if (cubit.selectedInterests.isEmpty) {
                        showErrorToast("Please select your interest");
                      } else {
                        // Update adModel with selected interests
                        cubit.adModel = cubit.adModel.copyWith(
                          interests: interests,
                        );
                        ReachEstimateCubit.get(context).getReachEstimate(
                            context: context,
                            imagesFiles: CreateAdCubit.get(context).adImages,
                            videosFiles: CreateAdCubit.get(context).adVideo);
                        // Debug: Print the updated adModel
                        print(
                            "Updated adModel: ${interests.map((e) => e.name).toList().toString()}");

                        // Pop the current context
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                ),
              ),
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
          );
        },
      ),
    );
  }
}

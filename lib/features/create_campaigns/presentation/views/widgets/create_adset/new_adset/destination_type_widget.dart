import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/svg_widget.dart';

class DestinationTypeWidget extends StatelessWidget {
  final String name;
  final String icon;
  final int index;
  final bool isSelected; // Determine if this ObjectiveWidget is selected
  final void Function(int) callback;

  const DestinationTypeWidget({
    super.key,
    required this.name,
    required this.icon,
    required this.index,
    required this.isSelected,
    required this.callback,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        callback(index);
      },
      child: Container(
        width: 81.h,
        height: 81.h,
        decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(25.r),
            color: Colors.white,
            boxShadow: Constants.unSelectedShadow,
            gradient: isSelected ? Constants.defGradient : null),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon.contains(".png"))
                Image.asset(
                  icon,
                  height: 20.h,
                  width: 20.h,
                  color:
                      isSelected ? AppColors.white : AppColors.iconBottomColor,
                ),
              if (icon.contains(".svg"))
                CustomSvgWidget(
                  svg: icon,
                  height: 20.h,
                  width: 20.h,
                  color:
                      isSelected ? AppColors.white : AppColors.iconBottomColor,
                ),
              SizedBox(height: 8.h),
              FittedBox(
                child: CustomText(
                  text: name,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color:
                      isSelected ? AppColors.white : AppColors.iconBottomColor,
                  textAlign: TextAlign.center,
                  alignment: AlignmentDirectional.center,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  String getIcon(String name) {
    if (name == "App install") {
      return 'assets/images/Download.png';
    } else if (name == "Call") {
      return 'assets/icons/Vector.svg';
    } else if (name == "Website") {
      return 'assets/images/Internet.png';
    } else {
      return 'assets/icons/instagram.svg';
    }
  }
}

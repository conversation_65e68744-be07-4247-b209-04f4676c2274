import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../utils/res/meta_constants.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/svg_widget.dart';
import '../../../../../../../widgets/text_field_widget.dart';
import '../../../../controllers/create_ad/create_ad_cubit.dart';
import '../../../../controllers/education_statuses/education_statuses_cubit.dart';

class EducationStatusScreen extends StatefulWidget {
  const EducationStatusScreen({super.key});

  @override
  State<EducationStatusScreen> createState() => _EducationStatusScreenState();
}

class _EducationStatusScreenState extends State<EducationStatusScreen> {
  var educationStatusesController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EducationStatusesCubit(),
      child: BlocBuilder<EducationStatusesCubit, List<EducationStatuses>>(
        builder: (context, state) {
          return Scaffold(
            appBar: const CustomAppBar(
              title: "Education Statuses",
              showBackButton: true,
              hasDrawer: true,
            ),
            body: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Container(
                      //    width: 125.w,
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(37),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 20,
                            offset: Offset(0, 0),
                            spreadRadius: -4,
                          )
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: educationStatusesController,
                              borderColor: Colors.white,
                              radius: 37,
                              radiusOnly: true,
                              hintText: "Search for ...",
                              hintStyle: const TextStyle(fontSize: 14),
                              icon: ShaderMask(
                                shaderCallback: (Rect bounds) {
                                  return const LinearGradient(
                                    colors: [
                                      Color(0xFFFF006F),
                                      Color(0xFFF6BA00),
                                    ],
                                  ).createShader(bounds);
                                },
                                child: const Padding(
                                  padding: EdgeInsets.all(12.0),
                                  child: CustomSvgWidget(
                                      width: 13,
                                      height: 13,
                                      svg: AppAssets.search,
                                      color: Colors.white),
                                ),
                              ),
                              // TextField properties
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              EducationStatusesCubit.get(context)
                                  .filterSearchResults(
                                  educationStatusesController.text);
                            },
                            child: Container(
                                width: 81.w,
                                padding: EdgeInsets.zero,
                                decoration: const ShapeDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment(-0.99, -0.10),
                                    end: Alignment(0.99, 0.1),
                                    colors: [
                                      Color(0xFF0B0F26),
                                      Color(0xFF1C4294)
                                    ],
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(100),
                                      bottomRight: Radius.circular(100),
                                    ),
                                  ),
                                ),
                                child: const Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 15.0, vertical: 15.0),
                                  child: CustomText(
                                      text: "Search",
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white),
                                )),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    educationStatusesController.text.isEmpty
                        ? const SizedBox()
                        : BlocBuilder<CreateAdCubit, CreateAdState>(
                      builder: (context1, adState) {
                        return Column(
                          children: [
                            CustomText(
                              text: 'Search Result (${state.length})',
                              fontSize: 16.sp,
                              color: AppColors.iconBottomColor,
                              fontWeight: FontWeight.w400,
                              alignment: AlignmentDirectional.centerStart,
                            ),
                            SizedBox(
                              height: 20.h,
                            ),
                          ],
                        );
                      },
                    ),
                    BlocBuilder<CreateAdCubit, CreateAdState>(
                      builder: (context1, adState) {
                        return ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          itemCount: state.length,
                          itemBuilder: (item, index) {
                            return (!CreateAdCubit
                                .get(context1)
                                .educationStatuses
                                .contains(state[index]))
                                ? InkWell(
                              onTap: () {
                                CreateAdCubit.get(context1)
                                    .setSelectedStatuses(state[index]);
                                CreateAdCubit.get(context1)
                                    .updateTargetingProcess5();
                                CreateAdCubit.get(context1)
                                    .updateAdSetProcess7();
                              },
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: CustomText(
                                          text: state[index]
                                              .displayName
                                              .split(' ')
                                              .map((word) =>
                                          word[0].toUpperCase() +
                                              word.substring(1))
                                              .join(
                                              ' '), // Convert to title case

                                          color: Constants.darkColor,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      Container(
                                        height: 22,
                                        width: 22,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.rectangle,
                                          borderRadius:
                                          BorderRadius.circular(5.r),
                                          border: Border.all(
                                            color: Constants.darkColor,
                                            width: 2.w,
                                            style: BorderStyle.solid,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 5),
                                  const Divider(color: Constants.gray),
                                ],
                              ),
                            )
                                : Padding(
                              padding: const EdgeInsets.only(
                                  bottom: 10.0, left: 8),
                              child: InkWell(
                                onTap: () {
                                  CreateAdCubit.get(context1)
                                      .setSelectedStatuses(state[index]);

                                  CreateAdCubit.get(context1)
                                      .undoTargetingProcess5();
                                  CreateAdCubit.get(context1)
                                      .updateAdSetProcess7();
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.zero,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                        BorderRadius.circular(8)),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 20,
                                        offset: Offset(0, 0),
                                        spreadRadius: -6,
                                      )
                                    ],
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10.0, vertical: 12),
                                    child: Row(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Center(
                                            child: ShaderMask(
                                              shaderCallback: (Rect bounds) {
                                                return const LinearGradient(
                                                  colors: [
                                                    Color(0xFFFF006F),
                                                    Color(0xFFF6BA00),
                                                  ],
                                                ).createShader(bounds);
                                              },
                                              child: CustomText(
                                                text: state[index]
                                                    .displayName
                                                    .split(' ')
                                                    .map((word) =>
                                                word[0]
                                                    .toUpperCase() +
                                                    word.substring(1))
                                                    .join(
                                                    ' '),
                                                // Convert to title case

                                                color: Colors.white,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ),
                                        ),
                                        ShaderMask(
                                          shaderCallback: (Rect bounds) {
                                            return const LinearGradient(
                                              colors: [
                                                Color(0xFFFF006F),
                                                Color(0xFFF6BA00),
                                              ],
                                            ).createShader(bounds);
                                          },
                                          child: const Icon(
                                            Icons.check_box,
                                            size: 28,
                                            color: Colors.white,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),

                    const SizedBox(height: 25),
                    SizedBox(
                        width: 235.w,
                        child: ButtonWidget(
                            text: "Save",
                            onTap: () {
                              Navigator.of(context).pop();
                            })),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

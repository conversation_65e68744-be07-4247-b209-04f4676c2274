import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/svg_widget.dart';

class GenderWidget extends StatelessWidget {
  final String name;
  final int index;
  final bool isSelected; // Determine if this ObjectiveWidget is selected
  final void Function(int) callback;
  const GenderWidget({
    super.key,
    required this.name,
    required this.index,
    required this.isSelected,
    required this.callback,
  });
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        callback(index);
      },
      child: Container(
        width: 81.h,
        height: 81.h,
        decoration: BoxDecoration(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(25.r),
          color: Colors.white,
          boxShadow: Constants.unSelectedShadow,
          border: isSelected
              ? Border.all(
            color: AppColors.mainColor,
            width: 2.w,
            style: BorderStyle.solid,
          )
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomSvgWidget(
              svg: getIcon(name),
              height: 25.h,
              width: 25.h,
              color:isSelected ?   AppColors.mainColor:AppColors.iconBottomColor,
            ),
            SizedBox(height: 8.h),
            FittedBox(
              child: CustomText(
                text: name,
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: isSelected ?   AppColors.mainColor:AppColors.iconBottomColor,
                textAlign: TextAlign.center,
                alignment: AlignmentDirectional.center,
              ),
            )
          ],
        ),
      ),
    );
  }

  String getIcon(String name) {
    if (name == "Male") {
      return 'assets/icons/male.svg';
    } else if (name == "Female") {
      return 'assets/icons/female.svg';
    } else {
      return 'assets/icons/all.svg';
    }
  }
}
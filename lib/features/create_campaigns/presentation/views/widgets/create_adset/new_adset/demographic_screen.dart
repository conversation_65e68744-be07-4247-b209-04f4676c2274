import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/search/search_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/gender_widget.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:ads_dv/widgets/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../controllers/get_reach_estimate/reach_estimate_cubit.dart';
import '../../estimated_card_widget.dart';

class DemographicScreen extends StatefulWidget {
  CreateAdCubit createAdCubit;

  DemographicScreen({super.key, required this.createAdCubit});

  @override
  State<DemographicScreen> createState() => _DemographicScreenState();
}

class _DemographicScreenState extends State<DemographicScreen> {
  FocusNode fromNode = FocusNode();
  int fromAgeValue = 0;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SearchCubit>(
      create: (context) => SearchCubit(),
      child: BlocBuilder<SearchCubit, SearchState>(
        builder: (context, state) {
          return Scaffold(
            appBar: CustomAppBar(
              title: "Demographic".tr,
              showBackButton: true,
              hasDrawer: true,
            ),
            body: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.age,
                          width: 24.w,
                          height: 24.h,
                          //    color: Colors.white,
                        ),
                        SizedBox(width: 10.w),
                        CustomText(
                          text: 'Age'.tr,
                          fontSize: 18.sp,
                          color: Constants.primaryTextColor,
                          fontWeight: FontWeight.w700,
                          alignment: AlignmentDirectional.centerStart,
                        )
                      ],
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    BlocBuilder<CreateAdCubit, CreateAdState>(
                      bloc: CreateAdCubit.get(context),
                      builder: (context, state) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Container(
                              width: 125.w,
                              height: 42.h,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(37),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 20,
                                    offset: Offset(0, 0),
                                    spreadRadius: -4,
                                  )
                                ],
                              ),
                              child: Row(
                                children: [
                                  // SizedBox(
                                  //   width: 80.w,
                                  //   child: CustomTextField(
                                  //     borderColor: Colors.transparent,
                                  //     hintText: "From".tr,
                                  //     textInputType: TextInputType.number,
                                  //     hintStyle: const TextStyle(fontSize: 14),
                                  //     controller: widget.createAdCubit.minAge,
                                  //     node: fromNode,
                                  //     inputFormatters: [
                                  //       FilteringTextInputFormatter.digitsOnly,
                                  //       // Only allow numbers
                                  //     ],
                                  //     onChanged: (val) {
                                  //       fromAgeValue = int.tryParse(val) ?? 18;
                                  //       setState(() {});
                                  //       // if (val.isNotEmpty) {
                                  //       //   int ageValue = int.parse(val);
                                  //       //
                                  //       //   // Check if the value is less than 18
                                  //       //   if (ageValue < 18 || ageValue > 65) {
                                  //       //     // If less than 18, set the value to 18
                                  //       //     widget.createAdCubit.minAge.text =
                                  //       //         '18';
                                  //       //     widget.createAdCubit.minAge
                                  //       //             .selection =
                                  //       //         TextSelection.fromPosition(
                                  //       //       TextPosition(
                                  //       //           offset: widget.createAdCubit
                                  //       //               .minAge.text.length),
                                  //       //     );
                                  //       //     ageValue = 18;
                                  //       //   }
                                  //       //
                                  //       //   widget.createAdCubit
                                  //       //       .updateDemoProcess1();
                                  //       //   widget.createAdCubit
                                  //       //       .updateAdSetProcess9();
                                  //       //
                                  //       //   widget.createAdCubit.adModel =
                                  //       //       CreateAdCubit.get(context)
                                  //       //           .adModel
                                  //       //           .copyWith(ageMin: ageValue);
                                  //       //
                                  //       //   ReachEstimateCubit.get(context)
                                  //       //       .getReachEstimate(
                                  //       //     context: context,
                                  //       //     imagesFiles:
                                  //       //         CreateAdCubit.get(context)
                                  //       //             .adImages,
                                  //       //     videosFiles:
                                  //       //         CreateAdCubit.get(context)
                                  //       //                 .adVideo +
                                  //       //             CreateAdCubit.get(context)
                                  //       //                 .videoImage,
                                  //       //   );
                                  //       // } else {
                                  //       //   widget.createAdCubit
                                  //       //       .undoDemoProcess1();
                                  //       //   widget.createAdCubit
                                  //       //       .updateAdSetProcess9();
                                  //       // }
                                  //     },
                                  //   ),
                                  // ),
                                  5.horizontalSpace,
                                  Text(
                                    "From".tr,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  5.horizontalSpace,
                                  const VerticalDivider(
                                    width: 2,
                                    color: Constants.primaryTextColor,
                                  ),
                                  SizedBox(
                                    width: 80.w,
                                    child: FittedBox(
                                      child: DropdownButton<int>(
                                        padding: EdgeInsets.zero,
                                        // selectedItemBuilder:
                                        //     (BuildContext context) {
                                        //   return []; // Return an empty list to disable displaying the selected item next to the icon
                                        // },
                                        disabledHint: const SizedBox(),
                                        borderRadius:
                                            BorderRadius.circular(37.0),
                                        underline: const SizedBox(),
                                        hint: const SizedBox(),
                                        icon: const Icon(
                                          Icons.expand_more,
                                          color: Constants.primaryTextColor,
                                          size: 30,
                                        ),
                                        value: CreateAdCubit.get(context)
                                            .selectedMinAge,
                                        style: const TextStyle(
                                            color: Colors.black),
                                        onChanged: (int? value) {
                                          setState(() {
                                            CreateAdCubit.get(context)
                                                .selectedMinAge = value!;

                                            // widget.createAdCubit.minAge.text =
                                            //     CreateAdCubit.get(context)
                                            //         .selectedMinAge
                                            //         .toString();
                                          });
                                          widget.createAdCubit
                                              .updateDemoProcess1();
                                          widget.createAdCubit
                                              .updateAdSetProcess9();
                                          widget.createAdCubit.adModel =
                                              CreateAdCubit.get(context)
                                                  .adModel
                                                  .copyWith(
                                                    ageMin: widget.createAdCubit
                                                        .selectedMinAge,
                                                  );
                                          // ReachEstimateCubit.get(context)
                                          //     .getReachEstimate(
                                          //         context: context,
                                          //         imagesFiles:
                                          //             CreateAdCubit.get(context)
                                          //                 .adImages,
                                          //         videosFiles:
                                          //             CreateAdCubit.get(context)
                                          //                 .adVideo);
                                        },
                                        items: List<
                                                DropdownMenuItem<int>>.generate(
                                            48, (int index) {
                                          return DropdownMenuItem<int>(
                                            value: index + 18,
                                            child: Text(
                                              (index + 18).toString(),
                                              style: const TextStyle(
                                                  color: Colors.black),
                                            ),
                                          );
                                        }),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              width: 125.w,
                              height: 42.h,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(37),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 20,
                                    offset: Offset(0, 0),
                                    spreadRadius: -4,
                                  )
                                ],
                              ),
                              child: Row(
                                children: [
                                  // SizedBox(
                                  //   width: 80.w,
                                  //   child: CustomTextField(
                                  //     borderColor: Colors.transparent,
                                  //     hintText: "To".tr,
                                  //     textInputType: TextInputType.number,
                                  //     controller: widget.createAdCubit.maxAge,
                                  //     hintStyle: const TextStyle(fontSize: 14),
                                  //     inputFormatters: [
                                  //       FilteringTextInputFormatter.digitsOnly,
                                  //       // Only allow numbers
                                  //     ],
                                  //     onChanged: (val) {
                                  //       if (val.isNotEmpty) {
                                  //         // int ageValue = int.parse(val);
                                  //
                                  //         // Check if the value is less than 18
                                  //         if (fromAgeValue < 18 ||
                                  //             fromAgeValue > 65) {
                                  //           // If less than 18, set the value to 18
                                  //           widget.createAdCubit.minAge.text =
                                  //               '18';
                                  //           widget.createAdCubit.minAge
                                  //                   .selection =
                                  //               TextSelection.fromPosition(
                                  //             TextPosition(
                                  //                 offset: widget.createAdCubit
                                  //                     .minAge.text.length),
                                  //           );
                                  //           fromAgeValue = 18;
                                  //         }
                                  //
                                  //         widget.createAdCubit
                                  //             .updateDemoProcess1();
                                  //         widget.createAdCubit
                                  //             .updateAdSetProcess9();
                                  //
                                  //         widget.createAdCubit.adModel =
                                  //             CreateAdCubit.get(context)
                                  //                 .adModel
                                  //                 .copyWith(
                                  //                     ageMin: fromAgeValue);
                                  //
                                  //         ReachEstimateCubit.get(context)
                                  //             .getReachEstimate(
                                  //           context: context,
                                  //           imagesFiles:
                                  //               CreateAdCubit.get(context)
                                  //                   .adImages,
                                  //           videosFiles:
                                  //               CreateAdCubit.get(context)
                                  //                   .adVideo,
                                  //         );
                                  //       } else {
                                  //         widget.createAdCubit
                                  //             .undoDemoProcess1();
                                  //         widget.createAdCubit
                                  //             .updateAdSetProcess9();
                                  //       }
                                  //       if (val.isNotEmpty) {
                                  //         int ageValue = int.parse(val);
                                  //
                                  //         // Check if the value exceeds 65
                                  //         if (ageValue > 65) {
                                  //           // If greater than 65, set the value to 65
                                  //           widget.createAdCubit.maxAge.text =
                                  //               '65';
                                  //           widget.createAdCubit.maxAge
                                  //                   .selection =
                                  //               TextSelection.fromPosition(
                                  //             TextPosition(
                                  //                 offset: widget.createAdCubit
                                  //                     .maxAge.text.length),
                                  //           );
                                  //           ageValue = 65;
                                  //         }
                                  //
                                  //         widget.createAdCubit
                                  //             .updateDemoProcess2();
                                  //         widget.createAdCubit
                                  //             .updateAdSetProcess9();
                                  //
                                  //         widget.createAdCubit.adModel =
                                  //             CreateAdCubit.get(context)
                                  //                 .adModel
                                  //                 .copyWith(ageMax: ageValue);
                                  //
                                  //         ReachEstimateCubit.get(context)
                                  //             .getReachEstimate(
                                  //           context: context,
                                  //           imagesFiles:
                                  //               CreateAdCubit.get(context)
                                  //                   .adImages,
                                  //           videosFiles:
                                  //               CreateAdCubit.get(context)
                                  //                   .adVideo,
                                  //         );
                                  //       } else {
                                  //         widget.createAdCubit
                                  //             .undoDemoProcess2();
                                  //         widget.createAdCubit
                                  //             .updateAdSetProcess9();
                                  //       }
                                  //     },
                                  //   ),
                                  // ),
                                  5.horizontalSpace,
                                  Text(
                                    "To".tr,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  5.horizontalSpace,
                                  const VerticalDivider(
                                    width: 2,
                                    color: Constants.primaryTextColor,
                                  ),
                                  SizedBox(
                                    width: 80.w,
                                    child: FittedBox(
                                      child: DropdownButton<int>(
                                        padding: EdgeInsets.zero,
                                        // selectedItemBuilder:
                                        //     (BuildContext context) {
                                        //   return []; // Return an empty list to disable displaying the selected item next to the icon
                                        // },
                                        disabledHint: const SizedBox(),
                                        borderRadius:
                                            BorderRadius.circular(37.0),
                                        underline: const SizedBox(),
                                        hint: const SizedBox(),
                                        icon: const Icon(
                                          Icons.expand_more,
                                          color: Constants.primaryTextColor,
                                          size: 30,
                                        ),
                                        value: CreateAdCubit.get(context)
                                            .selectedMaxAge,
                                        style: const TextStyle(
                                            color: Colors.black),
                                        onChanged: (int? value) {
                                          setState(() {
                                            CreateAdCubit.get(context)
                                                .selectedMaxAge = value!;

                                            // widget.createAdCubit.minAge.text =
                                            //     CreateAdCubit.get(context)
                                            //         .selectedMinAge
                                            //         .toString();
                                          });
                                          widget.createAdCubit
                                              .updateDemoProcess1();
                                          widget.createAdCubit
                                              .updateAdSetProcess9();
                                          widget.createAdCubit.adModel =
                                              CreateAdCubit.get(context)
                                                  .adModel
                                                  .copyWith(
                                                    ageMax: widget.createAdCubit
                                                        .selectedMaxAge,
                                                  );
                                          ReachEstimateCubit.get(context)
                                              .getReachEstimate(
                                                  context: context,
                                                  imagesFiles:
                                                      CreateAdCubit.get(context)
                                                          .adImages,
                                                  videosFiles:
                                                      CreateAdCubit.get(context)
                                                          .adVideo);
                                        },
                                        items: List<
                                                DropdownMenuItem<int>>.generate(
                                            48, (int index) {
                                          return DropdownMenuItem<int>(
                                            value: index + 18,
                                            child: Text(
                                              (index + 18).toString(),
                                              style: const TextStyle(
                                                  color: Colors.black),
                                            ),
                                          );
                                        }),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Divider(color: Constants.textColor, thickness: 2),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.gender,
                          width: 24.w,
                          height: 24.h,
                          //    color: Colors.white,
                        ),
                        SizedBox(width: 10.w),
                        CustomText(
                          text: 'Gender'.tr,
                          fontSize: 18.sp,
                          color: Constants.primaryTextColor,
                          fontWeight: FontWeight.w700,
                          alignment: AlignmentDirectional.centerStart,
                        )
                      ],
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: genders.map((e) {
                        return GenderWidget(
                          isSelected: genders.indexOf(e) ==
                              CreateAdCubit.get(context).selectedIndex,
                          callback: (index) {
                            widget.createAdCubit.updateDemoProcess3();
                            widget.createAdCubit.updateAdSetProcess9();

                            setState(() {
                              // Update selected index when an ObjectiveWidget is tapped
                              CreateAdCubit.get(context).selectedIndex = index;
                              e == "Male".tr
                                  ? CreateAdCubit.get(context).genders = [1]
                                  : e == "Female".tr
                                      ? CreateAdCubit.get(context).genders = [2]
                                      : CreateAdCubit.get(context).genders = [
                                          1,
                                          2
                                        ];
                            });
                            widget.createAdCubit.adModel =
                                CreateAdCubit.get(context).adModel.copyWith(
                                    genders: widget.createAdCubit.genders);
                            ReachEstimateCubit.get(context).getReachEstimate(
                                context: context,
                                imagesFiles:
                                    CreateAdCubit.get(context).adImages,
                                videosFiles:
                                    CreateAdCubit.get(context).adVideo);
                          },
                          name: e,
                          index: genders.indexOf(e),
                        );
                      }).toList(),
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Divider(color: Constants.textColor, thickness: 2),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.lang,
                          width: 24.w,
                          height: 24.h,
                          //    color: Colors.white,
                        ),
                        SizedBox(width: 10.w),
                        CustomText(
                          text: 'Language'.tr,
                          fontSize: 18.sp,
                          color: Constants.primaryTextColor,
                          fontWeight: FontWeight.w700,
                          alignment: AlignmentDirectional.centerStart,
                        )
                      ],
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    Container(
                      //    width: 125.w,
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(37),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 20,
                            offset: Offset(0, 0),
                            spreadRadius: -4,
                          )
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              onChanged: (val) {
                                //  searchWord = textController.text;
                              },
                              borderColor: Colors.transparent,
                              hintText: "Search for ...".tr,
                              controller:
                                  CreateAdCubit.get(context).textController,
                              hintStyle: const TextStyle(fontSize: 14),
                              icon: ShaderMask(
                                shaderCallback: (Rect bounds) {
                                  return const LinearGradient(
                                    colors: [
                                      Color(0xFFFF006F),
                                      Color(0xFFF6BA00),
                                    ],
                                  ).createShader(bounds);
                                },
                                child: const Padding(
                                  padding: EdgeInsets.all(12.0),
                                  child: CustomSvgWidget(
                                      width: 13,
                                      height: 13,
                                      svg: AppAssets.search,
                                      color: Colors.white),
                                ),
                              ),
                              // TextField properties
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              CreateAdCubit.get(context)
                                      .textController
                                      .text
                                      .isEmpty
                                  ? null
                                  : SearchCubit.get(context).search(
                                      context: context,
                                      type: "adlocale",
                                      keyword: CreateAdCubit.get(context)
                                          .textController
                                          .text);
                            },
                            child: Container(
                                width: 81.w,
                                padding: EdgeInsets.zero,
                                decoration: const ShapeDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment(-0.99, -0.10),
                                    end: Alignment(0.99, 0.1),
                                    colors: [
                                      Color(0xFF0B0F26),
                                      Color(0xFF1C4294)
                                    ],
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(100),
                                      bottomRight: Radius.circular(100),
                                    ),
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15.0, vertical: 15.0),
                                  child: CustomText(
                                      text: "Search".tr,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white),
                                )),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    CreateAdCubit.get(context).textController.text.isEmpty
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Lottie.asset(AppAssets.searchLo,
                                  width: 111.h, height: 112.h),
                              SizedBox(
                                height: 10.h,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomText(
                                    text:
                                        'Search for  your  Target Language'.tr,
                                    fontSize: 14.sp,
                                    color: AppColors.iconBottomColor,
                                    fontWeight: FontWeight.w400,
                                    alignment: AlignmentDirectional.centerStart,
                                  ),
                                ],
                              ),
                            ],
                          )
                        : const SizedBox(),
                    if (state is SearchStateLoading)
                      const LoadingWidget(isCircle: true)
                    else if (state is SearchStateError)
                      HandleErrorWidget(
                          fun: () {
                            SearchCubit.get(context).search(
                                context: context,
                                type: "adlocale",
                                keyword: CreateAdCubit.get(context)
                                    .textController
                                    .text);
                          },
                          failure: state.message)
                    else if (state is SearchStateLoaded)
                      Column(
                        children: [
                          CustomText(
                            text:
                                '${"Search Result".tr} (${state.data.length})',
                            fontSize: 14.sp,
                            color: AppColors.iconBottomColor,
                            fontWeight: FontWeight.w400,
                            alignment: AlignmentDirectional.centerStart,
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                          BlocBuilder<CreateAdCubit, CreateAdState>(
                            builder: (context, adState) {
                              return ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.zero,
                                itemCount: state.data.length,
                                itemBuilder: (item, index) {
                                  return (!widget.createAdCubit.languages
                                          .contains(state.data[index]))
                                      ? InkWell(
                                          onTap: () {
                                            widget.createAdCubit
                                                .setSelectedLanguages(
                                                    state.data[index]);
                                            widget.createAdCubit
                                                .updateDemoProcess4();
                                            widget.createAdCubit
                                                .updateAdSetProcess9();
                                            widget.createAdCubit.adModel =
                                                CreateAdCubit.get(context)
                                                    .adModel
                                                    .copyWith(
                                                      languages: widget
                                                          .createAdCubit
                                                          .languages,
                                                    );
                                            ReachEstimateCubit.get(context)
                                                .getReachEstimate(
                                                    context: context,
                                                    imagesFiles:
                                                        CreateAdCubit.get(
                                                                context)
                                                            .adImages,
                                                    videosFiles:
                                                        CreateAdCubit.get(
                                                                context)
                                                            .adVideo);
                                            print("asdasda${widget.createAdCubit.languages}");
                                          },
                                          child: Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  CustomText(
                                                    text: state
                                                            .data[index].name ??
                                                        "",
                                                    color: Constants.darkColor,
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                  Container(
                                                    height: 22,
                                                    width: 22,
                                                    decoration: BoxDecoration(
                                                      border: Border.all(
                                                        color:
                                                            Constants.darkColor,
                                                        width: 1.5,
                                                      ),
                                                      shape: BoxShape.circle,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 5),
                                              const Divider(
                                                  color: Constants.gray),
                                            ],
                                          ),
                                        )
                                      : Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 10.0, left: 8),
                                          child: InkWell(
                                            onTap: () {
                                              widget.createAdCubit
                                                  .setSelectedLanguages(
                                                      state.data[index]);
                                              widget.createAdCubit
                                                  .undoDemoProcess4();
                                              widget.createAdCubit
                                                  .updateAdSetProcess9();

                                              ReachEstimateCubit.get(context)
                                                  .getReachEstimate(
                                                      context: context,
                                                      imagesFiles:
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .adImages,
                                                      videosFiles:
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .adVideo);
                                              print("asdasda${widget.createAdCubit.languages}");
                                            },
                                            child: Container(
                                              alignment: Alignment.center,
                                              padding: EdgeInsets.zero,
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8)),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x33000000),
                                                    blurRadius: 20,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: -6,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10.0,
                                                        vertical: 12),
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Center(
                                                      child: ShaderMask(
                                                        shaderCallback:
                                                            (Rect bounds) {
                                                          return const LinearGradient(
                                                            colors: [
                                                              Color(0xFFFF006F),
                                                              Color(0xFFF6BA00),
                                                            ],
                                                          ).createShader(
                                                              bounds);
                                                        },
                                                        child: CustomText(
                                                          text: state
                                                                  .data[index]
                                                                  .name ??
                                                              "",
                                                          color: Colors.white,
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                        ),
                                                      ),
                                                    ),
                                                    ShaderMask(
                                                      shaderCallback:
                                                          (Rect bounds) {
                                                        return const LinearGradient(
                                                          colors: [
                                                            Color(0xFFFF006F),
                                                            Color(0xFFF6BA00),
                                                          ],
                                                        ).createShader(bounds);
                                                      },
                                                      child: const Icon(
                                                        Icons.check_circle,
                                                        size: 22,
                                                        color: Colors.white,
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                },
                              );
                            },
                          ),
                          SizedBox(
                            height: 10.h,
                          ),
                        ],
                      ),
                    BlocBuilder<CreateAdCubit, CreateAdState>(
                      builder: (context, adState) {
                        return widget.createAdCubit.languages.isEmpty
                            ? const SizedBox()
                            : Column(
                                children: [
                                  CustomText(
                                    text: 'Selected Languages'.tr,
                                    fontSize: 14.sp,
                                    color: AppColors.iconBottomColor,
                                    fontWeight: FontWeight.w400,
                                    alignment: AlignmentDirectional.centerStart,
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    padding: EdgeInsets.zero,
                                    itemCount:
                                        widget.createAdCubit.languages.length,
                                    itemBuilder: (item, index) {
                                      return Padding(
                                        padding: const EdgeInsets.only(
                                            bottom: 10.0, left: 8),
                                        child: InkWell(
                                          onTap: () {
                                            widget.createAdCubit
                                                .setSelectedLanguages(widget
                                                    .createAdCubit
                                                    .languages[index]);

                                            widget.createAdCubit
                                                .undoDemoProcess4();
                                            widget.createAdCubit
                                                .updateAdSetProcess9();

                                            print("asdasda${widget.createAdCubit.languages}");
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.zero,
                                            decoration: ShapeDecoration(
                                              color: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8)),
                                              shadows: const [
                                                BoxShadow(
                                                  color: Color(0x33000000),
                                                  blurRadius: 20,
                                                  offset: Offset(0, 0),
                                                  spreadRadius: -6,
                                                )
                                              ],
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 10.0,
                                                      vertical: 12),
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Center(
                                                    child: ShaderMask(
                                                      shaderCallback:
                                                          (Rect bounds) {
                                                        return const LinearGradient(
                                                          colors: [
                                                            Color(0xFFFF006F),
                                                            Color(0xFFF6BA00),
                                                          ],
                                                        ).createShader(bounds);
                                                      },
                                                      child: CustomText(
                                                        text: widget
                                                                .createAdCubit
                                                                .languages[
                                                                    index]
                                                                .name ??
                                                            "",
                                                        color: Colors.white,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                    ),
                                                  ),
                                                  ShaderMask(
                                                    shaderCallback:
                                                        (Rect bounds) {
                                                      return const LinearGradient(
                                                        colors: [
                                                          Color(0xFFFF006F),
                                                          Color(0xFFF6BA00),
                                                        ],
                                                      ).createShader(bounds);
                                                    },
                                                    child: const Icon(
                                                      Icons.check_circle,
                                                      size: 22,
                                                      color: Colors.white,
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              );
                      },
                    ),
                    // const SizedBox(height: 25),
                    // const Padding(
                    //   padding: EdgeInsets.symmetric(horizontal: 8.0),
                    //   child: Divider(color: Constants.textColor),
                    // ),
                    // BlocBuilder<ReachEstimateCubit, ReachEstimateState>(
                    //   builder: (context, state) {
                    //     if(state is ReachStateLoaded){
                    //       return EstimatedCardWidget(upperNum: state.reachEstimatedModel?.usersUpperBound?.toDouble() ?? 0.0, lowerNum: state.reachEstimatedModel?.usersLowerBound?.toDouble() ?? 0.0,);
                    //     }else{
                    //       return const SizedBox();
                    //     }
                    //   },
                    // ),
                    SizedBox(
                      width: 235.w,
                      child: ButtonWidget(
                        text: "Save".tr,
                        onTap: () {
                          if (widget.createAdCubit.selectedMinAge == null) {
                            showErrorToast("please select min age".tr);
                          } else if (widget.createAdCubit.selectedMaxAge ==
                              null) {
                            showErrorToast("please select max age".tr);
                          } else if (widget.createAdCubit.selectedMaxAge! <
                                  18 ||
                              widget.createAdCubit.selectedMinAge! < 18) {
                            showErrorToast("Valid age must be over or 18".tr);
                          } else if (widget.createAdCubit.selectedMaxAge! <
                              widget.createAdCubit.selectedMinAge!) {
                            showErrorToast(
                                "Min age must be less than max age".tr);
                          } else if (widget.createAdCubit.genders.isEmpty) {
                            showErrorToast("please select the gender".tr);
                          } else if (widget.createAdCubit.languages.isEmpty) {
                            showErrorToast("please select the languages".tr);
                          } else {
                            widget.createAdCubit.adModel =
                                CreateAdCubit.get(context).adModel.copyWith(
                                    ageMin: widget.createAdCubit.selectedMinAge,
                                    ageMax: widget.createAdCubit.selectedMaxAge,
                                    languages: widget.createAdCubit.languages,
                                    genders: widget.createAdCubit.genders);
                            print("testadasd${widget.createAdCubit.adModel
                                    .toJson()}");
                            Navigator.of(context).pop();
                          }
                        },
                      ),
                    ),
                    SizedBox(
                      height: 60.h,
                    ),
                  ],
                ),
              ),
            ),
            floatingActionButton: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                BlocBuilder<ReachEstimateCubit, ReachEstimateState>(
                  builder: (context, state) {
                    if (state is ReachStateLoaded) {
                      return Card(
                          margin: EdgeInsets.symmetric(horizontal: 20.w),
                          elevation: 0.0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                              side: const BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.circular(25.0)),
                          child: EstimatedCardWidget(
                            upperNum: state.reachEstimatedModel?.usersUpperBound
                                    ?.toDouble() ??
                                0.0,
                            lowerNum: state.reachEstimatedModel?.usersLowerBound
                                    ?.toDouble() ??
                                0.0,
                            isDemoGraphic: true,
                          ));
                    } else {
                      return const SizedBox.shrink();
                    }
                  },
                ),
              ],
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
          );
        },
      ),
    );
  }

  List<String> genders = ["Male".tr, "Female".tr, "All genders".tr];
}

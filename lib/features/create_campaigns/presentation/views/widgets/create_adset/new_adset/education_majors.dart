import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../../widgets/loading_widget.dart';
import '../../../../../../../widgets/svg_widget.dart';
import '../../../../../../../widgets/text_field_widget.dart';
import '../../../../controllers/create_ad/create_ad_cubit.dart';
import '../../../../controllers/search/search_cubit.dart';

class EducationMajorsScreen extends StatefulWidget {
  const EducationMajorsScreen({super.key});

  @override
  State<EducationMajorsScreen> createState() => _EducationMajorsScreenState();
}

class _EducationMajorsScreenState extends State<EducationMajorsScreen> {
  var searchEducationController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SearchCubit(),
      child: Scaffold(
        appBar: const CustomAppBar(
          title: "Education Majors",
          showBackButton: true,
          hasDrawer: true,
        ),
        body: BlocBuilder<SearchCubit, SearchState>(
          builder: (context, state) {
            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Container(
                      //    width: 125.w,
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(37),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 20,
                            offset: Offset(0, 0),
                            spreadRadius: -4,
                          )
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              borderColor: Colors.white,
                              radius: 37,
                              controller: searchEducationController,
                              radiusOnly: true,
                              hintText: "Search for ...",
                              hintStyle: const TextStyle(fontSize: 14),
                              icon: ShaderMask(
                                shaderCallback: (Rect bounds) {
                                  return const LinearGradient(
                                    colors: [
                                      Color(0xFFFF006F),
                                      Color(0xFFF6BA00),
                                    ],
                                  ).createShader(bounds);
                                },
                                child: const Padding(
                                  padding: EdgeInsets.all(12.0),
                                  child: CustomSvgWidget(
                                      width: 13,
                                      height: 13,
                                      svg: AppAssets.search,
                                      color: Colors.white),
                                ),
                              ),
                              // TextField properties
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              searchEducationController.text.isEmpty
                                  ? null
                                  : SearchCubit.get(context).search(
                                      context: context,
                                      type: "adeducationmajor",
                                      keyword: searchEducationController.text);
                            },
                            child: Container(
                                width: 81.w,
                                padding: EdgeInsets.zero,
                                decoration: const ShapeDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment(-0.99, -0.10),
                                    end: Alignment(0.99, 0.1),
                                    colors: [
                                      Color(0xFF0B0F26),
                                      Color(0xFF1C4294)
                                    ],
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(100),
                                      bottomRight: Radius.circular(100),
                                    ),
                                  ),
                                ),
                                child: const Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 15.0, vertical: 15.0),
                                  child: CustomText(
                                      text: "Search",
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white),
                                )),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    searchEducationController.text.isEmpty
                        ? SizedBox(
                            height: 500.h,
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Lottie.asset(AppAssets.searchLo,
                                      width: 111.h, height: 112.h),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CustomText(
                                        text:
                                            'Search for  your  Education Majors',
                                        fontSize: 14.sp,
                                        color: AppColors.iconBottomColor,
                                        fontWeight: FontWeight.w400,
                                        alignment:
                                            AlignmentDirectional.centerStart,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox(),
                    if (state is SearchStateLoading)
                      SizedBox(
                          height: 500.h,
                          child: const Center(
                              child: LoadingWidget(isCircle: true)))
                    else if (state is SearchStateError)
                      SizedBox(
                        height: 500.h,
                        child: Center(
                          child: HandleErrorWidget(
                              fun: () {
                                SearchCubit.get(context).search(
                                    context: context,
                                    type: "adeducationmajor",
                                    keyword: searchEducationController.text);
                              },
                              failure: state.message),
                        ),
                      )
                    else if (state is SearchStateLoaded)
                      BlocBuilder<CreateAdCubit, CreateAdState>(
                        builder: (context1, adState) {
                          return Column(
                            children: [
                              CustomText(
                                text: 'Search Result (${state.data.length})',
                                fontSize: 16.sp,
                                color: AppColors.iconBottomColor,
                                fontWeight: FontWeight.w400,
                                alignment: AlignmentDirectional.centerStart,
                              ),
                              SizedBox(
                                height: 20.h,
                              ),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.zero,
                                itemCount: state.data.length,
                                itemBuilder: (item, index) {
                                  return (!CreateAdCubit.get(context1)
                                          .majors
                                          .contains(state.data[index]))
                                      ? InkWell(
                                          onTap: () {
                                            CreateAdCubit.get(context1)
                                                .setSelectedMajors(
                                                    state.data[index]);
                                            CreateAdCubit.get(context1)
                                                .updateTargetingProcess4();
                                            CreateAdCubit.get(context1)
                                                .updateAdSetProcess7();
                                          },
                                          child: Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                    child: CustomText(
                                                      text: state.data[index]
                                                              .name ??
                                                          "",
                                                      color:
                                                          Constants.darkColor,
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 5),
                                                  Container(
                                                    height: 22,
                                                    width: 22,
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.rectangle,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5.r),
                                                      border: Border.all(
                                                        color:
                                                            Constants.textColor,
                                                        width: 2.w,
                                                        style:
                                                            BorderStyle.solid,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 5),
                                              const Divider(
                                                  color: Constants.gray),
                                            ],
                                          ),
                                        )
                                      : Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 10.0, left: 8),
                                          child: InkWell(
                                            onTap: () {
                                              CreateAdCubit.get(context1)
                                                  .setSelectedMajors(
                                                      state.data[index]);
                                              CreateAdCubit.get(context1)
                                                  .undoTargetingProcess4();
                                              CreateAdCubit.get(context1)
                                                  .updateAdSetProcess7();
                                            },
                                            child: Container(
                                              alignment: Alignment.center,
                                              padding: EdgeInsets.zero,
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8)),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x33000000),
                                                    blurRadius: 20,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: -6,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10.0,
                                                        vertical: 12),
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Expanded(
                                                      child: Center(
                                                        child: ShaderMask(
                                                          shaderCallback:
                                                              (Rect bounds) {
                                                            return const LinearGradient(
                                                              colors: [
                                                                Color(
                                                                    0xFFFF006F),
                                                                Color(
                                                                    0xFFF6BA00),
                                                              ],
                                                            ).createShader(
                                                                bounds);
                                                          },
                                                          child: CustomText(
                                                            text: state
                                                                    .data[index]
                                                                    .name ??
                                                                "",
                                                            color: Colors.white,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 5),
                                                    ShaderMask(
                                                      shaderCallback:
                                                          (Rect bounds) {
                                                        return const LinearGradient(
                                                          colors: [
                                                            Color(0xFFFF006F),
                                                            Color(0xFFF6BA00),
                                                          ],
                                                        ).createShader(bounds);
                                                      },
                                                      child: const Icon(
                                                        Icons.check_box,
                                                        size: 28,
                                                        color: Colors.white,
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                },
                              ),
                            ],
                          );
                        },
                      ),
                   BlocBuilder<CreateAdCubit, CreateAdState>(
                     builder: (context2, state) {
                       return  CreateAdCubit.get(context2).majors.isEmpty
                           ? const SizedBox()
                           : Column(
                         children: [
                           CustomText(
                             text: 'Selected Educational Majors',
                             fontSize: 14.sp,
                             color: AppColors.iconBottomColor,
                             fontWeight: FontWeight.w400,
                             alignment: AlignmentDirectional.centerStart,
                           ),
                           SizedBox(
                             height: 10.h,
                           ),
                           ListView.builder(
                             shrinkWrap: true,
                             physics:
                                 const NeverScrollableScrollPhysics(),
                             padding: EdgeInsets.zero,
                             itemCount: CreateAdCubit.get(context2)
                                 .majors
                                 .length,
                             itemBuilder: (item, index) {
                               return InkWell(
                                 onTap: () {
                                   print("asda${CreateAdCubit.get(context2)
                                           .majors}");
                                   CreateAdCubit.get(context2)
                                       .setSelectedMajors(
                                           CreateAdCubit.get(context2)
                                               .majors[index]);
                                   CreateAdCubit.get(context2)
                                       .undoTargetingProcess4();
                                   CreateAdCubit.get(context2)
                                       .updateAdSetProcess7();
                                 },
                                 child: Padding(
                                   padding: const EdgeInsets.only(
                                       bottom: 10.0, left: 8),
                                   child: Container(
                                     alignment: Alignment.center,
                                     padding: EdgeInsets.zero,
                                     decoration: ShapeDecoration(
                                       color: Colors.white,
                                       shape: RoundedRectangleBorder(
                                           borderRadius:
                                               BorderRadius.circular(8)),
                                       shadows: const [
                                         BoxShadow(
                                           color: Color(0x33000000),
                                           blurRadius: 20,
                                           offset: Offset(0, 0),
                                           spreadRadius: -6,
                                         )
                                       ],
                                     ),
                                     child: Padding(
                                       padding:
                                           const EdgeInsets.symmetric(
                                               horizontal: 10.0,
                                               vertical: 12),
                                       child: Row(
                                         crossAxisAlignment:
                                             CrossAxisAlignment.center,
                                         mainAxisAlignment:
                                             MainAxisAlignment
                                                 .spaceBetween,
                                         children: [
                                           Expanded(
                                             child: Center(
                                               child: ShaderMask(
                                                 shaderCallback:
                                                     (Rect bounds) {
                                                   return const LinearGradient(
                                                     colors: [
                                                       Color(0xFFFF006F),
                                                       Color(0xFFF6BA00),
                                                     ],
                                                   ).createShader(
                                                       bounds);
                                                 },
                                                 child: CustomText(
                                                   text: CreateAdCubit.get(
                                                               context2)
                                                           .majors[index]
                                                           .name ??
                                                       "",
                                                   color: Colors.white,
                                                   fontSize: 14,
                                                   fontWeight:
                                                       FontWeight.w400,
                                                 ),
                                               ),
                                             ),
                                           ),
                                           const SizedBox(width: 5),
                                           ShaderMask(
                                             shaderCallback:
                                                 (Rect bounds) {
                                               return const LinearGradient(
                                                 colors: [
                                                   Color(0xFFFF006F),
                                                   Color(0xFFF6BA00),
                                                 ],
                                               ).createShader(bounds);
                                             },
                                             child: const Icon(
                                               Icons.check_box,
                                               size: 28,
                                               color: Colors.white,
                                             ),
                                           )
                                         ],
                                       ),
                                     ),
                                   ),
                                 ),
                               );
                             },
                           ),
                         ],
                       );
                     },
                   ),
                    const SizedBox(height: 25),
                    SizedBox(
                      width: 235.w,
                      child: ButtonWidget(
                        text: "Save",
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

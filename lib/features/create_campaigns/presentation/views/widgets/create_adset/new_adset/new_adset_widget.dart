import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_reach_estimate/reach_estimate_cubit.dart';
import 'package:ads_dv/features/maps/presentation/controllers/select_map_cubit.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/controllers/get_add_accounts/get_add_accounts_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/utils/res/router/routes.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/circular_percent_indicator_widget.dart';
import '../../../../../../../widgets/custom_switch.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../../../../../widgets/text_field_widget.dart';
import '../../../../../../../widgets/unfinished_target_widget.dart';
import '../../estimated_card_widget.dart';

class NewAdSetWidget extends StatefulWidget {
  const NewAdSetWidget({super.key});

  @override
  State<NewAdSetWidget> createState() => _NewAdSetWidgetState();
}

class _NewAdSetWidgetState extends State<NewAdSetWidget> {
  FocusNode dailyBudgetNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => SelectMapCubit(),
        ),
        BlocProvider(
          create: (context) =>
              GetAdAccountsCubit()..getAdAccounts(context: context),
        ),
      ],
      child: BlocBuilder<CreateAdCubit, CreateAdState>(
        builder: (context, state) {
          return Column(
            children: [
              const SizedBox(height: 20),
              Form(
                key: CreateAdCubit.get(context).adSetFormKey,
                child: CustomTextFormField(
                  controller: CreateAdCubit.get(context).adSetNameController,
                  textFontSize: 12,
                  key: const ValueKey('adset_name'),
                  hintText: "Ad Set Name".tr,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.text,
                  validator: (value) =>
                      AppValidator.validateIdentity(value, context),
                  onChanged: (val) {
                    if (CreateAdCubit.get(context)
                        .adSetNameController
                        .text
                        .isNotEmpty) {
                      CreateAdCubit.get(context).updateAdSetProcess1();
                    } else {
                      CreateAdCubit.get(context).undoAdSetProcess1();
                    }
                  },
                ),
              ),
              const SizedBox(height: 20),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Divider(color: Constants.textColor),
              ),
              const SizedBox(height: 5),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CustomText(
                      text: 'Status'.tr,
                      fontSize: 12.sp,
                      color: Constants.primaryTextColor,
                      fontWeight: FontWeight.w400,
                      alignment: AlignmentDirectional.centerStart,
                    ),
                    CustomSwitch(
                      value: CreateAdCubit.get(context).isAdSetActive,
                      onChanged: (newValue) {
                        CreateAdCubit.get(context).updateAdSetProcess2();
                        CreateAdCubit.get(context).changeAdSetStatus(newValue);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 5),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Divider(color: Constants.textColor),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: CustomText(
                  text: 'Platform'.tr,
                  color: Constants.primaryTextColor,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 10),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.fbFill,
                          height: 30.h,
                          width: 30.h,
                        ),
                        10.horizontalSpace,
                        CustomText(
                          text: 'Facebook'.tr,
                          color: Constants.primaryTextColor,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w700,
                        )
                      ],
                    ),
                    CustomSwitch(
                      value: CreateAdCubit.get(context).fbPlatformActive,
                      onChanged: (newValue) {
                        CreateAdCubit.get(context)
                            .fbPlatformStatusStatus(newValue);
                        print("kakfkajkl${CreateAdCubit.get(context)
                                .publisherPlatforms}");
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Visibility(
                visible: CreateAdCubit.get(context).fbPlatformActive,
                child: Column(
                  children: [
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 4,
                        crossAxisSpacing: 12.0,
                        mainAxisSpacing: 12.0,
                      ),
                      itemBuilder: (item, index) {
                        return Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: const BorderSide(
                                  width: 1, color: Color(0xFF06398A)),
                              borderRadius: BorderRadius.circular(50),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: SizedBox(
                                    width: 200,
                                    child: FittedBox(
                                      child: CustomText(
                                        text: CreateAdCubit.get(context)
                                                .facebookPositions[index]
                                                .name
                                                ?.tr ??
                                            "",
                                        color: Constants.primaryTextColor,
                                        alignment: AlignmentDirectional.center,
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Checkbox(
                                onChanged: (value) {
                                  CreateAdCubit.get(context)
                                          .facebookPositions[index]
                                          .isChecked =
                                      value!; // Toggle the checkbox value

                                  CreateAdCubit.get(context)
                                      .setSelectedFbPositions();
                                  print("fbPositions${CreateAdCubit.get(context)
                                          .fbPositions}");
                                },
                                value: CreateAdCubit.get(context)
                                    .facebookPositions[index]
                                    .isChecked,
                                activeColor: Constants.primaryTextColor,
                                checkColor: Colors.white,
                                // Color of the checkmark
                                focusColor: Constants.primaryTextColor,
                                // Color of the border when focused
                                side: const BorderSide(
                                  color: Constants.primaryTextColor,
                                  width: 2,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      itemCount:
                          CreateAdCubit.get(context).facebookPositions.length,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                height: 50,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.inFill,
                          height: 30.h,
                          width: 30.h,
                        ),
                        10.horizontalSpace,
                        CustomText(
                          text: 'Instagram'.tr,
                          color: Constants.primaryTextColor,
                          fontSize: 14.sp,
                          alignment: AlignmentDirectional.center,
                          fontWeight: FontWeight.w700,
                        )
                      ],
                    ),
                    CustomSwitch(
                      value: CreateAdCubit.get(context).instagramPlatformActive,
                      onChanged: (newValue) {
                        CreateAdCubit.get(context)
                            .instagramPlatformStatusStatus(newValue);
                        print(
                            "kakfkajkl${CreateAdCubit.get(context).adModel.instAccId}");
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Visibility(
                visible: CreateAdCubit.get(context).instagramPlatformActive,
                child: Column(
                  children: [
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 4,
                        crossAxisSpacing: 12.0,
                        mainAxisSpacing: 12.0,
                      ),
                      itemBuilder: (item, index) {
                        return Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: const BorderSide(
                                  width: 1, color: Color(0xFF06398A)),
                              borderRadius: BorderRadius.circular(50),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: SizedBox(
                                    width: 200,
                                    child: FittedBox(
                                      child: CustomText(
                                        text: CreateAdCubit.get(context)
                                                .instagramPositions[index]
                                                .name
                                                ?.tr ??
                                            "",
                                        color: Constants.primaryTextColor,
                                        alignment: AlignmentDirectional.center,
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Checkbox(
                                onChanged: (value) {
                                  CreateAdCubit.get(context)
                                          .instagramPositions[index]
                                          .isChecked =
                                      value!; // Toggle the checkbox value
                                  CreateAdCubit.get(context)
                                      .setSelectedInstagramPositions();
                                  print("igPositions${CreateAdCubit.get(context)
                                          .igPositions}");
                                },
                                value: CreateAdCubit.get(context)
                                    .instagramPositions[index]
                                    .isChecked,
                                activeColor: Constants.primaryTextColor,
                                checkColor: Colors.white,
                                // Color of the checkmark
                                focusColor: Constants.primaryTextColor,
                                // Color of the border when focused
                                side: const BorderSide(
                                  color: Constants.primaryTextColor,
                                  width: 2,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      itemCount:
                          CreateAdCubit.get(context).instagramPositions.length,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(width: 1, color: Color(0xFF0B0F26)),
                    borderRadius: BorderRadius.circular(50),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        CustomSvgWidget(
                          svg: AppAssets.meFill,
                          height: 30.h,
                          width: 30.h,
                        ),
                        10.horizontalSpace,
                        CustomText(
                          text: 'Messenger'.tr,
                          color: Constants.primaryTextColor,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w700,
                        )
                      ],
                    ),
                    CustomSwitch(
                      value: CreateAdCubit.get(context).messengerPlatformActive,
                      onChanged: (newValue) {
                        CreateAdCubit.get(context)
                            .messengerPlatformStatusStatus(newValue);
                        print("kakfkajkl${CreateAdCubit.get(context)
                                .publisherPlatforms}");
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Visibility(
                visible: CreateAdCubit.get(context).messengerPlatformActive,
                child: Column(
                  children: [
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 4,
                        crossAxisSpacing: 12.0,
                        mainAxisSpacing: 12.0,
                      ),
                      itemBuilder: (item, index) {
                        return Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: const BorderSide(
                                  width: 1, color: Color(0xFF06398A)),
                              borderRadius: BorderRadius.circular(50),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(3.0),
                                  child: SizedBox(
                                    width: 200,
                                    child: FittedBox(
                                      child: CustomText(
                                        text: CreateAdCubit.get(context)
                                                .messengerPositions[index]
                                                .name
                                                ?.tr ??
                                            "",
                                        color: Constants.primaryTextColor,
                                        alignment: AlignmentDirectional.center,
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Checkbox(
                                onChanged: (value) {
                                  CreateAdCubit.get(context)
                                          .messengerPositions[index]
                                          .isChecked =
                                      value!; // Toggle the checkbox value
                                  CreateAdCubit.get(context)
                                      .setSelectedMessengerPositions();
                                  print("mPositions${CreateAdCubit.get(context)
                                          .mPositions}");
                                },
                                value: CreateAdCubit.get(context)
                                    .messengerPositions[index]
                                    .isChecked,
                                activeColor: Constants.primaryTextColor,
                                checkColor: Colors.white,
                                // Color of the checkmark
                                focusColor: Constants.primaryTextColor,
                                // Color of the border when focused
                                side: const BorderSide(
                                  color: Constants.primaryTextColor,
                                  width: 2,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      itemCount:
                          CreateAdCubit.get(context).messengerPositions.length,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Divider(color: Constants.textColor),
              ),
              const SizedBox(height: 10),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, Routes.map, arguments: {
                    "cubit": CreateAdCubit.get(context),
                    "isFromSnapChat": false
                  });
                },
                child: UnFinishedTargetWidget(
                  name: "Location".tr,
                  icon: AppAssets.location,
                  processPercentage: CircularIndicatorWidget(
                    isDemographic: false,
                    isLocation: true,
                    isTargeting: false,
                    isAdSet: false,
                    isAdCreative: false,
                    adCubit: CreateAdCubit.get(context),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    Routes.details,
                  );
                },
                child: UnFinishedTargetWidget(
                  name: "Detailed Targeting".tr,
                  icon: AppAssets.target,
                  processPercentage: CircularIndicatorWidget(
                    isDemographic: false,
                    isLocation: false,
                    isTargeting: true,
                    isAdSet: false,
                    isAdCreative: false,
                    adCubit: CreateAdCubit.get(context),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, Routes.demo,
                      arguments: CreateAdCubit.get(context));
                },
                child: UnFinishedTargetWidget(
                  name: 'Demographic'.tr,
                  icon: AppAssets.demo,
                  processPercentage: CircularIndicatorWidget(
                    isDemographic: true,
                    isLocation: false,
                    isTargeting: false,
                    isAdSet: false,
                    isAdCreative: false,
                    adCubit: CreateAdCubit.get(context),
                  ),
                ),
              ),
              SizedBox(height: 40.h),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //   children: destinationType.map((e) {
              //
              //      CreateAdCubit.get(context).destinationIndex = destinationType.indexOf(e);
              //      CreateAdCubit.get(context).isSelectedDestination= CreateAdCubit.get(context).destinationIndex == CreateAdCubit.get(context).selectedDestinationIndex;
              //
              //
              //     return DestinationTypeWidget(
              //       isSelected: CreateAdCubit.get(context).isSelectedDestination ?? false,
              //       callback: (index) {
              //         CreateAdCubit.get(context).updateAdSetProcess3();
              //
              //         setState(() {
              //
              //           CreateAdCubit.get(context).selectedDestinationIndex = index;
              //
              //           // Update destination type based on index
              //           if (e == "Whatsapp") {
              //             CreateAdCubit.get(context).destinationType =
              //                 "WHATSAPP";
              //           } else if (e == "Messenger") {
              //             CreateAdCubit.get(context).destinationType =
              //                 "MESSENGER";
              //           } else {
              //             CreateAdCubit.get(context).destinationType =
              //                 "INSTAGRAM_DIRECT";
              //           }
              //           print("INSTAGRAM" +
              //               CreateAdCubit.get(context).destinationType);
              //         });
              //       },
              //       name: e,
              //       index: CreateAdCubit.get(context).destinationIndex ?? 0,
              //     );
              //   }).toList(),
              // ),
              // SizedBox(height: 40.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: "Daily Budget".tr,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: Constants.primaryTextColor,
                  ),
                  Container(
                    width: 125.w,
                    height: 42.h,
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x33000000),
                          blurRadius: 20,
                          offset: Offset(0, 0),
                          spreadRadius: -4,
                        )
                      ],
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 80.w,
                          child: CustomTextField(
                            textInputType: TextInputType.number,
                            borderColor: Colors.transparent,
                            hintText: "0",
                            maxLength: 5,
                            hintStyle: const TextStyle(
                                fontSize: 16, color: Constants.gray),
                            controller: CreateAdCubit.get(context).dailyBudget,
                            onChanged: (val) {
                              if (CreateAdCubit.get(context)
                                  .dailyBudget
                                  .text
                                  .isNotEmpty) {
                                CreateAdCubit.get(context)
                                    .updateAdSetProcess4();

                                if (val.length > 1) {
                                  CreateAdCubit.get(context).adModel =
                                      CreateAdCubit.get(context)
                                          .adModel
                                          .copyWith(
                                            dailyBudget: (double.parse(
                                                        CreateAdCubit.get(
                                                                context)
                                                            .dailyBudget
                                                            .text) *
                                                    100)
                                                .toInt(), // Convert to int if needed for API
                                          );
                                }
                              } else {
                                CreateAdCubit.get(context).undoAdSetProcess4();

                                if (val.length > 1) {
                                  CreateAdCubit.get(context).adModel =
                                      CreateAdCubit.get(context)
                                          .adModel
                                          .copyWith(
                                            dailyBudget: (double.parse(
                                                        CreateAdCubit.get(
                                                                context)
                                                            .dailyBudget
                                                            .text) *
                                                    100)
                                                .toInt(),
                                          );
                                }
                              }
                            },
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                          ),
                        ),
                        const VerticalDivider(
                          width: 2,
                          color: Constants.primaryTextColor,
                        ),
                        BlocBuilder<GetAdAccountsCubit, GetAdAccountsState>(
                          builder: (adContext, adState) {
                            return SizedBox(
                                width: 40.w,
                                child: FittedBox(
                                  child: Padding(
                                    padding: const EdgeInsets.all(3.0),
                                    child: CustomText(
                                      text: GetAdAccountsCubit.get(adContext)
                                              .adAccounts
                                              .where((element) =>
                                                  element.id ==
                                                  instance<HiveHelper>()
                                                      .getUser()
                                                      ?.defaultAccountId)
                                              .isEmpty
                                          ? "AED"
                                          : GetAdAccountsCubit.get(adContext)
                                                  .adAccounts
                                                  .where((element) =>
                                                      element.id ==
                                                      instance<HiveHelper>()
                                                          .getUser()
                                                          ?.defaultAccountId)
                                                  .first
                                                  .currency ??
                                              "",
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w400,
                                      color: Constants.primaryTextColor,
                                    ),
                                  ),
                                ));
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15.h),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Divider(color: Constants.textColor),
              ),
              SizedBox(height: 15.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      CustomText(
                        text: "Start Date".tr,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        color: Constants.primaryTextColor,
                      ),
                      10.horizontalSpace,
                      GestureDetector(
                        onTap: () {
                          dailyBudgetNode.unfocus();
                          if (!dailyBudgetNode.hasFocus) {
                            ReachEstimateCubit.get(context).getReachEstimate(
                                context: context,
                                imagesFiles:
                                    CreateAdCubit.get(context).adImages,
                                videosFiles:
                                    CreateAdCubit.get(context).adVideo);
                          }
                          _showDatePicker(context,
                              CreateAdCubit.get(context).startDate, true);
                        },
                        child: AbsorbPointer(
                          child: Container(
                            width: 80.w,
                            height: 40.h,
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x33000000),
                                  blurRadius: 20,
                                  offset: Offset(0, 0),
                                  spreadRadius: -4,
                                )
                              ],
                            ),
                            child: CustomTextField(
                              onSaved: (val) {
                                CreateAdCubit.get(context)
                                    .updateAdSetProcess5();
                              },
                              validator: (value) =>
                                  AppValidator.validateIdentity(value, context),
                              textInputType: TextInputType.number,
                              borderColor: Colors.transparent,
                              hintText: "dd/mm/yy",
                              style: const TextStyle(
                                  fontSize: 10, color: Colors.black),
                              hintStyle: const TextStyle(
                                  fontSize: 12, color: Constants.gray),
                              controller: CreateAdCubit.get(context).startDate,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      CustomText(
                        text: "End Date".tr,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        color: Constants.primaryTextColor,
                      ),
                      10.horizontalSpace,
                      GestureDetector(
                        onTap: () {
                          _showDatePicker(context,
                              CreateAdCubit.get(context).endDate, false);
                        },
                        child: AbsorbPointer(
                          child: Container(
                            width: 80.w,
                            height: 40.h,
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x33000000),
                                  blurRadius: 20,
                                  offset: Offset(0, 0),
                                  spreadRadius: -4,
                                )
                              ],
                            ),
                            child: CustomTextField(
                              textInputType: TextInputType.number,
                              borderColor: Colors.transparent,

                              hintText: "dd/mm/yy",
                              style: const TextStyle(
                                  fontSize: 10, color: Colors.black),
                              hintStyle: const TextStyle(
                                  fontSize: 12, color: Constants.gray),
                              controller: CreateAdCubit.get(context).endDate,
                              validator: (value) =>
                                  AppValidator.validateIdentity(value, context),
                              // TextField properties
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 15.h),
              // const Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 8.0),
              //   child: Divider(color: Constants.textColor),
              // ),
              // SizedBox(height: 15.h),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     CustomText(
              //       text: "Total Budget",
              //       fontSize: 16.sp,
              //       fontWeight: FontWeight.w400,
              //       color: Constants.primaryTextColor,
              //     ),
              //     Container(
              //       padding: EdgeInsets.zero,
              //       width: 125.w,
              //       // height: 42.h,
              //       decoration: ShapeDecoration(
              //         color: Constants.gray.withOpacity(0.3),
              //         shape: RoundedRectangleBorder(
              //           borderRadius: BorderRadius.circular(12),
              //         ),
              //       ),
              //       child: Padding(
              //         padding: EdgeInsets.symmetric(
              //             vertical: 15.sp, horizontal: 20.sp),
              //         child: Row(
              //           mainAxisAlignment: MainAxisAlignment.center,
              //           children: [
              //             BlocBuilder<GetAdAccountsCubit, GetAdAccountsState>(
              //               builder: (adContext, state) {
              //                 return CustomText(
              //                   text:
              //                       "${CreateAdCubit.get(context).dailyBudget.text} ${GetAdAccountsCubit.get(adContext).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).isEmpty ? "AED" : GetAdAccountsCubit.get(adContext).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.currency ?? ""}",
              //                   fontSize: 14.sp,
              //                   fontWeight: FontWeight.w400,
              //                   color: Constants.primaryTextColor,
              //                 );
              //               },
              //             ),
              //           ],
              //         ),
              //       ),
              //     ),
              //   ],
              // ),
              // const SizedBox(height: 20),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Divider(color: Constants.textColor),
              ),
              const SizedBox(height: 5),
              BlocBuilder<ReachEstimateCubit, ReachEstimateState>(
                builder: (context, state) {
                  if (state is ReachStateLoaded) {
                    return EstimatedCardWidget(
                      upperNum: state.reachEstimatedModel?.usersUpperBound
                              ?.toDouble() ??
                          0.0,
                      lowerNum: state.reachEstimatedModel?.usersLowerBound
                              ?.toDouble() ??
                          0.0,
                      isDemoGraphic: false,
                    );
                  } else {
                    return const SizedBox();
                  }
                },
              ),
              // SizedBox(height: 40.h),
              SizedBox(
                width: 235.w,
                child: ButtonWidget(
                  text: "Save".tr,
                  onTap: () {
                    // Validate the form
                    if (CreateAdCubit.get(context)
                        .adSetFormKey
                        .currentState!
                        .validate()) {
                      // If the form is valid, check required fields
                      if (areRequiredFieldsValid()) {
                        // Proceed with model update if all fields are valid
                        if (CreateAdCubit.get(context)
                            .dailyBudget
                            .text
                            .contains('.')) {
                          showErrorToast('Not accept decimal in daily budget');
                          return;
                        }
                        if (CreateAdCubit.get(context)
                            .selectedInterests
                            .isEmpty) {
                          showErrorToast(
                              "Please select your detailed targeting");
                          return;
                        }
                        if (CreateAdCubit.get(context).selectedMinAge == null &&
                            CreateAdCubit.get(context).selectedMaxAge == null &&
                            CreateAdCubit.get(context).languages.isEmpty &&
                            CreateAdCubit.get(context).genders.isEmpty) {
                          showErrorToast("Please select your demographic");
                          return;
                        }
                        if (CreateAdCubit.get(context)
                            .endDate
                            .text
                            .isNotEmpty) {
                          if (DateTime.tryParse(
                                  CreateAdCubit.get(context).endDate.text)!
                              .isBefore(DateTime.tryParse(
                                  CreateAdCubit.get(context)
                                      .startDate
                                      .text)!)) {
                            showErrorToast(
                                'The end date should be after start date');
                            return;
                          }
                        }

                        if (CreateAdCubit.get(context)
                            .publisherPlatforms
                            .isEmpty) {
                          showErrorToast('Choose a platform');
                          return;
                        }
                        updateAdModel();
                        CreateAdCubit.get(context).isAdSetCreated = true;
                        Constants.adSetExpansionTileKey.currentState
                            ?.collapse();
                        print("Geo Locations: ${CreateAdCubit.get(context)
                                .adModel
                                .geoLocations}");
                      }
                    } else {
                      // Show error toast if the form is invalid
                      showErrorToast(
                          "Please fill in all the required fields correctly.");
                    }
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  bool areRequiredFieldsValid() {
    final cubit = CreateAdCubit.get(context);

    if (cubit.dailyBudget.text.isEmpty) {
      showErrorToast("Please enter your budget".tr);
      return false;
    } else if (cubit.adSetNameController.text.isEmpty) {
      showErrorToast("Please enter ad set name".tr);
      return false;
    } else if (cubit.startDate.text.isEmpty) {
      showErrorToast("Please enter start date".tr);
      return false;
    } else if (cubit.geoLocations.isEmpty) {
      showErrorToast("Please select target location".tr);
      return false;
    }
    return true;
  }

  void updateAdModel() {
    final cubit = CreateAdCubit.get(context);
    cubit.adModel = cubit.adModel.copyWith(
      // publisherPlatforms: cubit.publisherPlatforms,
      // facebookPositions: cubit.fbPositions,
      // instagramPositions: cubit.igPositions,
      // messengerPositions: cubit.mPositions,
      adSetName: cubit.adSetNameController.text,
      dailyBudget: int.parse(cubit.dailyBudget.text) * 100,
      // Budget in cents
      startDate: cubit.startDate.text,
      endDate: cubit.endDate.text,
      bidAmount: 2,
      // Default bid amount, could be dynamic
      geoLocations: cubit.geoLocations,
    );
  }

  void _showDatePicker(BuildContext context, TextEditingController controller,
      bool isStartDate) async {
    final DateTime? startDate = await showDatePicker(
      context: context,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Constants.primaryTextColor, // header background color
              onPrimary: Colors.white, // header text color
              onSurface: Constants.primaryTextColor, // body text color
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor:
                    Constants.primaryTextColor, // button text color
              ),
            ),
          ),
          child: child ?? const Text(""),
        );
      },
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (startDate != null) {
      // Format the pickedDate as desired (e.g., "yyyy-MM-dd")
      final formattedDate = DateFormat("yyyy-MM-dd").format(startDate);

      // Check if the picked date is before the delivery date
      // if (CreateAdCubit.get(context).endDate.text.isNotEmpty) {
      //   final deliveryDate = DateFormat("yyyy-MM-dd")
      //       .parse(CreateAdCubit.get(context).endDate.text);
      //   if (startDate.isBefore(deliveryDate)) {
      //     showDialog(
      //       context: context,
      //       builder: (BuildContext context) {
      //         return AlertDialog(
      //           title: const Text("Invalid Date"),
      //           content: const Text("End Date can't be before start date"),
      //           actions: [
      //             TextButton(
      //               child: const Text("OK"),
      //               onPressed: () {
      //                 Navigator.of(context).pop();
      //               },
      //             ),
      //           ],
      //         );
      //       },
      //     );
      //     return;
      //   }
      // }

      controller.text = formattedDate;
      if (isStartDate) {
        CreateAdCubit.get(context).updateAdSetProcess5();
      } else {
        CreateAdCubit.get(context).updateAdSetProcess6();
      }
    }
  }
}

List<String> destinationType = ["Whatsapp", "Messenger", "Instagram"];

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../controllers/get_adsets/get_adsets_cubit.dart';

class ExistingAdSetWidget extends StatefulWidget {
  CreateAdCubit createAdCubit;

  ExistingAdSetWidget({super.key, required this.createAdCubit});

  @override
  State<ExistingAdSetWidget> createState() => _ExistingAdSetWidgetState();
}

class _ExistingAdSetWidgetState extends State<ExistingAdSetWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetAdSetCubit()
        ..getAdSet(
            context: context,
            campaignId: widget.createAdCubit.existingCampaign?.id ?? "",
            pageAccessToken:
                instance<HiveHelper>().getUser()?.defaultPageAccessToken ??
                    widget.createAdCubit.metaPages?.accessToken ??
                    ""),
      child: BlocBuilder<GetAdSetCubit, GetAdSetState>(
        builder: (context, state) {
          if (state is GetAdSetStateLoading) {
            return const Column(
              children: [
                SizedBox(height: 20),
                LoadingWidget(isCircle: true),
              ],
            );
          } else if (state is GetAdSetStateError) {
            return Column(
              children: [
                const SizedBox(height: 20),
                HandleErrorWidget(
                    fun: () {
                      GetAdSetCubit.get(context).getAdSet(
                          context: context,
                          campaignId:
                              widget.createAdCubit.existingCampaign?.id ?? "",
                          pageAccessToken: instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultPageAccessToken ??
                              widget.createAdCubit.metaPages?.accessToken ??
                              "");
                    },
                    failure: state.message),
              ],
            );
          } else if (state is GetAdSetStateLoaded) {
            return Column(
              children: [
                const SizedBox(height: 40),
                GetAdSetCubit.get(context).adSets!.isEmpty
                    ? const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(height: 20),
                          CustomText(
                            text: "There is no existing adsets",
                            color: Constants.gray,
                            alignment: AlignmentDirectional.center,
                          ),
                        ],
                      )
                    : Column(children: [
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          itemCount:
                              GetAdSetCubit.get(context).adSets?.length ?? 0,
                          itemBuilder: (context, index) {
                            final adSet =
                                GetAdSetCubit.get(context).adSets?[index];
                            final isSelected =
                                widget.createAdCubit.selectedExistingAdSet ==
                                    index;

                            return InkWell(
                              onTap: () {
                                widget.createAdCubit
                                    .selectExistingAdSet(index, adSet!);
                                widget.createAdCubit.adSetPercentage = 1.0;
                                setState(() {});
                              },
                              child: isSelected
                                  ? _buildSelectedAdSet(adSet)
                                  : _buildUnselectedAdSet(adSet),
                            );
                          },
                        ),
                        if (state.data.result?.next != null)
                          InkWell(
                            onTap: () {
                              GetAdSetCubit.get(context).loadMoreAdSet(
                                  url: state.data.result!.next!,
                                  context: context);
                            },
                            child: Container(
                              decoration: ShapeDecoration(
                                gradient: Constants.defGradient,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(38),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x19000000),
                                    blurRadius: 22,
                                    offset: Offset(0, 4),
                                    spreadRadius: 0,
                                  )
                                ],
                              ),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 8.0, horizontal: 14.0),
                                child: Text(
                                  'Load More',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ]),
                SizedBox(height: 50.h),
                SizedBox(
                  width: 235.w,
                  child: ButtonWidget(
                    text: "Next",
                    onTap: () {
                      if (widget.createAdCubit.existingCampaign == null) {
                        showErrorToast("Please Select Adset");
                      } else {
                        widget.createAdCubit.adModel =
                            widget.createAdCubit.adModel.copyWith(
                          existAdSet: widget.createAdCubit.existingAdSet?.id,
                        );
                        CreateAdCubit.get(context).isAdSetCreated = true;
                        Constants.adSetExpansionTileKey.currentState
                            ?.collapse();
                        print(
                            "create campaign ${widget.createAdCubit.adModel.toJson()}");
                      }
                    },
                  ),
                ),
              ],
            );
          }

          return const SizedBox();
        },
      ),
    );
  }

  Widget _buildUnselectedAdSet(adSet) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(
              text: adSet?.name ?? "",
              color: Constants.darkColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            Container(
              height: 22,
              width: 22,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Constants.darkColor,
                  width: 1.5,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        const Divider(color: Constants.gray),
      ],
    );
  }

  Widget _buildSelectedAdSet(adSet) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0, left: 8),
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.zero,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          shadows: const [
            BoxShadow(
              color: Color(0x33000000),
              blurRadius: 20,
              offset: Offset(0, 0),
              spreadRadius: -6,
            )
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ShaderMask(
                shaderCallback: (Rect bounds) {
                  return const LinearGradient(
                    colors: [Color(0xFFFF006F), Color(0xFFF6BA00)],
                  ).createShader(bounds);
                },
                child: CustomText(
                  text: adSet?.name ?? "",
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
              ShaderMask(
                shaderCallback: (Rect bounds) {
                  return const LinearGradient(
                    colors: [Color(0xFFFF006F), Color(0xFFF6BA00)],
                  ).createShader(bounds);
                },
                child: const Icon(
                  Icons.check_circle,
                  size: 22,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

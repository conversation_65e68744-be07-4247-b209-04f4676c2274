import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:showcaseview/showcaseview.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../widgets/custom_text.dart';

class EstimatedCardWidget extends StatefulWidget {
  double lowerNum = 0;
  double upperNum = 0;
  bool isDemoGraphic = false;

  EstimatedCardWidget({super.key, required this.upperNum, required this.lowerNum, required this.isDemoGraphic});

  @override
  State<EstimatedCardWidget> createState() => _EstimatedCardWidgetState();
}

class _EstimatedCardWidgetState extends State<EstimatedCardWidget> {

  var format = NumberFormat("#,##0.00", "en_US");
  final GlobalKey _key = GlobalKey();

  double normalizeToPercentage(double value) {
    print('Value Before Normalization: $value');
    if (value > 1000000.0) {
      double normalizedValue = value / 1000000.0;
      print('Normalized Value (Large Scaling): $normalizedValue');
      return normalizedValue;
    } else if (value > 100000.0) {
      double normalizedValue = value / 100000.0;
      print('Normalized Value (Medium Scaling): $normalizedValue');
      return normalizedValue;
    } else {
      return value;
    }
  }

  @override
  Widget build(BuildContext context) {
    String formattedUpperNumber = format.format(widget.upperNum);
    String formattedLowerNumber = format.format(widget.lowerNum);
    double difference = widget.upperNum - widget.lowerNum;
    print('Difference Between Upper and Lower: $difference');

    double normalizedUpperNum = normalizeToPercentage(difference);

    normalizedUpperNum = normalizedUpperNum.clamp(0.0, 1.0);

    print('Clamped Normalized Value: $normalizedUpperNum');

    return Card(
      elevation: 0.0,
      color: Colors.transparent,
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 5.0.w, vertical: 10.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if(widget.isDemoGraphic == false)...[
              Row(
                children: [
                  CustomText(
                    text: 'Reach estimate'.tr,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: Constants.primaryTextColor,
                  ),
                  // Text(
                  //   'Reach estimate'.tr,
                  //   style: TextStyle(
                  //       fontSize: 16.sp,
                  //       fontWeight: FontWeight.w400,
                  //       color: AppColors.mainColor),
                  // ),
                  Showcase(
                    key: _key,
                    // targetBorderRadius: const BorderRadius.all(
                    //     Radius.elliptical(60, 80)),
                    // targetPadding: const EdgeInsets.symmetric(
                    //     vertical: 5, horizontal: 5),
                    // tooltipPosition: TooltipPosition.top,
                    description:
                    'The total number of people your ad will reach daily.'.tr,
                    scaleAnimationAlignment: Alignment.topCenter,
                    textColor: Colors.black,
                    child: InkWell(
                      onTap: () {
                        ShowCaseWidget.of(context)
                            .startShowCase([_key]);
                      },
                      child: Image.asset(AppAssets.info,height: 25.h,width: 25.w,),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.h),
              LinearPercentIndicator(
                barRadius: const Radius.circular(20.0),
                lineHeight: 20.h,
                padding: EdgeInsets.zero,
                linearGradient: Constants.secGradient,
                percent: normalizedUpperNum,
              ),
              SizedBox(height: 10.h),
            ],

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomText(
                        text: "$formattedLowerNumber - $formattedUpperNumber",
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.mainColor,
                        alignment: AlignmentDirectional.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}


import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/common_utils.dart';
import '../../../../../../widgets/cached__image.dart';
import '../../../../../../widgets/decoration_container.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../../../../widgets/uploaded_image_widget.dart';
import '../../../../../../widgets/video_player.dart';
import '../../../controllers/create_ad/create_ad_cubit.dart';

class ReviewAdWidget extends StatelessWidget {
  const ReviewAdWidget({super.key});

  @override
  Widget build(BuildContext context) {
    print('asdasdvcxnbzm,vmb ${CreateAdCubit.get(context).post?.mediaType}');
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      bloc: CreateAdCubit.get(context),
      builder: (ctx, state) {
        return DecoratedContainer(
          width: double.infinity,
          color: Colors.white,
          boxShadow: const BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 150,
            offset: Offset(0, 4),
            spreadRadius: 0,
          ),
          radius: 20.sp,
          widget: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.sp, horizontal: 30.sp),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset(
                          height: 24.h,
                          AppAssets.review,
                          color: AppColors.mainColor,
                        ),
                        10.horizontalSpace,
                        CustomText(
                            text: 'Ad Review'.tr,
                            color: AppColors.mainColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w700),
                      ],
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();

                        Constants.adExpansionTileKey.currentState?.expand();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 3),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 1, color: Color(0xFF0B0F26)),
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 2.0),
                              child: CustomText(
                                text: 'Change'.tr,
                                color: Constants.primaryTextColor,
                                fontSize: 10.sp,
                                alignment: AlignmentDirectional.center,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                15.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Ad Set Name".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    CustomText(
                      text: CreateAdCubit.get(context).adName.text,
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Ad Review".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ],
                ),
                12.verticalSpace,
                Container(
                  //height: 354.h,
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x26000000),
                        blurRadius: 38.90,
                        offset: Offset(0, 0),
                        spreadRadius: 0,
                      )
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      children: [
                        FittedBox(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(50.sp),
                                    child: CachedImageWidget(
                                      image: CreateAdCubit.get(context)
                                              .metaPages
                                              ?.profilePic ??
                                          CreateAdCubit.get(context)
                                              .accessedMetaPages
                                              ?.picture
                                              ?.data
                                              ?.url ??
                                          instance<HiveHelper>()
                                              .getUser()
                                              ?.pagePic ??
                                          "",
                                      height: 50.h,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CustomText(
                                        text: CreateAdCubit.get(context)
                                                .metaPages
                                                ?.name ??
                                            instance<HiveHelper>()
                                                .getUser()
                                                ?.pageName ??
                                            "",
                                        color: AppColors.blackColor,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      const SizedBox(height: 2),
                                      SizedBox(
                                        width: 60,
                                        child: FittedBox(
                                          child: CustomText(
                                            text: CreateAdCubit.get(context)
                                                    .startDate
                                                    .text
                                                    .isNotEmpty
                                                ? CommonUtils.convertDateString(
                                                    CreateAdCubit.get(context)
                                                        .startDate
                                                        .text)
                                                : "March 8, 2021",
                                            color: Constants.darkGray,
                                            fontSize: 8,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8.0, vertical: 16),
                                child: CustomSvgWidget(
                                    svg: AppAssets.dots,
                                    width: 16.w,
                                    height: 5.h),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 15),
                        CustomText(
                          text: CreateAdCubit.get(context)
                                  .adModel
                                  .description ??
                              CreateAdCubit.get(context).post?.caption ??
                              "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.",
                          color: AppColors.blackColor,
                          textAlign: TextAlign.right,
                          fontSize: 14,
                          maxLines: 3,
                          fontWeight: FontWeight.w400,
                        ),
                        const SizedBox(height: 15),
                        CreateAdCubit.get(context).adImages.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(5),
                                child: UploadedImageWidget(
                                  text: "",
                                  onPressed: () {},
                                  image:
                                      CreateAdCubit.get(context).adImages.first,
                                  height: 180.h,
                                ),
                              )
                            : CreateAdCubit.get(context).adVideo.isNotEmpty
                                ? CustomVideoPlayer.file(
                                    file: CreateAdCubit.get(context)
                                        .adVideo
                                        .first)
                                : CreateAdCubit.get(context).post != null
                                    ? (CreateAdCubit.get(context)
                                                    .post
                                                    ?.mediaType ==
                                                "IMAGE" ||
                                            CreateAdCubit.get(context)
                                                    .post
                                                    ?.mediaType ==
                                                "photo")
                                        ? ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            child: CachedImageWidget(
                                              image: CreateAdCubit.get(context)
                                                  .post
                                                  ?.mediaUrl,
                                              height: 180.h,
                                            ),
                                          )
                                        : (CreateAdCubit.get(context)
                                                        .post
                                                        ?.mediaType ==
                                                    "VIDEO" ||
                                                CreateAdCubit.get(context)
                                                        .post
                                                        ?.mediaType ==
                                                    "video")
                                            ? CustomVideoPlayer.network(
                                                videoUrl:
                                                    CreateAdCubit.get(context)
                                                            .post
                                                            ?.mediaUrl ??
                                                        "")
                                            : ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(7.sp),
                                                child: const CachedImageWidget(
                                                  assetsImage: AppAssets.post,
                                                ),
                                              )
                                    : const SizedBox(),
                        const SizedBox(height: 15),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CachedImageWidget(
                                  assetsImage: AppAssets.care,
                                  height: 15.h,
                                ),
                                CachedImageWidget(
                                  assetsImage: AppAssets.like,
                                  height: 15.h,
                                )
                              ],
                            ),
                            const CustomText(
                              text: "45 Comments",
                              color: Constants.darkGray,
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

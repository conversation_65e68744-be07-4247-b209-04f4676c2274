
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/media_query_config.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/uploaded_image_widget.dart';
import '../../../../../../widgets/video_player.dart';
import '../../../controllers/create_ad/create_ad_cubit.dart';

class AdCreativeReviewWidget extends StatelessWidget {
  const AdCreativeReviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      bloc: CreateAdCubit.get(context),
      builder: (ctx, state) {
        return DecoratedContainer(
          width: double.infinity,
          color: Colors.white,
          boxShadow: const BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 150,
            offset: Offset(0, 4),
            spreadRadius: 0,
          ),
          radius: 20.sp,
          widget: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.sp, horizontal: 30.sp),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        SvgPicture.asset(
                          height: 24.h,
                          AppAssets.ad,
                          color: AppColors.mainColor,
                        ),
                        10.horizontalSpace,
                        CustomText(
                            text: 'Advertising Content'.tr,
                            color: AppColors.mainColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w700),
                      ],
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();

                        Constants.adCreativeExpansionTileKey.currentState
                            ?.expand();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 3),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 1, color: Color(0xFF0B0F26)),
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 2.0),
                              child: CustomText(
                                text: 'Change'.tr,
                                color: Constants.primaryTextColor,
                                fontSize: 10.sp,
                                alignment: AlignmentDirectional.center,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                15.verticalSpace,
                (CreateAdCubit.get(context).adImages.isNotEmpty)
                    // ||
                    //     CreateAdCubit.get(context).post != null)
                    ? Column(
                        children: [
                          CustomText(
                            text: "Image Advertising".tr,
                            color: const Color(0xFF848484),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          8.verticalSpace,
                          CreateAdCubit.get(context).post != null
                              ? Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: CachedImageWidget(
                                      image: CreateAdCubit.get(context)
                                          .post
                                          ?.mediaUrl,
                                      width:
                                          SizeConfig.screenWidth(context) * 0.9,
                                      height: 100.h,
                                    ),
                                  ),
                                )
                              : Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: CreateAdCubit.get(context)
                                                    .adImages
                                                    .length ==
                                                1
                                            ? ListView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                itemCount:
                                                    CreateAdCubit.get(context)
                                                        .adImages
                                                        .length,
                                                itemBuilder: (context, index) {
                                                  return CreateAdCubit.get(
                                                                  context)
                                                              .adImages
                                                              .length ==
                                                          1
                                                      ? UploadedImageWidget(
                                                          height: 258.h,
                                                          onPressed: () {},
                                                          image: CreateAdCubit
                                                                  .get(context)
                                                              .adImages[index],
                                                          text:
                                                              "Upload file images here"
                                                                  .tr,
                                                          icon: Icons.add,
                                                        )
                                                      : UploadedImageWidget(
                                                          height: 100,
                                                          onPressed: () {
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .removeImage(
                                                                    index,
                                                                    true);
                                                          },
                                                          image: CreateAdCubit
                                                                  .get(context)
                                                              .adImages[index],
                                                          text:
                                                              "Upload file images here"
                                                                  .tr,
                                                          icon: Icons.add,
                                                        );
                                                },
                                              )
                                            : GridView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                gridDelegate:
                                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                                  crossAxisSpacing: 4,
                                                  mainAxisSpacing: 4,
                                                  crossAxisCount: 3,
                                                ),
                                                itemCount:
                                                    CreateAdCubit.get(context)
                                                        .adImages
                                                        .length,
                                                itemBuilder: (context, index) {
                                                  return UploadedImageWidget(
                                                    height: 100,
                                                    onPressed: () {},
                                                    image: CreateAdCubit.get(
                                                            context)
                                                        .adImages[index],
                                                    text: "",
                                                  );
                                                },
                                              ),
                                      ),
                                    ],
                                  ),
                                ),
                        ],
                      )
                    : Column(
                        children: [
                          CustomText(
                            text: "Video Advertising".tr,
                            color: const Color(0xFF848484),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          // 8.verticalSpace,
                          // UploadedImageWidget(
                          //   height: 258.h,
                          //   onPressed: () {},
                          //   image: CreateAdCubit.get(context).videoImage.first,
                          //   text: "Upload file images here".tr,
                          //   icon: Icons.add,
                          // ),
                          8.verticalSpace,
                          const Divider(
                            color: Constants.darkColor,
                            thickness: 0.5,
                          ),
                          8.verticalSpace,
                          // CustomText(
                          //   text: "Video Advertising".tr,
                          //   color: Color(0xFF848484),
                          //   fontSize: 12,
                          //   fontWeight: FontWeight.w500,
                          // ),
                          if (CreateAdCubit.get(context)
                              .adVideo
                              .isNotEmpty) ...[
                            8.verticalSpace,
                            CustomVideoPlayer.file(
                                file: CreateAdCubit.get(context).adVideo.first),
                          ],
                          if (CreateAdCubit.get(context).post != null) ...[
                            if (CreateAdCubit.get(context).post?.mediaType ==
                                    "VIDEO" ||
                                CreateAdCubit.get(context).post?.mediaType ==
                                    "video") ...[
                              CustomVideoPlayer.network(
                                  videoUrl: CreateAdCubit.get(context)
                                          .post
                                          ?.mediaUrl ??
                                      "")
                            ],
                            if (CreateAdCubit.get(context).post?.mediaType ==
                                    "IMAGE" ||
                                CreateAdCubit.get(context).post?.mediaType ==
                                    "photo") ...[
                              ClipRRect(
                                borderRadius: BorderRadius.circular(5),
                                child: CachedImageWidget(
                                  image:
                                      CreateAdCubit.get(context).post?.mediaUrl,
                                  height: 180.h,
                                ),
                              )
                            ],
                          ],

                          8.verticalSpace,
                          const Divider(
                            color: Constants.darkColor,
                            thickness: 0.5,
                          ),
                        ],
                      ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "Headline Advertising Post".tr,
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    CustomText(
                      text: CreateAdCubit.get(context).adCreativeName.text,
                      color: Constants.primaryTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: "${"Website Link".tr}:",
                      color: const Color(0xFF848484),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    Expanded(
                      child: CustomText(
                        text: CreateAdCubit.get(context).webSiteLink.text,
                        color: Constants.primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        maxLines: 1,
                        textOverflow: TextOverflow.fade,
                      ),
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CustomText(
                        text: "Description Advertising Post".tr,
                        color: const Color(0xFF848484),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: CustomText(
                        text: CreateAdCubit.get(context).headline.text,
                        textAlign: TextAlign.justify,
                        maxLines: 10,
                        color: Constants.primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                8.verticalSpace,
                const Divider(
                  color: Constants.darkColor,
                  thickness: 0.5,
                ),
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CustomText(
                        text: "Action Description".tr,
                        color: const Color(0xFF848484),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: CustomText(
                        text: CreateAdCubit.get(context).linkDesc.text,
                        textAlign: TextAlign.justify,
                        maxLines: 10,
                        color: Constants.primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

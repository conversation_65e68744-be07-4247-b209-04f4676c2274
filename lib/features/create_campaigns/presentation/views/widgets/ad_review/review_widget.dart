import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/common_utils.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/circular_percent_indicator_widget.dart';
import '../../../../../../widgets/custom_switch.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import '../../../../../../widgets/uploaded_image_widget.dart';
import '../../../../../../widgets/video_player.dart';

class AdReviewWidget extends StatefulWidget {
  const AdReviewWidget({super.key});

  @override
  State<AdReviewWidget> createState() => _AdReviewWidgetState();
}

class _AdReviewWidgetState extends State<AdReviewWidget> {
  @override
  void initState() {
    // print(
    //     'pageNamedfjklzfhv ${instance<HiveHelper>().getUser()?.pageName} ${CreateAdCubit.get(context).metaPages?.name}');
    if (CreateAdCubit.get(context).adVideo.isNotEmpty) {
      print(
          'pageNamedfjklzfhv ${CreateAdCubit.get(context).adVideo.first.path}');
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return ExpansionTileItem(
          onExpansionChanged: (val) {
            CreateAdCubit.get(context).setReviewExpansionState(val);
          },
          expansionKey: Constants.adExpansionTileKey,
          childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
          iconColor: AppColors.secondColor,
          collapsedIconColor: AppColors.secondColor,
          expandedAlignment: Alignment.center,
          expandedCrossAxisAlignment: CrossAxisAlignment.center,
          leading: SvgPicture.asset(
            AppAssets.review,
            color: AppColors.mainColor,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularIndicatorWidget(
                adCubit: CreateAdCubit.get(context),
                isAdCreative: false,
                isAdSet: false,
                isDemographic: false,
                isTargeting: false,
                isLocation: false,
              ),
              const Padding(
                padding: EdgeInsets.only(left: 10.0),
                child: CustomText(
                  text: "|",
                  color: AppColors.mainColor,
                  fontSize: 35,
                  fontWeight: FontWeight.w200,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 6.0),
                child: ShaderMask(
                  shaderCallback: (Rect bounds) {
                    return const LinearGradient(
                      colors: [
                        Color(0xFFFF006F),
                        Color(0xFFF6BA00),
                      ],
                    ).createShader(bounds);
                  },
                  child: Icon(
                    CreateAdCubit.get(context).isReviewTileExpanded
                        ? Icons.expand_less
                        : Icons.expand_more,
                    size: 24.0,
                    color: Colors
                        .white, // This color will be replaced by the gradient
                  ),
                ),
              )
            ],
          ),
          title: CustomText(
              text: 'Ad Review'.tr,
              color: AppColors.mainColor,
              fontSize: 22,
              fontWeight: FontWeight.w700),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: AppColors.mainColor)
              // color: AppColors.borderColor,
              ),
          children: [
            const SizedBox(height: 20),
            Form(
              key: CreateAdCubit.get(context).adFormKey,
              child: CustomTextFormField(
                textFontSize: 12,
                borderRadius: 33,
                key: const ValueKey('adName_name'),
                hintText: "Advertising Name".tr,
                controller: CreateAdCubit.get(context).adName,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (val) {
                  // if (CreateAdCubit.get(context).adName.text.isNotEmpty) {
                  //   CreateAdCubit.get(context).updateProcess1();
                  // } else {
                  //   CreateAdCubit.get(context).undoProcess1();
                  // }
                },
              ),
            ),
            const SizedBox(height: 15),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            const SizedBox(height: 5),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomText(
                    text: 'Status'.tr,
                    fontSize: 12.sp,
                    color: Constants.primaryTextColor,
                    fontWeight: FontWeight.w400,
                    alignment: AlignmentDirectional.centerStart,
                  ),
                  CustomSwitch(
                    value: CreateAdCubit.get(context).isAdActive,
                    onChanged: (newValue) {
                      CreateAdCubit.get(context).changeAdStatus(newValue);
                      print("teaa${CreateAdCubit.get(context).adStatusType}");
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 5),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: Constants.textColor),
            ),
            const SizedBox(height: 10),
            Container(
              //height: 354.h,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
                shadows: const [
                  BoxShadow(
                    color: Color(0x26000000),
                    blurRadius: 38.90,
                    offset: Offset(0, 0),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(50.sp),
                              child: CachedImageWidget(
                                image: CreateAdCubit.get(context)
                                        .metaPages
                                        ?.profilePic ??
                                    CreateAdCubit.get(context)
                                        .accessedMetaPages
                                        ?.picture
                                        ?.data
                                        ?.url ??
                                    instance<HiveHelper>().getUser()?.pagePic ??
                                    "",
                                height: 50.h,
                              ),
                            ),
                            const SizedBox(width: 10),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomText(
                                  text: CreateAdCubit.get(context)
                                          .metaPages
                                          ?.name ??
                                      instance<HiveHelper>()
                                          .getUser()
                                          ?.pageUserName ??
                                      "Page Name",
                                  color: AppColors.blackColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                                const SizedBox(height: 2),
                                SizedBox(
                                  width: 60,
                                  child: FittedBox(
                                    child: CustomText(
                                      text: CreateAdCubit.get(context)
                                              .startDate
                                              .text
                                              .isNotEmpty
                                          ? CommonUtils.convertDateString(
                                              CreateAdCubit.get(context)
                                                  .startDate
                                                  .text)
                                          : "March 8, 2021",
                                      color: Constants.darkGray,
                                      fontSize: 8,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 16),
                          child: CustomSvgWidget(
                              svg: AppAssets.dots, width: 16.w, height: 5.h),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    CustomText(
                      text: CreateAdCubit.get(context).adModel.description ??
                          CreateAdCubit.get(context).post?.caption ??
                          "review",
                      color: AppColors.blackColor,
                      textAlign: TextAlign.right,
                      fontSize: 14,
                      maxLines: 3,
                      fontWeight: FontWeight.w400,
                    ),
                    const SizedBox(height: 15),
                    CreateAdCubit.get(context).adImages.isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(5),
                            child: UploadedImageWidget(
                              text: "",
                              onPressed: () {},
                              image: CreateAdCubit.get(context).adImages.first,
                              height: 180.h,
                            ),
                          )
                        : CreateAdCubit.get(context).adVideo.isNotEmpty
                            ? CustomVideoPlayer.file(
                                file: CreateAdCubit.get(context).adVideo.first)
                            : CreateAdCubit.get(context).post != null
                                ? (CreateAdCubit.get(context).post?.mediaType ==
                                            "IMAGE" ||
                                        CreateAdCubit.get(context)
                                                .post
                                                ?.mediaType ==
                                            "photo")
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(5),
                                        child: CachedImageWidget(
                                          image: CreateAdCubit.get(context)
                                              .post
                                              ?.mediaUrl,
                                          height: 180.h,
                                        ),
                                      )
                                    : CustomVideoPlayer.network(
                                        videoUrl: CreateAdCubit.get(context)
                                            .post
                                            ?.mediaUrl)
                                : ClipRRect(
                                    borderRadius: BorderRadius.circular(7.sp),
                                    child: const CachedImageWidget(
                                      assetsImage: AppAssets.post,
                                      width: double.infinity,
                                    ),
                                  ),
                    const SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            CachedImageWidget(
                              assetsImage: AppAssets.care,
                              height: 15.h,
                            ),
                            CachedImageWidget(
                              assetsImage: AppAssets.like,
                              height: 15.h,
                            )
                          ],
                        ),
                        const CustomText(
                          text: "45 Comments",
                          color: Constants.darkGray,
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 25),
            SizedBox(
              width: 235.w,
              child: ButtonWidget(
                text: "Save".tr,
                onTap: () {
                  if (CreateAdCubit.get(context)
                      .adFormKey
                      .currentState!
                      .validate()) {
                    CreateAdCubit.get(context).adModel =
                        CreateAdCubit.get(context).adModel.copyWith(
                            adName: CreateAdCubit.get(context).adName.text,
                            type: CreateAdCubit.get(context).type,
                            adStatus: CreateAdCubit.get(context).adStatusType,
                            geoLocations:
                                CreateAdCubit.get(context).geoLocations);
                    if (CreateAdCubit.get(context).adName.text.isNotEmpty) {
                      CreateAdCubit.get(context).updateProcess1();
                    } else {
                      CreateAdCubit.get(context).undoProcess1();
                    }
                    CreateAdCubit.get(context).isAdReviewCreated = true;
                    Constants.adExpansionTileKey.currentState?.collapse();
                  } else {
                    // Show error toast if the form is invalid
                    showErrorToast(
                        "Please fill in all the required fields correctly.");
                  }
                },
              ),
            ),
            const SizedBox(height: 25),
          ],
        );
      },
    );
  }
}

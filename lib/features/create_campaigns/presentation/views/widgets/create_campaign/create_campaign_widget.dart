import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_campaign/create_campaign_cubit.dart';
import 'package:ads_dv/widgets/circular_percent_indicator_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import 'existing_campaign/existing_campaign_widget.dart';
import 'new_campaign/new_campaign_widget.dart';
import 'new_campaign/tabs_widget.dart';

class CreateCampaignWidget extends StatefulWidget {
  CreateAdCubit createAdCubit;
  CreateCampaignWidget({super.key, required this.createAdCubit});

  @override
  State<CreateCampaignWidget> createState() => _CreateCampaignWidgetState();
}

class _CreateCampaignWidgetState extends State<CreateCampaignWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CreateCampaignCubit(),
      child: BlocBuilder<CreateCampaignCubit, CreateCampaignState>(
        builder: (context, state) {
          return ExpansionTileItem(
            expansionKey: Constants.expansionTileKey,
            onExpansionChanged: (val) {
              CreateCampaignCubit.get(context).setGoalExpansionState(val);
            },
            childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
            iconColor: AppColors.secondColor,
            collapsedIconColor: AppColors.secondColor,
            expandedAlignment: Alignment.center,
            expandedCrossAxisAlignment: CrossAxisAlignment.center,
            leading:
                SvgPicture.asset(AppAssets.goal, color: AppColors.mainColor),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularIndicatorWidget(
                    isAdSet: false,
                    isAdCreative: false,
                    isDemographic: false,
                    isTargeting: false,
                    isLocation: false,
                    cubit: CreateCampaignCubit.get(context)),
                const Padding(
                  padding: EdgeInsets.only(left: 10.0),
                  child: CustomText(
                    text: "|",
                    color: AppColors.mainColor,
                    fontSize: 35,
                    fontWeight: FontWeight.w200,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 6.0),
                  child: ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return Constants.secGradient.createShader(bounds);
                    },
                    child: Icon(
                      CreateCampaignCubit.get(context).isGoalTileExpanded
                          ? Icons.expand_less
                          : Icons.expand_more,
                      size: 24.0,
                      color: Colors.white,
                    ),
                  ),
                )
              ],
            ),
            title: CustomText(
                text: 'Advertising Type'.tr,
                color: AppColors.mainColor,
                fontSize: 22,
                fontWeight: FontWeight.w700),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppColors.mainColor)),
            children: [
              const SizedBox(height: 20),
              Container(
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(40),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x3D000000),
                      blurRadius: 13.93,
                      offset: Offset(0, 0),
                      spreadRadius: -3.80,
                    )
                  ],
                ),
                child: TabsWidget(
                  newObject: 'New Advertising'.tr,
                  existObject: 'Previous Advertising'.tr,
                  selectedTab: widget.createAdCubit.selectCampaignTab,
                  onTabChanged: (tab) {

                    if (widget.createAdCubit.selectCampaignTab != tab) {
                      widget.createAdCubit.changeCampaignTabIndex(tab);
                      widget.createAdCubit.campaignProcessPercentage = 0.0;

                      widget.createAdCubit.campaignNameController.text = "";
                      widget.createAdCubit.objective = null;
                      widget.createAdCubit.optimization = null;
                      widget.createAdCubit.isCampaignProcess1Updated = false;
                      widget.createAdCubit.isCampaignProcess2Updated = false;
                      widget.createAdCubit.isCampaignProcess3Updated = false;
                      widget.createAdCubit.isCampaignProcess4Updated = false;
                      widget.createAdCubit.selectedGoal = null;
                      widget.createAdCubit.billingEvent = null;

                      widget.createAdCubit.existingCampaign = null;
                      widget.createAdCubit.selectedExistingCampaign = null;
                      widget.createAdCubit.existingAdSet = null;
                      widget.createAdCubit.selectedExistingAdSet = null;
                      widget.createAdCubit.selectAdSetTab = 0;
                      widget.createAdCubit.adSetPercentage = 0.0;

                    }
                  },
                ),
              ),
              (widget.createAdCubit.selectCampaignTab == 0)
                  ? NewCampaignWidget(
                      cubit: CreateCampaignCubit.get(context),
                      expansionTileKey: Constants.expansionTileKey,
                    )
                  : ExistingCampaignWidget(
                      adCubit: widget.createAdCubit,
                      cubit: CreateCampaignCubit.get(context),
                    ),
              const SizedBox(height: 25),
            ],
          );
        },
      ),
    );
  }
}

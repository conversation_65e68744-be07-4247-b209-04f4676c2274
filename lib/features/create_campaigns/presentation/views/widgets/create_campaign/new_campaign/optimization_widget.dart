import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/svg_widget.dart';



class OptimizationWidget extends StatelessWidget {
  final String name;
  final int index;
  final bool isSelected; // Determine if this ObjectiveWidget is selected
  final void Function(int) callback;
  const OptimizationWidget({
    super.key,
    required this.name,
    required this.index,
    required this.isSelected,
    required this.callback,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {

          callback(index);

      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Container(
          height: 40.h,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(25.r),
            color: isSelected
                ? AppColors.mainColor
                : Colors.white,
            gradient:
            isSelected ? Constants.defGradient : null,
            boxShadow: Constants.unSelectedShadow,
            border: null,
          ),
          width: 80.h,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: CustomSvgWidget(
                    svg: getOptimizationIcon(name),
                    height: 20.h,
                    width: 20.h,
                    color: isSelected
                        ? AppColors.white
                        : Constants.textColor,
                  ),
                ),
                SizedBox(height: 8.h),
                FittedBox(
                  child: CustomText(
                    text: name,
                    fontSize: 12.sp,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.w400,
                    color: isSelected
                        ? AppColors.white
                        : Constants.textColor,
                    textAlign: TextAlign.center,
                    alignment: AlignmentDirectional.center,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
  String getOptimizationIcon(String name) {
    if (name == "Video Views") {
      return 'assets/icons/video.svg';
    } else if (name == "Message on instagram & Whatsapp") {
      return 'assets/icons/chat.svg';
    } else {
      return 'assets/icons/sales.svg';
    }
  }

}

import 'package:flutter/material.dart';
import 'package:toggle_switch/toggle_switch.dart';
import '../../../../../../../utils/res/constants.dart';

class TabsWidget extends StatefulWidget {
  final String newObject;
  final String existObject;
  final String? thirdObject; // Nullable third object
  final double? fontSize;
  final double? radius;

  final FontWeight fontWeight;
  final bool isMap;
  final int selectedTab;
  final ValueChanged<int> onTabChanged;

  const TabsWidget({
    Key? key,
    required this.selectedTab,
    required this.onTabChanged,
    required this.newObject,
    required this.existObject,
    this.thirdObject,
    this.fontWeight = FontWeight.w600,
    this.fontSize = 12,
    this.radius = 20,
    this.isMap = false,
  }) : super(key: key);

  @override
  State<TabsWidget> createState() => _TabsWidgetState();
}

class _TabsWidgetState extends State<TabsWidget> {
  @override
  Widget build(BuildContext context) {
    // Create a list of labels based on the input objects
    final labels = [
      widget.newObject,
      widget.existObject,
      if (widget.thirdObject != null) widget.thirdObject!,
    ];

    return ToggleSwitch(
      customHeights: List.filled(labels.length, 60),
      minWidth: double.infinity,
      cornerRadius: widget.radius ?? 20,
      activeBgColors: widget.isMap
          ? List.generate(labels.length, (_) => [
        const Color(0xFFF6BA00),
        const Color(0xFFFF006F),
      ])
          : List.generate(labels.length, (_) => [
        const Color(0xFF296AEB),
        const Color(0xFF0B0F26),
      ]),
      customTextStyles: [
        TextStyle(fontWeight: widget.fontWeight, fontSize: widget.fontSize),
      ],
      activeFgColor: Colors.white,
      inactiveBgColor: Colors.white,
      inactiveFgColor: Constants.textColor,
      initialLabelIndex: widget.selectedTab,
      totalSwitches: labels.length, // Dynamic number of tabs
      labels: labels,
      radiusStyle: true,
      onToggle: (index) {
        if (index != null) {
          widget.onTabChanged(index);
        }
      },
    );
  }
}
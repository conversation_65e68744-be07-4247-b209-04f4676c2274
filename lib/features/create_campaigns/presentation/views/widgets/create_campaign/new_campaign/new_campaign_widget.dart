import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_campaign/create_campaign_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_billing_events/get_billing_events_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_forms/get_forms_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_objectives/get_objectives_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_optimizations/get_optimizations_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_campaign/new_campaign/objective_widget.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_campaign/new_campaign/tabs_widget.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_switch.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../../../../data/models/language.dart';
import '../../../../../data/models/question.dart';

class NewCampaignWidget extends StatefulWidget {
  GlobalKey<ExpansionTileCustomState> expansionTileKey;
  CreateCampaignCubit cubit;

  NewCampaignWidget(
      {super.key, required this.cubit, required this.expansionTileKey});

  @override
  State<NewCampaignWidget> createState() => _NewCampaignWidgetState();
}

class _NewCampaignWidgetState extends State<NewCampaignWidget> {
  @override
  void initState() {
    Future.microtask(
        () => GetObjectivesCubit.get(context).getObjectives(context: context));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetBillingEventsCubit, GetBillingEventsState>(
      bloc: GetBillingEventsCubit.get(context),
      builder: (billContext, billState) {
        return BlocBuilder<GetOptimizationsCubit, GetOptimizationsState>(
          bloc: GetOptimizationsCubit.get(context),
          builder: (optContext, optState) {
            return BlocBuilder<GetObjectivesCubit, GetObjectivesState>(
              bloc: GetObjectivesCubit.get(context),
              builder: (ctx, state) {
                if (state is GetObjectivesStateLoading) {
                  return const Column(
                    children: [
                      SizedBox(height: 20),
                      LoadingWidget(isCircle: true),
                    ],
                  );
                } else if (state is GetObjectivesStateError) {
                  return Column(
                    children: [
                      const SizedBox(height: 20),
                      HandleErrorWidget(
                          fun: () {
                            GetObjectivesCubit.get(ctx)
                                .getObjectives(context: ctx);
                          },
                          failure: state.message),
                    ],
                  );
                }
                if (state is GetObjectivesStateLoaded) {
                  return BlocBuilder<CreateAdCubit, CreateAdState>(
                    builder: (ctx1, createAddState) {
                      return Column(
                        children: [
                          const SizedBox(height: 20),
                          Form(
                            key: CreateAdCubit.get(context).campaignFormKey,
                            child: CustomTextFormField(
                              label: "Advertising Name".tr,
                              showIsReqiredFlag: true,
                              textFontSize: 12,
                              key: const ValueKey('campaign_name'),
                              hintText: "Advertising Name".tr,
                              textInputAction: TextInputAction.next,
                              keyboardType: TextInputType.text,
                              controller: CreateAdCubit.get(context)
                                  .campaignNameController,
                              validator: (value) =>
                                  AppValidator.validateIdentity(value, context),
                              onChanged: (va) {
                                if (CreateAdCubit.get(context)
                                    .campaignNameController
                                    .text
                                    .isNotEmpty) {
                                  CreateAdCubit.get(ctx1)
                                      .updateCampaignProcess1();
                                } else {
                                  CreateAdCubit.get(ctx1)
                                      .undoCampaignProcess1();
                                }
                              },
                            ),
                          ),
                          const SizedBox(height: 20),
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Divider(color: Constants.textColor),
                          ),
                          const SizedBox(height: 5),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CustomText(
                                  text: 'Status'.tr,
                                  fontSize: 12.sp,
                                  color: Constants.primaryTextColor,
                                  fontWeight: FontWeight.w400,
                                  alignment: AlignmentDirectional.centerStart,
                                ),
                                CustomSwitch(
                                  value:
                                      CreateAdCubit.get(ctx1).isCampaignActive,
                                  onChanged: (newValue) {
                                    CreateAdCubit.get(ctx1)
                                        .changeCampaignStatus(newValue);
                                    CreateAdCubit.get(ctx1)
                                        .updateCampaignProcess2();
                                    print("SATA${CreateAdCubit.get(ctx1).statusType}");
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 5),
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Divider(color: Constants.textColor),
                          ),
                          const SizedBox(height: 5),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: CustomText(
                              text: "Decide What You Want To Achieve".tr,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              alignment: AlignmentDirectional.centerStart,
                              color: Constants.primaryTextColor,
                            ),
                          ),
                          20.verticalSpace,
                          GridView.builder(
                            padding: EdgeInsets.zero,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3, // Number of columns
                              childAspectRatio:
                                  1, // Adjust based on your design
                              crossAxisSpacing: 5.w, // Horizontal spacing
                              mainAxisSpacing: 5.h, // Vertical spacing
                            ),
                            itemCount: state.data.length,
                            itemBuilder: (BuildContext context, int index) {
                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 5,
                                    vertical: 5), // You can adjust padding here
                                child: ObjectiveWidget(
                                  isSelected: index ==
                                      CreateAdCubit.get(ctx1).selectedGoal,
                                  callback: (_) {
                                    CreateAdCubit.get(ctx1)
                                        .updateCampaignProcess3();
                                    CreateAdCubit.get(ctx1).setSelectedGoal(
                                      index,
                                      state.data[index].actualName ?? "",
                                      //  state.data[index].opimizations!,
                                    );
                                    GetOptimizationsCubit.get(optContext)
                                        .getOptimizations(
                                            context: context,
                                            objectiveId:
                                                state.data[index].id ?? 0);
                                    CreateAdCubit.get(context).optimization =
                                        null;
                                    // CreateAdCubit.get(ctx1).objective ==
                                    //         "OUTCOME_LEADS"
                                    //     ? _createLeadFormDialog(
                                    //         context, CreateAdCubit.get(ctx1))
                                    //     : null;
                                    if (CreateAdCubit.get(ctx1).objective !=
                                        "OUTCOME_LEADS") {
                                      CreateAdCubit.get(context).leadDesc.text =
                                          "";
                                      CreateAdCubit.get(context)
                                          .leadMessage
                                          .text = "";
                                      CreateAdCubit.get(context)
                                          .ctaController
                                          .text = "";
                                      CreateAdCubit.get(context)
                                          .websiteLinkController
                                          .text = "";
                                      CreateAdCubit.get(context)
                                          .linkTextController
                                          .text = "";
                                      CreateAdCubit.get(context)
                                          .linkController
                                          .text = "";
                                      CreateAdCubit.get(context)
                                          .leadHeadlineController
                                          .text = "";
                                      // CreateAdCubit.get(context).addedQuestions=[];
                                      CreateAdCubit.get(context).langValue =
                                          null;
                                      CreateAdCubit.get(context).langIndex =
                                          null;

                                      CreateAdCubit.get(context).formId = null;
                                      CreateAdCubit.get(context).formIndex =
                                          null;
                                      CreateAdCubit.get(context)
                                          .addedQuestions
                                          .clear();
                                      CreateAdCubit.get(context)
                                          .questions
                                          .clear();
                                      CreateAdCubit.get(context).questions = [
                                        Question(
                                            name: 'What is your city?',
                                            value: 'CITY',
                                            isChecked: false),
                                        Question(
                                            name: 'What is your company name?',
                                            value: 'COMPANY_NAME',
                                            isChecked: false),
                                        Question(
                                            name: 'What is your country name?',
                                            value: 'COUNTRY',
                                            isChecked: false),
                                        Question(
                                            name: 'What is your gender?',
                                            value: 'GENDER',
                                            isChecked: false),
                                        Question(
                                            name: 'What is your first name?',
                                            value: 'FIRST_NAME',
                                            isChecked: false),
                                        Question(
                                            name: 'What is your full name?',
                                            value: 'FULL_NAME',
                                            isChecked: false),
                                        Question(
                                            name: 'What is your job title?',
                                            value: 'JOB_TITLE',
                                            isChecked: false),
                                      ];
                                    }
                                  },
                                  name: state.data[index].showName ?? "",
                                  index: index,
                                ),
                              );
                            },
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                          ),
                          const SizedBox(height: 20),
                          // ListView.separated(
                          //   shrinkWrap: true,
                          //   physics: const BouncingScrollPhysics(
                          //       parent: NeverScrollableScrollPhysics()),
                          //   scrollDirection: Axis.vertical,
                          //   itemCount: GetOptimizationsCubit.get(optContext)
                          //       .opt
                          //       .length,
                          //   clipBehavior: Clip.none,
                          //   separatorBuilder: (context, index) =>
                          //       SizedBox(height: 10.h),
                          //   itemBuilder: (context, index) => OptimizationWidget(
                          //     isSelected: index ==
                          //         CreateAdCubit.get(ctx1).selectOptimizationIndex,
                          //     callback: (index) {
                          //       CreateAdCubit.get(ctx1).updateCampaignProcess4();
                          //       CreateAdCubit.get(ctx1).setSelectedOptimization(
                          //           index,
                          //           GetOptimizationsCubit.get(optContext)
                          //               .opt[index]
                          //               .actualName ??
                          //               "");
                          //     },
                          //     name:  GetOptimizationsCubit.get(optContext)
                          //         .opt[index]
                          //         .showName ??
                          //         "",
                          //     index: index,
                          //   ),
                          // ),
                          optState is GetOptimizationsStateLoading
                              ? const LoadingWidget(
                                  isCircle: true,
                                )
                              : GetOptimizationsCubit.get(optContext)
                                      .opt
                                      .isEmpty
                                  ? const SizedBox()
                                  : Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8.0),
                                          child: Row(
                                            children: [
                                              CustomText(
                                                text:
                                                    "Advertising Objective".tr,
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w400,
                                                alignment: AlignmentDirectional
                                                    .centerStart,
                                                color:
                                                    Constants.primaryTextColor,
                                              ),
                                              CustomText(
                                                text: "*",
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w400,
                                                alignment: AlignmentDirectional
                                                    .centerStart,
                                                color: Constants.redColor,
                                              ),
                                            ],
                                          ),
                                        ),
                                        20.verticalSpace,
                                        CreateAdCubit.get(ctx1).objective ==
                                                "OUTCOME_AWARENESS"
                                            ? ExpansionTileItem(
                                                expansionKey:
                                                    Constants.optimizationKey,
                                                onExpansionChanged: (val) {},
                                                childrenPadding:
                                                    EdgeInsets.zero,
                                                iconColor:
                                                    AppColors.secondColor,
                                                collapsedIconColor:
                                                    AppColors.secondColor,
                                                expandedAlignment:
                                                    Alignment.center,
                                                expandedCrossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                trailing: const Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.only(
                                                          left: 6.0),
                                                      child: Icon(
                                                        Icons.expand_more,
                                                        size: 30.0,
                                                        color:
                                                            Constants.darkColor,
                                                      ),
                                                    )
                                                  ],
                                                ),
                                                title: CreateAdCubit.get(
                                                                context)
                                                            .optimization !=
                                                        null
                                                    ? CustomText(
                                                        fontSize: 12.sp,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        text: CreateAdCubit.get(
                                                                    context)
                                                                .optimization
                                                                ?.showName ??
                                                            "")
                                                    : AccountHintText(
                                                        isDefaultHint: false,
                                                        hint:
                                                            'Advertising Objective'
                                                                .tr,
                                                      ),
                                                decoration: ShapeDecoration(
                                                  color: Colors.white,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                  ),
                                                  shadows: const [
                                                    BoxShadow(
                                                      color: Color(0x3F000000),
                                                      blurRadius: 40,
                                                      offset: Offset(0, 0),
                                                      spreadRadius: -10,
                                                    )
                                                  ],
                                                ),
                                                children: [
                                                  ListView.builder(
                                                    shrinkWrap: true,
                                                    physics:
                                                        const NeverScrollableScrollPhysics(),
                                                    itemCount:
                                                        GetOptimizationsCubit.get(
                                                                    optContext)
                                                                .opt
                                                                .length +
                                                            1,
                                                    // 3 for the first text + 2 for the last text
                                                    itemBuilder:
                                                        (context, index) {
                                                      // Check if the index is for the text before the first three items
                                                      if (index == 0) {
                                                        return Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(16.0),
                                                          child: CustomText(
                                                              text:
                                                                  'Brand Awareness'
                                                                      .tr,
                                                              fontSize: 13.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        );
                                                      } else
                                                      //   if (index >= 1
                                                      //     // && index <= 3
                                                      // )
                                                      {
                                                        // Return the first three items
                                                        final optIndex = index -
                                                            1; // Adjust index for the optimization list
                                                        return InkWell(
                                                          onTap: () {
                                                            CreateAdCubit.get(
                                                                    ctx1)
                                                                .updateCampaignProcess4();
                                                            CreateAdCubit.get(
                                                                    ctx1)
                                                                .setSelectedOptimization(
                                                                    GetOptimizationsCubit.get(
                                                                            optContext)
                                                                        .opt[optIndex]);
                                                            print("asdfafgdsg${CreateAdCubit.get(
                                                                        context)
                                                                    .optimization!
                                                                    .actualName}");
                                                            GetBillingEventsCubit
                                                                    .get(
                                                                        billContext)
                                                                .getBillingEvents(
                                                                    context:
                                                                        context,
                                                                    optimizationId:
                                                                        GetOptimizationsCubit.get(optContext).opt[optIndex].id ??
                                                                            0);
                                                            CreateAdCubit.get(
                                                                        context)
                                                                    .billingEvent =
                                                                null;
                                                            Constants
                                                                .optimizationKey
                                                                .currentState
                                                                ?.collapse();
                                                          },
                                                          child: Container(
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Constants
                                                                  .gray
                                                                  .withOpacity(
                                                                      0.15),
                                                              border: Border
                                                                  .symmetric(
                                                                horizontal: BorderSide(
                                                                    color: Constants
                                                                        .gray
                                                                        .withOpacity(
                                                                            0.3)),
                                                              ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          18,
                                                                      horizontal:
                                                                          20),
                                                              child: CustomText(
                                                                maxLines: 3,
                                                                fontSize: 12.sp,
                                                                text: GetOptimizationsCubit.get(
                                                                            optContext)
                                                                        .opt[
                                                                            optIndex]
                                                                        .showName ??
                                                                    "",
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }
                                                      // else if (index == 4) {
                                                      //   // Text before the last 2 items
                                                      //   return Padding(
                                                      //     padding:
                                                      //         const EdgeInsets
                                                      //             .all(16.0),
                                                      //     child: CustomText(
                                                      //         text:
                                                      //             'Video view goals',
                                                      //         fontSize: 13.sp,
                                                      //         fontWeight:
                                                      //             FontWeight
                                                      //                 .w600),
                                                      //   );
                                                      // } else if (index >= 5) {
                                                      //   // Return the last two items
                                                      //   final optIndex = index -
                                                      //       5; // Adjust index for the optimization list
                                                      //   final originalIndex =
                                                      //       GetOptimizationsCubit
                                                      //                   .get(
                                                      //                       optContext)
                                                      //               .opt
                                                      //               .length -
                                                      //           2 +
                                                      //           optIndex; // Correctly mapping to the last items
                                                      //
                                                      //   // Check if the original index is valid
                                                      //   if (originalIndex <
                                                      //       GetOptimizationsCubit
                                                      //               .get(
                                                      //                   optContext)
                                                      //           .opt
                                                      //           .length) {
                                                      //     return InkWell(
                                                      //       onTap: () {
                                                      //         CreateAdCubit
                                                      //                 .get(
                                                      //                     ctx1)
                                                      //             .updateCampaignProcess4();
                                                      //         CreateAdCubit
                                                      //                 .get(
                                                      //                     ctx1)
                                                      //             .setSelectedOptimization(
                                                      //                 GetOptimizationsCubit.get(optContext)
                                                      //                     .opt[originalIndex]);
                                                      //         GetBillingEventsCubit
                                                      //                 .get(
                                                      //                     billContext)
                                                      //             .getBillingEvents(
                                                      //                 context:
                                                      //                     context,
                                                      //                 optimizationId:
                                                      //                     GetOptimizationsCubit.get(optContext).opt[originalIndex].id ??
                                                      //                         0);
                                                      //         print("asdfafgdsg" +
                                                      //             CreateAdCubit.get(
                                                      //                     context)
                                                      //                 .optimization!
                                                      //                 .actualName
                                                      //                 .toString());
                                                      //
                                                      //         CreateAdCubit.get(
                                                      //                 context)
                                                      //             .billingEvent = null;
                                                      //         Constants
                                                      //             .optimizationKey
                                                      //             .currentState
                                                      //             ?.collapse();
                                                      //       },
                                                      //       child: Container(
                                                      //         decoration:
                                                      //             BoxDecoration(
                                                      //           color: Constants
                                                      //               .gray
                                                      //               .withOpacity(
                                                      //                   0.15),
                                                      //           border: Border
                                                      //               .symmetric(
                                                      //             horizontal: BorderSide(
                                                      //                 color: Constants
                                                      //                     .gray
                                                      //                     .withOpacity(0.3)),
                                                      //           ),
                                                      //         ),
                                                      //         child: Padding(
                                                      //           padding: const EdgeInsets
                                                      //               .symmetric(
                                                      //               vertical:
                                                      //                   18,
                                                      //               horizontal:
                                                      //                   20),
                                                      //           child:
                                                      //               CustomText(
                                                      //             maxLines: 3,
                                                      //             fontSize:
                                                      //                 12.sp,
                                                      //             text: GetOptimizationsCubit.get(optContext)
                                                      //                     .opt[originalIndex]
                                                      //                     .showName ??
                                                      //                 "",
                                                      //           ),
                                                      //         ),
                                                      //       ),
                                                      //     );
                                                      //   }
                                                      // }

                                                      // Fallback in case of an unexpected index
                                                      return const SizedBox
                                                          .shrink();
                                                    },
                                                  ),
                                                ],
                                              )
                                            : CreateAdCubit.get(ctx1)
                                                        .objective ==
                                                    "OUTCOME_ENGAGEMENT"
                                                ? ExpansionTileItem(
                                                    expansionKey: Constants
                                                        .optimizationKey,
                                                    onExpansionChanged:
                                                        (val) {},
                                                    childrenPadding:
                                                        EdgeInsets.zero,
                                                    iconColor:
                                                        AppColors.secondColor,
                                                    collapsedIconColor:
                                                        AppColors.secondColor,
                                                    expandedAlignment:
                                                        Alignment.center,
                                                    expandedCrossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    trailing: const Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Padding(
                                                          padding:
                                                              EdgeInsets.only(
                                                                  left: 6.0),
                                                          child: Icon(
                                                            Icons.expand_more,
                                                            size: 30.0,
                                                            color: Constants
                                                                .darkColor,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    title: CreateAdCubit.get(
                                                                    context)
                                                                .optimization !=
                                                            null
                                                        ? CustomText(
                                                            fontSize: 12.sp,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            text: CreateAdCubit.get(
                                                                        context)
                                                                    .optimization
                                                                    ?.showName ??
                                                                "")
                                                        : AccountHintText(
                                                            isDefaultHint:
                                                                false,
                                                            hint:
                                                                'Advertising Objective'
                                                                    .tr,
                                                          ),
                                                    decoration: ShapeDecoration(
                                                      color: Colors.white,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(20),
                                                      ),
                                                      shadows: const [
                                                        BoxShadow(
                                                          color:
                                                              Color(0x3F000000),
                                                          blurRadius: 40,
                                                          offset: Offset(0, 0),
                                                          spreadRadius: -10,
                                                        ),
                                                      ],
                                                    ),
                                                    children: [
                                                      ListView.builder(
                                                        shrinkWrap: true,
                                                        physics:
                                                            const NeverScrollableScrollPhysics(),
                                                        itemCount: GetOptimizationsCubit
                                                                    .get(
                                                                        optContext)
                                                                .opt
                                                                .length +
                                                            1, // +1 for the header
                                                        itemBuilder:
                                                            (context, index) {
                                                          if (index == 0) {
                                                            // Header for Awareness goals
                                                            return Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(
                                                                      16.0),
                                                              child: CustomText(
                                                                text:
                                                                    'Engagement With Ad Objectives'
                                                                        .tr,
                                                                fontSize: 13.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                              ),
                                                            );
                                                          } else {
                                                            final optIndex = index -
                                                                1; // Adjust index for the optimization list
                                                            return InkWell(
                                                              onTap: () {
                                                                CreateAdCubit
                                                                        .get(
                                                                            ctx1)
                                                                    .updateCampaignProcess4();
                                                                CreateAdCubit
                                                                        .get(
                                                                            ctx1)
                                                                    .setSelectedOptimization(
                                                                        GetOptimizationsCubit.get(optContext)
                                                                            .opt[optIndex]);
                                                                GetBillingEventsCubit
                                                                        .get(
                                                                            billContext)
                                                                    .getBillingEvents(
                                                                        context:
                                                                            context,
                                                                        optimizationId:
                                                                            GetOptimizationsCubit.get(optContext).opt[optIndex].id ??
                                                                                0);
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .billingEvent = null;
                                                                Constants
                                                                    .optimizationKey
                                                                    .currentState
                                                                    ?.collapse();
                                                              },
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: Constants
                                                                      .gray
                                                                      .withOpacity(
                                                                          0.15),
                                                                  border: Border
                                                                      .symmetric(
                                                                    horizontal: BorderSide(
                                                                        color: Constants
                                                                            .gray
                                                                            .withOpacity(0.3)),
                                                                  ),
                                                                ),
                                                                child: Padding(
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          18,
                                                                      horizontal:
                                                                          20),
                                                                  child:
                                                                      CustomText(
                                                                    maxLines: 3,
                                                                    fontSize:
                                                                        12.sp,
                                                                    text: GetOptimizationsCubit.get(optContext)
                                                                            .opt[optIndex]
                                                                            .showName ??
                                                                        "",
                                                                  ),
                                                                ),
                                                              ),
                                                            );
                                                          }
                                                        },
                                                      ),
                                                    ],
                                                  )
                                                : CreateAdCubit.get(ctx1)
                                                            .objective ==
                                                        "OUTCOME_SALES"
                                                    ? ExpansionTileItem(
                                                        expansionKey: Constants
                                                            .optimizationKey,
                                                        onExpansionChanged:
                                                            (val) {},
                                                        childrenPadding:
                                                            EdgeInsets.zero,
                                                        iconColor: AppColors
                                                            .secondColor,
                                                        collapsedIconColor:
                                                            AppColors
                                                                .secondColor,
                                                        expandedAlignment:
                                                            Alignment.center,
                                                        expandedCrossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        trailing: const Row(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Padding(
                                                              padding: EdgeInsets
                                                                  .only(
                                                                      left:
                                                                          6.0),
                                                              child: Icon(
                                                                Icons
                                                                    .expand_more,
                                                                size: 30.0,
                                                                color: Constants
                                                                    .darkColor,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        title: CreateAdCubit.get(
                                                                        context)
                                                                    .optimization !=
                                                                null
                                                            ? CustomText(
                                                                fontSize: 12.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                text: CreateAdCubit.get(
                                                                            context)
                                                                        .optimization
                                                                        ?.showName ??
                                                                    "")
                                                            : AccountHintText(
                                                                isDefaultHint:
                                                                    false,
                                                                hint:
                                                                    'Advertising Objective'
                                                                        .tr,
                                                              ),
                                                        decoration:
                                                            ShapeDecoration(
                                                          color: Colors.white,
                                                          shape:
                                                              RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        20),
                                                          ),
                                                          shadows: const [
                                                            BoxShadow(
                                                              color: Color(
                                                                  0x3F000000),
                                                              blurRadius: 40,
                                                              offset:
                                                                  Offset(0, 0),
                                                              spreadRadius: -10,
                                                            ),
                                                          ],
                                                        ),
                                                        children: [
                                                          ListView.builder(
                                                            shrinkWrap: true,
                                                            physics:
                                                                const NeverScrollableScrollPhysics(),
                                                            itemCount: GetOptimizationsCubit
                                                                        .get(
                                                                            optContext)
                                                                    .opt
                                                                    .length +
                                                                1, // +1 for the header
                                                            itemBuilder:
                                                                (context,
                                                                    index) {
                                                              if (index == 0) {
                                                                // Header for Awareness goals
                                                                return Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          16.0),
                                                                  child:
                                                                      CustomText(
                                                                    text:
                                                                        'Increase Sales Objectives'
                                                                            .tr,
                                                                    fontSize:
                                                                        13.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                  ),
                                                                );
                                                              } else {
                                                                final optIndex =
                                                                    index -
                                                                        1; // Adjust index for the optimization list
                                                                return InkWell(
                                                                  onTap: () {
                                                                    CreateAdCubit.get(
                                                                            ctx1)
                                                                        .updateCampaignProcess4();
                                                                    CreateAdCubit.get(
                                                                            ctx1)
                                                                        .setSelectedOptimization(
                                                                            GetOptimizationsCubit.get(optContext).opt[optIndex]);
                                                                    GetBillingEventsCubit.get(billContext).getBillingEvents(
                                                                        context:
                                                                            context,
                                                                        optimizationId:
                                                                            GetOptimizationsCubit.get(optContext).opt[optIndex].id ??
                                                                                0);
                                                                    CreateAdCubit.get(
                                                                            context)
                                                                        .billingEvent = null;
                                                                    Constants
                                                                        .optimizationKey
                                                                        .currentState
                                                                        ?.collapse();
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: Constants
                                                                          .gray
                                                                          .withOpacity(
                                                                              0.15),
                                                                      border: Border
                                                                          .symmetric(
                                                                        horizontal:
                                                                            BorderSide(color: Constants.gray.withOpacity(0.3)),
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Padding(
                                                                      padding: const EdgeInsets
                                                                          .symmetric(
                                                                          vertical:
                                                                              18,
                                                                          horizontal:
                                                                              20),
                                                                      child:
                                                                          CustomText(
                                                                        maxLines:
                                                                            3,
                                                                        fontSize:
                                                                            12.sp,
                                                                        text: GetOptimizationsCubit.get(optContext).opt[optIndex].showName ??
                                                                            "",
                                                                      ),
                                                                    ),
                                                                  ),
                                                                );
                                                              }
                                                            },
                                                          ),
                                                        ],
                                                      )
                                                    : CreateAdCubit.get(ctx1)
                                                                .objective ==
                                                            "OUTCOME_LEADS"
                                                        ? ExpansionTileItem(
                                                            expansionKey: Constants
                                                                .optimizationKey,
                                                            onExpansionChanged:
                                                                (val) {},
                                                            childrenPadding:
                                                                EdgeInsets.zero,
                                                            iconColor: AppColors
                                                                .secondColor,
                                                            collapsedIconColor:
                                                                AppColors
                                                                    .secondColor,
                                                            expandedAlignment:
                                                                Alignment
                                                                    .center,
                                                            expandedCrossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .center,
                                                            trailing: const Row(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Padding(
                                                                  padding: EdgeInsets
                                                                      .only(
                                                                          left:
                                                                              6.0),
                                                                  child: Icon(
                                                                    Icons
                                                                        .expand_more,
                                                                    size: 30.0,
                                                                    color: Constants
                                                                        .darkColor,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            title: CreateAdCubit.get(
                                                                            context)
                                                                        .optimization !=
                                                                    null
                                                                ? CustomText(
                                                                    fontSize:
                                                                        12.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    text: CreateAdCubit.get(context)
                                                                            .optimization
                                                                            ?.showName ??
                                                                        "")
                                                                : AccountHintText(
                                                                    isDefaultHint:
                                                                        false,
                                                                    hint:
                                                                        'Advertising Objective'
                                                                            .tr,
                                                                  ),
                                                            decoration:
                                                                ShapeDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            20),
                                                              ),
                                                              shadows: const [
                                                                BoxShadow(
                                                                  color: Color(
                                                                      0x3F000000),
                                                                  blurRadius:
                                                                      40,
                                                                  offset:
                                                                      Offset(
                                                                          0, 0),
                                                                  spreadRadius:
                                                                      -10,
                                                                ),
                                                              ],
                                                            ),
                                                            children: [
                                                              ListView.builder(
                                                                shrinkWrap:
                                                                    true,
                                                                physics:
                                                                    const NeverScrollableScrollPhysics(),
                                                                itemCount: GetOptimizationsCubit.get(
                                                                            optContext)
                                                                        .opt
                                                                        .length +
                                                                    1, // +1 for the header
                                                                itemBuilder:
                                                                    (context,
                                                                        index) {
                                                                  if (index ==
                                                                      0) {
                                                                    // Header for Awareness goals
                                                                    return Padding(
                                                                      padding: const EdgeInsets
                                                                          .all(
                                                                          16.0),
                                                                      child:
                                                                          CustomText(
                                                                        text: 'Attracting Interested Customers Objectives'
                                                                            .tr,
                                                                        fontSize:
                                                                            13.sp,
                                                                        fontWeight:
                                                                            FontWeight.w600,
                                                                      ),
                                                                    );
                                                                  } else {
                                                                    final optIndex =
                                                                        index -
                                                                            1; // Adjust index for the optimization list
                                                                    return InkWell(
                                                                      onTap:
                                                                          () {
                                                                        CreateAdCubit.get(ctx1)
                                                                            .updateCampaignProcess4();
                                                                        CreateAdCubit.get(ctx1)
                                                                            .setSelectedOptimization(GetOptimizationsCubit.get(optContext).opt[optIndex]);
                                                                        GetBillingEventsCubit.get(billContext).getBillingEvents(
                                                                            context:
                                                                                context,
                                                                            optimizationId:
                                                                                GetOptimizationsCubit.get(optContext).opt[optIndex].id ?? 0);
                                                                        CreateAdCubit.get(context).billingEvent =
                                                                            null;
                                                                        GetOptimizationsCubit.get(optContext).opt[optIndex].actualName == "LEAD_GENERATION" ?
                                                                        _createLeadFormDialog(
                                                                            context, CreateAdCubit.get(ctx1)): null;
                                                                        Constants
                                                                            .optimizationKey
                                                                            .currentState
                                                                            ?.collapse();
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          color: Constants
                                                                              .gray
                                                                              .withOpacity(0.15),
                                                                          border:
                                                                              Border.symmetric(
                                                                            horizontal:
                                                                                BorderSide(color: Constants.gray.withOpacity(0.3)),
                                                                          ),
                                                                        ),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsets
                                                                              .symmetric(
                                                                              vertical: 18,
                                                                              horizontal: 20),
                                                                          child:
                                                                              CustomText(
                                                                            maxLines:
                                                                                3,
                                                                            fontSize:
                                                                                12.sp,
                                                                            text:
                                                                                GetOptimizationsCubit.get(optContext).opt[optIndex].showName ?? "",
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    );
                                                                  }
                                                                },
                                                              ),
                                                            ],
                                                          )
                                                        : ExpansionTileItem(
                                                            expansionKey: Constants
                                                                .optimizationKey,
                                                            onExpansionChanged:
                                                                (val) {},
                                                            childrenPadding:
                                                                EdgeInsets.zero,
                                                            iconColor: AppColors
                                                                .secondColor,
                                                            collapsedIconColor:
                                                                AppColors
                                                                    .secondColor,
                                                            expandedAlignment:
                                                                Alignment
                                                                    .center,
                                                            expandedCrossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .center,
                                                            trailing: const Row(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Padding(
                                                                  padding: EdgeInsets
                                                                      .only(
                                                                          left:
                                                                              6.0),
                                                                  child: Icon(
                                                                    Icons
                                                                        .expand_more,
                                                                    size: 30.0,
                                                                    color: Constants
                                                                        .darkColor,
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                            title: CreateAdCubit.get(
                                                                            context)
                                                                        .optimization !=
                                                                    null
                                                                ? CustomText(
                                                                    fontSize:
                                                                        12.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    text: CreateAdCubit.get(context)
                                                                            .optimization
                                                                            ?.showName ??
                                                                        "")
                                                                : AccountHintText(
                                                                    isDefaultHint:
                                                                        false,
                                                                    hint:
                                                                        'Advertising Objective'
                                                                            .tr,
                                                                  ),
                                                            decoration:
                                                                ShapeDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            20),
                                                              ),
                                                              shadows: const [
                                                                BoxShadow(
                                                                  color: Color(
                                                                      0x3F000000),
                                                                  blurRadius:
                                                                      40,
                                                                  offset:
                                                                      Offset(
                                                                          0, 0),
                                                                  spreadRadius:
                                                                      -10,
                                                                )
                                                              ],
                                                            ),
                                                            children: [
                                                              ListView.builder(
                                                                shrinkWrap:
                                                                    true,
                                                                physics:
                                                                    const NeverScrollableScrollPhysics(),
                                                                itemCount: GetOptimizationsCubit.get(
                                                                            optContext)
                                                                        .opt
                                                                        .length +
                                                                    1,
                                                                // 3 for the first text + 3 for the second text
                                                                itemBuilder:
                                                                    (context,
                                                                        index) {
                                                                  // Check if the index is for the first section title
                                                                  if (index ==
                                                                      0) {
                                                                    return Padding(
                                                                      padding: const EdgeInsets
                                                                          .all(
                                                                          16.0),
                                                                      child:
                                                                          CustomText(
                                                                        text: 'Drive More Visits Objectives'
                                                                            .tr,
                                                                        fontSize:
                                                                            13.sp,
                                                                        fontWeight:
                                                                            FontWeight.w600,
                                                                      ),
                                                                    );
                                                                  } else
                                                                  // if (index >=
                                                                  //       1 &&
                                                                  //   index <=
                                                                  //       3)
                                                                  {
                                                                    // Return the first three items
                                                                    final optIndex =
                                                                        index -
                                                                            1; // Adjust index for the optimization list
                                                                    return InkWell(
                                                                      onTap:
                                                                          () {
                                                                        CreateAdCubit.get(ctx1)
                                                                            .updateCampaignProcess4();
                                                                        CreateAdCubit.get(ctx1)
                                                                            .setSelectedOptimization(GetOptimizationsCubit.get(optContext).opt[optIndex]);
                                                                        GetBillingEventsCubit.get(billContext)
                                                                            .getBillingEvents(
                                                                          context:
                                                                              context,
                                                                          optimizationId:
                                                                              GetOptimizationsCubit.get(optContext).opt[optIndex].id ?? 0,
                                                                        );
                                                                        CreateAdCubit.get(context).billingEvent =
                                                                            null;
                                                                        Constants
                                                                            .optimizationKey
                                                                            .currentState
                                                                            ?.collapse();
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          color: Constants
                                                                              .gray
                                                                              .withOpacity(0.15),
                                                                          border:
                                                                              Border.symmetric(
                                                                            horizontal:
                                                                                BorderSide(color: Constants.gray.withOpacity(0.3)),
                                                                          ),
                                                                        ),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsets
                                                                              .symmetric(
                                                                              vertical: 18,
                                                                              horizontal: 20),
                                                                          child:
                                                                              CustomText(
                                                                            maxLines:
                                                                                3,
                                                                            fontSize:
                                                                                12.sp,
                                                                            text:
                                                                                GetOptimizationsCubit.get(optContext).opt[optIndex].showName ?? "",
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    );
                                                                  }
                                                                  // else if (index ==
                                                                  //     4) {
                                                                  //   // Text before the second section
                                                                  //   return Padding(
                                                                  //     padding: const EdgeInsets
                                                                  //         .all(
                                                                  //         16.0),
                                                                  //     child:
                                                                  //         CustomText(
                                                                  //       text:
                                                                  //           'other goals',
                                                                  //       fontSize:
                                                                  //           13.sp,
                                                                  //       fontWeight:
                                                                  //           FontWeight.w600,
                                                                  //     ),
                                                                  //   );
                                                                  // } else if (index >=
                                                                  //         5 &&
                                                                  //     index <=
                                                                  //         7) {
                                                                  //   // Return the second set of three items
                                                                  //   final optIndex =
                                                                  //       index -
                                                                  //           5; // Adjust index for the optimization list
                                                                  //   return InkWell(
                                                                  //     onTap:
                                                                  //         () {
                                                                  //       CreateAdCubit.get(ctx1)
                                                                  //           .updateCampaignProcess4();
                                                                  //       CreateAdCubit.get(ctx1).setSelectedOptimization(GetOptimizationsCubit.get(optContext).opt[GetOptimizationsCubit.get(optContext).opt.length -
                                                                  //           3 +
                                                                  //           optIndex]);
                                                                  //       GetBillingEventsCubit.get(billContext)
                                                                  //           .getBillingEvents(
                                                                  //         context:
                                                                  //             context,
                                                                  //         optimizationId:
                                                                  //             GetOptimizationsCubit.get(optContext).opt[GetOptimizationsCubit.get(optContext).opt.length - 3 + optIndex].id ?? 0,
                                                                  //       );
                                                                  //       CreateAdCubit.get(context).billingEvent =
                                                                  //           null;
                                                                  //       Constants
                                                                  //           .optimizationKey
                                                                  //           .currentState
                                                                  //           ?.collapse();
                                                                  //     },
                                                                  //     child:
                                                                  //         Container(
                                                                  //       decoration:
                                                                  //           BoxDecoration(
                                                                  //         color:
                                                                  //             Constants.gray.withOpacity(0.15),
                                                                  //         border:
                                                                  //             Border.symmetric(
                                                                  //           horizontal: BorderSide(color: Constants.gray.withOpacity(0.3)),
                                                                  //         ),
                                                                  //       ),
                                                                  //       child:
                                                                  //           Padding(
                                                                  //         padding:
                                                                  //             const EdgeInsets.symmetric(vertical: 18, horizontal: 20),
                                                                  //         child:
                                                                  //             CustomText(
                                                                  //           maxLines: 3,
                                                                  //           fontSize: 12.sp,
                                                                  //           text: GetOptimizationsCubit.get(optContext).opt[GetOptimizationsCubit.get(optContext).opt.length - 3 + optIndex].showName ?? "",
                                                                  //         ),
                                                                  //       ),
                                                                  //     ),
                                                                  //   );
                                                                  // }

                                                                  // Fallback in case of an unexpected index
                                                                  return const SizedBox
                                                                      .shrink();
                                                                },
                                                              ),
                                                            ],
                                                          ),
                                      ],
                                    ),
                          GetBillingEventsCubit.get(billContext).events.isEmpty
                              ? const SizedBox()
                              : Column(
                                  children: [
                                    const SizedBox(height: 20),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8.0),
                                      child: Row(
                                        children: [
                                          CustomText(
                                            text: "Billing Event".tr,
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w400,
                                            alignment: AlignmentDirectional
                                                .centerStart,
                                            color: Constants.primaryTextColor,
                                          ),
                                          CustomText(
                                            text: "*",
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w400,
                                            alignment: AlignmentDirectional
                                                .centerStart,
                                            color: Constants.redColor,
                                          ),
                                        ],
                                      ),
                                    ),
                                    20.verticalSpace,
                                    ExpansionTileItem(
                                      expansionKey: Constants.billingKey,
                                      onExpansionChanged: (val) {},
                                      childrenPadding: EdgeInsets.zero,
                                      iconColor: AppColors.secondColor,
                                      collapsedIconColor: AppColors.secondColor,
                                      expandedAlignment: Alignment.center,
                                      expandedCrossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      trailing: const Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(left: 6.0),
                                            child: Icon(
                                              Icons.expand_more,
                                              size: 30.0,
                                              color: Constants.darkColor,
                                            ),
                                          )
                                        ],
                                      ),
                                      title: CreateAdCubit.get(context)
                                                  .billingEvent !=
                                              null
                                          ? CustomText(
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w500,
                                              text: CreateAdCubit.get(context)
                                                      .billingEvent
                                                      ?.showName ??
                                                  "")
                                          : AccountHintText(
                                              isDefaultHint: false,
                                              hint: 'Billing Event  ',
                                            ),
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x3F000000),
                                            blurRadius: 40,
                                            offset: Offset(0, 0),
                                            spreadRadius: -10,
                                          )
                                        ],
                                      ),
                                      children: [
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemBuilder: (item, index) {
                                            return InkWell(
                                              onTap: () {
                                                CreateAdCubit.get(ctx1)
                                                    .updateCampaignProcess4();
                                                CreateAdCubit.get(ctx1)
                                                    .setSelectedBillingEvent(
                                                        GetBillingEventsCubit
                                                                .get(
                                                                    billContext)
                                                            .events[index]);
                                                Constants
                                                    .billingKey.currentState
                                                    ?.collapse();
                                              },
                                              child: Container(
                                                decoration: BoxDecoration(
                                                    color: Constants.gray
                                                        .withOpacity(0.15),
                                                    border: Border.symmetric(
                                                        horizontal: BorderSide(
                                                            color: Constants
                                                                .gray
                                                                .withOpacity(
                                                                    0.3)))),
                                                child: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 18,
                                                      horizontal: 20),
                                                  child: CustomText(
                                                      maxLines: 3,
                                                      fontSize: 12.sp,
                                                      text: GetBillingEventsCubit
                                                                  .get(
                                                                      billContext)
                                                              .events[index]
                                                              .showName ??
                                                          ""),
                                                ),
                                              ),
                                            );
                                          },
                                          itemCount: GetBillingEventsCubit.get(
                                                  billContext)
                                              .events
                                              .length,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                          SizedBox(height: 30.h),
                          SizedBox(
                            width: 235.w,
                            child: ButtonWidget(
                              text: "Save".tr,
                              onTap: () {
                                if (CreateAdCubit.get(context)
                                    .campaignFormKey
                                    .currentState!
                                    .validate()) {
                                  if (CreateAdCubit.get(ctx1).objective ==
                                      null) {
                                    showErrorToast(
                                        "Please Select Ad Objective".tr);
                                  } else if (CreateAdCubit.get(ctx1)
                                          .optimization ==
                                      null) {
                                    showErrorToast(
                                        "please select performance goal".tr);
                                  } else if (CreateAdCubit.get(context)
                                          .billingEvent ==
                                      null) {
                                    showErrorToast("Billing Event".tr);
                                  } else {
                                    CreateAdCubit.get(ctx1).adModel =
                                        CreateAdCubit.get(ctx1)
                                            .adModel
                                            .copyWith(
                                      x: 'jimy',
                                      isInstaPost: true,
                                      instaUserId: instance
                                          .get<HiveHelper>()
                                          .getUser()
                                          ?.instUserId,
                                      adAccountId: instance<HiveHelper>()
                                          .getUser()
                                          ?.defaultAccountId,
                                      pageId: int.parse(instance<HiveHelper>()
                                              .getUser()
                                              ?.defaultPageId ??
                                          "0"),
                                      pageAccessToken: instance<HiveHelper>()
                                          .getUser()
                                          ?.defaultPageAccessToken,
                                      geoLocations: CreateAdCubit.get(context)
                                          .geoLocations,
                                      campaignName: CreateAdCubit.get(context)
                                          .campaignNameController
                                          .text,
                                      objective:
                                          CreateAdCubit.get(ctx1).objective,
                                      optimizationGoal: CreateAdCubit.get(ctx1)
                                              .optimization
                                              ?.actualName ??
                                          "",
                                      billingEven: CreateAdCubit.get(ctx1)
                                              .billingEvent
                                              ?.actualName ??
                                          "",
                                      status:
                                          CreateAdCubit.get(ctx1).statusType,
                                      specialAdCategories: ["NONE"],
                                    );
                                    CreateAdCubit.get(ctx1).isAddCreated =
                                        false;

                                    widget.expansionTileKey.currentState
                                        ?.collapse();
                                    print(
                                        "create campaign ${CreateAdCubit.get(ctx1).adModel.campaignName}");
                                    print(
                                        "create campaign ${CreateAdCubit.get(ctx1).adModel.adAccountId}");
                                    print(
                                        "create campaign ${CreateAdCubit.get(ctx1).adModel.objective}");
                                    print(
                                        "create campaign ${CreateAdCubit.get(ctx1).adModel.status}");
                                    print(
                                        "create campaign ${CreateAdCubit.get(ctx1).adModel.specialAdCategories}");
                                    CreateAdCubit.get(ctx1).isCampaignCreated =
                                        true;
                                  }
                                } else {
                                  // Show error toast if the form is invalid
                                  showErrorToast(
                                      "Please fill in all the required fields correctly."
                                          .tr);
                                }
                              },
                            ),
                          ),
                        ],
                      );
                    },
                  );
                }
                return const SizedBox();
              },
            );
          },
        );
      },
    );
  }

  void _createLeadFormDialog(
      BuildContext context, CreateAdCubit createAdCubit) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppColors.whiteColor,
          child: PageViewDialog(
            createAdCubit: createAdCubit,
          ),
        );
      },
    );
  }
}

class PageViewDialog extends StatefulWidget {
  CreateAdCubit createAdCubit;

  PageViewDialog({super.key, required this.createAdCubit});

  @override
  _PageViewDialogState createState() => _PageViewDialogState();
}

class _PageViewDialogState extends State<PageViewDialog> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  void _goToNextPage(GlobalKey<FormState> key) {
    // if(key.currentState!.validate()){
    //
    // }
    if (_currentPage < 3) {
      _controller.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
      setState(() {
        _currentPage++;
      });
    }
  }

  void _goToPreviousPage() {
    if (_currentPage > 0) {
      _controller.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
      setState(() {
        _currentPage--;
      });
    }
  }

  @override
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: SizeConfig.screenHeight(context) * 0.5, // Adjust height as needed
      child: Form(
        key: CreateAdCubit.get(context).leadsFormKey,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 30.sp),
              child: Container(
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(40),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x3D000000),
                      blurRadius: 13.93,
                      offset: Offset(0, 0),
                      spreadRadius: -3.80,
                    )
                  ],
                ),
                child: TabsWidget(
                  newObject: 'New Form',
                  existObject: 'Existing Form',
                  selectedTab: CreateAdCubit.get(context).selectFormTab,
                  onTabChanged: (tab) {
                    if (CreateAdCubit.get(context).selectFormTab != tab) {
                      setState(() {
                        CreateAdCubit.get(context).changeFormTabIndex(tab);
                        CreateAdCubit.get(context).leadDesc.text = "";
                        CreateAdCubit.get(context).leadMessage.text = "";
                        CreateAdCubit.get(context).ctaController.text = "";
                        CreateAdCubit.get(context).websiteLinkController.text =
                            "";
                        CreateAdCubit.get(context).linkTextController.text = "";
                        CreateAdCubit.get(context).linkController.text = "";
                        CreateAdCubit.get(context).leadHeadlineController.text =
                            "";
                        CreateAdCubit.get(context).questions = [
                          Question(
                              name: 'What is your city?',
                              value: 'CITY',
                              isChecked: false),
                          Question(
                              name: 'What is your company name?',
                              value: 'COMPANY_NAME',
                              isChecked: false),
                          Question(
                              name: 'What is your country name?',
                              value: 'COUNTRY',
                              isChecked: false),
                          Question(
                              name: 'What is your gender?',
                              value: 'GENDER',
                              isChecked: false),
                          Question(
                              name: 'What is your first name?',
                              value: 'FIRST_NAME',
                              isChecked: false),
                          Question(
                              name: 'What is your full name?',
                              value: 'FULL_NAME',
                              isChecked: false),
                          Question(
                              name: 'What is your job title?',
                              value: 'JOB_TITLE',
                              isChecked: false),
                          Question(
                              name: 'What is your date of birth?',
                              value: 'DOB',
                              isChecked: false),
                          Question(
                              name: 'What is your email?',
                              value: 'EMAIL',
                              isChecked: false),
                          Question(
                              name: 'What is your last name?',
                              value: 'LAST_NAME',
                              isChecked: false),
                          Question(
                              name: 'What is your marital status?',
                              value: 'MARITIAL_STATUS',
                              isChecked: false),
                          Question(
                              name: 'What is your phone?',
                              value: 'PHONE',
                              isChecked: false),
                          Question(
                              name: 'What is your state?',
                              value: 'STATE',
                              isChecked: false),
                          Question(
                              name: 'What is your street address?',
                              value: 'STREET_ADDRESS',
                              isChecked: false),
                        ];
                        // CUSTOM, CITY, COMPANY_NAME, COUNTRY, DOB, EMAIL,
                        // GENDER, FIRST_NAME, FULL_NAME, JOB_TITLE, LAST_NAME, MARITIAL_STATUS, PHONE, PHONE_OTP, POST_CODE, PROVINCE,
                        // RELATIONSHIP_STATUS, STATE, STREET_ADDRESS, ZIP, WORK_EMAIL, MILITARY_STATUS, WORK_PHONE_NUMBER, SLIDER, STORE_LOOKUP,
                        // STORE_LOOKUP_WITH_TYPEAHEAD, DATE_TIME, ID_CPF, ID_AR_DNI, ID_CL_RUT, ID_CO_CC, ID_EC_CI, ID_PE_DNI, ID_MX_RFC,
                        // JOIN_CODE, USER_PROVIDED_PHONE_NUMBER, FACEBOOK_LEAD_ID, EMAIL_ALIAS, MESSENGER

                        CreateAdCubit.get(context).lang = [
                          Language(name: 'Arabic', value: 'AR_AR'),
                          Language(name: 'English', value: 'EN_US'),
                        ];

                        CreateAdCubit.get(context).addedQuestions = [];
                        CreateAdCubit.get(context).langValue = null;
                        CreateAdCubit.get(context).langIndex = null;

                        CreateAdCubit.get(context).formId = null;
                        CreateAdCubit.get(context).formIndex = null;
                      });
                    }
                  },
                ),
              ),
            ),
            (CreateAdCubit.get(context).selectFormTab == 0)
                ? Expanded(
                    child: Column(
                      children: [
                        Expanded(
                          child: PageView(
                            controller: _controller,
                            onPageChanged: (index) {
                              setState(() {
                                _currentPage = index;
                              });
                            },
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.sp, vertical: 16.sp),
                                child: Column(
                                  children: [
                                    CustomTextFormField(
                                      label: "Headline",
                                      showIsReqiredFlag: true,
                                      textFontSize: 12,
                                      key: const ValueKey('headline'),
                                      hintText: "Headline",
                                      textInputAction: TextInputAction.next,
                                      keyboardType: TextInputType.text,
                                      controller: CreateAdCubit.get(context)
                                          .leadHeadlineController,
                                      validator: (value) =>
                                          AppValidator.validateIdentity(
                                              value, context),
                                    ),
                                    25.verticalSpace,
                                    ListView.builder(
                                      padding: EdgeInsets.zero,
                                      itemBuilder: (item, index) {
                                        return Row(
                                          children: [
                                            Radio<int>(
                                              value: index,
                                              groupValue:
                                                  CreateAdCubit.get(context)
                                                      .langIndex,
                                              onChanged: (int? newValue) {
                                                setState(() {
                                                  CreateAdCubit.get(context)
                                                      .setSelectedLang(
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .lang[index]
                                                              .value,
                                                          index);
                                                });
                                                print("agdsgdsfs${CreateAdCubit.get(context)
                                                        .langValue}");
                                              },
                                              activeColor: AppColors.mainColor,
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8.0),
                                              child: SizedBox(
                                                width: 150.h,
                                                child: CustomText(
                                                  text:
                                                      CreateAdCubit.get(context)
                                                              .lang[index]
                                                              .name ??
                                                          "",
                                                  maxLines: 10,
                                                  textAlign: TextAlign.right,
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                      itemCount: CreateAdCubit.get(context)
                                          .lang
                                          .length,
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                    ),
                                  ],
                                ),
                              ),
                              Center(
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  // physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (item, index) {
                                    return Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Checkbox(
                                          onChanged: (value) {
                                            setState(() {
                                              CreateAdCubit.get(context)
                                                      .questions[index]
                                                      .isChecked =
                                                  value!; // Toggle the checkbox value
                                              CreateAdCubit.get(context)
                                                  .setSelectedQuestion();
                                              print("igPositions${CreateAdCubit.get(context)
                                                      .addedQuestions}");
                                            });
                                          },
                                          value: CreateAdCubit.get(context)
                                              .questions[index]
                                              .isChecked,
                                          activeColor:
                                              Constants.primaryTextColor,
                                          checkColor: Colors.white,
                                          // Color of the checkmark
                                          focusColor:
                                              Constants.primaryTextColor,
                                          // Color of the border when focused
                                          side: const BorderSide(
                                            color: Constants.primaryTextColor,
                                            width: 2,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(2.0),
                                          child: CustomText(
                                            text: CreateAdCubit.get(context)
                                                    .questions[index]
                                                    .name ??
                                                "",
                                            color: Constants.primaryTextColor,
                                            alignment:
                                                AlignmentDirectional.center,
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                  itemCount: CreateAdCubit.get(context)
                                      .questions
                                      .length,
                                ),
                              ),
                              Center(
                                  child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.sp, vertical: 16.sp),
                                child: Column(
                                  children: [
                                    CustomTextFormField(
                                      label: "Privacy Policy Link",
                                      showIsReqiredFlag: true,
                                      textFontSize: 12,
                                      key: const ValueKey('link'),
                                      hintText: "Privacy Policy Link",
                                      textInputAction: TextInputAction.next,
                                      keyboardType: TextInputType.text,
                                      controller: CreateAdCubit.get(context)
                                          .linkController,
                                      validator: (value) =>
                                          AppValidator.validateIdentity(
                                              value, context),
                                    ),
                                    const SizedBox(height: 20),
                                    CustomTextFormField(
                                      label: "Privacy Policy Text",
                                      showIsReqiredFlag: true,
                                      textFontSize: 12,
                                      key: const ValueKey('lint_text'),
                                      hintText: "Privacy Policy Text",
                                      textInputAction: TextInputAction.next,
                                      keyboardType: TextInputType.text,
                                      controller: CreateAdCubit.get(context)
                                          .linkTextController,
                                      validator: (value) =>
                                          AppValidator.validateIdentity(
                                              value, context),
                                    ),
                                  ],
                                ),
                              )),
                              Center(
                                  child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.sp, vertical: 16.sp),
                                child: SingleChildScrollView(
                                  child: Column(
                                    children: [
                                      CustomTextFormField(
                                        label: "Lead Message",
                                        showIsReqiredFlag: true,
                                        textFontSize: 12,
                                        key: const ValueKey('message'),
                                        hintText: "Lead Message",
                                        textInputAction: TextInputAction.next,
                                        keyboardType: TextInputType.text,
                                        controller: CreateAdCubit.get(context)
                                            .leadMessage,
                                        validator: (value) =>
                                            AppValidator.validateIdentity(
                                                value, context),
                                      ),
                                      const SizedBox(height: 20),
                                      CustomTextFormField(
                                        label: "Description",
                                        showIsReqiredFlag: true,
                                        textFontSize: 12,
                                        key: const ValueKey('description'),
                                        hintText: "Description",
                                        textInputAction: TextInputAction.next,
                                        keyboardType: TextInputType.text,
                                        controller:
                                            CreateAdCubit.get(context).leadDesc,
                                        validator: (value) =>
                                            AppValidator.validateIdentity(
                                                value, context),
                                      ),
                                      const SizedBox(height: 20),
                                      CustomTextFormField(
                                        label: "Website Link",
                                        showIsReqiredFlag: true,
                                        textFontSize: 12,
                                        key: const ValueKey('web_link'),
                                        hintText: "Website Link",
                                        textInputAction: TextInputAction.next,
                                        keyboardType: TextInputType.text,
                                        controller: CreateAdCubit.get(context)
                                            .websiteLinkController,
                                        validator: (value) =>
                                            AppValidator.validateIdentity(
                                                value, context),
                                      ),
                                      const SizedBox(height: 20),
                                      CustomTextFormField(
                                        label: "Call to action",
                                        showIsReqiredFlag: true,
                                        textFontSize: 12,
                                        key: const ValueKey('cta'),
                                        hintText: "Call to action",
                                        textInputAction: TextInputAction.next,
                                        keyboardType: TextInputType.text,
                                        controller: CreateAdCubit.get(context)
                                            .ctaController,
                                        validator: (value) =>
                                            AppValidator.validateIdentity(
                                                value, context),
                                      ),
                                      const SizedBox(height: 20),
                                      // CustomText(
                                      //   text: 'Additional Action',
                                      //   fontSize: 12.sp,
                                      //   color: Constants.primaryTextColor,
                                      //   fontWeight: FontWeight.w400,
                                      //   alignment: AlignmentDirectional.centerStart,
                                      // ),
                                      // 10.verticalSpace,
                                      // ListView.builder(
                                      //   padding: EdgeInsets.zero,
                                      //   itemBuilder: (item, index) {
                                      //     return Row(
                                      //       children: [
                                      //         Radio<int>(
                                      //           value: index,
                                      //           groupValue:
                                      //           CreateAdCubit.get(context)
                                      //               .actionIndex,
                                      //           onChanged: (int? newValue) {
                                      //             setState(() {
                                      //               CreateAdCubit.get(context)
                                      //                   .setSelectedAction(
                                      //                   CreateAdCubit.get(
                                      //                       context)
                                      //                       .actions[index]
                                      //                       .value,
                                      //                   index);
                                      //             });
                                      //             print("agdsgdsfs" +
                                      //                 CreateAdCubit.get(context)
                                      //                     .actionValue
                                      //                     .toString());
                                      //           },
                                      //           activeColor: AppColors.mainColor,
                                      //         ),
                                      //         Padding(
                                      //           padding:
                                      //           const EdgeInsets.symmetric(
                                      //               horizontal: 8.0),
                                      //           child: SizedBox(
                                      //             width: 150.h,
                                      //             child: CustomText(
                                      //               text:
                                      //               CreateAdCubit.get(context)
                                      //                   .actions[index]
                                      //                   .name ??
                                      //                   "",
                                      //               maxLines: 10,
                                      //               textAlign: TextAlign.right,
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ],
                                      //     );
                                      //   },
                                      //   itemCount: CreateAdCubit.get(context)
                                      //       .actions
                                      //       .length,
                                      //   shrinkWrap: true,
                                      //   physics:
                                      //   const NeverScrollableScrollPhysics(),
                                      // ),
                                      SizedBox(
                                        width: 235.w,
                                        child: ButtonWidget(
                                          text: "Save",
                                          onTap: () {
                                            CreateAdCubit.get(context)
                                                .destinationType = "ON_AD";

                                            CreateAdCubit.get(context).adModel = CreateAdCubit.get(context).adModel.copyWith(
                                                formName: CreateAdCubit.get(context)
                                                    .leadHeadlineController
                                                    .text,
                                                formLocale: CreateAdCubit.get(context)
                                                    .langValue,
                                                questions: CreateAdCubit.get(context)
                                                    .addedQuestions,
                                                formLinkText:
                                                    CreateAdCubit.get(context)
                                                        .linkTextController
                                                        .text,
                                                formUrl: CreateAdCubit.get(context)
                                                    .linkController
                                                    .text,
                                                formTitle: CreateAdCubit.get(context)
                                                    .leadMessage
                                                    .text,
                                                formBody: CreateAdCubit.get(context)
                                                    .leadDesc
                                                    .text,
                                                formButtonType: "VIEW_WEBSITE",
                                                destinationType:
                                                    CreateAdCubit.get(context)
                                                        .destinationType,
                                                formButtonText: CreateAdCubit.get(context).ctaController.text,
                                                formWebsiteUrl: CreateAdCubit.get(context).websiteLinkController.text);
                                            CreateAdCubit.get(context)
                                                .isCampaignCreated = true;
                                            Navigator.of(context).pop();
                                            print("asfasf${CreateAdCubit.get(context)
                                                    .addedQuestions}");
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              color: _currentPage == 0 ? Colors.grey : null,
                              onPressed:
                                  _currentPage == 0 ? null : _goToPreviousPage,
                            ),
                            Text('${_currentPage + 1} of 4'),
                            IconButton(
                              icon: const Icon(Icons.arrow_forward),
                              color: _currentPage == 3 ? Colors.grey : null,
                              onPressed: () => _currentPage == 3
                                  ? null
                                  : _goToNextPage(
                                      CreateAdCubit.get(context).leadsFormKey),
                            ),
                          ],
                        ),
                      ],
                    ),
                  )
                : Expanded(
                    child: Column(
                      children: [
                        Expanded(
                          child: Center(
                            child:
                                BlocBuilder<GetFormsCubit, GetLeadsFormsState>(
                              builder: (leadContext, leadState) {
                                return leadState is GetLeadsFormsStateLoading
                                    ? const LoadingWidget(
                                        isCircle: true,
                                      )
                                    : leadState is GetLeadsFormsStateError
                                        ? HandleErrorWidget(
                                            fun: () {
                                              GetFormsCubit.get(leadContext)
                                                  .getLeadForms(
                                                pageAccessToken: instance<
                                                            HiveHelper>()
                                                        .getUser()
                                                        ?.defaultPageAccessToken ??
                                                    CreateAdCubit.get(context)
                                                        .metaPages
                                                        ?.accessToken ??
                                                    "",
                                                pageId: instance<HiveHelper>()
                                                        .getUser()
                                                        ?.defaultPageId ??
                                                    CreateAdCubit.get(context)
                                                        .metaPages
                                                        ?.id ??
                                                    "",
                                                context: Constants.navigatorKey
                                                        .currentContext ??
                                                    leadContext,
                                              );
                                            },
                                            failure: leadState.message)
                                        : Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 20.sp,
                                                vertical: 16.sp),
                                            child: ListView.builder(
                                              padding: EdgeInsets.zero,
                                              itemBuilder: (item, index) {
                                                return FittedBox(
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Radio<int>(
                                                        value: index,
                                                        groupValue:
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .formIndex,
                                                        onChanged:
                                                            (int? newValue) {
                                                          setState(() {
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .setSelectedForm(
                                                                    GetFormsCubit.get(
                                                                            leadContext)
                                                                        .forms[
                                                                            index]
                                                                        .id,
                                                                    index);
                                                          });
                                                          print("agdsgdsfs${CreateAdCubit.get(
                                                                      context)
                                                                  .formId}");
                                                        },
                                                        activeColor:
                                                            AppColors.mainColor,
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal:
                                                                    8.0),
                                                        child: SizedBox(
                                                          width: 200.h,
                                                          child: CustomText(
                                                            text: GetFormsCubit.get(
                                                                        leadContext)
                                                                    .forms[
                                                                        index]
                                                                    .name ??
                                                                "",
                                                            maxLines: 10,
                                                            textAlign:
                                                                TextAlign.left,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                              itemCount:
                                                  GetFormsCubit.get(leadContext)
                                                      .forms
                                                      .length,
                                            ),
                                          );
                              },
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 235.w,
                          child: ButtonWidget(
                            text: "Save",
                            onTap: () {
                              if (CreateAdCubit.get(context).formId == null) {
                                showErrorToast("Please select you form");
                              } else {
                                CreateAdCubit.get(context).adModel =
                                    CreateAdCubit.get(context).adModel.copyWith(
                                          formId:
                                              CreateAdCubit.get(context).formId,
                                        );
                                CreateAdCubit.get(context).destinationType =
                                    "ON_AD";
                                Navigator.pop(context);
                              }
                            },
                          ),
                        ),
                        20.verticalSpace,
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_campaign/create_campaign_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_all_campaigns/get_all_campaigns_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/selected_option_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/di/injection.dart';
import '../../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../../utils/res/colors.dart';
import '../../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../../widgets/unselected_option_widget.dart';
import '../../../../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../../../controllers/get_billing_events/get_billing_events_cubit.dart';
import '../../../../controllers/get_objectives/get_objectives_cubit.dart';
import '../../../../controllers/get_optimizations/get_optimizations_cubit.dart';
import '../new_campaign/new_campaign_widget.dart';

class ExistingCampaignWidget extends StatefulWidget {
  CreateCampaignCubit cubit;
  CreateAdCubit adCubit;

  ExistingCampaignWidget(
      {super.key, required this.cubit, required this.adCubit});

  @override
  State<ExistingCampaignWidget> createState() => _ExistingCampaignState();
}

class _ExistingCampaignState extends State<ExistingCampaignWidget> {
  @override
  void initState() {
    GetCampaignsCubit.get(context).getCampaigns(
        context: context,
        accountId: instance<HiveHelper>().getUser()?.defaultAccountId ??
            CreateAdCubit.get(context).adAccount?.id ??
            "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetCampaignsCubit, GetCampaignsState>(
      builder: (context, state) {
        if (state is GetCampaignsStateLoading) {
          return const Column(
            children: [
              SizedBox(height: 20),
              LoadingWidget(
                isCircle: true,
              ),
            ],
          );
        } else if (state is GetCampaignsStateError) {
          return Column(
            children: [
              const SizedBox(height: 20),
              HandleErrorWidget(
                  fun: () {
                    GetCampaignsCubit.get(context).getCampaigns(
                        context: context,
                        accountId: instance<HiveHelper>()
                                .getUser()
                                ?.defaultAccountId ??
                            CreateAdCubit.get(context).adAccount?.id ??
                            "");
                  },
                  failure: state.message),
            ],
          );
        } else if (state is GetCampaignsStateLoaded) {
          return Column(
            children: [
              const SizedBox(height: 20),
              GetCampaignsCubit.get(context).campaigns!.isEmpty
                  ? const Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: 20),
                        CustomText(
                          text: "There is no existing campaigns",
                          color: Constants.gray,
                          alignment: AlignmentDirectional.center,
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          itemCount:
                              GetCampaignsCubit.get(context).campaigns?.length,
                          itemBuilder: (item, index) {
                            return (widget.adCubit.selectedExistingCampaign !=
                                    index)
                                ? InkWell(
                                    onTap: () {
                                      widget.adCubit.selectExistingCampaign(
                                          index,
                                          GetCampaignsCubit.get(context)
                                              .campaigns![index]);
                                      widget.adCubit.campaignProcessPercentage =
                                          0.5;
                                      GetObjectivesCubit.get(context)
                                          .getCurrentCampaignObjective(
                                              context: context,
                                              campaignId:
                                                  GetCampaignsCubit.get(context)
                                                      .campaigns![index]
                                                      .id!);
                                    },
                                    child: UnselectedOptionWidget(
                                        name: GetCampaignsCubit.get(context)
                                                .campaigns?[index]
                                                .name ??
                                            ""),
                                  )
                                : SelectedOptionWidget(
                                    name: GetCampaignsCubit.get(context)
                                            .campaigns?[index]
                                            .name ??
                                        "");
                          },
                        ),
                        if (state.data.result?.next != null)
                          InkWell(
                            onTap: () {
                              GetCampaignsCubit.get(context).loadMoreCampaigns(
                                  url: state.data.result!.next!,
                                  context: context);
                            },
                            child: Container(
                              decoration: ShapeDecoration(
                                gradient: Constants.defGradient,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(38),
                                ),
                                shadows: const [
                                  BoxShadow(
                                    color: Color(0x19000000),
                                    blurRadius: 22,
                                    offset: Offset(0, 4),
                                    spreadRadius: 0,
                                  )
                                ],
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 8.sp, horizontal: 14.sp),
                                child: Text(
                                  'Load More'.tr,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
              BlocBuilder<GetOptimizationsCubit, GetOptimizationsState>(
                  bloc: GetOptimizationsCubit.get(context),
                  builder: (optContext, optState) {
                    return optState is GetOptimizationsStateLoading
                        ? const LoadingWidget(
                            isCircle: true,
                          )
                        : GetOptimizationsCubit.get(optContext).opt.isEmpty
                            ? const SizedBox()
                            : Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: Row(
                                      children: [
                                        CustomText(
                                          text: "Advertising Objective".tr,
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w400,
                                          alignment:
                                              AlignmentDirectional.centerStart,
                                          color: Constants.primaryTextColor,
                                        ),
                                        CustomText(
                                          text: "*",
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w400,
                                          alignment:
                                              AlignmentDirectional.centerStart,
                                          color: Constants.redColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                  20.verticalSpace,
                                  CreateAdCubit.get(optContext).objective ==
                                          "OUTCOME_AWARENESS"
                                      ? ExpansionTileItem(
                                          expansionKey:
                                              Constants.optimizationKey,
                                          onExpansionChanged: (val) {},
                                          childrenPadding: EdgeInsets.zero,
                                          iconColor: AppColors.secondColor,
                                          collapsedIconColor:
                                              AppColors.secondColor,
                                          expandedAlignment: Alignment.center,
                                          expandedCrossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          trailing: const Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Padding(
                                                padding:
                                                    EdgeInsets.only(left: 6.0),
                                                child: Icon(
                                                  Icons.expand_more,
                                                  size: 30.0,
                                                  color: Constants.darkColor,
                                                ),
                                              )
                                            ],
                                          ),
                                          title: CreateAdCubit.get(context)
                                                      .optimization !=
                                                  null
                                              ? CustomText(
                                                  fontSize: 12.sp,
                                                  fontWeight: FontWeight.w500,
                                                  text:
                                                      CreateAdCubit.get(context)
                                                              .optimization
                                                              ?.showName ??
                                                          "")
                                              : AccountHintText(
                                                  isDefaultHint: false,
                                                  hint: 'Advertising Objective'
                                                      .tr,
                                                ),
                                          decoration: ShapeDecoration(
                                            color: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            shadows: const [
                                              BoxShadow(
                                                color: Color(0x3F000000),
                                                blurRadius: 40,
                                                offset: Offset(0, 0),
                                                spreadRadius: -10,
                                              )
                                            ],
                                          ),
                                          children: [
                                            ListView.builder(
                                              shrinkWrap: true,
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  GetOptimizationsCubit.get(
                                                              optContext)
                                                          .opt
                                                          .length +
                                                      1,
                                              // 3 for the first text + 2 for the last text
                                              itemBuilder: (context, index) {
                                                // Check if the index is for the text before the first three items
                                                if (index == 0) {
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            16.0),
                                                    child: CustomText(
                                                        text: 'Brand Awareness'
                                                            .tr,
                                                        fontSize: 13.sp,
                                                        fontWeight:
                                                            FontWeight.w600),
                                                  );
                                                } else
                                                //   if (index >= 1
                                                //     // && index <= 3
                                                // )
                                                {
                                                  // Return the first three items
                                                  final optIndex = index -
                                                      1; // Adjust index for the optimization list
                                                  return InkWell(
                                                    onTap: () {
                                                      CreateAdCubit.get(
                                                              optContext)
                                                          .updateCampaignProcess4();
                                                      CreateAdCubit.get(
                                                              optContext)
                                                          .setSelectedOptimization(
                                                              GetOptimizationsCubit
                                                                      .get(
                                                                          optContext)
                                                                  .opt[optIndex]);
                                                      print("asdfafgdsg${CreateAdCubit.get(
                                                                  context)
                                                              .optimization!
                                                              .actualName}");
                                                      GetBillingEventsCubit.get(
                                                              optContext)
                                                          .getBillingEvents(
                                                              context: context,
                                                              optimizationId:
                                                                  GetOptimizationsCubit.get(
                                                                              optContext)
                                                                          .opt[
                                                                              optIndex]
                                                                          .id ??
                                                                      0);
                                                      CreateAdCubit.get(context)
                                                          .billingEvent = null;
                                                      Constants.optimizationKey
                                                          .currentState
                                                          ?.collapse();
                                                    },
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        color: Constants.gray
                                                            .withOpacity(0.15),
                                                        border:
                                                            Border.symmetric(
                                                          horizontal: BorderSide(
                                                              color: Constants
                                                                  .gray
                                                                  .withOpacity(
                                                                      0.3)),
                                                        ),
                                                      ),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 18,
                                                                horizontal: 20),
                                                        child: CustomText(
                                                          maxLines: 3,
                                                          fontSize: 12.sp,
                                                          text: GetOptimizationsCubit
                                                                      .get(
                                                                          optContext)
                                                                  .opt[optIndex]
                                                                  .showName ??
                                                              "",
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }
                                                // else if (index == 4) {
                                                //   // Text before the last 2 items
                                                //   return Padding(
                                                //     padding:
                                                //         const EdgeInsets
                                                //             .all(16.0),
                                                //     child: CustomText(
                                                //         text:
                                                //             'Video view goals',
                                                //         fontSize: 13.sp,
                                                //         fontWeight:
                                                //             FontWeight
                                                //                 .w600),
                                                //   );
                                                // } else if (index >= 5) {
                                                //   // Return the last two items
                                                //   final optIndex = index -
                                                //       5; // Adjust index for the optimization list
                                                //   final originalIndex =
                                                //       GetOptimizationsCubit
                                                //                   .get(
                                                //                       optContext)
                                                //               .opt
                                                //               .length -
                                                //           2 +
                                                //           optIndex; // Correctly mapping to the last items
                                                //
                                                //   // Check if the original index is valid
                                                //   if (originalIndex <
                                                //       GetOptimizationsCubit
                                                //               .get(
                                                //                   optContext)
                                                //           .opt
                                                //           .length) {
                                                //     return InkWell(
                                                //       onTap: () {
                                                //         CreateAdCubit
                                                //                 .get(
                                                //                     ctx1)
                                                //             .updateCampaignProcess4();
                                                //         CreateAdCubit
                                                //                 .get(
                                                //                     ctx1)
                                                //             .setSelectedOptimization(
                                                //                 GetOptimizationsCubit.get(optContext)
                                                //                     .opt[originalIndex]);
                                                //         GetBillingEventsCubit
                                                //                 .get(
                                                //                     billContext)
                                                //             .getBillingEvents(
                                                //                 context:
                                                //                     context,
                                                //                 optimizationId:
                                                //                     GetOptimizationsCubit.get(optContext).opt[originalIndex].id ??
                                                //                         0);
                                                //         print("asdfafgdsg" +
                                                //             CreateAdCubit.get(
                                                //                     context)
                                                //                 .optimization!
                                                //                 .actualName
                                                //                 .toString());
                                                //
                                                //         CreateAdCubit.get(
                                                //                 context)
                                                //             .billingEvent = null;
                                                //         Constants
                                                //             .optimizationKey
                                                //             .currentState
                                                //             ?.collapse();
                                                //       },
                                                //       child: Container(
                                                //         decoration:
                                                //             BoxDecoration(
                                                //           color: Constants
                                                //               .gray
                                                //               .withOpacity(
                                                //                   0.15),
                                                //           border: Border
                                                //               .symmetric(
                                                //             horizontal: BorderSide(
                                                //                 color: Constants
                                                //                     .gray
                                                //                     .withOpacity(0.3)),
                                                //           ),
                                                //         ),
                                                //         child: Padding(
                                                //           padding: const EdgeInsets
                                                //               .symmetric(
                                                //               vertical:
                                                //                   18,
                                                //               horizontal:
                                                //                   20),
                                                //           child:
                                                //               CustomText(
                                                //             maxLines: 3,
                                                //             fontSize:
                                                //                 12.sp,
                                                //             text: GetOptimizationsCubit.get(optContext)
                                                //                     .opt[originalIndex]
                                                //                     .showName ??
                                                //                 "",
                                                //           ),
                                                //         ),
                                                //       ),
                                                //     );
                                                //   }
                                                // }

                                                // Fallback in case of an unexpected index
                                                return const SizedBox.shrink();
                                              },
                                            ),
                                          ],
                                        )
                                      : CreateAdCubit.get(context).objective ==
                                              "OUTCOME_ENGAGEMENT"
                                          ? ExpansionTileItem(
                                              expansionKey:
                                                  Constants.optimizationKey,
                                              onExpansionChanged: (val) {},
                                              childrenPadding: EdgeInsets.zero,
                                              iconColor: AppColors.secondColor,
                                              collapsedIconColor:
                                                  AppColors.secondColor,
                                              expandedAlignment:
                                                  Alignment.center,
                                              expandedCrossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              trailing: const Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Padding(
                                                    padding: EdgeInsets.only(
                                                        left: 6.0),
                                                    child: Icon(
                                                      Icons.expand_more,
                                                      size: 30.0,
                                                      color:
                                                          Constants.darkColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              title: CreateAdCubit.get(context)
                                                          .optimization !=
                                                      null
                                                  ? CustomText(
                                                      fontSize: 12.sp,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      text: CreateAdCubit.get(
                                                                  context)
                                                              .optimization
                                                              ?.showName ??
                                                          "")
                                                  : AccountHintText(
                                                      isDefaultHint: false,
                                                      hint:
                                                          'Advertising Objective'
                                                              .tr,
                                                    ),
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x3F000000),
                                                    blurRadius: 40,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: -10,
                                                  ),
                                                ],
                                              ),
                                              children: [
                                                ListView.builder(
                                                  shrinkWrap: true,
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  itemCount:
                                                      GetOptimizationsCubit.get(
                                                                  optContext)
                                                              .opt
                                                              .length +
                                                          1, // +1 for the header
                                                  itemBuilder:
                                                      (context, index) {
                                                    if (index == 0) {
                                                      // Header for Awareness goals
                                                      return Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(16.0),
                                                        child: CustomText(
                                                          text:
                                                              'Engagement With Ad Objectives'
                                                                  .tr,
                                                          fontSize: 13.sp,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      );
                                                    } else {
                                                      final optIndex = index -
                                                          1; // Adjust index for the optimization list
                                                      return InkWell(
                                                        onTap: () {
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .updateCampaignProcess4();
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .setSelectedOptimization(
                                                                  GetOptimizationsCubit.get(
                                                                              optContext)
                                                                          .opt[
                                                                      optIndex]);
                                                          GetBillingEventsCubit
                                                                  .get(context)
                                                              .getBillingEvents(
                                                                  context:
                                                                      context,
                                                                  optimizationId:
                                                                      GetOptimizationsCubit.get(optContext)
                                                                              .opt[optIndex]
                                                                              .id ??
                                                                          0);
                                                          CreateAdCubit.get(
                                                                      context)
                                                                  .billingEvent =
                                                              null;
                                                          Constants
                                                              .optimizationKey
                                                              .currentState
                                                              ?.collapse();
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Constants
                                                                .gray
                                                                .withOpacity(
                                                                    0.15),
                                                            border: Border
                                                                .symmetric(
                                                              horizontal: BorderSide(
                                                                  color: Constants
                                                                      .gray
                                                                      .withOpacity(
                                                                          0.3)),
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        18,
                                                                    horizontal:
                                                                        20),
                                                            child: CustomText(
                                                              maxLines: 3,
                                                              fontSize: 12.sp,
                                                              text: GetOptimizationsCubit
                                                                          .get(
                                                                              optContext)
                                                                      .opt[
                                                                          optIndex]
                                                                      .showName ??
                                                                  "",
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    }
                                                  },
                                                ),
                                              ],
                                            )
                                          : CreateAdCubit.get(context)
                                                      .objective ==
                                                  "OUTCOME_SALES"
                                              ? ExpansionTileItem(
                                                  expansionKey:
                                                      Constants.optimizationKey,
                                                  onExpansionChanged: (val) {},
                                                  childrenPadding:
                                                      EdgeInsets.zero,
                                                  iconColor:
                                                      AppColors.secondColor,
                                                  collapsedIconColor:
                                                      AppColors.secondColor,
                                                  expandedAlignment:
                                                      Alignment.center,
                                                  expandedCrossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  trailing: const Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                left: 6.0),
                                                        child: Icon(
                                                          Icons.expand_more,
                                                          size: 30.0,
                                                          color: Constants
                                                              .darkColor,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  title: CreateAdCubit.get(
                                                                  context)
                                                              .optimization !=
                                                          null
                                                      ? CustomText(
                                                          fontSize: 12.sp,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          text: CreateAdCubit.get(
                                                                      context)
                                                                  .optimization
                                                                  ?.showName ??
                                                              "")
                                                      : AccountHintText(
                                                          isDefaultHint: false,
                                                          hint:
                                                              'Advertising Objective'
                                                                  .tr,
                                                        ),
                                                  decoration: ShapeDecoration(
                                                    color: Colors.white,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20),
                                                    ),
                                                    shadows: const [
                                                      BoxShadow(
                                                        color:
                                                            Color(0x3F000000),
                                                        blurRadius: 40,
                                                        offset: Offset(0, 0),
                                                        spreadRadius: -10,
                                                      ),
                                                    ],
                                                  ),
                                                  children: [
                                                    ListView.builder(
                                                      shrinkWrap: true,
                                                      physics:
                                                          const NeverScrollableScrollPhysics(),
                                                      itemCount: GetOptimizationsCubit
                                                                  .get(
                                                                      optContext)
                                                              .opt
                                                              .length +
                                                          1, // +1 for the header
                                                      itemBuilder:
                                                          (context, index) {
                                                        if (index == 0) {
                                                          // Header for Awareness goals
                                                          return Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(16.0),
                                                            child: CustomText(
                                                              text:
                                                                  'Increase Sales Objectives'
                                                                      .tr,
                                                              fontSize: 13.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                            ),
                                                          );
                                                        } else {
                                                          final optIndex = index -
                                                              1; // Adjust index for the optimization list
                                                          return InkWell(
                                                            onTap: () {
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .updateCampaignProcess4();
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .setSelectedOptimization(
                                                                      GetOptimizationsCubit.get(
                                                                              optContext)
                                                                          .opt[optIndex]);
                                                              GetBillingEventsCubit
                                                                      .get(
                                                                          context)
                                                                  .getBillingEvents(
                                                                      context:
                                                                          context,
                                                                      optimizationId:
                                                                          GetOptimizationsCubit.get(optContext).opt[optIndex].id ??
                                                                              0);
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .billingEvent = null;
                                                              Constants
                                                                  .optimizationKey
                                                                  .currentState
                                                                  ?.collapse();
                                                            },
                                                            child: Container(
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: Constants
                                                                    .gray
                                                                    .withOpacity(
                                                                        0.15),
                                                                border: Border
                                                                    .symmetric(
                                                                  horizontal: BorderSide(
                                                                      color: Constants
                                                                          .gray
                                                                          .withOpacity(
                                                                              0.3)),
                                                                ),
                                                              ),
                                                              child: Padding(
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        18,
                                                                    horizontal:
                                                                        20),
                                                                child:
                                                                    CustomText(
                                                                  maxLines: 3,
                                                                  fontSize:
                                                                      12.sp,
                                                                  text: GetOptimizationsCubit.get(
                                                                              optContext)
                                                                          .opt[
                                                                              optIndex]
                                                                          .showName ??
                                                                      "",
                                                                ),
                                                              ),
                                                            ),
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ],
                                                )
                                              : CreateAdCubit.get(context)
                                                          .objective ==
                                                      "OUTCOME_LEADS"
                                                  ? ExpansionTileItem(
                                                      expansionKey: Constants
                                                          .optimizationKey,
                                                      onExpansionChanged:
                                                          (val) {},
                                                      childrenPadding:
                                                          EdgeInsets.zero,
                                                      iconColor:
                                                          AppColors.secondColor,
                                                      collapsedIconColor:
                                                          AppColors.secondColor,
                                                      expandedAlignment:
                                                          Alignment.center,
                                                      expandedCrossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      trailing: const Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                                    left: 6.0),
                                                            child: Icon(
                                                              Icons.expand_more,
                                                              size: 30.0,
                                                              color: Constants
                                                                  .darkColor,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      title: CreateAdCubit.get(
                                                                      context)
                                                                  .optimization !=
                                                              null
                                                          ? CustomText(
                                                              fontSize: 12.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              text: CreateAdCubit
                                                                          .get(
                                                                              context)
                                                                      .optimization
                                                                      ?.showName ??
                                                                  "")
                                                          : AccountHintText(
                                                              isDefaultHint:
                                                                  false,
                                                              hint:
                                                                  'Advertising Objective'
                                                                      .tr,
                                                            ),
                                                      decoration:
                                                          ShapeDecoration(
                                                        color: Colors.white,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(20),
                                                        ),
                                                        shadows: const [
                                                          BoxShadow(
                                                            color: Color(
                                                                0x3F000000),
                                                            blurRadius: 40,
                                                            offset:
                                                                Offset(0, 0),
                                                            spreadRadius: -10,
                                                          ),
                                                        ],
                                                      ),
                                                      children: [
                                                        ListView.builder(
                                                          shrinkWrap: true,
                                                          physics:
                                                              const NeverScrollableScrollPhysics(),
                                                          itemCount: GetOptimizationsCubit
                                                                      .get(
                                                                          optContext)
                                                                  .opt
                                                                  .length +
                                                              1, // +1 for the header
                                                          itemBuilder:
                                                              (context, index) {
                                                            if (index == 0) {
                                                              // Header for Awareness goals
                                                              return Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        16.0),
                                                                child:
                                                                    CustomText(
                                                                  text:
                                                                      'Attracting Interested Customers Objectives'
                                                                          .tr,
                                                                  fontSize:
                                                                      13.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                ),
                                                              );
                                                            } else {
                                                              final optIndex =
                                                                  index -
                                                                      1; // Adjust index for the optimization list
                                                              return InkWell(
                                                                onTap: () {
                                                                  CreateAdCubit.get(
                                                                          context)
                                                                      .updateCampaignProcess4();
                                                                  CreateAdCubit.get(
                                                                          context)
                                                                      .setSelectedOptimization(
                                                                          GetOptimizationsCubit.get(optContext)
                                                                              .opt[optIndex]);
                                                                  GetBillingEventsCubit.get(context).getBillingEvents(
                                                                      context:
                                                                          context,
                                                                      optimizationId:
                                                                          GetOptimizationsCubit.get(optContext).opt[optIndex].id ??
                                                                              0);
                                                                  CreateAdCubit.get(
                                                                          context)
                                                                      .billingEvent = null;
                                                                  Constants
                                                                      .optimizationKey
                                                                      .currentState
                                                                      ?.collapse();
                                                                },
                                                                child:
                                                                    Container(
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Constants
                                                                        .gray
                                                                        .withOpacity(
                                                                            0.15),
                                                                    border: Border
                                                                        .symmetric(
                                                                      horizontal: BorderSide(
                                                                          color: Constants
                                                                              .gray
                                                                              .withOpacity(0.3)),
                                                                    ),
                                                                  ),
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        vertical:
                                                                            18,
                                                                        horizontal:
                                                                            20),
                                                                    child:
                                                                        CustomText(
                                                                      maxLines:
                                                                          3,
                                                                      fontSize:
                                                                          12.sp,
                                                                      text: GetOptimizationsCubit.get(optContext)
                                                                              .opt[optIndex]
                                                                              .showName ??
                                                                          "",
                                                                    ),
                                                                  ),
                                                                ),
                                                              );
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    )
                                                  : ExpansionTileItem(
                                                      expansionKey: Constants
                                                          .optimizationKey,
                                                      onExpansionChanged:
                                                          (val) {},
                                                      childrenPadding:
                                                          EdgeInsets.zero,
                                                      iconColor:
                                                          AppColors.secondColor,
                                                      collapsedIconColor:
                                                          AppColors.secondColor,
                                                      expandedAlignment:
                                                          Alignment.center,
                                                      expandedCrossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      trailing: const Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                                    left: 6.0),
                                                            child: Icon(
                                                              Icons.expand_more,
                                                              size: 30.0,
                                                              color: Constants
                                                                  .darkColor,
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                      title: CreateAdCubit.get(
                                                                      context)
                                                                  .optimization !=
                                                              null
                                                          ? CustomText(
                                                              fontSize: 12.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              text: CreateAdCubit
                                                                          .get(
                                                                              context)
                                                                      .optimization
                                                                      ?.showName ??
                                                                  "")
                                                          : AccountHintText(
                                                              isDefaultHint:
                                                                  false,
                                                              hint:
                                                                  'Advertising Objective'
                                                                      .tr,
                                                            ),
                                                      decoration:
                                                          ShapeDecoration(
                                                        color: Colors.white,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(20),
                                                        ),
                                                        shadows: const [
                                                          BoxShadow(
                                                            color: Color(
                                                                0x3F000000),
                                                            blurRadius: 40,
                                                            offset:
                                                                Offset(0, 0),
                                                            spreadRadius: -10,
                                                          )
                                                        ],
                                                      ),
                                                      children: [
                                                        ListView.builder(
                                                          shrinkWrap: true,
                                                          physics:
                                                              const NeverScrollableScrollPhysics(),
                                                          itemCount:
                                                              GetOptimizationsCubit
                                                                          .get(
                                                                              optContext)
                                                                      .opt
                                                                      .length +
                                                                  1,
                                                          // 3 for the first text + 3 for the second text
                                                          itemBuilder:
                                                              (context, index) {
                                                            // Check if the index is for the first section title
                                                            if (index == 0) {
                                                              return Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        16.0),
                                                                child:
                                                                    CustomText(
                                                                  text:
                                                                      'Drive More Visits Objectives'
                                                                          .tr,
                                                                  fontSize:
                                                                      13.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                ),
                                                              );
                                                            } else
                                                            // if (index >=
                                                            //       1 &&
                                                            //   index <=
                                                            //       3)
                                                            {
                                                              // Return the first three items
                                                              final optIndex =
                                                                  index -
                                                                      1; // Adjust index for the optimization list
                                                              return InkWell(
                                                                onTap: () {
                                                                  CreateAdCubit.get(
                                                                          context)
                                                                      .updateCampaignProcess4();
                                                                  CreateAdCubit.get(
                                                                          context)
                                                                      .setSelectedOptimization(
                                                                          GetOptimizationsCubit.get(optContext)
                                                                              .opt[optIndex]);
                                                                  GetBillingEventsCubit
                                                                          .get(
                                                                              context)
                                                                      .getBillingEvents(
                                                                    context:
                                                                        context,
                                                                    optimizationId:
                                                                        GetOptimizationsCubit.get(optContext).opt[optIndex].id ??
                                                                            0,
                                                                  );
                                                                  CreateAdCubit.get(
                                                                          context)
                                                                      .billingEvent = null;
                                                                  Constants
                                                                      .optimizationKey
                                                                      .currentState
                                                                      ?.collapse();
                                                                },
                                                                child:
                                                                    Container(
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Constants
                                                                        .gray
                                                                        .withOpacity(
                                                                            0.15),
                                                                    border: Border
                                                                        .symmetric(
                                                                      horizontal: BorderSide(
                                                                          color: Constants
                                                                              .gray
                                                                              .withOpacity(0.3)),
                                                                    ),
                                                                  ),
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        vertical:
                                                                            18,
                                                                        horizontal:
                                                                            20),
                                                                    child:
                                                                        CustomText(
                                                                      maxLines:
                                                                          3,
                                                                      fontSize:
                                                                          12.sp,
                                                                      text: GetOptimizationsCubit.get(optContext)
                                                                              .opt[optIndex]
                                                                              .showName ??
                                                                          "",
                                                                    ),
                                                                  ),
                                                                ),
                                                              );
                                                            }
                                                            // else if (index ==
                                                            //     4) {
                                                            //   // Text before the second section
                                                            //   return Padding(
                                                            //     padding: const EdgeInsets
                                                            //         .all(
                                                            //         16.0),
                                                            //     child:
                                                            //         CustomText(
                                                            //       text:
                                                            //           'other goals',
                                                            //       fontSize:
                                                            //           13.sp,
                                                            //       fontWeight:
                                                            //           FontWeight.w600,
                                                            //     ),
                                                            //   );
                                                            // } else if (index >=
                                                            //         5 &&
                                                            //     index <=
                                                            //         7) {
                                                            //   // Return the second set of three items
                                                            //   final optIndex =
                                                            //       index -
                                                            //           5; // Adjust index for the optimization list
                                                            //   return InkWell(
                                                            //     onTap:
                                                            //         () {
                                                            //       CreateAdCubit.get(ctx1)
                                                            //           .updateCampaignProcess4();
                                                            //       CreateAdCubit.get(ctx1).setSelectedOptimization(GetOptimizationsCubit.get(optContext).opt[GetOptimizationsCubit.get(optContext).opt.length -
                                                            //           3 +
                                                            //           optIndex]);
                                                            //       GetBillingEventsCubit.get(billContext)
                                                            //           .getBillingEvents(
                                                            //         context:
                                                            //             context,
                                                            //         optimizationId:
                                                            //             GetOptimizationsCubit.get(optContext).opt[GetOptimizationsCubit.get(optContext).opt.length - 3 + optIndex].id ?? 0,
                                                            //       );
                                                            //       CreateAdCubit.get(context).billingEvent =
                                                            //           null;
                                                            //       Constants
                                                            //           .optimizationKey
                                                            //           .currentState
                                                            //           ?.collapse();
                                                            //     },
                                                            //     child:
                                                            //         Container(
                                                            //       decoration:
                                                            //           BoxDecoration(
                                                            //         color:
                                                            //             Constants.gray.withOpacity(0.15),
                                                            //         border:
                                                            //             Border.symmetric(
                                                            //           horizontal: BorderSide(color: Constants.gray.withOpacity(0.3)),
                                                            //         ),
                                                            //       ),
                                                            //       child:
                                                            //           Padding(
                                                            //         padding:
                                                            //             const EdgeInsets.symmetric(vertical: 18, horizontal: 20),
                                                            //         child:
                                                            //             CustomText(
                                                            //           maxLines: 3,
                                                            //           fontSize: 12.sp,
                                                            //           text: GetOptimizationsCubit.get(optContext).opt[GetOptimizationsCubit.get(optContext).opt.length - 3 + optIndex].showName ?? "",
                                                            //         ),
                                                            //       ),
                                                            //     ),
                                                            //   );
                                                            // }

                                                            // Fallback in case of an unexpected index
                                                            return const SizedBox
                                                                .shrink();
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                ],
                              );
                  }),
              BlocBuilder(
                  bloc: GetBillingEventsCubit.get(context),
                  builder: (billContext, billingState) {
                    return GetBillingEventsCubit.get(billContext).events.isEmpty
                        ? const SizedBox()
                        : Column(
                            children: [
                              const SizedBox(height: 20),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  children: [
                                    CustomText(
                                      text: "Billing Event",
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.primaryTextColor,
                                    ),
                                    CustomText(
                                      text: "*",
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                      alignment:
                                          AlignmentDirectional.centerStart,
                                      color: Constants.redColor,
                                    ),
                                  ],
                                ),
                              ),
                              20.verticalSpace,
                              ExpansionTileItem(
                                expansionKey: Constants.billingKey,
                                onExpansionChanged: (val) {},
                                childrenPadding: EdgeInsets.zero,
                                iconColor: AppColors.secondColor,
                                collapsedIconColor: AppColors.secondColor,
                                expandedAlignment: Alignment.center,
                                expandedCrossAxisAlignment:
                                    CrossAxisAlignment.center,
                                trailing: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(left: 6.0),
                                      child: Icon(
                                        Icons.expand_more,
                                        size: 30.0,
                                        color: Constants.darkColor,
                                      ),
                                    )
                                  ],
                                ),
                                title:
                                    CreateAdCubit.get(context).billingEvent !=
                                            null
                                        ? CustomText(
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w500,
                                            text: CreateAdCubit.get(context)
                                                    .billingEvent
                                                    ?.showName ??
                                                "")
                                        : AccountHintText(
                                            isDefaultHint: false,
                                            hint: 'Billing Event  ',
                                          ),
                                decoration: ShapeDecoration(
                                  color: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  shadows: const [
                                    BoxShadow(
                                      color: Color(0x3F000000),
                                      blurRadius: 40,
                                      offset: Offset(0, 0),
                                      spreadRadius: -10,
                                    )
                                  ],
                                ),
                                children: [
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder: (item, index) {
                                      return InkWell(
                                        onTap: () {
                                          CreateAdCubit.get(billContext)
                                              .updateCampaignProcess4();
                                          CreateAdCubit.get(billContext)
                                              .setSelectedBillingEvent(
                                                  GetBillingEventsCubit.get(
                                                          billContext)
                                                      .events[index]);
                                          Constants.billingKey.currentState
                                              ?.collapse();
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                              color: Constants.gray
                                                  .withOpacity(0.15),
                                              border: Border.symmetric(
                                                  horizontal: BorderSide(
                                                      color: Constants.gray
                                                          .withOpacity(0.3)))),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 18, horizontal: 20),
                                            child: CustomText(
                                                maxLines: 3,
                                                fontSize: 12.sp,
                                                text: GetBillingEventsCubit.get(
                                                            billContext)
                                                        .events[index]
                                                        .showName ??
                                                    ""),
                                          ),
                                        ),
                                      );
                                    },
                                    itemCount:
                                        GetBillingEventsCubit.get(billContext)
                                            .events
                                            .length,
                                  ),
                                ],
                              ),
                            ],
                          );
                  }),
              // const SizedBox(height: 20),
              SizedBox(height: 50.h),
              SizedBox(
                width: 235.w,
                child: ButtonWidget(
                  text: "Save",
                  onTap: () {
                    if (widget.adCubit.existingCampaign == null) {
                      showErrorToast("Please Select Campaign");
                    } else {
                      CreateAdCubit.get(context).isAddCreated = false;

                      widget.adCubit.adModel = widget.adCubit.adModel.copyWith(
                        existCampaign: widget.adCubit.existingCampaign?.id,
                        adAccountId:
                            instance<HiveHelper>().getUser()?.defaultAccountId,
                        pageId: int.parse(
                            instance<HiveHelper>().getUser()?.defaultPageId ??
                                "0"),
                        pageAccessToken: instance<HiveHelper>()
                            .getUser()
                            ?.defaultPageAccessToken,
                        campaignName: null,
                        objective: null,
                        optimizationGoal:
                            CreateAdCubit.get(context).optimization?.actualName,
                        billingEven: widget.adCubit.billingEvent?.actualName,
                        status: null,
                        specialAdCategories: null,
                      );
                      CreateAdCubit.get(context).isCampaignCreated = true;
                      Constants.expansionTileKey.currentState?.collapse();
                      print(
                          "create campaign ${widget.adCubit.adModel.toJson()}");
                    }
                  },
                ),
              ),
            ],
          );
        }

        return const SizedBox();
      },
    );
  }

  void _createLeadFormDialog(
      BuildContext context, CreateAdCubit createAdCubit) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppColors.whiteColor,
          child: PageViewDialog(
            createAdCubit: createAdCubit,
          ),
        );
      },
    );
  }
}

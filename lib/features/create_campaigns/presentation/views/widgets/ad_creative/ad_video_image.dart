import 'dart:io';

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/widgets/video_player.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
// import 'package:mobkit_dashed_border/mobkit_dashed_border.dart';
import 'package:video_player/video_player.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/validations.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';

class AdVideoWidget extends StatefulWidget {
  const AdVideoWidget({super.key});

  @override
  State<AdVideoWidget> createState() => _AdVideoWidgetState();
}

class _AdVideoWidgetState extends State<AdVideoWidget> {
  VideoPlayerController? _controller;

  // String? convertedVideoPath;
  // bool isConverting = false;

  // Future<void> convertToMp4(String inputVideoPath) async {
  //   setState(() {
  //     isConverting = true;
  //   });
  //
  //   try {
  //     // Get Temporary Directory
  //     final tempDir = await getTemporaryDirectory();
  //     final outputVideoPath =
  //         p.join(tempDir.path, '${DateTime.now().millisecondsSinceEpoch}.mp4');
  //
  //     // FFmpeg command to convert video to MP4
  //     final command =
  //         "-i '${inputVideoPath}' -c:v libx264 -crf 28 -preset fast -c:a aac -strict experimental '$outputVideoPath'";
  //
  //     await FFmpegKit.execute(command).then((session) async {
  //       final returnCode = await session.getReturnCode();
  //       print('iam in converter $returnCode');
  //       // if (ReturnCode.isSuccess()) {
  //       setState(() {
  //         convertedVideoPath = outputVideoPath;
  //       });
  //       debugPrint('Video converted successfully: $outputVideoPath');
  //       // } else {
  //       //   debugPrint('Video conversion failed');
  //       // }
  //     });
  //   } catch (e) {
  //     debugPrint('Error during video conversion: $e');
  //   } finally {
  //     setState(() {
  //       isConverting = false;
  //     });
  //   }
  // }

  @override
  void initState() {
    // convertToMp4(
    //     'https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/720/Big_Buck_Bunny_720_10s_1MB.mp4');
    // print('converted ${convertedVideoPath}');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return Form(
          key: CreateAdCubit.get(context).adCreativeVideoFormKey,
          child: Column(
            children: [
              const SizedBox(height: 25),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'Headline Advertising Post'.tr,
                    color: AppColors.mainColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
              const SizedBox(height: 10),
              CustomTextFormField(
                controller: CreateAdCubit.get(context).adCreativeName,
                textFontSize: 12,
                borderRadius: 12,
                key: const ValueKey('adCreative_name'),
                hintText: "Add Your Text".tr,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (val) {
                  if (CreateAdCubit.get(context)
                      .adCreativeName
                      .text
                      .isNotEmpty) {
                    CreateAdCubit.get(context).updateAdCreativeProcess1();
                  } else {
                    CreateAdCubit.get(context).undoAdCreativeProcess1();
                  }
                },
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'Description Advertising Post'.tr,
                    color: AppColors.mainColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
              const SizedBox(height: 10),
              CustomTextFormField(
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (val) {
                  if (CreateAdCubit.get(context).message.text.isNotEmpty) {
                    CreateAdCubit.get(context).updateAdCreativeProcess2();
                  } else {
                    CreateAdCubit.get(context).undoAdCreativeProcess2();
                  }
                },
                controller: CreateAdCubit.get(context).message,
                textFontSize: 12,
                borderRadius: 12,
                key: const ValueKey('primary_text'),
                hintText: "Description Advertising Post".tr,

                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                // onSaved: (value) => controller.newCampaign.name = value,
              ),
              const SizedBox(height: 25),
              // InkWell(
              //   onTap: () {
              //     showDialog(
              //       context: context,
              //       builder: (BuildContext ctx) {
              //         return AlertDialog(
              //           title: Center(
              //             child: Text("Images source".tr),
              //           ),
              //           content: Text("Choose images source".tr),
              //           actionsAlignment: MainAxisAlignment.spaceBetween,
              //           actions: [
              //             TextButton(
              //               onPressed: () async {
              //                 Navigator.of(context).pop();
              //
              //                 FilePickerResult? result =
              //                     await FilePicker.platform.pickFiles(
              //                   type: FileType.image,
              //                   allowMultiple: false,
              //                   allowCompression: true,
              //                 );
              //
              //                 if (result != null && result.files.isNotEmpty) {
              //                   List<File> imageFile = result.files
              //                       .map((file) => File(file.path!))
              //                       .where((file) => ['.jpg', '.jpeg', '.png']
              //                           .contains(p
              //                               .extension(file.path)
              //                               .toLowerCase()))
              //                       .toList();
              //                   File(result.files.single.path!);
              //                   String fileExtension = '';
              //                   for (File image in imageFile) {
              //                     fileExtension =
              //                         p.extension(image.path).toLowerCase();
              //                   }
              //                   // final fileExtension =
              //                   //     p.extension(imageFile.path);
              //                   if (!['.jpg', '.jpeg', '.png']
              //                       .contains(fileExtension.toLowerCase())) {
              //                     final snackBar = SnackBar(
              //                       elevation: 0,
              //                       behavior: SnackBarBehavior.floating,
              //                       backgroundColor: Colors.transparent,
              //                       content: AwesomeSnackbarContent(
              //                         title: 'On Snap!'.tr,
              //                         message: 'Please select a image file'.tr,
              //                         contentType: ContentType.failure,
              //                       ),
              //                     );
              //
              //                     ScaffoldMessenger.of(context)
              //                       ..hideCurrentSnackBar()
              //                       ..showSnackBar(snackBar);
              //                   } else {
              //                     // Reduce image size
              //
              //                     setState(() {
              //                       CreateAdCubit.get(context).videoImage =
              //                           imageFile;
              //                     });
              //                     CreateAdCubit.get(context).updateStatus();
              //                     CreateAdCubit.get(context)
              //                         .updateAdCreativeProcess5(false);
              //                   }
              //                 }
              //               },
              //               child: Text("Gallery".tr),
              //             ),
              //             TextButton(
              //               onPressed: () async {
              //                 Navigator.of(context).pop();
              //
              //                 // If file picker is not supported, capture image from camera
              //                 final XFile? capturedImage =
              //                     await ImagePicker().pickImage(
              //                   source: ImageSource.camera,
              //                 );
              //
              //                 if (capturedImage != null) {
              //                   setState(() {
              //                     CreateAdCubit.get(context)
              //                         .videoImage
              //                         .add(File(capturedImage.path));
              //                   });
              //                   CreateAdCubit.get(context).updateStatus();
              //                   CreateAdCubit.get(context)
              //                       .updateAdCreativeProcess5(false);
              //
              //                   // print("imagessasdas" +
              //                   //     CreateAdCubit.get(context)
              //                   //         .adImages
              //                   //         .toString());
              //                 }
              //               },
              //               child: Text("Camera".tr),
              //             ),
              //           ],
              //         );
              //       },
              //     );
              //   },
              //   child: CreateAdCubit.get(context).videoImage.isEmpty
              //       ? Container(
              //           height: 258.h,
              //           decoration: BoxDecoration(
              //             border: DashedBorder.all(
              //                 width: 1,
              //                 dashLength: 5,
              //                 color: AppColors.mainColor),
              //             borderRadius: const BorderRadius.all(
              //               Radius.circular(10),
              //             ),
              //           ),
              //           child: Center(
              //             child: Column(
              //               crossAxisAlignment: CrossAxisAlignment.center,
              //               mainAxisAlignment: MainAxisAlignment.center,
              //               children: [
              //                 Lottie.asset(AppAssets.upload,
              //                     width: 60.h, height: 60.h),
              //                 Row(
              //                   mainAxisAlignment: MainAxisAlignment.center,
              //                   children: [
              //                     CustomText(
              //                       text: 'Upload Your Video Image'.tr,
              //                       color: AppColors.mainColor,
              //                       fontSize: 12,
              //                       fontWeight: FontWeight.w700,
              //                     ),
              //                   ],
              //                 ),
              //               ],
              //             ),
              //           ),
              //         )
              //       : UploadedImageWidget(
              //           height: 258.h,
              //           onPressed: () {
              //             setState(() {
              //               CreateAdCubit.get(context).videoImage.clear();
              //             });
              //             CreateAdCubit.get(context).undoAdCreativeProcess5();
              //           },
              //           image: CreateAdCubit.get(context).videoImage.first,
              //           text: "Upload file images here".tr,
              //           icon: Icons.add,
              //         ),
              // ),
              // const SizedBox(height: 25),
              InkWell(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext ctx) {
                      return AlertDialog(
                        title: Center(
                          child: Text("Video source".tr),
                        ),
                        content: Text("Choose Video source".tr),
                        actionsAlignment: MainAxisAlignment.spaceBetween,
                        actions: [
                          TextButton(
                            onPressed: () async {
                              Navigator.of(context).pop(); // Close the dialog

                              // Pick a video file using FilePicker
                              FilePickerResult? result =
                                  await FilePicker.platform.pickFiles(
                                type: FileType.video,
                                allowMultiple: false,
                                allowCompression: true,
                              );

                              List<File> convertPlatformFilesToFile(
                                  List<PlatformFile> platformFiles) {
                                return platformFiles
                                    .where((file) =>
                                        file.path !=
                                        null) // Ensure path is not null
                                    .map((file) =>
                                        File(file.path!)) // Convert to File
                                    .toList();
                              }

                              if (result != null && result.files.isNotEmpty) {
                                // Extract PlatformFile objects and filter by supported formats (.mp4, .mov, .avi)

                                convertPlatformFilesToFile(result.files)
                                    .toList();

                                // if (filteredFiles.isEmpty) {
                                //   ScaffoldMessenger.of(context).showSnackBar(
                                //     SnackBar(
                                //         content: Text(
                                //             "No valid video files selected.")),
                                //   );
                                //   return;
                                // }

                                // Update the adVideo list in the CreateAdCubit
                                setState(() {
                                  CreateAdCubit.get(context)
                                      .adVideo
                                      .clear(); // Clear previous selections
                                  CreateAdCubit.get(context).adVideo.addAll(
                                      convertPlatformFilesToFile(result.files)
                                          .toList());

                                  // Initialize the VideoPlayerController with the first valid file
                                  // _controller = VideoPlayerController.file(
                                  //     convertPlatformFilesToFile(result.files)
                                  //         .toList()
                                  //         .first)
                                  //   ..initialize().then((_) {
                                  //     setState(
                                  //         () {}); // Rebuild the widget after initialization
                                  //   });

                                  // Update status and progress in the CreateAdCubit
                                  CreateAdCubit.get(context).updateStatus();
                                  CreateAdCubit.get(context)
                                      .updateAdCreativeProcess6();
                                });

                                print(
                                    'Selected video: ${convertPlatformFilesToFile(result.files).toList().first.path}');
                                // _controller?.play();
                              }
                            },
                            child: Text("Gallery".tr),
                          ),
                          TextButton(
                            onPressed: () async {
                              Navigator.of(context).pop();

                              // If file picker is not supported, capture image from camera
                              final XFile? capturedImage =
                                  await ImagePicker().pickImage(
                                source: ImageSource.camera,
                              );

                              if (capturedImage != null) {
                                setState(() {
                                  CreateAdCubit.get(context)
                                      .adVideo
                                      .add(File(capturedImage.path));
                                });
                                CreateAdCubit.get(context).updateStatus();
                                CreateAdCubit.get(context)
                                    .updateAdCreativeProcess6();

                                print("imagessasdas${CreateAdCubit.get(context)
                                        .adVideo}");
                              }
                            },
                            child: Text("Camera".tr),
                          ),
                        ],
                      );
                    },
                  );
                },
                child: CreateAdCubit.get(context).adVideo.isEmpty
                    ? Container(
                        height: 258.h,
                        decoration: BoxDecoration(
                          border: Border.all(
                              width: 1,
                              // dashLength: 5,
                              color: AppColors.mainColor),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                        child: Center(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset(AppAssets.upload,
                                  width: 60.h, height: 60.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomText(
                                    text: 'Upload Your Video'.tr,
                                    color: AppColors.mainColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )
                    : Stack(
                        children: [
                          CustomVideoPlayer.file(
                              file: CreateAdCubit.get(context).adVideo.first),
                          // if (_controller != null)
                          //   AspectRatio(
                          //     aspectRatio: _controller!.value.aspectRatio,
                          //     child: VideoPlayer(_controller!),
                          //   ),
                          // const CustomVideoPlayer.network(
                          //     videoUrl:
                          //         'https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/720/Big_Buck_Bunny_720_10s_1MB.mp4'),
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Align(
                              alignment: Alignment.topRight,
                              child: IconButton(
                                onPressed: () {
                                  setState(() {
                                    CreateAdCubit.get(context).adVideo.clear();
                                  });
                                  CreateAdCubit.get(context)
                                      .undoAdCreativeProcess6();
                                },
                                icon: const Icon(
                                  Icons.cancel_outlined,
                                ),
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              // CreateAdCubit.get(context).objective == "OUTCOME_TRAFFIC"
              //     ? Column(
              //         children: [
              //           SizedBox(height: 25.h),
              //           CustomText(
              //             text: 'post-ad action'.tr,
              //             color: AppColors.mainColor,
              //             fontSize: 16,
              //             fontWeight: FontWeight.w500,
              //           ),
              //           const SizedBox(height: 25),
              //           SizedBox(
              //             height: 80.h,
              //             child: Material(
              //               elevation: 0,
              //               color: Colors.transparent,
              //               child: ListView.builder(
              //                 scrollDirection: Axis.horizontal,
              //                 itemCount: destinationType.length,
              //                 itemBuilder: (context, index) {
              //                   final e = destinationType[index];
              //
              //                   // Update the destination index
              //                   CreateAdCubit.get(context).destinationIndex =
              //                       index;
              //
              //                   // Determine if the destination is selected
              //                   CreateAdCubit.get(context)
              //                           .isSelectedDestination =
              //                       CreateAdCubit.get(context)
              //                               .destinationIndex ==
              //                           CreateAdCubit.get(context)
              //                               .selectedDestinationIndex;
              //
              //                   return Padding(
              //                     padding: const EdgeInsets.symmetric(
              //                         horizontal: 5.0),
              //                     child: DestinationTypeWidget(
              //                       isSelected: CreateAdCubit.get(context)
              //                               .isSelectedDestination ??
              //                           false,
              //                       callback: (selectedIndex) {
              //                         setState(() {
              //                           CreateAdCubit.get(context)
              //                                   .selectedDestinationIndex =
              //                               selectedIndex;
              //
              //                           // Update destination type based on index
              //                           if (e == "Whatsapp") {
              //                             CreateAdCubit.get(context)
              //                                 .destinationType = "WHATSAPP";
              //                           } else if (e == "Messenger") {
              //                             CreateAdCubit.get(context)
              //                                 .destinationType = "MESSENGER";
              //                           } else if (e == "Website") {
              //                             CreateAdCubit.get(context)
              //                                 .destinationType = "WEBSITE";
              //                           } else {
              //                             CreateAdCubit.get(context)
              //                                     .destinationType =
              //                                 "INSTAGRAM_DIRECT";
              //                           }
              //
              //                           print("destinationType" +
              //                               CreateAdCubit.get(context)
              //                                   .destinationType);
              //                         });
              //                       },
              //                       name: e,
              //                       index: index,
              //                     ),
              //                   );
              //                 },
              //               ),
              //             ),
              //           ),
              //         ],
              //       )
              //     : const SizedBox(),
              // const SizedBox(height: 25),
              // // if (CreateAdCubit.get(context).optimization?.id != 5)
              // //   DestinationWidget(),
              // Column(
              //   children: [
              //     Row(
              //       mainAxisAlignment: MainAxisAlignment.start,
              //       children: [
              //         CustomText(
              //           text: 'Action Headline'.tr,
              //           color: AppColors.mainColor,
              //           fontSize: 14,
              //           fontWeight: FontWeight.w400,
              //         ),
              //       ],
              //     ),
              //     const SizedBox(height: 10),
              //     CustomTextFormField(
              //       validator: (value) =>
              //           AppValidator.validateIdentity(value, context),
              //       onChanged: (val) {
              //         if (CreateAdCubit.get(context).headline.text.isNotEmpty) {
              //           CreateAdCubit.get(context).updateAdCreativeProcess3();
              //         } else {
              //           CreateAdCubit.get(context).undoAdCreativeProcess3();
              //         }
              //       },
              //       controller: CreateAdCubit.get(context).headline,
              //       textFontSize: 12,
              //       borderRadius: 12,
              //       key: const ValueKey('headline'),
              //       hintText: "Action Headline".tr,
              //
              //       textInputAction: TextInputAction.next,
              //       keyboardType: TextInputType.text,
              //       // validator: (value) => Validator.lengthValidator(value, minLength: 3),
              //       // onSaved: (value) => controller.newCampaign.name = value,
              //     ),
              //     const SizedBox(height: 10),
              //     Column(
              //       children: [
              //         Row(
              //           mainAxisAlignment: MainAxisAlignment.start,
              //           children: [
              //             CustomText(
              //               text: 'Website Link'.tr,
              //               color: AppColors.mainColor,
              //               fontSize: 14,
              //               fontWeight: FontWeight.w400,
              //             ),
              //           ],
              //         ),
              //         const SizedBox(height: 10),
              //         CustomTextFormField(
              //           //validator: (value) => AppValidator.validateIdentity(value, context),
              //
              //           controller: CreateAdCubit.get(context).webSiteLink,
              //           textFontSize: 12,
              //           borderRadius: 12,
              //           key: const ValueKey('website_name'),
              //           hintText: 'Website Link'.tr,
              //           onChanged: (val) {
              //             if (CreateAdCubit.get(context)
              //                 .webSiteLink
              //                 .text
              //                 .isNotEmpty) {
              //               CreateAdCubit.get(context)
              //                   .updateAdCreativeProcess4();
              //             } else {
              //               CreateAdCubit.get(context).undoAdCreativeProcess4();
              //             }
              //           },
              //           textInputAction: TextInputAction.next,
              //           keyboardType: TextInputType.text,
              //           // validator: (value) => Validator.lengthValidator(value, minLength: 3),
              //           // onSaved: (value) => controller.newCampaign.name = value,
              //         ),
              //         const SizedBox(height: 10),
              //       ],
              //     ),
              //     Row(
              //       mainAxisAlignment: MainAxisAlignment.start,
              //       children: [
              //         CustomText(
              //           text: 'Action Description'.tr,
              //           color: AppColors.mainColor,
              //           fontSize: 14,
              //           fontWeight: FontWeight.w400,
              //         ),
              //       ],
              //     ),
              //     const SizedBox(height: 10),
              //     CustomTextFormField(
              //       // validator: (value) => AppValidator.validateIdentity(value, context),
              //       onChanged: (val) {
              //         if (CreateAdCubit.get(context).linkDesc.text.isNotEmpty) {
              //           CreateAdCubit.get(context).updateAdCreativeProcess4();
              //         } else {
              //           CreateAdCubit.get(context).undoAdCreativeProcess4();
              //         }
              //       },
              //       controller: CreateAdCubit.get(context).linkDesc,
              //       textFontSize: 12,
              //       borderRadius: 12,
              //       key: const ValueKey('linkdesc_name'),
              //       hintText: 'Action Description'.tr,
              //
              //       textInputAction: TextInputAction.next,
              //       keyboardType: TextInputType.text,
              //       // validator: (value) => Validator.lengthValidator(value, minLength: 3),
              //       // onSaved: (value) => controller.newCampaign.name = value,
              //     ),
              //     Column(
              //       children: [
              //         const SizedBox(height: 25),
              //         CustomText(
              //           text: 'Action Post Watching Ad'.tr,
              //           color: AppColors.mainColor,
              //           fontSize: 16,
              //           fontWeight: FontWeight.w500,
              //         ),
              //         const SizedBox(height: 25),
              //         ExpansionTileItem(
              //           expansionKey: Constants.callToActionKey,
              //           onExpansionChanged: (val) {},
              //           childrenPadding: EdgeInsets.symmetric(vertical: 8),
              //           iconColor: AppColors.secondColor,
              //           collapsedIconColor: AppColors.secondColor,
              //           expandedAlignment: Alignment.center,
              //           expandedCrossAxisAlignment: CrossAxisAlignment.center,
              //           trailing: const Row(
              //             mainAxisSize: MainAxisSize.min,
              //             children: [
              //               Padding(
              //                 padding: EdgeInsets.only(left: 6.0),
              //                 child: Icon(
              //                   Icons.expand_more,
              //                   size: 30.0,
              //                   color: Constants.darkColor,
              //                 ),
              //               )
              //             ],
              //           ),
              //           title: CreateAdCubit.get(context).typeName != null
              //               ? CustomText(
              //                   fontSize: 12.sp,
              //                   fontWeight: FontWeight.w500,
              //                   text: CreateAdCubit.get(context).typeName ?? "")
              //               : CustomText(
              //                   fontSize: 12.sp,
              //                   fontWeight: FontWeight.w500,
              //                   text: 'Action Post Watching Ad'.tr),
              //           decoration: ShapeDecoration(
              //             color: Colors.white,
              //             shape: RoundedRectangleBorder(
              //               borderRadius: BorderRadius.circular(20),
              //             ),
              //             shadows: const [
              //               BoxShadow(
              //                 color: Color(0x3F000000),
              //                 blurRadius: 40,
              //                 offset: Offset(0, 0),
              //                 spreadRadius: -10,
              //               )
              //             ],
              //           ),
              //           children: [
              //             (CreateAdCubit.get(context).objective) ==
              //                         'OUTCOME_AWARENESS' ||
              //                     (CreateAdCubit.get(context)
              //                             .existingCampaign
              //                             ?.objective) ==
              //                         'OUTCOME_AWARENESS'
              //                 ? ListView.separated(
              //                     shrinkWrap: true,
              //                     physics: const BouncingScrollPhysics(
              //                         parent: NeverScrollableScrollPhysics()),
              //                     scrollDirection: Axis.vertical,
              //                     itemCount: CreateAdCubit.get(context)
              //                         .awarenessCallToAction
              //                         .length,
              //                     clipBehavior: Clip.none,
              //                     separatorBuilder: (context, index) =>
              //                         SizedBox(height: 10.h),
              //                     itemBuilder: (context, index) {
              //                       return GestureDetector(
              //                         onTap: () {
              //                           CreateAdCubit.get(context)
              //                               .setCallToAction(CreateAdCubit.get(
              //                                       context)
              //                                   .awarenessCallToAction[index]);
              //                           Constants.callToActionKey.currentState
              //                               ?.collapse();
              //                         },
              //                         child: Padding(
              //                           padding: const EdgeInsets.symmetric(
              //                               horizontal: 8),
              //                           child: Container(
              //                             height: 40.h,
              //                             decoration: BoxDecoration(
              //                               shape: BoxShape.rectangle,
              //                               borderRadius:
              //                                   BorderRadius.circular(25.r),
              //                               color: CreateAdCubit.get(context)
              //                                           .type ==
              //                                       CreateAdCubit.get(context)
              //                                           .awarenessCallToAction[
              //                                               index]
              //                                           .value
              //                                   ? AppColors.mainColor
              //                                   : Colors.white,
              //                               gradient: CreateAdCubit.get(context)
              //                                           .type ==
              //                                       CreateAdCubit.get(context)
              //                                           .awarenessCallToAction[
              //                                               index]
              //                                           .value
              //                                   ? Constants.defGradient
              //                                   : null,
              //                               boxShadow:
              //                                   Constants.unSelectedShadow,
              //                               border: null,
              //                             ),
              //                             width: 80.h,
              //                             child: Padding(
              //                               padding: const EdgeInsets.all(8.0),
              //                               child: CustomText(
              //                                 text: CreateAdCubit.get(context)
              //                                         .awarenessCallToAction[
              //                                             index]
              //                                         .name ??
              //                                     "",
              //                                 fontSize: 12.sp,
              //                                 fontWeight: CreateAdCubit.get(
              //                                                 context)
              //                                             .type ==
              //                                         CreateAdCubit.get(context)
              //                                             .awarenessCallToAction[
              //                                                 index]
              //                                             .value
              //                                     ? FontWeight.w600
              //                                     : FontWeight.w400,
              //                                 color: CreateAdCubit.get(context)
              //                                             .type ==
              //                                         CreateAdCubit.get(context)
              //                                             .awarenessCallToAction[
              //                                                 index]
              //                                             .value
              //                                     ? AppColors.white
              //                                     : Constants.textColor,
              //                                 textAlign: TextAlign.center,
              //                                 alignment:
              //                                     AlignmentDirectional.center,
              //                               ),
              //                             ),
              //                           ),
              //                         ),
              //                       );
              //                     },
              //                   )
              //                 : CreateAdCubit.get(context).objective ==
              //                             'OUTCOME_SALES' ||
              //                         (CreateAdCubit.get(context)
              //                                 .existingCampaign
              //                                 ?.objective) ==
              //                             'OUTCOME_SALES'
              //                     ? ListView.separated(
              //                         shrinkWrap: true,
              //                         physics: const BouncingScrollPhysics(
              //                             parent:
              //                                 NeverScrollableScrollPhysics()),
              //                         scrollDirection: Axis.vertical,
              //                         itemCount: CreateAdCubit.get(context)
              //                             .salesCallToAction
              //                             .length,
              //                         clipBehavior: Clip.none,
              //                         separatorBuilder: (context, index) =>
              //                             SizedBox(height: 10.h),
              //                         itemBuilder: (context, index) {
              //                           return GestureDetector(
              //                             onTap: () {
              //                               CreateAdCubit.get(context)
              //                                   .setCallToAction(CreateAdCubit
              //                                           .get(context)
              //                                       .salesCallToAction[index]);
              //                               Constants
              //                                   .callToActionKey.currentState
              //                                   ?.collapse();
              //                             },
              //                             child: Padding(
              //                               padding: const EdgeInsets.symmetric(
              //                                   horizontal: 8),
              //                               child: Container(
              //                                 height: 40.h,
              //                                 decoration: BoxDecoration(
              //                                   shape: BoxShape.rectangle,
              //                                   borderRadius:
              //                                       BorderRadius.circular(25.r),
              //                                   color: CreateAdCubit.get(
              //                                                   context)
              //                                               .type ==
              //                                           CreateAdCubit.get(
              //                                                   context)
              //                                               .salesCallToAction[
              //                                                   index]
              //                                               .value
              //                                       ? AppColors.mainColor
              //                                       : Colors.white,
              //                                   gradient: CreateAdCubit.get(
              //                                                   context)
              //                                               .type ==
              //                                           CreateAdCubit.get(
              //                                                   context)
              //                                               .salesCallToAction[
              //                                                   index]
              //                                               .value
              //                                       ? Constants.defGradient
              //                                       : null,
              //                                   boxShadow:
              //                                       Constants.unSelectedShadow,
              //                                   border: null,
              //                                 ),
              //                                 width: 80.h,
              //                                 child: Padding(
              //                                   padding:
              //                                       const EdgeInsets.all(8.0),
              //                                   child: CustomText(
              //                                     text: CreateAdCubit.get(
              //                                                 context)
              //                                             .salesCallToAction[
              //                                                 index]
              //                                             .name ??
              //                                         "",
              //                                     fontSize: 12.sp,
              //                                     fontWeight: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .salesCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? FontWeight.w600
              //                                         : FontWeight.w400,
              //                                     color: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .salesCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? AppColors.white
              //                                         : Constants.textColor,
              //                                     textAlign: TextAlign.center,
              //                                     alignment:
              //                                         AlignmentDirectional
              //                                             .center,
              //                                   ),
              //                                 ),
              //                               ),
              //                             ),
              //                           );
              //                         },
              //                       )
              //                     : ListView.separated(
              //                         shrinkWrap: true,
              //                         physics: const BouncingScrollPhysics(
              //                             parent:
              //                                 NeverScrollableScrollPhysics()),
              //                         scrollDirection: Axis.vertical,
              //                         itemCount: CreateAdCubit.get(context)
              //                             .engagementCallToAction
              //                             .length,
              //                         clipBehavior: Clip.none,
              //                         separatorBuilder: (context, index) =>
              //                             SizedBox(height: 10.h),
              //                         itemBuilder: (context, index) {
              //                           return GestureDetector(
              //                             onTap: () {
              //                               CreateAdCubit.get(context)
              //                                   .setCallToAction(CreateAdCubit
              //                                               .get(context)
              //                                           .engagementCallToAction[
              //                                       index]);
              //                               Constants
              //                                   .callToActionKey.currentState
              //                                   ?.collapse();
              //                             },
              //                             child: Padding(
              //                               padding: const EdgeInsets.symmetric(
              //                                   horizontal: 8),
              //                               child: Container(
              //                                 height: 40.h,
              //                                 decoration: BoxDecoration(
              //                                   shape: BoxShape.rectangle,
              //                                   borderRadius:
              //                                       BorderRadius.circular(25.r),
              //                                   color: CreateAdCubit.get(
              //                                                   context)
              //                                               .type ==
              //                                           CreateAdCubit.get(
              //                                                   context)
              //                                               .engagementCallToAction[
              //                                                   index]
              //                                               .value
              //                                       ? AppColors.mainColor
              //                                       : Colors.white,
              //                                   gradient: CreateAdCubit.get(
              //                                                   context)
              //                                               .type ==
              //                                           CreateAdCubit.get(
              //                                                   context)
              //                                               .engagementCallToAction[
              //                                                   index]
              //                                               .value
              //                                       ? Constants.defGradient
              //                                       : null,
              //                                   boxShadow:
              //                                       Constants.unSelectedShadow,
              //                                   border: null,
              //                                 ),
              //                                 width: 80.h,
              //                                 child: Padding(
              //                                   padding:
              //                                       const EdgeInsets.all(8.0),
              //                                   child: CustomText(
              //                                     text: CreateAdCubit.get(
              //                                                 context)
              //                                             .engagementCallToAction[
              //                                                 index]
              //                                             .name ??
              //                                         "",
              //                                     fontSize: 12.sp,
              //                                     fontWeight: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .engagementCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? FontWeight.w600
              //                                         : FontWeight.w400,
              //                                     color: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .engagementCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? AppColors.white
              //                                         : Constants.textColor,
              //                                     textAlign: TextAlign.center,
              //                                     alignment:
              //                                         AlignmentDirectional
              //                                             .center,
              //                                   ),
              //                                 ),
              //                               ),
              //                             ),
              //                           );
              //                         },
              //                       ),
              //           ],
              //         ),
              //       ],
              //     ),
              //   ],
              // ),
              // const SizedBox(height: 25),
              // SizedBox(
              //   width: 235.w,
              //   child: ButtonWidget(
              //     text: "Save".tr,
              //     onTap: () async {
              //       if (CreateAdCubit.get(context)
              //           .adCreativeVideoFormKey
              //           .currentState!
              //           .validate()) {
              //         if (CreateAdCubit.get(context)
              //             .adVideo
              //             .first
              //             .path
              //             .isEmpty) {
              //           showErrorToast("Please select your ad video");
              //         } else if (CreateAdCubit.get(context)
              //             .videoImage
              //             .first
              //             .path
              //             .isEmpty) {
              //           showErrorToast("Please select your ad video image");
              //         } else {
              //           // else if (await validateImage(
              //           //     CreateAdCubit.get(context).videoImage)) {
              //           CreateAdCubit.get(context).adModel =
              //               CreateAdCubit.get(context).adModel.copyWith(
              //                     video: CreateAdCubit.get(context).adVideo,
              //                     thumb: CreateAdCubit.get(context).videoImage,
              //                     adCreativeName: CreateAdCubit.get(context)
              //                         .adCreativeName
              //                         .text,
              //                     description:
              //                         CreateAdCubit.get(context).headline.text,
              //                     message:
              //                         CreateAdCubit.get(context).message.text,
              //                     webSiteLinkMain: CreateAdCubit.get(context)
              //                         .webSiteLink
              //                         .text,
              //
              //                     linkDescription:
              //                         CreateAdCubit.get(context).linkDesc.text,
              //                     type: CreateAdCubit.get(context)
              //                                 .destinationType ==
              //                             "WEBSITE"
              //                         ? CreateAdCubit.get(context).type
              //                         : CreateAdCubit.get(context)
              //                                     .destinationType ==
              //                                 "ON_AD"
              //                             ? "SIGN_UP"
              //                             : CreateAdCubit.get(context)
              //                                         .destinationType ==
              //                                     "INSTAGRAM_DIRECT"
              //                                 ? "INSTAGRAM_MESSAGE"
              //                                 : "MESSAGE_PAGE",
              //                     destinationType: CreateAdCubit.get(context)
              //                                 .optimization
              //                                 ?.actualName
              //                                 .toString() ==
              //                             'CONVERSATIONS'
              //                         ? "MESSENGER"
              //                         : "WEBSITE",
              //                     instAccId: instance<HiveHelper>()
              //                         .getUser()
              //                         ?.instAccId
              //                         .toString(),
              //
              //                     // link: CreateAdCubit.get(context)
              //                     //             .destinationType ==
              //                     //         "MESSENGER"
              //                     //     ? "https://m.me/${instance<HiveHelper>().getUser()?.pageUserName}"
              //                     //     : CreateAdCubit.get(context)
              //                     //                 .destinationType ==
              //                     //             "INSTAGRAM_DIRECT"
              //                     //         ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
              //                     //         : CreateAdCubit.get(context)
              //                     //                     .destinationType ==
              //                     //                 "WHATSAPP"
              //                     //             ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
              //                     //             : null,
              //                   );
              //
              //           Constants.adCreativeExpansionTileKey.currentState
              //               ?.collapse();
              //
              //           print(
              //               "create campaign ${CreateAdCubit.get(context).adModel.toJson()}");
              //         }
              //         // }
              //       } else {
              //         // Show error toast if the form is invalid
              //         showErrorToast(
              //             "Please fill in all the required fields correctly.");
              //       }
              //     },
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }
}

// Future<bool> validateImage(File imageFile) async {
//   const int minimumWidth = 1080;
//   const int minimumHeight = 1080;
//
//   try {
//     final imageBytes = await imageFile.readAsBytes();
//     final image = img.decodeImage(imageBytes);
//
//     if (image != null) {
//       if (image.width < minimumWidth || image.height < minimumHeight) {
//         showErrorToast(
//             'Select Image is too small. Please use a larger image. Minimum size: $minimumWidth x $minimumHeight pixels');
//         return false;
//       }
//
//       if (image.width < image.height) {
//         showErrorToast(
//             'Selected Image will be masked on Mobile News Feed. The tallest supported aspect ratio for images without links and videos on Mobile Feed is vertical (4:5). Images used for in-stream ads cannot be portrait.');
//         return false;
//       }
//
//       return true;
//     } else {
//       showErrorToast('Error decoding image file');
//       return false;
//     }
//   } catch (e) {
//     // Handle any errors that may occur during image decoding
//     showErrorToast('Error validating image: $e');
//     return false;
//   }
// }

List<String> destinationType = [
  "Whatsapp",
  "Messenger",
  "Instagram",
  "Website"
];

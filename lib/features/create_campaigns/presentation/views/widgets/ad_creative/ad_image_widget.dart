import 'dart:io';

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/utils/res/validations.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
// import 'package:mobkit_dashed_border/mobkit_dashed_border.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import 'package:path/path.dart' as p;
import '../../../../../../widgets/uploaded_image_widget.dart';

class AdImageWidget extends StatefulWidget {
  bool isDestination = false;

  AdImageWidget({super.key, this.isDestination = false});

  @override
  State<AdImageWidget> createState() => _AdImageWidgetState();
}

class _AdImageWidgetState extends State<AdImageWidget> {
  @override
  Widget build(BuildContext context) {
    // print(
    //     "askasl," + instance<HiveHelper>().getUser()!.pageUserName.toString());
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return Form(
          key: CreateAdCubit.get(context).adCreativeFormKey,
          child: Column(
            children: [
              const SizedBox(height: 25),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'Headline Advertising Post'.tr,
                    color: AppColors.mainColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
              const SizedBox(height: 10),
              CustomTextFormField(
                controller: CreateAdCubit.get(context).adCreativeName,
                textFontSize: 12,
                borderRadius: 12,
                key: const ValueKey('adCreative_name'),
                hintText: "Add Your Text".tr,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (val) {
                  if (CreateAdCubit.get(context)
                      .adCreativeName
                      .text
                      .isNotEmpty) {
                    CreateAdCubit.get(context).updateAdCreativeProcess1();
                  } else {
                    CreateAdCubit.get(context).undoAdCreativeProcess1();
                  }
                },
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'Description Advertising Post'.tr,
                    color: AppColors.mainColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
              const SizedBox(height: 10),
              CustomTextFormField(
                validator: (value) =>
                    AppValidator.validateIdentity(value, context),
                onChanged: (val) {
                  if (CreateAdCubit.get(context).message.text.isNotEmpty) {
                    CreateAdCubit.get(context).updateAdCreativeProcess2();
                  } else {
                    CreateAdCubit.get(context).undoAdCreativeProcess2();
                  }
                },
                controller: CreateAdCubit.get(context).message,
                textFontSize: 12,
                borderRadius: 12,
                key: const ValueKey('primary_text'),
                hintText: "Description Advertising Post".tr,

                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.text,
                // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                // onSaved: (value) => controller.newCampaign.name = value,
              ),
              const SizedBox(height: 25),
              (CreateAdCubit.get(context).adImages.isEmpty)
                  ? InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext ctx) {
                            return AlertDialog(
                              title: Center(
                                child: Text("Images source".tr),
                              ),
                              content: Text("Choose images source".tr),
                              actionsAlignment: MainAxisAlignment.spaceBetween,
                              actions: [
                                TextButton(
                                  onPressed: () async {
                                    Navigator.of(context).pop();

                                    FilePickerResult? result =
                                        await FilePicker.platform.pickFiles(
                                            type: FileType.image,
                                            allowMultiple: true);

                                    if (result != null &&
                                        result.files.isNotEmpty) {
                                      List<File> images = result.files
                                          .map((file) => File(file.path!))
                                          .where((file) => [
                                                '.jpg',
                                                '.jpeg',
                                                '.png'
                                              ].contains(p
                                                  .extension(file.path)
                                                  .toLowerCase()))
                                          .toList();
                                      for (File image in images) {
                                        String extension = p
                                            .extension(image.path)
                                            .toLowerCase();
                                      }
                                      if (CreateAdCubit.get(context)
                                          .adImages
                                          .isNotEmpty) {
                                        for (var img in images) {
                                          CreateAdCubit.get(context)
                                                .adImages
                                                .add(img);
                                        }
                                        setState(() {});
                                        print(
                                            'imagesAdded ${CreateAdCubit.get(context).adImages}');
                                        return;
                                      }
                                      if (images.isNotEmpty) {
                                        setState(() {
                                          CreateAdCubit.get(context).adImages =
                                              images;
                                        });
                                        CreateAdCubit.get(context)
                                            .updateStatus();

                                        CreateAdCubit.get(context)
                                            .updateAdCreativeProcess5(true);
                                      } else {
                                        SnackBar snackBar = SnackBar(
                                          elevation: 0,
                                          behavior: SnackBarBehavior.floating,
                                          backgroundColor: Colors.transparent,
                                          content: AwesomeSnackbarContent(
                                            title: 'On Snap!'.tr,
                                            message:
                                                'Please select a image file'.tr,
                                            contentType: ContentType.failure,
                                          ),
                                        );
                                        ScaffoldMessenger.of(context)
                                          ..hideCurrentSnackBar()
                                          ..showSnackBar(snackBar);
                                      }
                                    }
                                  },
                                  child: Text("Gallery".tr),
                                ),
                                TextButton(
                                  onPressed: () async {
                                    Navigator.of(context).pop();

                                    // If file picker is not supported, capture image from camera
                                    final XFile? capturedImage =
                                        await ImagePicker().pickImage(
                                      source: ImageSource.camera,
                                    );

                                    if (capturedImage != null) {
                                      setState(() {
                                        CreateAdCubit.get(context)
                                            .adImages
                                            .add(File(capturedImage.path));
                                      });
                                      CreateAdCubit.get(context).updateStatus();
                                      if (CreateAdCubit.get(context)
                                          .adImages
                                          .isNotEmpty) {
                                        CreateAdCubit.get(context)
                                            .updateAdCreativeProcess5(true);
                                      }
                                    }
                                  },
                                  child: Text("Camera".tr),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: Container(
                        height: 258.h,
                        decoration: BoxDecoration(
                          border: Border.all(
                              width: 1,
                              // dashLength: 5,
                              color: AppColors.mainColor),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                        child: Center(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset(AppAssets.upload,
                                  width: 60.h, height: 60.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomText(
                                    text: 'Upload Your Image'.tr,
                                    color: AppColors.mainColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                  : Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(
                            children: [
                              Expanded(
                                child: CreateAdCubit.get(context)
                                            .adImages
                                            .length ==
                                        1
                                    ? ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: CreateAdCubit.get(context)
                                            .adImages
                                            .length,
                                        itemBuilder: (context, index) {
                                          return CreateAdCubit.get(context)
                                                      .adImages
                                                      .length ==
                                                  1
                                              ? UploadedImageWidget(
                                                  height: 258.h,
                                                  isDestination: true,
                                                  onPressed: () {
                                                    setState(() {
                                                      CreateAdCubit.get(context)
                                                          .removeImage(
                                                              index, true);
                                                    });
                                                  },
                                                  image:
                                                      CreateAdCubit.get(context)
                                                          .adImages[index],
                                                  text:
                                                      "Upload file images here"
                                                          .tr,
                                                  icon: Icons.add,
                                                )
                                              : UploadedImageWidget(
                                                  height: 100,
                                                  onPressed: () {
                                                    CreateAdCubit.get(context)
                                                        .removeImage(
                                                            index, true);
                                                  },
                                                  image:
                                                      CreateAdCubit.get(context)
                                                          .adImages[index],
                                                  text:
                                                      "Upload file images here"
                                                          .tr,
                                                  icon: Icons.add,
                                                );
                                        },
                                      )
                                    : GridView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisSpacing: 4,
                                          mainAxisSpacing: 4,
                                          crossAxisCount: 3,
                                        ),
                                        itemCount: CreateAdCubit.get(context)
                                            .adImages
                                            .length,
                                        itemBuilder: (context, index) {
                                          return UploadedImageWidget(
                                            height: 100,
                                            isDestination: true,
                                            onPressed: () {
                                              setState(() {
                                                CreateAdCubit.get(context)
                                                    .removeImage(index, true);
                                              });
                                            },
                                            image: CreateAdCubit.get(context)
                                                .adImages[index],
                                            text: "",
                                          );
                                        },
                                      ),
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext ctx) {
                                return AlertDialog(
                                  title: Center(
                                    child: Text("Images source".tr),
                                  ),
                                  content: Text("Choose images source".tr),
                                  actionsAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  actions: [
                                    TextButton(
                                      onPressed: () async {
                                        Navigator.of(context).pop();

                                        FilePickerResult? result =
                                            await FilePicker.platform.pickFiles(
                                                type: FileType.image,
                                                allowMultiple: true);

                                        if (result != null &&
                                            result.files.isNotEmpty) {
                                          List<File> images = result.files
                                              .map((file) => File(file.path!))
                                              .where((file) => [
                                                    '.jpg',
                                                    '.jpeg',
                                                    '.png'
                                                  ].contains(p
                                                      .extension(file.path)
                                                      .toLowerCase()))
                                              .toList();
                                          for (File image in images) {
                                            String extension = p
                                                .extension(image.path)
                                                .toLowerCase();
                                          }
                                          if (CreateAdCubit.get(context)
                                              .adImages
                                              .isNotEmpty) {
                                            for (var img in images) {
                                              CreateAdCubit.get(context)
                                                    .adImages
                                                    .add(img);
                                            }
                                            setState(() {});
                                            print(
                                                'imagesAdded ${CreateAdCubit.get(context).adImages}');
                                            return;
                                          }
                                          if (images.isNotEmpty) {
                                            setState(() {
                                              CreateAdCubit.get(context)
                                                  .adImages = images;
                                            });
                                            CreateAdCubit.get(context)
                                                .updateStatus();

                                            CreateAdCubit.get(context)
                                                .updateAdCreativeProcess5(true);
                                          } else {
                                            SnackBar snackBar = SnackBar(
                                              elevation: 0,
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              backgroundColor:
                                                  Colors.transparent,
                                              content: AwesomeSnackbarContent(
                                                title: 'On Snap!'.tr,
                                                message:
                                                    'Please select a image file'
                                                        .tr,
                                                contentType:
                                                    ContentType.failure,
                                              ),
                                            );
                                            ScaffoldMessenger.of(context)
                                              ..hideCurrentSnackBar()
                                              ..showSnackBar(snackBar);
                                          }
                                        }
                                      },
                                      child: Text("Gallery".tr),
                                    ),
                                    TextButton(
                                      onPressed: () async {
                                        Navigator.of(context).pop();

                                        // If file picker is not supported, capture image from camera
                                        final XFile? capturedImage =
                                            await ImagePicker().pickImage(
                                          source: ImageSource.camera,
                                        );

                                        if (capturedImage != null) {
                                          setState(() {
                                            CreateAdCubit.get(context)
                                                .adImages
                                                .add(File(capturedImage.path));
                                          });
                                          CreateAdCubit.get(context)
                                              .updateStatus();
                                          if (CreateAdCubit.get(context)
                                              .adImages
                                              .isNotEmpty) {
                                            CreateAdCubit.get(context)
                                                .updateAdCreativeProcess5(true);
                                          }
                                        }
                                      },
                                      child: Text("Camera".tr),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              // Lottie.asset(AppAssets.upload,
                              //     width: 60.h, height: 60.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  CustomText(
                                    text: "+Add Another Image ".tr,
                                    color: AppColors.mainColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
              // DestinationWidget(),
              // SizedBox(height: 25.h),
              // CreateAdCubit.get(context).objective != "OUTCOME_LEADS"
              //     ? Column(
              //         children: [
              //           const CustomText(
              //             text: 'Destination',
              //             color: AppColors.mainColor,
              //             fontSize: 16,
              //             fontWeight: FontWeight.w500,
              //           ),
              //           const SizedBox(height: 25),
              //           SizedBox(
              //             height: 80.h,
              //             child: Material(
              //               elevation: 0,
              //               color: Colors.transparent,
              //               child: ListView.builder(
              //                 scrollDirection: Axis.horizontal,
              //                 itemCount: destinationType.length,
              //                 itemBuilder: (context, index) {
              //                   final e = destinationType[index];
              //
              //                   // Update the destination index
              //                   CreateAdCubit.get(context).destinationIndex =
              //                       index;
              //
              //                   // Determine if the destination is selected
              //                   CreateAdCubit.get(context)
              //                           .isSelectedDestination =
              //                       CreateAdCubit.get(context)
              //                               .destinationIndex ==
              //                           CreateAdCubit.get(context)
              //                               .selectedDestinationIndex;
              //
              //                   return Padding(
              //                     padding: const EdgeInsets.symmetric(
              //                         horizontal: 5.0),
              //                     child: DestinationTypeWidget(
              //                       isSelected: CreateAdCubit.get(context)
              //                               .isSelectedDestination ??
              //                           false,
              //                       callback: (selectedIndex) {
              //                         setState(() {
              //                           CreateAdCubit.get(context)
              //                                   .selectedDestinationIndex =
              //                               selectedIndex;
              //
              //                           // Update destination type based on index
              //                           if (e == "Whatsapp") {
              //                             CreateAdCubit.get(context)
              //                                 .destinationType = "WHATSAPP";
              //                           } else if (e == "Messenger") {
              //                             CreateAdCubit.get(context)
              //                                 .destinationType = "MESSENGER";
              //                           } else if (e == "Website") {
              //                             CreateAdCubit.get(context)
              //                                 .destinationType = "WEBSITE";
              //                           } else {
              //                             CreateAdCubit.get(context)
              //                                     .destinationType =
              //                                 "INSTAGRAM_DIRECT";
              //                           }
              //
              //                           print("destinationType" +
              //                               CreateAdCubit.get(context)
              //                                   .destinationType);
              //                         });
              //                       },
              //                       name: e,
              //                       index: index,
              //                     ),
              //                   );
              //                 },
              //               ),
              //             ),
              //           ),
              //         ],
              //       )
              //     : SizedBox(),
              // const SizedBox(height: 25),
              // Visibility(
              //   visible: CreateAdCubit.get(context).destinationType != "",
              //   child: Column(
              //     children: [
              //       const Row(
              //         mainAxisAlignment: MainAxisAlignment.start,
              //         children: [
              //           CustomText(
              //             text: 'Headline',
              //             color: AppColors.mainColor,
              //             fontSize: 14,
              //             fontWeight: FontWeight.w400,
              //           ),
              //         ],
              //       ),
              //       const SizedBox(height: 10),
              //       CustomTextFormField(
              //         validator: (value) =>
              //             AppValidator.validateIdentity(value, context),
              //         onChanged: (val) {
              //           if (CreateAdCubit.get(context)
              //               .headline
              //               .text
              //               .isNotEmpty) {
              //             CreateAdCubit.get(context).updateAdCreativeProcess3();
              //           } else {
              //             CreateAdCubit.get(context).undoAdCreativeProcess3();
              //           }
              //         },
              //         controller: CreateAdCubit.get(context).headline,
              //         textFontSize: 12,
              //         borderRadius: 12,
              //         key: const ValueKey('headline'),
              //         hintText: "Add Your Headline",
              //
              //         textInputAction: TextInputAction.next,
              //         keyboardType: TextInputType.text,
              //         // validator: (value) => Validator.lengthValidator(value, minLength: 3),
              //         // onSaved: (value) => controller.newCampaign.name = value,
              //       ),
              //       const SizedBox(height: 10),
              //       Visibility(
              //         visible: (CreateAdCubit.get(context).destinationType ==
              //                 "WEBSITE" ||
              //             CreateAdCubit.get(context).destinationType ==
              //                 "ON_AD"),
              //         child: Column(
              //           children: [
              //             const Row(
              //               mainAxisAlignment: MainAxisAlignment.start,
              //               children: [
              //                 CustomText(
              //                   text: 'Website Link',
              //                   color: AppColors.mainColor,
              //                   fontSize: 14,
              //                   fontWeight: FontWeight.w400,
              //                 ),
              //               ],
              //             ),
              //             const SizedBox(height: 10),
              //             CustomTextFormField(
              //               //validator: (value) => AppValidator.validateIdentity(value, context),
              //
              //               controller: CreateAdCubit.get(context).webSiteLink,
              //               textFontSize: 12,
              //               borderRadius: 12,
              //               key: const ValueKey('website_name'),
              //               hintText: "Add Your Website link",
              //               onChanged: (val) {
              //                 if (CreateAdCubit.get(context)
              //                     .webSiteLink
              //                     .text
              //                     .isNotEmpty) {
              //                   CreateAdCubit.get(context)
              //                       .updateAdCreativeProcess4();
              //                 } else {
              //                   CreateAdCubit.get(context)
              //                       .undoAdCreativeProcess4();
              //                 }
              //               },
              //               textInputAction: TextInputAction.next,
              //               keyboardType: TextInputType.text,
              //               // validator: (value) => Validator.lengthValidator(value, minLength: 3),
              //               // onSaved: (value) => controller.newCampaign.name = value,
              //             ),
              //             const SizedBox(height: 10),
              //           ],
              //         ),
              //       ),
              //       const Row(
              //         mainAxisAlignment: MainAxisAlignment.start,
              //         children: [
              //           CustomText(
              //             text: 'Description',
              //             color: AppColors.mainColor,
              //             fontSize: 14,
              //             fontWeight: FontWeight.w400,
              //           ),
              //         ],
              //       ),
              //       const SizedBox(height: 10),
              //       CustomTextFormField(
              //         // validator: (value) => AppValidator.validateIdentity(value, context),
              //         onChanged: (val) {
              //           if (CreateAdCubit.get(context)
              //               .linkDesc
              //               .text
              //               .isNotEmpty) {
              //             CreateAdCubit.get(context).updateAdCreativeProcess6();
              //           } else {
              //             CreateAdCubit.get(context).undoAdCreativeProcess6();
              //           }
              //         },
              //         controller: CreateAdCubit.get(context).linkDesc,
              //         textFontSize: 12,
              //         borderRadius: 12,
              //         key: const ValueKey('linkdesc_name'),
              //         hintText: "Add Your Description",
              //
              //         textInputAction: TextInputAction.next,
              //         keyboardType: TextInputType.text,
              //         // validator: (value) => Validator.lengthValidator(value, minLength: 3),
              //         // onSaved: (value) => controller.newCampaign.name = value,
              //       ),
              //       Column(
              //         children: [
              //           const SizedBox(height: 25),
              //           const CustomText(
              //             text: 'Call To Action',
              //             color: AppColors.mainColor,
              //             fontSize: 16,
              //             fontWeight: FontWeight.w500,
              //           ),
              //           const SizedBox(height: 25),
              //           ExpansionTileItem(
              //             expansionKey: Constants.callToActionKey,
              //             onExpansionChanged: (val) {},
              //             childrenPadding: EdgeInsets.symmetric(vertical: 8),
              //             iconColor: AppColors.secondColor,
              //             collapsedIconColor: AppColors.secondColor,
              //             expandedAlignment: Alignment.center,
              //             expandedCrossAxisAlignment:
              //                 CrossAxisAlignment.center,
              //             trailing: const Row(
              //               mainAxisSize: MainAxisSize.min,
              //               children: [
              //                 Padding(
              //                   padding: EdgeInsets.only(left: 6.0),
              //                   child: Icon(
              //                     Icons.expand_more,
              //                     size: 30.0,
              //                     color: Constants.darkColor,
              //                   ),
              //                 )
              //               ],
              //             ),
              //             title: CreateAdCubit.get(context).typeName != null
              //                 ? CustomText(
              //                     fontSize: 12.sp,
              //                     fontWeight: FontWeight.w500,
              //                     text: CreateAdCubit.get(context).typeName ??
              //                         "")
              //                 : CustomText(
              //                     fontSize: 12.sp,
              //                     fontWeight: FontWeight.w500,
              //                     text: "Call To Action"),
              //             decoration: ShapeDecoration(
              //               color: Colors.white,
              //               shape: RoundedRectangleBorder(
              //                 borderRadius: BorderRadius.circular(20),
              //               ),
              //               shadows: const [
              //                 BoxShadow(
              //                   color: Color(0x3F000000),
              //                   blurRadius: 40,
              //                   offset: Offset(0, 0),
              //                   spreadRadius: -10,
              //                 )
              //               ],
              //             ),
              //             children: [
              //               (CreateAdCubit.get(context).objective) ==
              //                           'OUTCOME_AWARENESS' ||
              //                       (CreateAdCubit.get(context)
              //                               .existingCampaign
              //                               ?.objective) ==
              //                           'OUTCOME_AWARENESS'
              //                   ? ListView.separated(
              //                       shrinkWrap: true,
              //                       physics: const BouncingScrollPhysics(
              //                           parent:
              //                               NeverScrollableScrollPhysics()),
              //                       scrollDirection: Axis.vertical,
              //                       itemCount: CreateAdCubit.get(context)
              //                           .awarenessCallToAction
              //                           .length,
              //                       clipBehavior: Clip.none,
              //                       separatorBuilder: (context, index) =>
              //                           SizedBox(height: 10.h),
              //                       itemBuilder: (context, index) {
              //                         return GestureDetector(
              //                           onTap: () {
              //                             CreateAdCubit.get(context)
              //                                 .setCallToAction(CreateAdCubit
              //                                             .get(context)
              //                                         .awarenessCallToAction[
              //                                     index]);
              //                             Constants
              //                                 .callToActionKey.currentState
              //                                 ?.collapse();
              //                           },
              //                           child: Padding(
              //                             padding: const EdgeInsets.symmetric(
              //                                 horizontal: 8),
              //                             child: Container(
              //                               height: 40.h,
              //                               decoration: BoxDecoration(
              //                                 shape: BoxShape.rectangle,
              //                                 borderRadius:
              //                                     BorderRadius.circular(25.r),
              //                                 color: CreateAdCubit.get(
              //                                                 context)
              //                                             .type ==
              //                                         CreateAdCubit.get(
              //                                                 context)
              //                                             .awarenessCallToAction[
              //                                                 index]
              //                                             .value
              //                                     ? AppColors.mainColor
              //                                     : Colors.white,
              //                                 gradient: CreateAdCubit.get(
              //                                                 context)
              //                                             .type ==
              //                                         CreateAdCubit.get(
              //                                                 context)
              //                                             .awarenessCallToAction[
              //                                                 index]
              //                                             .value
              //                                     ? Constants.defGradient
              //                                     : null,
              //                                 boxShadow:
              //                                     Constants.unSelectedShadow,
              //                                 border: null,
              //                               ),
              //                               width: 80.h,
              //                               child: Padding(
              //                                 padding:
              //                                     const EdgeInsets.all(8.0),
              //                                 child: CustomText(
              //                                   text: CreateAdCubit.get(
              //                                               context)
              //                                           .awarenessCallToAction[
              //                                               index]
              //                                           .name ??
              //                                       "",
              //                                   fontSize: 12.sp,
              //                                   fontWeight: CreateAdCubit.get(
              //                                                   context)
              //                                               .type ==
              //                                           CreateAdCubit.get(
              //                                                   context)
              //                                               .awarenessCallToAction[
              //                                                   index]
              //                                               .value
              //                                       ? FontWeight.w600
              //                                       : FontWeight.w400,
              //                                   color: CreateAdCubit.get(
              //                                                   context)
              //                                               .type ==
              //                                           CreateAdCubit.get(
              //                                                   context)
              //                                               .awarenessCallToAction[
              //                                                   index]
              //                                               .value
              //                                       ? AppColors.white
              //                                       : Constants.textColor,
              //                                   textAlign: TextAlign.center,
              //                                   alignment:
              //                                       AlignmentDirectional
              //                                           .center,
              //                                 ),
              //                               ),
              //                             ),
              //                           ),
              //                         );
              //                       },
              //                     )
              //                   : CreateAdCubit.get(context).objective ==
              //                               'OUTCOME_SALES' ||
              //                           (CreateAdCubit.get(context)
              //                                   .existingCampaign
              //                                   ?.objective) ==
              //                               'OUTCOME_SALES'
              //                       ? ListView.separated(
              //                           shrinkWrap: true,
              //                           physics: const BouncingScrollPhysics(
              //                               parent:
              //                                   NeverScrollableScrollPhysics()),
              //                           scrollDirection: Axis.vertical,
              //                           itemCount: CreateAdCubit.get(context)
              //                               .salesCallToAction
              //                               .length,
              //                           clipBehavior: Clip.none,
              //                           separatorBuilder: (context, index) =>
              //                               SizedBox(height: 10.h),
              //                           itemBuilder: (context, index) {
              //                             return GestureDetector(
              //                               onTap: () {
              //                                 CreateAdCubit.get(context)
              //                                     .setCallToAction(CreateAdCubit
              //                                                 .get(context)
              //                                             .salesCallToAction[
              //                                         index]);
              //                                 Constants.callToActionKey
              //                                     .currentState
              //                                     ?.collapse();
              //                               },
              //                               child: Padding(
              //                                 padding:
              //                                     const EdgeInsets.symmetric(
              //                                         horizontal: 8),
              //                                 child: Container(
              //                                   height: 40.h,
              //                                   decoration: BoxDecoration(
              //                                     shape: BoxShape.rectangle,
              //                                     borderRadius:
              //                                         BorderRadius.circular(
              //                                             25.r),
              //                                     color: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .salesCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? AppColors.mainColor
              //                                         : Colors.white,
              //                                     gradient: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .salesCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? Constants.defGradient
              //                                         : null,
              //                                     boxShadow: Constants
              //                                         .unSelectedShadow,
              //                                     border: null,
              //                                   ),
              //                                   width: 80.h,
              //                                   child: Padding(
              //                                     padding:
              //                                         const EdgeInsets.all(
              //                                             8.0),
              //                                     child: CustomText(
              //                                       text: CreateAdCubit.get(
              //                                                   context)
              //                                               .salesCallToAction[
              //                                                   index]
              //                                               .name ??
              //                                           "",
              //                                       fontSize: 12.sp,
              //                                       fontWeight: CreateAdCubit
              //                                                       .get(
              //                                                           context)
              //                                                   .type ==
              //                                               CreateAdCubit.get(
              //                                                       context)
              //                                                   .salesCallToAction[
              //                                                       index]
              //                                                   .value
              //                                           ? FontWeight.w600
              //                                           : FontWeight.w400,
              //                                       color: CreateAdCubit.get(
              //                                                       context)
              //                                                   .type ==
              //                                               CreateAdCubit.get(
              //                                                       context)
              //                                                   .salesCallToAction[
              //                                                       index]
              //                                                   .value
              //                                           ? AppColors.white
              //                                           : Constants.textColor,
              //                                       textAlign:
              //                                           TextAlign.center,
              //                                       alignment:
              //                                           AlignmentDirectional
              //                                               .center,
              //                                     ),
              //                                   ),
              //                                 ),
              //                               ),
              //                             );
              //                           },
              //                         )
              //                       : ListView.separated(
              //                           shrinkWrap: true,
              //                           physics: const BouncingScrollPhysics(
              //                               parent:
              //                                   NeverScrollableScrollPhysics()),
              //                           scrollDirection: Axis.vertical,
              //                           itemCount: CreateAdCubit.get(context)
              //                               .engagementCallToAction
              //                               .length,
              //                           clipBehavior: Clip.none,
              //                           separatorBuilder: (context, index) =>
              //                               SizedBox(height: 10.h),
              //                           itemBuilder: (context, index) {
              //                             return GestureDetector(
              //                               onTap: () {
              //                                 CreateAdCubit.get(context)
              //                                     .setCallToAction(CreateAdCubit
              //                                                 .get(context)
              //                                             .engagementCallToAction[
              //                                         index]);
              //                                 Constants.callToActionKey
              //                                     .currentState
              //                                     ?.collapse();
              //                               },
              //                               child: Padding(
              //                                 padding:
              //                                     const EdgeInsets.symmetric(
              //                                         horizontal: 8),
              //                                 child: Container(
              //                                   height: 40.h,
              //                                   decoration: BoxDecoration(
              //                                     shape: BoxShape.rectangle,
              //                                     borderRadius:
              //                                         BorderRadius.circular(
              //                                             25.r),
              //                                     color: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .engagementCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? AppColors.mainColor
              //                                         : Colors.white,
              //                                     gradient: CreateAdCubit.get(
              //                                                     context)
              //                                                 .type ==
              //                                             CreateAdCubit.get(
              //                                                     context)
              //                                                 .engagementCallToAction[
              //                                                     index]
              //                                                 .value
              //                                         ? Constants.defGradient
              //                                         : null,
              //                                     boxShadow: Constants
              //                                         .unSelectedShadow,
              //                                     border: null,
              //                                   ),
              //                                   width: 80.h,
              //                                   child: Padding(
              //                                     padding:
              //                                         const EdgeInsets.all(
              //                                             8.0),
              //                                     child: CustomText(
              //                                       text: CreateAdCubit.get(
              //                                                   context)
              //                                               .engagementCallToAction[
              //                                                   index]
              //                                               .name ??
              //                                           "",
              //                                       fontSize: 12.sp,
              //                                       fontWeight: CreateAdCubit
              //                                                       .get(
              //                                                           context)
              //                                                   .type ==
              //                                               CreateAdCubit.get(
              //                                                       context)
              //                                                   .engagementCallToAction[
              //                                                       index]
              //                                                   .value
              //                                           ? FontWeight.w600
              //                                           : FontWeight.w400,
              //                                       color: CreateAdCubit.get(
              //                                                       context)
              //                                                   .type ==
              //                                               CreateAdCubit.get(
              //                                                       context)
              //                                                   .engagementCallToAction[
              //                                                       index]
              //                                                   .value
              //                                           ? AppColors.white
              //                                           : Constants.textColor,
              //                                       textAlign:
              //                                           TextAlign.center,
              //                                       alignment:
              //                                           AlignmentDirectional
              //                                               .center,
              //                                     ),
              //                                   ),
              //                                 ),
              //                               ),
              //                             );
              //                           },
              //                         ),
              //             ],
              //           ),
              //         ],
              //       ),
              //     ],
              //   ),
              // ),
              // const SizedBox(height: 25),
              // SizedBox(
              //   width: 235.w,
              //   child: ButtonWidget(
              //     text: "Save".tr,
              //     onTap: () async {
              //       if (CreateAdCubit.get(context)
              //           .adCreativeFormKey
              //           .currentState!
              //           .validate()) {
              //         if (CreateAdCubit.get(context).adImages.isEmpty) {
              //           showErrorToast("Please select your ad images");
              //         } else
              //         // if (await validateImages(
              //         //   CreateAdCubit.get(context).adImages))
              //         {
              //           CreateAdCubit.get(context).adModel =
              //               CreateAdCubit.get(context).adModel.copyWith(
              //                   images: CreateAdCubit.get(context).adImages,
              //                   adCreativeName: CreateAdCubit.get(context)
              //                       .adCreativeName
              //                       .text,
              //                   description:
              //                       CreateAdCubit.get(context).linkDesc.text,
              //                   message:
              //                       CreateAdCubit.get(context).message.text,
              //                   webSiteLinkMain:
              //                       CreateAdCubit.get(context).destinationType == "ON_AD"
              //                           ? null
              //                           : CreateAdCubit.get(context)
              //                               .webSiteLink
              //                               .text,
              //                   linkDescription:
              //                       CreateAdCubit.get(context).linkDesc.text,
              //                   type: CreateAdCubit.get(context).destinationType ==
              //                           "WEBSITE"
              //                       ? CreateAdCubit.get(context).type
              //                       : CreateAdCubit.get(context).destinationType ==
              //                               "ON_AD"
              //                           ? "SIGN_UP"
              //                           : CreateAdCubit.get(context).destinationType ==
              //                                   "INSTAGRAM_DIRECT"
              //                               ? "INSTAGRAM_MESSAGE"
              //                               : "MESSAGE_PAGE",
              //                   destinationType:
              //                       CreateAdCubit.get(context).destinationType,
              //                   link: CreateAdCubit.get(context).destinationType ==
              //                           "MESSENGER"
              //                       ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
              //                       : CreateAdCubit.get(context).destinationType ==
              //                               "INSTAGRAM_DIRECT"
              //                           ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
              //                           : CreateAdCubit.get(context).destinationType ==
              //                                   "WHATSAPP"
              //                               ? "wa.me/+201024357231"
              //                               : CreateAdCubit.get(context).webSiteLink.text,
              //                   instAccId: instance<HiveHelper>().getUser()?.instAccId.toString());
              //
              //           print(
              //               'linkzxczxc ${CreateAdCubit.get(context).adModel.link}');
              //
              //           Constants.adCreativeExpansionTileKey.currentState
              //               ?.collapse();
              //         }
              //       } else {
              //         // Show error toast if the form is invalid
              //         showErrorToast(
              //             "Please fill in all the required fields correctly.");
              //       }
              //     },
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }
}

// Future<bool> validateImages(List<File> images) async {
//   const int minimumWidth = 1080;
//   const int minimumHeight = 1080;
//
//   final validationResults = await Future.wait(
//     images.map((imageFile) async {
//       try {
//         final imageBytes = await imageFile.readAsBytes();
//         final image = img.decodeImage(imageBytes);
//
//         if (image != null) {
//           if (image.width < minimumWidth || image.height < minimumHeight) {
//             showErrorToast(
//                 'Select Image is too small. Please use a larger image. Minimum size: $minimumWidth x $minimumHeight pixels');
//             return false;
//           }
//
//           if (image.width < image.height) {
//             showErrorToast(
//                 'Selected Image will be masked on Mobile News Feed. The tallest supported aspect ratio for images without links and videos on Mobile Feed is vertical (4:5). Images used for in-stream ads cannot be portrait.');
//             return false;
//           }
//         }
//
//         return true;
//       } catch (e) {
//         // Handle any errors that may occur during image decoding
//         showErrorToast('Error validating image: $e');
//         return false;
//       }
//     }),
//   );
//
//   return !validationResults.contains(false);
// }

// List<String> destinationType = [
//   "Whatsapp",
//   "Messenger",
//   "Instagram",
//   "Website"
// ];

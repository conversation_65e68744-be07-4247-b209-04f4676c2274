import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/ad_creative/ad_video_image.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/destination_type_widget.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../utils/res/validations.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/custom_text_field.dart';
import '../../../controllers/create_ad/create_ad_cubit.dart';
import 'ad_image_widget.dart';

class DestinationWidget extends StatefulWidget {
  int selectedTab = 0;
  bool isPrevPost = false;

  DestinationWidget(
      {super.key, required this.selectedTab, required this.isPrevPost});

  @override
  State<DestinationWidget> createState() => _DestinationWidgetState();
}

class _DestinationWidgetState extends State<DestinationWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      bloc: CreateAdCubit.get(context),
      builder: (context, state) {
        return Column(
          children: [
            if (widget.isPrevPost == false) ...[
              (widget.selectedTab == 0)
                  ? AdImageWidget(
                      isDestination: true,
                    )
                  : const AdVideoWidget(),
            ],
            SizedBox(height: 25.h),
            (CreateAdCubit.get(context).objective != "OUTCOME_LEADS" &&
                    CreateAdCubit.get(context).optimization?.id != 5)
                ? Column(
                    children: [
                      CustomText(
                        text: 'post-ad action'.tr,
                        color: AppColors.mainColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      const SizedBox(height: 25),
                      SizedBox(
                        height: 80.h,
                        child: Material(
                          elevation: 0,
                          color: Colors.transparent,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: destinationType.length,
                            itemBuilder: (context, index) {
                              final e = destinationType[index];

                              // Update the destination index
                              CreateAdCubit.get(context).destinationIndex =
                                  index;

                              // Determine if the destination is selected
                              CreateAdCubit.get(context).isSelectedDestination =
                                  CreateAdCubit.get(context).destinationIndex ==
                                      CreateAdCubit.get(context)
                                          .selectedDestinationIndex;

                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 5.0),
                                child: DestinationTypeWidget(
                                  isSelected: CreateAdCubit.get(context)
                                          .isSelectedDestination ??
                                      false,
                                  callback: (selectedIndex) {
                                    CreateAdCubit.get(context).webSiteLink =
                                        TextEditingController();
                                    CreateAdCubit.get(context).linkDesc =
                                        TextEditingController();
                                    CreateAdCubit.get(context).headline =
                                        TextEditingController();
                                    // CreateAdCubit.get(context).message =
                                    //     TextEditingController();
                                    // CreateAdCubit.get(context)
                                    //     .isAdCreativeProcess1Updated = false;
                                    // CreateAdCubit.get(context)
                                    //     .isAdCreativeProcess2Updated = false;
                                    // CreateAdCubit.get(context)
                                    //     .isAdCreativeProcess3Updated = false;
                                    // CreateAdCubit.get(context)
                                    //     .isAdCreativeProcess4Updated = false;
                                    // CreateAdCubit.get(context)
                                    //     .isAdCreativeProcess5Updated = false;
                                    // CreateAdCubit.get(context)
                                    //     .isAdCreativeProcess6Updated = false;
                                    setState(() {
                                      CreateAdCubit.get(context)
                                              .selectedDestinationIndex =
                                          selectedIndex;

                                      // Update destination type based on index
                                      if (e == "Whatsapp") {
                                        CreateAdCubit.get(context)
                                            .destinationType = "WHATSAPP";
                                      } else if (e == "Messenger") {
                                        CreateAdCubit.get(context)
                                            .destinationType = "MESSENGER";
                                      } else if (e == "Website") {
                                        CreateAdCubit.get(context)
                                            .destinationType = "WEBSITE";
                                      } else {
                                        CreateAdCubit.get(context)
                                                .destinationType =
                                            "INSTAGRAM_DIRECT";
                                      }

                                      print("destinationType${CreateAdCubit.get(context)
                                              .destinationType}");
                                    });
                                  },
                                  name: e,
                                  icon: e == "Whatsapp"
                                      ? AppAssets.whatsapp
                                      : e == "Messenger"
                                          ? AppAssets.messenger
                                          : e == "Website"
                                              ? AppAssets.snapChatAppWebsite
                                              : e == "Whatsapp"
                                                  ? AppAssets.whatsapp
                                                  : e == "Instagram"
                                                      ? AppAssets.instagram
                                                      : "",
                                  index: index,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  )
                : const SizedBox(),
            SizedBox(height: 25.h),
            const SizedBox(height: 25),
            if (CreateAdCubit.get(context).optimization?.id == 5)
              Column(
                children: [
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.start,
                  //   children: [
                  //     CustomText(
                  //       text: 'Action Headline'.tr,
                  //       color: AppColors.mainColor,
                  //       fontSize: 14,
                  //       fontWeight: FontWeight.w400,
                  //     ),
                  //   ],
                  // ),
                  // const SizedBox(height: 10),
                  // CustomTextFormField(
                  //   validator: (value) =>
                  //       AppValidator.validateIdentity(value, context),
                  //   onChanged: (val) {
                  //     if (CreateAdCubit.get(context).headline.text.isNotEmpty) {
                  //       CreateAdCubit.get(context).updateAdCreativeProcess3();
                  //     } else {
                  //       CreateAdCubit.get(context).undoAdCreativeProcess3();
                  //     }
                  //   },
                  //   controller: CreateAdCubit.get(context).headline,
                  //   textFontSize: 12,
                  //   borderRadius: 12,
                  //   key: const ValueKey('headline'),
                  //   hintText: "Action Headline".tr,
                  //
                  //   textInputAction: TextInputAction.next,
                  //   keyboardType: TextInputType.text,
                  //   // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                  //   // onSaved: (value) => controller.newCampaign.name = value,
                  // ),
                  const SizedBox(height: 10),
                  Visibility(
                    visible: (CreateAdCubit.get(context).destinationType ==
                            "WEBSITE" ||
                        CreateAdCubit.get(context).destinationType == "ON_AD"),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            CustomText(
                              text: 'Website Link'.tr,
                              color: AppColors.mainColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        CustomTextFormField(
                          //validator: (value) => AppValidator.validateIdentity(value, context),

                          controller: CreateAdCubit.get(context).webSiteLink,
                          textFontSize: 12,
                          borderRadius: 12,
                          key: const ValueKey('website_name'),
                          hintText: 'Website Link'.tr,
                          onChanged: (val) {
                            if (CreateAdCubit.get(context)
                                .webSiteLink
                                .text
                                .isNotEmpty) {
                              CreateAdCubit.get(context)
                                  .updateAdCreativeProcess4();
                            } else {
                              CreateAdCubit.get(context)
                                  .undoAdCreativeProcess4();
                            }
                          },
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.text,
                          // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                          // onSaved: (value) => controller.newCampaign.name = value,
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.start,
                  //   children: [
                  //     CustomText(
                  //       text: 'Action Description'.tr,
                  //       color: AppColors.mainColor,
                  //       fontSize: 14,
                  //       fontWeight: FontWeight.w400,
                  //     ),
                  //   ],
                  // ),
                  // const SizedBox(height: 10),
                  // CustomTextFormField(
                  //   // validator: (value) => AppValidator.validateIdentity(value, context),
                  //   onChanged: (val) {
                  //     if (CreateAdCubit.get(context).linkDesc.text.isNotEmpty) {
                  //       CreateAdCubit.get(context).updateAdCreativeProcess6();
                  //     } else {
                  //       CreateAdCubit.get(context).undoAdCreativeProcess6();
                  //     }
                  //   },
                  //   controller: CreateAdCubit.get(context).linkDesc,
                  //   textFontSize: 12,
                  //   borderRadius: 12,
                  //   key: const ValueKey('linkdesc_name'),
                  //   hintText: 'Action Description'.tr,
                  //
                  //   textInputAction: TextInputAction.next,
                  //   keyboardType: TextInputType.text,
                  //   // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                  //   // onSaved: (value) => controller.newCampaign.name = value,
                  // ),
                  // Column(
                  //   children: [
                  //     const SizedBox(height: 25),
                  //     CustomText(
                  //       text: 'Action Post Watching Ad'.tr,
                  //       color: AppColors.mainColor,
                  //       fontSize: 16,
                  //       fontWeight: FontWeight.w500,
                  //     ),
                  //     const SizedBox(height: 25),
                  //     BlocBuilder<CreateAdCubit, CreateAdState>(
                  //       builder: (context, state) {
                  //         return ExpansionTileItem(
                  //           expansionKey: Constants.callToActionKey,
                  //           onExpansionChanged: (val) {},
                  //           childrenPadding:
                  //               const EdgeInsets.symmetric(vertical: 8),
                  //           iconColor: AppColors.secondColor,
                  //           collapsedIconColor: AppColors.secondColor,
                  //           expandedAlignment: Alignment.center,
                  //           expandedCrossAxisAlignment:
                  //               CrossAxisAlignment.center,
                  //           trailing: const Row(
                  //             mainAxisSize: MainAxisSize.min,
                  //             children: [
                  //               Padding(
                  //                 padding: EdgeInsets.only(left: 6.0),
                  //                 child: Icon(
                  //                   Icons.expand_more,
                  //                   size: 30.0,
                  //                   color: Constants.darkColor,
                  //                 ),
                  //               )
                  //             ],
                  //           ),
                  //           title: CreateAdCubit.get(context).typeName != null
                  //               ? CustomText(
                  //                   fontSize: 12.sp,
                  //                   fontWeight: FontWeight.w500,
                  //                   text: CreateAdCubit.get(context).typeName ??
                  //                       "")
                  //               : CustomText(
                  //                   fontSize: 12.sp,
                  //                   fontWeight: FontWeight.w500,
                  //                   text: 'Action Post Watching Ad'.tr),
                  //           decoration: ShapeDecoration(
                  //             color: Colors.white,
                  //             shape: RoundedRectangleBorder(
                  //               borderRadius: BorderRadius.circular(20),
                  //             ),
                  //             shadows: const [
                  //               BoxShadow(
                  //                 color: Color(0x3F000000),
                  //                 blurRadius: 40,
                  //                 offset: Offset(0, 0),
                  //                 spreadRadius: -10,
                  //               )
                  //             ],
                  //           ),
                  //           children: [
                  //             (CreateAdCubit.get(context).objective) ==
                  //                         'OUTCOME_AWARENESS' ||
                  //                     (CreateAdCubit.get(context)
                  //                             .existingCampaign
                  //                             ?.objective) ==
                  //                         'OUTCOME_AWARENESS'
                  //                 ? ListView.separated(
                  //                     shrinkWrap: true,
                  //                     physics: const BouncingScrollPhysics(
                  //                         parent:
                  //                             NeverScrollableScrollPhysics()),
                  //                     scrollDirection: Axis.vertical,
                  //                     itemCount: CreateAdCubit.get(context)
                  //                         .awarenessCallToAction
                  //                         .length,
                  //                     clipBehavior: Clip.none,
                  //                     separatorBuilder: (context, index) =>
                  //                         SizedBox(height: 10.h),
                  //                     itemBuilder: (context, index) {
                  //                       return GestureDetector(
                  //                         onTap: () {
                  //                           CreateAdCubit.get(context)
                  //                               .setCallToAction(CreateAdCubit
                  //                                           .get(context)
                  //                                       .awarenessCallToAction[
                  //                                   index]);
                  //                           Constants
                  //                               .callToActionKey.currentState
                  //                               ?.collapse();
                  //                         },
                  //                         child: Padding(
                  //                           padding: const EdgeInsets.symmetric(
                  //                               horizontal: 8),
                  //                           child: Container(
                  //                             height: 40.h,
                  //                             decoration: BoxDecoration(
                  //                               shape: BoxShape.rectangle,
                  //                               borderRadius:
                  //                                   BorderRadius.circular(25.r),
                  //                               color: CreateAdCubit.get(
                  //                                               context)
                  //                                           .type ==
                  //                                       CreateAdCubit.get(
                  //                                               context)
                  //                                           .awarenessCallToAction[
                  //                                               index]
                  //                                           .value
                  //                                   ? AppColors.mainColor
                  //                                   : Colors.white,
                  //                               gradient: CreateAdCubit.get(
                  //                                               context)
                  //                                           .type ==
                  //                                       CreateAdCubit.get(
                  //                                               context)
                  //                                           .awarenessCallToAction[
                  //                                               index]
                  //                                           .value
                  //                                   ? Constants.defGradient
                  //                                   : null,
                  //                               boxShadow:
                  //                                   Constants.unSelectedShadow,
                  //                               border: null,
                  //                             ),
                  //                             width: 80.h,
                  //                             child: Padding(
                  //                               padding:
                  //                                   const EdgeInsets.all(8.0),
                  //                               child: CustomText(
                  //                                 text: CreateAdCubit.get(
                  //                                             context)
                  //                                         .awarenessCallToAction[
                  //                                             index]
                  //                                         .name ??
                  //                                     "",
                  //                                 fontSize: 12.sp,
                  //                                 fontWeight: CreateAdCubit.get(
                  //                                                 context)
                  //                                             .type ==
                  //                                         CreateAdCubit.get(
                  //                                                 context)
                  //                                             .awarenessCallToAction[
                  //                                                 index]
                  //                                             .value
                  //                                     ? FontWeight.w600
                  //                                     : FontWeight.w400,
                  //                                 color: CreateAdCubit.get(
                  //                                                 context)
                  //                                             .type ==
                  //                                         CreateAdCubit.get(
                  //                                                 context)
                  //                                             .awarenessCallToAction[
                  //                                                 index]
                  //                                             .value
                  //                                     ? AppColors.white
                  //                                     : Constants.textColor,
                  //                                 textAlign: TextAlign.center,
                  //                                 alignment:
                  //                                     AlignmentDirectional
                  //                                         .center,
                  //                               ),
                  //                             ),
                  //                           ),
                  //                         ),
                  //                       );
                  //                     },
                  //                   )
                  //                 : CreateAdCubit.get(context).objective ==
                  //                             'OUTCOME_SALES' ||
                  //                         (CreateAdCubit.get(context)
                  //                                 .existingCampaign
                  //                                 ?.objective) ==
                  //                             'OUTCOME_SALES'
                  //                     ? ListView.separated(
                  //                         shrinkWrap: true,
                  //                         physics: const BouncingScrollPhysics(
                  //                             parent:
                  //                                 NeverScrollableScrollPhysics()),
                  //                         scrollDirection: Axis.vertical,
                  //                         itemCount: CreateAdCubit.get(context)
                  //                             .salesCallToAction
                  //                             .length,
                  //                         clipBehavior: Clip.none,
                  //                         separatorBuilder: (context, index) =>
                  //                             SizedBox(height: 10.h),
                  //                         itemBuilder: (context, index) {
                  //                           return GestureDetector(
                  //                             onTap: () {
                  //                               CreateAdCubit.get(context)
                  //                                   .setCallToAction(CreateAdCubit
                  //                                               .get(context)
                  //                                           .salesCallToAction[
                  //                                       index]);
                  //                               Constants.callToActionKey
                  //                                   .currentState
                  //                                   ?.collapse();
                  //                             },
                  //                             child: Padding(
                  //                               padding:
                  //                                   const EdgeInsets.symmetric(
                  //                                       horizontal: 8),
                  //                               child: Container(
                  //                                 height: 40.h,
                  //                                 decoration: BoxDecoration(
                  //                                   shape: BoxShape.rectangle,
                  //                                   borderRadius:
                  //                                       BorderRadius.circular(
                  //                                           25.r),
                  //                                   color: CreateAdCubit.get(
                  //                                                   context)
                  //                                               .type ==
                  //                                           CreateAdCubit.get(
                  //                                                   context)
                  //                                               .salesCallToAction[
                  //                                                   index]
                  //                                               .value
                  //                                       ? AppColors.mainColor
                  //                                       : Colors.white,
                  //                                   gradient: CreateAdCubit.get(
                  //                                                   context)
                  //                                               .type ==
                  //                                           CreateAdCubit.get(
                  //                                                   context)
                  //                                               .salesCallToAction[
                  //                                                   index]
                  //                                               .value
                  //                                       ? Constants.defGradient
                  //                                       : null,
                  //                                   boxShadow: Constants
                  //                                       .unSelectedShadow,
                  //                                   border: null,
                  //                                 ),
                  //                                 width: 80.h,
                  //                                 child: Padding(
                  //                                   padding:
                  //                                       const EdgeInsets.all(
                  //                                           8.0),
                  //                                   child: CustomText(
                  //                                     text: CreateAdCubit.get(
                  //                                                 context)
                  //                                             .salesCallToAction[
                  //                                                 index]
                  //                                             .name ??
                  //                                         "",
                  //                                     fontSize: 12.sp,
                  //                                     fontWeight: CreateAdCubit
                  //                                                     .get(
                  //                                                         context)
                  //                                                 .type ==
                  //                                             CreateAdCubit.get(
                  //                                                     context)
                  //                                                 .salesCallToAction[
                  //                                                     index]
                  //                                                 .value
                  //                                         ? FontWeight.w600
                  //                                         : FontWeight.w400,
                  //                                     color: CreateAdCubit.get(
                  //                                                     context)
                  //                                                 .type ==
                  //                                             CreateAdCubit.get(
                  //                                                     context)
                  //                                                 .salesCallToAction[
                  //                                                     index]
                  //                                                 .value
                  //                                         ? AppColors.white
                  //                                         : Constants.textColor,
                  //                                     textAlign:
                  //                                         TextAlign.center,
                  //                                     alignment:
                  //                                         AlignmentDirectional
                  //                                             .center,
                  //                                   ),
                  //                                 ),
                  //                               ),
                  //                             ),
                  //                           );
                  //                         },
                  //                       )
                  //                     : ListView.separated(
                  //                         shrinkWrap: true,
                  //                         physics: const BouncingScrollPhysics(
                  //                             parent:
                  //                                 NeverScrollableScrollPhysics()),
                  //                         scrollDirection: Axis.vertical,
                  //                         itemCount: CreateAdCubit.get(context)
                  //                             .engagementCallToAction
                  //                             .length,
                  //                         clipBehavior: Clip.none,
                  //                         separatorBuilder: (context, index) =>
                  //                             SizedBox(height: 10.h),
                  //                         itemBuilder: (context, index) {
                  //                           return GestureDetector(
                  //                             onTap: () {
                  //                               CreateAdCubit.get(context)
                  //                                   .setCallToAction(CreateAdCubit
                  //                                               .get(context)
                  //                                           .engagementCallToAction[
                  //                                       index]);
                  //                               Constants.callToActionKey
                  //                                   .currentState
                  //                                   ?.collapse();
                  //                             },
                  //                             child: Padding(
                  //                               padding:
                  //                                   const EdgeInsets.symmetric(
                  //                                       horizontal: 8),
                  //                               child: Container(
                  //                                 height: 40.h,
                  //                                 decoration: BoxDecoration(
                  //                                   shape: BoxShape.rectangle,
                  //                                   borderRadius:
                  //                                       BorderRadius.circular(
                  //                                           25.r),
                  //                                   color: CreateAdCubit.get(
                  //                                                   context)
                  //                                               .type ==
                  //                                           CreateAdCubit.get(
                  //                                                   context)
                  //                                               .engagementCallToAction[
                  //                                                   index]
                  //                                               .value
                  //                                       ? AppColors.mainColor
                  //                                       : Colors.white,
                  //                                   gradient: CreateAdCubit.get(
                  //                                                   context)
                  //                                               .type ==
                  //                                           CreateAdCubit.get(
                  //                                                   context)
                  //                                               .engagementCallToAction[
                  //                                                   index]
                  //                                               .value
                  //                                       ? Constants.defGradient
                  //                                       : null,
                  //                                   boxShadow: Constants
                  //                                       .unSelectedShadow,
                  //                                   border: null,
                  //                                 ),
                  //                                 width: 80.h,
                  //                                 child: Padding(
                  //                                   padding:
                  //                                       const EdgeInsets.all(
                  //                                           8.0),
                  //                                   child: CustomText(
                  //                                     text: CreateAdCubit.get(
                  //                                                 context)
                  //                                             .engagementCallToAction[
                  //                                                 index]
                  //                                             .name ??
                  //                                         "",
                  //                                     fontSize: 12.sp,
                  //                                     fontWeight: CreateAdCubit
                  //                                                     .get(
                  //                                                         context)
                  //                                                 .type ==
                  //                                             CreateAdCubit.get(
                  //                                                     context)
                  //                                                 .engagementCallToAction[
                  //                                                     index]
                  //                                                 .value
                  //                                         ? FontWeight.w600
                  //                                         : FontWeight.w400,
                  //                                     color: CreateAdCubit.get(
                  //                                                     context)
                  //                                                 .type ==
                  //                                             CreateAdCubit.get(
                  //                                                     context)
                  //                                                 .engagementCallToAction[
                  //                                                     index]
                  //                                                 .value
                  //                                         ? AppColors.white
                  //                                         : Constants.textColor,
                  //                                     textAlign:
                  //                                         TextAlign.center,
                  //                                     alignment:
                  //                                         AlignmentDirectional
                  //                                             .center,
                  //                                   ),
                  //                                 ),
                  //                               ),
                  //                             ),
                  //                           );
                  //                         },
                  //                       ),
                  //           ],
                  //         );
                  //       },
                  //     ),
                  //   ],
                  // ),
                ],
              ),
            Visibility(
              visible: CreateAdCubit.get(context).optimization?.actualName == "QUALITY_CALL",
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'phone'.tr,
                        color: AppColors.mainColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  CustomTextFormField(
                    controller: CreateAdCubit.get(context).phoneNumber,
                    textFontSize: 12,
                    borderRadius: 12,
                    key: const ValueKey('phone_code'),
                    hintText: 'Phone Number'.tr,
                    onChanged: (val) {
                      // Prevent leading zeros
                      if (val.startsWith('0')) {
                        // Remove the leading zero
                        final newValue = val.replaceFirst(RegExp(r'^0+'), '');
                        // Update the controller with the corrected value
                        CreateAdCubit.get(context).phoneNumber.text = newValue;
                        // Set the cursor position at the end
                        CreateAdCubit.get(context).phoneNumber.selection =
                            TextSelection.fromPosition(
                                TextPosition(offset: newValue.length));
                      } else {
                        CreateAdCubit.get(context).phoneNumber.text = val;
                      }
                    },

                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(RegExp(r'^0+')),
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    suffixIcon: CountryCodePicker(onChanged: (countryCode){
                      CreateAdCubit.get(context).phoneCode = countryCode.dialCode;
                    },),
                  ),
                  const SizedBox(height: 10),
                ],
              ),),
            const SizedBox(height: 10),
            Visibility(
              visible: (CreateAdCubit.get(context).optimization?.actualName == "QUALITY_CALL"),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'Website Link'.tr,
                        color: AppColors.mainColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  CustomTextFormField(
                    //validator: (value) => AppValidator.validateIdentity(value, context),

                    controller: CreateAdCubit.get(context).webSiteLink,
                    textFontSize: 12,
                    borderRadius: 12,
                    key: const ValueKey('website_name'),
                    hintText: 'Website Link'.tr,
                    onChanged: (val) {
                      if (CreateAdCubit.get(context)
                          .webSiteLink
                          .text
                          .isNotEmpty) {
                        CreateAdCubit.get(context)
                            .updateAdCreativeProcess4();
                      } else {
                        CreateAdCubit.get(context)
                            .undoAdCreativeProcess4();
                      }
                    },
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                    // onSaved: (value) => controller.newCampaign.name = value,
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
            Visibility(
              visible: CreateAdCubit.get(context).optimization?.id == 5 ||
                  CreateAdCubit.get(context).destinationType != "",
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'Action Headline'.tr,
                        color: AppColors.mainColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  CustomTextFormField(
                    validator: (value) =>
                        AppValidator.validateIdentity(value, context),
                    onChanged: (val) {
                      if (CreateAdCubit.get(context).headline.text.isNotEmpty) {
                        CreateAdCubit.get(context).updateAdCreativeProcess3();
                      } else {
                        CreateAdCubit.get(context).undoAdCreativeProcess3();
                      }
                    },
                    controller: CreateAdCubit.get(context).headline,
                    textFontSize: 12,
                    borderRadius: 12,
                    key: const ValueKey('headline'),
                    hintText: "Action Headline".tr,

                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                    // onSaved: (value) => controller.newCampaign.name = value,
                  ),
                  const SizedBox(height: 10),
                  Visibility(
                    visible: (CreateAdCubit.get(context).optimization?.id ==
                            5 ||
                        CreateAdCubit.get(context).destinationType ==
                            "WEBSITE" ||
                        CreateAdCubit.get(context).destinationType == "ON_AD"),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            CustomText(
                              text: 'Website Link'.tr,
                              color: AppColors.mainColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        CustomTextFormField(
                          //validator: (value) => AppValidator.validateIdentity(value, context),

                          controller: CreateAdCubit.get(context).webSiteLink,
                          textFontSize: 12,
                          borderRadius: 12,
                          key: const ValueKey('website_name'),
                          hintText: 'Website Link'.tr,
                          onChanged: (val) {
                            if (CreateAdCubit.get(context)
                                .webSiteLink
                                .text
                                .isNotEmpty) {
                              CreateAdCubit.get(context)
                                  .updateAdCreativeProcess4();
                            } else {
                              CreateAdCubit.get(context)
                                  .undoAdCreativeProcess4();
                            }
                          },
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.text,
                          // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                          // onSaved: (value) => controller.newCampaign.name = value,
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'Action Description'.tr,
                        color: AppColors.mainColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  CustomTextFormField(
                    // validator: (value) => AppValidator.validateIdentity(value, context),
                    onChanged: (val) {
                      if (CreateAdCubit.get(context).linkDesc.text.isNotEmpty) {
                        CreateAdCubit.get(context).updateAdCreativeProcess6();
                      } else {
                        CreateAdCubit.get(context).undoAdCreativeProcess6();
                      }
                    },
                    controller: CreateAdCubit.get(context).linkDesc,
                    textFontSize: 12,
                    borderRadius: 12,
                    key: const ValueKey('linkdesc_name'),
                    hintText: 'Action Description'.tr,

                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.text,
                    // validator: (value) => Validator.lengthValidator(value, minLength: 3),
                    // onSaved: (value) => controller.newCampaign.name = value,
                  ),
                  Column(
                    children: [
                      const SizedBox(height: 25),
                      CustomText(
                        text: 'callToAction'.tr,
                        color: AppColors.mainColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      const SizedBox(height: 25),
                      BlocBuilder<CreateAdCubit, CreateAdState>(
                        builder: (context, state) {
                          return ExpansionTileItem(
                            expansionKey: Constants.callToActionKey,
                            onExpansionChanged: (val) {},
                            childrenPadding:
                                const EdgeInsets.symmetric(vertical: 8),
                            iconColor: AppColors.secondColor,
                            collapsedIconColor: AppColors.secondColor,
                            expandedAlignment: Alignment.center,
                            expandedCrossAxisAlignment:
                                CrossAxisAlignment.center,
                            trailing: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(left: 6.0),
                                  child: Icon(
                                    Icons.expand_more,
                                    size: 30.0,
                                    color: Constants.darkColor,
                                  ),
                                )
                              ],
                            ),
                            title: CreateAdCubit.get(context).typeName != null
                                ? CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: CreateAdCubit.get(context).typeName ??
                                        "")
                                : CustomText(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                    text: 'callToAction'.tr),
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x3F000000),
                                  blurRadius: 40,
                                  offset: Offset(0, 0),
                                  spreadRadius: -10,
                                )
                              ],
                            ),
                            children: [
                              (CreateAdCubit.get(context).objective) ==
                                          'OUTCOME_AWARENESS' ||
                                      (CreateAdCubit.get(context)
                                              .existingCampaign
                                              ?.objective) ==
                                          'OUTCOME_AWARENESS'
                                  ? ListView.separated(
                                      shrinkWrap: true,
                                      physics: const BouncingScrollPhysics(
                                          parent:
                                              NeverScrollableScrollPhysics()),
                                      scrollDirection: Axis.vertical,
                                      itemCount: CreateAdCubit.get(context)
                                          .awarenessCallToAction
                                          .length,
                                      clipBehavior: Clip.none,
                                      separatorBuilder: (context, index) =>
                                          SizedBox(height: 10.h),
                                      itemBuilder: (context, index) {
                                        return GestureDetector(
                                          onTap: () {
                                            CreateAdCubit.get(context)
                                                .setCallToAction(CreateAdCubit
                                                            .get(context)
                                                        .awarenessCallToAction[
                                                    index]);
                                            Constants
                                                .callToActionKey.currentState
                                                ?.collapse();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8),
                                            child: Container(
                                              height: 40.h,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.rectangle,
                                                borderRadius:
                                                    BorderRadius.circular(25.r),
                                                color: CreateAdCubit.get(
                                                                context)
                                                            .type ==
                                                        CreateAdCubit.get(
                                                                context)
                                                            .awarenessCallToAction[
                                                                index]
                                                            .value
                                                    ? AppColors.mainColor
                                                    : Colors.white,
                                                gradient: CreateAdCubit.get(
                                                                context)
                                                            .type ==
                                                        CreateAdCubit.get(
                                                                context)
                                                            .awarenessCallToAction[
                                                                index]
                                                            .value
                                                    ? Constants.defGradient
                                                    : null,
                                                boxShadow:
                                                    Constants.unSelectedShadow,
                                                border: null,
                                              ),
                                              width: 80.h,
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: CustomText(
                                                  text: CreateAdCubit.get(
                                                              context)
                                                          .awarenessCallToAction[
                                                              index]
                                                          .name ??
                                                      "",
                                                  fontSize: 12.sp,
                                                  fontWeight: CreateAdCubit.get(
                                                                  context)
                                                              .type ==
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .awarenessCallToAction[
                                                                  index]
                                                              .value
                                                      ? FontWeight.w600
                                                      : FontWeight.w400,
                                                  color: CreateAdCubit.get(
                                                                  context)
                                                              .type ==
                                                          CreateAdCubit.get(
                                                                  context)
                                                              .awarenessCallToAction[
                                                                  index]
                                                              .value
                                                      ? AppColors.white
                                                      : Constants.textColor,
                                                  textAlign: TextAlign.center,
                                                  alignment:
                                                      AlignmentDirectional
                                                          .center,
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    )
                                  : CreateAdCubit.get(context).objective ==
                                              'OUTCOME_SALES' ||
                                          (CreateAdCubit.get(context)
                                                  .existingCampaign
                                                  ?.objective) ==
                                              'OUTCOME_SALES'
                                      ? ListView.separated(
                                          shrinkWrap: true,
                                          physics: const BouncingScrollPhysics(
                                              parent:
                                                  NeverScrollableScrollPhysics()),
                                          scrollDirection: Axis.vertical,
                                          itemCount: CreateAdCubit.get(context)
                                              .salesCallToAction
                                              .length,
                                          clipBehavior: Clip.none,
                                          separatorBuilder: (context, index) =>
                                              SizedBox(height: 10.h),
                                          itemBuilder: (context, index) {
                                            return GestureDetector(
                                              onTap: () {
                                                CreateAdCubit.get(context)
                                                    .setCallToAction(CreateAdCubit
                                                                .get(context)
                                                            .salesCallToAction[
                                                        index]);
                                                Constants.callToActionKey
                                                    .currentState
                                                    ?.collapse();
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8),
                                                child: Container(
                                                  height: 40.h,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.rectangle,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            25.r),
                                                    color: CreateAdCubit.get(
                                                                    context)
                                                                .type ==
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .salesCallToAction[
                                                                    index]
                                                                .value
                                                        ? AppColors.mainColor
                                                        : Colors.white,
                                                    gradient: CreateAdCubit.get(
                                                                    context)
                                                                .type ==
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .salesCallToAction[
                                                                    index]
                                                                .value
                                                        ? Constants.defGradient
                                                        : null,
                                                    boxShadow: Constants
                                                        .unSelectedShadow,
                                                    border: null,
                                                  ),
                                                  width: 80.h,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: CustomText(
                                                      text: CreateAdCubit.get(
                                                                  context)
                                                              .salesCallToAction[
                                                                  index]
                                                              .name ??
                                                          "",
                                                      fontSize: 12.sp,
                                                      fontWeight: CreateAdCubit
                                                                      .get(
                                                                          context)
                                                                  .type ==
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .salesCallToAction[
                                                                      index]
                                                                  .value
                                                          ? FontWeight.w600
                                                          : FontWeight.w400,
                                                      color: CreateAdCubit.get(
                                                                      context)
                                                                  .type ==
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .salesCallToAction[
                                                                      index]
                                                                  .value
                                                          ? AppColors.white
                                                          : Constants.textColor,
                                                      textAlign:
                                                          TextAlign.center,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        )
                                      : ListView.separated(
                                          shrinkWrap: true,
                                          physics: const BouncingScrollPhysics(
                                              parent:
                                                  NeverScrollableScrollPhysics()),
                                          scrollDirection: Axis.vertical,
                                          itemCount: CreateAdCubit.get(context)
                                              .engagementCallToAction
                                              .length,
                                          clipBehavior: Clip.none,
                                          separatorBuilder: (context, index) =>
                                              SizedBox(height: 10.h),
                                          itemBuilder: (context, index) {
                                            return GestureDetector(
                                              onTap: () {
                                                CreateAdCubit.get(context)
                                                    .setCallToAction(CreateAdCubit
                                                                .get(context)
                                                            .engagementCallToAction[
                                                        index]);
                                                Constants.callToActionKey
                                                    .currentState
                                                    ?.collapse();
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8),
                                                child: Container(
                                                  height: 40.h,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.rectangle,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            25.r),
                                                    color: CreateAdCubit.get(
                                                                    context)
                                                                .type ==
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .engagementCallToAction[
                                                                    index]
                                                                .value
                                                        ? AppColors.mainColor
                                                        : Colors.white,
                                                    gradient: CreateAdCubit.get(
                                                                    context)
                                                                .type ==
                                                            CreateAdCubit.get(
                                                                    context)
                                                                .engagementCallToAction[
                                                                    index]
                                                                .value
                                                        ? Constants.defGradient
                                                        : null,
                                                    boxShadow: Constants
                                                        .unSelectedShadow,
                                                    border: null,
                                                  ),
                                                  width: 80.h,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: CustomText(
                                                      text: CreateAdCubit.get(
                                                                  context)
                                                              .engagementCallToAction[
                                                                  index]
                                                              .name ??
                                                          "",
                                                      fontSize: 12.sp,
                                                      fontWeight: CreateAdCubit
                                                                      .get(
                                                                          context)
                                                                  .type ==
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .engagementCallToAction[
                                                                      index]
                                                                  .value
                                                          ? FontWeight.w600
                                                          : FontWeight.w400,
                                                      color: CreateAdCubit.get(
                                                                      context)
                                                                  .type ==
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .engagementCallToAction[
                                                                      index]
                                                                  .value
                                                          ? AppColors.white
                                                          : Constants.textColor,
                                                      textAlign:
                                                          TextAlign.center,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 25),
          ],
        );
      },
    );
  }
}


import 'package:ads_dv/features/create_campaigns/data/models/post_response.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_posts/get_posts_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_radio_button.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../widgets/button_widget.dart';
import '../../../../../../widgets/circular_percent_indicator_widget.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/video_player.dart';
import '../create_campaign/new_campaign/tabs_widget.dart';
import 'destination_widget.dart';

class CreateAdCreativeWidget extends StatefulWidget {
  CreateAdCubit adCubit;

  CreateAdCreativeWidget({super.key, required this.adCubit});

  @override
  State<CreateAdCreativeWidget> createState() => _CreateAdCreativeWidgetState();
}

class _CreateAdCreativeWidgetState extends State<CreateAdCreativeWidget> {
  late int selectTab;
  late int postsSelectTab;

  @override
  void initState() {
    selectTab = (widget.adCubit.optimization != null &&
            (widget.adCubit.optimization?.actualName ==
                    "TWO_SECOND_CONTINUOUS_VIDEO_VIEWS" ||
                widget.adCubit.optimization?.actualName == "THRUPLAY"))
        ? 1
        : 0;
    // CreateAdCubit.get(context).changePostValue(null);
    // CreateAdCubit.get(context).changeSocialPostsTabIndex(0);
    if (CreateAdCubit.get(context).selectSocialPostsTab == 0) {
      GetPostsCubit.get(context).getFbPosts(
        context: context,
        pageAccessToken:
            instance<HiveHelper>().getUser()?.defaultPageAccessToken ??
                CreateAdCubit.get(context).metaPages?.accessToken ??
                "",
        pageId: instance<HiveHelper>().getUser()?.defaultPageId ??
            CreateAdCubit.get(context).metaPages?.id ??
            "",
      );
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant CreateAdCreativeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Only change the tab if the optimization has changed significantly
    if (widget.adCubit.optimization?.actualName !=
        oldWidget.adCubit.optimization?.actualName) {
      setState(() {
        selectTab = (widget.adCubit.optimization != null &&
                (widget.adCubit.optimization?.actualName ==
                        "TWO_SECOND_CONTINUOUS_VIDEO_VIEWS" ||
                    widget.adCubit.optimization?.actualName == "THRUPLAY"))
            ? 1
            : 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ExpansionTileItem(
      onExpansionChanged: (val) {
        CreateAdCubit.get(context).setAdCreativeExpansionState(val);
      },
      expansionKey: Constants.adCreativeExpansionTileKey,
      childrenPadding: const EdgeInsets.symmetric(horizontal: 16),
      iconColor: AppColors.secondColor,
      collapsedIconColor: AppColors.secondColor,
      expandedAlignment: Alignment.center,
      expandedCrossAxisAlignment: CrossAxisAlignment.center,
      leading: SvgPicture.asset(AppAssets.ad, color: AppColors.mainColor),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularIndicatorWidget(
              isAdSet: false,
              isDemographic: false,
              isAdCreative: true,
              isLocation: false,
              isTargeting: false,
              adCubit: widget.adCubit),
          const Padding(
            padding: EdgeInsets.only(left: 10.0),
            child: CustomText(
              text: "|",
              color: AppColors.mainColor,
              fontSize: 35,
              fontWeight: FontWeight.w200,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 6.0),
            child: ShaderMask(
              shaderCallback: (Rect bounds) {
                return const LinearGradient(
                  colors: [
                    Color(0xFFFF006F),
                    Color(0xFFF6BA00),
                  ],
                ).createShader(bounds);
              },
              child: Icon(
                CreateAdCubit.get(context).isAdCreativeTileExpanded
                    ? Icons.expand_less
                    : Icons.expand_more,
                size: 24.0,
                color:
                    Colors.white, // This color will be replaced by the gradient
              ),
            ),
          )
        ],
      ),

      title: CustomText(
          text: 'Advertising Content'.tr,
          color: AppColors.mainColor,
          fontSize: 22,
          fontWeight: FontWeight.w700),

      // childrenPadding:  const EdgeInsets.symmetric(
      //     horizontal: 10, vertical:20),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: AppColors.mainColor)
          // color: AppColors.borderColor,
          ),
      children: [
        //
        //
        //
        //
        // const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CustomRadioButton(
              title: "New Post".tr,
              value: 0,
              createAdCubit: CreateAdCubit.get(context),
            ),
            // SizedBox(width: 20), // Add spacing between the buttons
            CustomRadioButton(
              title: "Previous Post".tr,
              value: 1,
              createAdCubit: CreateAdCubit.get(context),
            ),
          ],
        ),

        CreateAdCubit.get(context).selectedPost == null
            ? const SizedBox()
            : CreateAdCubit.get(context).selectedPost == 0
                ? Column(
                    children: [
                      const SizedBox(height: 20),
                      Container(
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x3D000000),
                              blurRadius: 13.93,
                              offset: Offset(0, 0),
                              spreadRadius: -3.80,
                            )
                          ],
                        ),
                        child: TabsWidget(
                          newObject: 'Image Advertising'.tr,
                          existObject: 'Video Advertising'.tr,
                          selectedTab: selectTab,
                          onTabChanged: (widget.adCubit.optimization != null &&
                                  (widget.adCubit.optimization?.actualName ==
                                          "TWO_SECOND_CONTINUOUS_VIDEO_VIEWS" ||
                                      widget.adCubit.optimization?.actualName ==
                                          "THRUPLAY"))
                              ? (tab) {
                                  showErrorToast(
                                      'Once you have selected ${widget.adCubit.optimization?.showName} you cannot select ad image creative');
                                  setState(() {
                                    selectTab = 1;
                                  });
                                }
                              : (tab) {
                                  CreateAdCubit.get(context)
                                      .adCreativePercentage = 0.0;
                                  if (selectTab != tab) {
                                    setState(() {
                                      selectTab = tab;
                                    });
                                  }
                                  if (selectTab == 1) {
                                    CreateAdCubit.get(context).adImages = [];
                                    CreateAdCubit.get(context).adCreativeName =
                                        TextEditingController();
                                    // CreateAdCubit.get(context).webSiteLink =
                                    //     TextEditingController();
                                    // CreateAdCubit.get(context).linkDesc =
                                    //     TextEditingController();
                                    CreateAdCubit.get(context).headline =
                                        TextEditingController();
                                    CreateAdCubit.get(context).message =
                                        TextEditingController();
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess1Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess2Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess3Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess4Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess5Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess6Updated = false;
                                  } else {
                                    CreateAdCubit.get(context).adVideo.clear();
                                    // CreateAdCubit.get(context)
                                    //     .videoImage
                                    //     .clear();
                                    CreateAdCubit.get(context).adCreativeName =
                                        TextEditingController();
                                    CreateAdCubit.get(context).webSiteLink =
                                        TextEditingController();
                                    CreateAdCubit.get(context).linkDesc =
                                        TextEditingController();
                                    CreateAdCubit.get(context).headline =
                                        TextEditingController();
                                    CreateAdCubit.get(context).message =
                                        TextEditingController();
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess1Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess2Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess3Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess4Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess5Updated = false;
                                    CreateAdCubit.get(context)
                                        .isAdCreativeProcess6Updated = false;
                                  }
                                },
                        ),
                      ),
                      // (selectTab == 0)
                      //     ? const AdImageWidget()
                      //     : const AdVideoWidget(),
                      DestinationWidget(
                        selectedTab: selectTab,
                        isPrevPost: false,
                      ),
                      SizedBox(
                        width: 235.w,
                        child: ButtonWidget(
                          text: "Save".tr,
                          onTap: () async {
                            if (CreateAdCubit.get(context).postId == null &&
                                CreateAdCubit.get(context).selectedPost == 1) {
                              print('else if 0');
                              showErrorToast("please select the post");
                            } else if (CreateAdCubit.get(context).postId !=
                                    null &&
                                CreateAdCubit.get(context).selectedPost == 1) {
                              print('else if 1');
                              CreateAdCubit.get(context).adModel =
                                  CreateAdCubit.get(context).adModel.copyWith(
                                        objectStoryId:
                                            CreateAdCubit.get(context).postId,
                                      );
                              CreateAdCubit.get(context).isAdCreativeCreated =
                                  true;
                              Constants.adCreativeExpansionTileKey.currentState
                                  ?.collapse();
                            } else if (selectTab == 1) {
                              if (CreateAdCubit.get(context)
                                  .adCreativeVideoFormKey
                                  .currentState!
                                  .validate()) {
                                print(
                                    'else if 2 ${CreateAdCubit.get(context).destinationType}');
                                if (CreateAdCubit.get(context)
                                    .adVideo
                                    .isEmpty) {
                                  print('else if 3');
                                  showErrorToast("Please select your ad video");
                                }
                                // else if (CreateAdCubit.get(context)
                                //     .videoImage
                                //     .isEmpty) {
                                //   print('else if 4');
                                //   showErrorToast(
                                //       "Please select your ad video image");
                                // }
                                else {
                                  // else if (await validateImage(
                                  //     CreateAdCubit.get(context).videoImage)) {
                                  print('else if 5');
                                  CreateAdCubit.get(context).adModel =
                                      CreateAdCubit.get(context)
                                          .adModel
                                          .copyWith(
                                            video: CreateAdCubit.get(context)
                                                .adVideo,
                                            // phone: CreateAdCubit.get(context).phoneCode! + CreateAdCubit.get(context).phoneNumber.text,
                                            // thumb: CreateAdCubit.get(context)
                                            //     .videoImage,
                                            adCreativeName:
                                                CreateAdCubit.get(context)
                                                    .adCreativeName
                                                    .text,
                                            description:
                                                CreateAdCubit.get(context)
                                                    .headline
                                                    .text,
                                            message: CreateAdCubit.get(context)
                                                .message
                                                .text,
                                            webSiteLinkMain:
                                                CreateAdCubit.get(context)
                                                    .webSiteLink
                                                    .text,

                                            linkDescription:
                                                CreateAdCubit.get(context)
                                                    .linkDesc
                                                    .text,
                                            type: CreateAdCubit.get(context)
                                                        .destinationType ==
                                                    "WEBSITE"
                                                ? CreateAdCubit.get(context)
                                                    .type
                                                : CreateAdCubit.get(context)
                                                            .destinationType ==
                                                        "ON_AD"
                                                    ? "SIGN_UP"
                                                    : CreateAdCubit.get(context)
                                                                .destinationType ==
                                                            "INSTAGRAM_DIRECT"
                                                        ? "INSTAGRAM_MESSAGE"
                                                        : "MESSAGE_PAGE",
                                            destinationType:
                                                CreateAdCubit.get(context)
                                                            .optimization
                                                            ?.id ==
                                                        5
                                                    ? "WEBSITE"
                                                    : CreateAdCubit.get(context)
                                                    .optimization
                                                    ?.actualName == "QUALITY_CALL" ? "PHONE_CALL" : CreateAdCubit.get(context)
                                                        .destinationType,
                                            // CreateAdCubit
                                            //     .get(context)
                                            //     .optimization
                                            //     ?.actualName
                                            //     .toString() ==
                                            //     'CONVERSATIONS'
                                            //     ? "MESSENGER"
                                            //     : "WEBSITE",
                                            instAccId: instance<HiveHelper>()
                                                .getUser()
                                                ?.instAccId
                                                .toString(),

                                            link: CreateAdCubit.get(context)
                                                        .destinationType ==
                                                    "MESSENGER"
                                                ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
                                                : CreateAdCubit.get(context)
                                                            .destinationType ==
                                                        "INSTAGRAM_DIRECT"
                                                    ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                                    : CreateAdCubit.get(context)
                                                                .destinationType ==
                                                            "WHATSAPP"
                                                        ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
                                                        : null,
                                          );
                                  CreateAdCubit.get(context)
                                      .isAdCreativeCreated = true;
                                  Constants
                                      .adCreativeExpansionTileKey.currentState
                                      ?.collapse();

                                  print(
                                      "create campaign ${instance<HiveHelper>().getUser()?.instAccId.toString()}");
                                }
                                // }
                              }
                            } else {
                              if (CreateAdCubit.get(context)
                                  .adCreativeFormKey
                                  .currentState!
                                  .validate()) {
                                if (CreateAdCubit.get(context)
                                    .adImages
                                    .isEmpty) {
                                  print('else if 3');
                                  showErrorToast(
                                      "Please select your ad images");
                                } else
                                // if (await validateImages(
                                //   CreateAdCubit.get(context).adImages))
                                {
                                  CreateAdCubit.get(context).adModel = CreateAdCubit.get(context)
                                      .adModel
                                      .copyWith(
                                          images: CreateAdCubit.get(context)
                                              .adImages,
                                          adCreativeName: CreateAdCubit.get(context)
                                              .adCreativeName
                                              .text,
                                          description: CreateAdCubit.get(context)
                                              .linkDesc
                                              .text,
                                          message: CreateAdCubit.get(context)
                                              .message
                                              .text,
                                      phone: CreateAdCubit.get(context).phoneNumber.text.isNotEmpty ? (CreateAdCubit.get(context).phoneCode! + CreateAdCubit.get(context).phoneNumber.text) : null,
                                          webSiteLinkMain: CreateAdCubit.get(context)
                                                      .destinationType ==
                                                  "ON_AD"
                                              ? null
                                              : CreateAdCubit.get(context)
                                                  .webSiteLink
                                                  .text,
                                          linkDescription:
                                              CreateAdCubit.get(context)
                                                  .linkDesc
                                                  .text,
                                          type: CreateAdCubit.get(context)
                                                      .destinationType ==
                                                  "WEBSITE"
                                              ? CreateAdCubit.get(context).type
                                              : CreateAdCubit.get(context).destinationType == "ON_AD"
                                                  ? "SIGN_UP"
                                                  : CreateAdCubit.get(context).destinationType == "INSTAGRAM_DIRECT"
                                                      ? "INSTAGRAM_MESSAGE"
                                                      : "MESSAGE_PAGE",
                                          destinationType: CreateAdCubit.get(context).destinationType,
                                          link: CreateAdCubit.get(context).destinationType == "MESSENGER"
                                              ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
                                              : CreateAdCubit.get(context).destinationType == "INSTAGRAM_DIRECT"
                                                  ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                                  : CreateAdCubit.get(context).destinationType == "WHATSAPP"
                                                      ? "wa.me/+201024357231"
                                                      : CreateAdCubit.get(context).webSiteLink.text,
                                          instAccId: instance<HiveHelper>().getUser()?.instAccId.toString());

                                  print(
                                      'linkzxczxc ${instance<HiveHelper>().getUser()?.instAccId.toString()}');

                                  CreateAdCubit.get(context)
                                      .isAdCreativeCreated = true;
                                  Constants
                                      .adCreativeExpansionTileKey.currentState
                                      ?.collapse();
                                }
                              } else {
                                print('else if 4');
                                // Show error toast if the form is invalid
                                showErrorToast(
                                    "Please fill in all the required fields correctly.");
                              }
                            }
                            // else {
                            //   print("sdfsdfjkdlm,bcnx.vb");
                            //   // Show error toast if the form is invalid
                            //   showErrorToast(
                            //       "Please fill in all the required fields correctly.");
                            // }
                          },
                        ),
                      ),
                    ],
                  )
                : Column(
                    children: [
                      Container(
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x3D000000),
                              blurRadius: 13.93,
                              offset: Offset(0, 0),
                              spreadRadius: -3.80,
                            )
                          ],
                        ),
                        child: TabsWidget(
                          newObject: 'facebook posts'.tr,
                          existObject: 'instagram posts'.tr,
                          selectedTab:
                              CreateAdCubit.get(context).selectSocialPostsTab ??
                                  0,
                          onTabChanged: (tab) {
                            if (instance<HiveHelper>()
                                        .getUser()!
                                        .instUserId
                                        .toString() ==
                                    'null' &&
                                tab == 1) {
                              CreateAdCubit.get(context)
                                  .changeSocialPostsTabIndex(0);
                              showErrorToast(
                                  'you does not have instgram posts');
                              return;
                            }
                            CreateAdCubit.get(context)
                                .changeSocialPostsTabIndex(tab);
                            if (CreateAdCubit.get(context)
                                    .selectSocialPostsTab ==
                                1) {
                              print(
                                  'instadfkgljgkjnzcx ${instance<HiveHelper>().getUser()!.instUserId.toString()}');
                              CreateAdCubit.get(context).postId = null;
                              CreateAdCubit.get(context).postIndex = null;
                              CreateAdCubit.get(context).post = null;
                              GetPostsCubit.get(context).getInstaPosts(
                                  context: context,
                                  pageAccessToken: instance<HiveHelper>()
                                          .getUser()
                                          ?.defaultPageAccessToken ??
                                      CreateAdCubit.get(context)
                                          .metaPages
                                          ?.accessToken ??
                                      "",
                                  pageId: instance<HiveHelper>()
                                          .getUser()
                                          ?.defaultPageId ??
                                      CreateAdCubit.get(context)
                                          .metaPages
                                          ?.id ??
                                      "",
                                  instaUserId: instance<HiveHelper>()
                                      .getUser()!
                                      .instUserId
                                      .toString());
                            } else {
                              CreateAdCubit.get(context).postId = null;
                              CreateAdCubit.get(context).postIndex = null;
                              CreateAdCubit.get(context).post = null;
                              GetPostsCubit.get(context).getFbPosts(
                                context: context,
                                pageAccessToken: instance<HiveHelper>()
                                        .getUser()
                                        ?.defaultPageAccessToken ??
                                    CreateAdCubit.get(context)
                                        .metaPages
                                        ?.accessToken ??
                                    "",
                                pageId: instance<HiveHelper>()
                                        .getUser()
                                        ?.defaultPageId ??
                                    CreateAdCubit.get(context).metaPages?.id ??
                                    "",
                              );
                            }
                          },
                        ),
                      ),
                      CreateAdCubit.get(context).selectSocialPostsTab == 1
                          ? BlocBuilder<GetPostsCubit, GetFbPostsState>(
                              builder: (instaPostsContext, instaPostsState) {
                                if (instaPostsState
                                    is GetInstaPostsStateLoading) {
                                  return const LoadingWidget(
                                    isCircle: true,
                                  );
                                } else if (instaPostsState
                                    is GetInstaPostsStateLoaded) {
                                  return Column(
                                    children: [
                                      ListView.separated(
                                        padding: EdgeInsets.zero,
                                        separatorBuilder: (context, index) {
                                          return const Divider();
                                        },
                                        itemBuilder: (item, index) {
                                          return Padding(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 12.sp),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  child: Radio<int>(
                                                    value: index,
                                                    groupValue:
                                                        CreateAdCubit.get(
                                                                context)
                                                            .postIndex,
                                                    onChanged: (int? newValue) {
                                                      CreateAdCubit.get(context).setSelectedPost(
                                                          GetPostsCubit.get(
                                                                  instaPostsContext)
                                                              .instaPosts[index]
                                                              .id,
                                                          index,
                                                          Post(
                                                              mediaUrl: GetPostsCubit.get(
                                                                      instaPostsContext)
                                                                  .instaPosts[
                                                                      index]
                                                                  .mediaUrl,
                                                              mediaType: GetPostsCubit.get(
                                                                      instaPostsContext)
                                                                  .instaPosts[
                                                                      index]
                                                                  .mediaType,
                                                              caption: GetPostsCubit.get(
                                                                      instaPostsContext)
                                                                  .instaPosts[
                                                                      index]
                                                                  .caption,
                                                              id: GetPostsCubit.get(
                                                                      instaPostsContext)
                                                                  .instaPosts[
                                                                      index]
                                                                  .id));
                                                      CreateAdCubit.get(
                                                              instaPostsContext)
                                                          .adModel = CreateAdCubit
                                                              .get(
                                                                  instaPostsContext)
                                                          .adModel
                                                          .copyWith(
                                                              isInstaPost: true,
                                                              instaUserId: instance
                                                                  .get<
                                                                      HiveHelper>()
                                                                  .getUser()
                                                                  ?.instUserId);
                                                      print(
                                                          'dsfdcvxbgxdgr ${CreateAdCubit.get(context).adModel.isInstaPost} ${CreateAdCubit.get(context).adModel.instaUserId}');
                                                      CreateAdCubit.get(context)
                                                          .updateAdCreativeProcess7();
                                                    },
                                                    activeColor:
                                                        AppColors.mainColor,
                                                  ),
                                                ),
                                                Expanded(
                                                  flex: 10,
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Column(
                                                        children: [
                                                          Text(GetPostsCubit.get(
                                                                      instaPostsContext)
                                                                  .instaPosts[
                                                                      index]
                                                                  .mediaType ??
                                                              ""),
                                                          5.verticalSpace,
                                                          ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        5.sp),
                                                            child:
                                                                GestureDetector(
                                                              onTap: () {
                                                                // Navigator.pushNamed(
                                                                //     context,
                                                                //     Routes
                                                                //         .image,
                                                                //     arguments: {
                                                                //       "imageUrl": GetPostsCubit.get(
                                                                //               instaPostsContext)
                                                                //           .instaPosts[
                                                                //               index]
                                                                //           .mediaUrl,
                                                                //     });
                                                              },
                                                              child: (GetPostsCubit.get(instaPostsContext)
                                                                              .instaPosts[
                                                                                  index]
                                                                              .mediaType ==
                                                                          "IMAGE" ||
                                                                      GetPostsCubit.get(instaPostsContext)
                                                                              .instaPosts[index]
                                                                              .mediaType ==
                                                                          "PHOTO")
                                                                  ? CachedImageWidget(
                                                                      image: GetPostsCubit.get(
                                                                              instaPostsContext)
                                                                          .instaPosts[
                                                                              index]
                                                                          .mediaUrl,
                                                                      height:
                                                                          80.h,
                                                                      width:
                                                                          80.h,
                                                                    )
                                                                  : SizedBox(
                                                                      height:
                                                                          80.h,
                                                                      width:
                                                                          100.h,
                                                                      child: CustomVideoPlayer.network(
                                                                          videoUrl: GetPostsCubit.get(instaPostsContext)
                                                                              .instaPosts[index]
                                                                              .mediaUrl),
                                                                    ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal:
                                                                    8.0),
                                                        child: SizedBox(
                                                          width: 150.h,
                                                          child: CustomText(
                                                            text: GetPostsCubit.get(
                                                                        instaPostsContext)
                                                                    .instaPosts[
                                                                        index]
                                                                    .caption ??
                                                                "",
                                                            maxLines: 10,
                                                            textAlign:
                                                                TextAlign.right,
                                                          ),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                        itemCount:
                                            GetPostsCubit.get(instaPostsContext)
                                                .instaPosts
                                                .length,
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                      ),
                                      20.verticalSpace,
                                      (instaPostsState.data.result?.next ==
                                                  "null" ||
                                              instaPostsState
                                                      .data.result?.next ==
                                                  null)
                                          ? const SizedBox()
                                          : Column(
                                              children: [
                                                instaPostsState
                                                        is GetMorePostsStateLoading
                                                    ? const LoadingWidget(
                                                        isCircle: true,
                                                      )
                                                    : InkWell(
                                                        onTap: () {
                                                          GetPostsCubit.get(
                                                                  instaPostsContext)
                                                              .loadMoreInstaPosts(
                                                            url: instaPostsState
                                                                    .data
                                                                    .result
                                                                    ?.next ??
                                                                "",
                                                            context:
                                                                instaPostsContext,
                                                            pageAccessToken: instance<
                                                                        HiveHelper>()
                                                                    .getUser()
                                                                    ?.defaultPageAccessToken ??
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .metaPages
                                                                    ?.accessToken ??
                                                                "",
                                                          );
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              ShapeDecoration(
                                                            gradient: Constants
                                                                .defGradient,
                                                            shape:
                                                                RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          38),
                                                            ),
                                                            shadows: const [
                                                              BoxShadow(
                                                                color: Color(
                                                                    0x19000000),
                                                                blurRadius: 22,
                                                                offset: Offset(
                                                                    0, 4),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Padding(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    vertical:
                                                                        8.sp,
                                                                    horizontal:
                                                                        14.sp),
                                                            child: Text(
                                                              'Load More'.tr,
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 12.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                              ],
                                            ),
                                      if (CreateAdCubit.get(context)
                                              .optimization
                                              ?.actualName !=
                                          "POST_ENGAGEMENT") ...[
                                        20.verticalSpace,
                                        DestinationWidget(
                                          selectedTab: selectTab,
                                          isPrevPost: true,
                                        ),
                                      ],
                                      const SizedBox(height: 25),
                                      if (GetPostsCubit.get(instaPostsContext)
                                                  .url !=
                                              "null" ||
                                          GetPostsCubit.get(instaPostsContext)
                                                  .url !=
                                              null)
                                        SizedBox(
                                          width: 235.w,
                                          child: ButtonWidget(
                                            text: "Save".tr,
                                            onTap: () async {
                                              if (CreateAdCubit.get(context)
                                                      .postId ==
                                                  null) {
                                                print('c 1');
                                                showErrorToast(
                                                    "please select the post");
                                              } else if (CreateAdCubit.get(
                                                          context)
                                                      .postId !=
                                                  null) {
                                                CreateAdCubit.get(context)
                                                        .adModel =
                                                    CreateAdCubit.get(context)
                                                        .adModel
                                                        .copyWith(
                                                          objectStoryId:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .postId,
                                                          // isInstaPost: true,
                                                          // video: CreateAdCubit.get(context)
                                                          //     .adVideo,
                                                          // thumb: CreateAdCubit.get(context)
                                                          //     .videoImage,
                                                          adCreativeName:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .adCreativeName
                                                                  .text,
                                                          description:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .headline
                                                                  .text,
                                                          message:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .message
                                                                  .text,
                                                          webSiteLinkMain:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .webSiteLink
                                                                  .text,

                                                          linkDescription:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .linkDesc
                                                                  .text,
                                                          type: CreateAdCubit.get(
                                                                          context)
                                                                      .destinationType ==
                                                                  "WEBSITE"
                                                              ? CreateAdCubit.get(
                                                                      context)
                                                                  .type
                                                              : CreateAdCubit.get(
                                                                              context)
                                                                          .destinationType ==
                                                                      "ON_AD"
                                                                  ? "SIGN_UP"
                                                                  : CreateAdCubit.get(context)
                                                                              .destinationType ==
                                                                          "INSTAGRAM_DIRECT"
                                                                      ? "INSTAGRAM_MESSAGE"
                                                                      : "MESSAGE_PAGE",
                                                          destinationType:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .destinationType,
                                                          // CreateAdCubit.get(context)
                                                          //             .optimization
                                                          //             ?.actualName
                                                          //             .toString() ==
                                                          //         'CONVERSATIONS'
                                                          //     ? "MESSENGER"
                                                          //     : "WEBSITE",
                                                          instAccId: instance<
                                                                  HiveHelper>()
                                                              .getUser()
                                                              ?.instAccId
                                                              .toString(),

                                                          link: CreateAdCubit.get(
                                                                          context)
                                                                      .destinationType ==
                                                                  "MESSENGER"
                                                              ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
                                                              : CreateAdCubit.get(
                                                                              context)
                                                                          .destinationType ==
                                                                      "INSTAGRAM_DIRECT"
                                                                  ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                                                  : CreateAdCubit.get(context)
                                                                              .destinationType ==
                                                                          "WHATSAPP"
                                                                      ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
                                                                      : null,
                                                        );
                                                print(
                                                    'c 2 ${CreateAdCubit.get(context).destinationType} ${CreateAdCubit.get(context).type}');
                                                CreateAdCubit.get(context)
                                                    .isAdCreativeCreated = true;
                                                Constants
                                                    .adCreativeExpansionTileKey
                                                    .currentState
                                                    ?.collapse();
                                              } else if (CreateAdCubit.get(
                                                      context)
                                                  .adCreativeVideoFormKey
                                                  .currentState!
                                                  .validate()) {
                                                print('c 3');
                                                if (CreateAdCubit.get(context)
                                                    .adVideo
                                                    .isEmpty) {
                                                  showErrorToast(
                                                      "Please select your ad video");
                                                }
                                                // else if (CreateAdCubit.get(
                                                //         context)
                                                //     .videoImage
                                                //     .isEmpty) {
                                                //   showErrorToast(
                                                //       "Please select your ad video image");
                                                // }
                                                else {
                                                  // else if (await validateImage(
                                                  //     CreateAdCubit.get(context).videoImage)) {
                                                  CreateAdCubit.get(context)
                                                          .adModel =
                                                      CreateAdCubit.get(context)
                                                          .adModel
                                                          .copyWith(
                                                            video: CreateAdCubit
                                                                    .get(
                                                                        context)
                                                                .adVideo,
                                                            // thumb: CreateAdCubit
                                                            //         .get(
                                                            //             context)
                                                            //     .videoImage,
                                                            adCreativeName:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .adCreativeName
                                                                    .text,
                                                            description:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .headline
                                                                    .text,
                                                            message:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .message
                                                                    .text,
                                                            webSiteLinkMain:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .webSiteLink
                                                                    .text,

                                                            linkDescription:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .linkDesc
                                                                    .text,
                                                            type: CreateAdCubit.get(
                                                                            context)
                                                                        .destinationType ==
                                                                    "WEBSITE"
                                                                ? CreateAdCubit.get(
                                                                        context)
                                                                    .type
                                                                : CreateAdCubit.get(context)
                                                                            .destinationType ==
                                                                        "ON_AD"
                                                                    ? "SIGN_UP"
                                                                    : CreateAdCubit.get(context).destinationType ==
                                                                            "INSTAGRAM_DIRECT"
                                                                        ? "INSTAGRAM_MESSAGE"
                                                                        : "MESSAGE_PAGE",
                                                            destinationType:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .destinationType,
                                                            // CreateAdCubit.get(context)
                                                            //             .optimization
                                                            //             ?.actualName
                                                            //             .toString() ==
                                                            //         'CONVERSATIONS'
                                                            //     ? "MESSENGER"
                                                            //     : "WEBSITE",
                                                            instAccId: instance<
                                                                    HiveHelper>()
                                                                .getUser()
                                                                ?.instAccId
                                                                .toString(),

                                                            link: CreateAdCubit.get(
                                                                            context)
                                                                        .destinationType ==
                                                                    "MESSENGER"
                                                                ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
                                                                : CreateAdCubit.get(context)
                                                                            .destinationType ==
                                                                        "INSTAGRAM_DIRECT"
                                                                    ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                                                    : CreateAdCubit.get(context).destinationType ==
                                                                            "WHATSAPP"
                                                                        ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
                                                                        : null,
                                                          );
                                                  CreateAdCubit.get(context)
                                                          .isAdCreativeCreated =
                                                      true;
                                                  Constants
                                                      .adCreativeExpansionTileKey
                                                      .currentState
                                                      ?.collapse();
                                                }
                                                // }
                                              } else {
                                                print('c 4');
                                                // Show error toast if the form is invalid
                                                showErrorToast(
                                                    "Please fill in all the required fields correctly.");
                                              }
                                              // CreateAdCubit.get(context)
                                              //     .adModel
                                              //     .toJson()
                                              //     .then((value) =>
                                              //         print("create campaign ${value}"));
                                            },
                                          ),
                                        ),
                                    ],
                                  );
                                } else {
                                  return const SizedBox();
                                }
                              },
                            )
                          : BlocBuilder<GetPostsCubit, GetFbPostsState>(
                              builder: (postsContext, postsState) {
                                if (postsState is GetFbPostsStateLoading) {
                                  return const LoadingWidget(
                                    isCircle: true,
                                  );
                                } else {
                                  return Column(
                                    children: [
                                      ListView.separated(
                                        padding: EdgeInsets.zero,
                                        separatorBuilder: (context, index) {
                                          return const Divider();
                                        },
                                        itemBuilder: (item, index) {
                                          return Padding(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 12.sp),
                                            child: Row(
                                              children: [
                                                Radio<int>(
                                                  value: index,
                                                  groupValue:
                                                      CreateAdCubit.get(context)
                                                          .postIndex,
                                                  onChanged: (int? newValue) {
                                                    CreateAdCubit.get(context)
                                                        .setSelectedPost(
                                                            GetPostsCubit.get(
                                                                    postsContext)
                                                                .posts[index]
                                                                .id,
                                                            index,
                                                            GetPostsCubit.get(
                                                                    postsContext)
                                                                .posts[index]);
                                                    CreateAdCubit.get(
                                                            postsContext)
                                                        .adModel = CreateAdCubit
                                                            .get(postsContext)
                                                        .adModel
                                                        .copyWith(
                                                            isInstaPost: false);
                                                    print("agdsgdsfs${CreateAdCubit.get(
                                                                context)
                                                            .postId}");
                                                    CreateAdCubit.get(context)
                                                        .updateAdCreativeProcess7();
                                                  },
                                                  activeColor:
                                                      AppColors.mainColor,
                                                ),
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Column(
                                                      children: [
                                                        Text(GetPostsCubit.get(
                                                                    postsContext)
                                                                .posts[index]
                                                                .mediaType ??
                                                            ""),
                                                        5.verticalSpace,
                                                        ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      5.sp),
                                                          child:
                                                              GestureDetector(
                                                            onTap: () {
                                                              // Navigator.pushNamed(
                                                              //     context,
                                                              //     Routes.image,
                                                              //     arguments: {
                                                              //       "imageUrl": GetPostsCubit.get(
                                                              //               postsContext)
                                                              //           .posts[
                                                              //               index]
                                                              //           .mediaUrl,
                                                              //     });
                                                            },
                                                            child: (GetPostsCubit.get(postsContext)
                                                                            .posts[
                                                                                index]
                                                                            .mediaType ==
                                                                        "IMAGE" ||
                                                                    GetPostsCubit.get(postsContext)
                                                                            .posts[index]
                                                                            .mediaType ==
                                                                        "photo")
                                                                ? CachedImageWidget(
                                                                    image: GetPostsCubit.get(
                                                                            postsContext)
                                                                        .posts[
                                                                            index]
                                                                        .mediaUrl,
                                                                    height:
                                                                        80.h,
                                                                    width: 80.h,
                                                                  )
                                                                : SizedBox(
                                                                    height:
                                                                        80.h,
                                                                    width:
                                                                        100.h,
                                                                    child: CustomVideoPlayer.network(
                                                                        videoUrl: GetPostsCubit.get(postsContext)
                                                                            .posts[index]
                                                                            .mediaUrl),
                                                                  ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 7.0),
                                                      child: SizedBox(
                                                        width: 150.h,
                                                        child: CustomText(
                                                          text: GetPostsCubit.get(
                                                                      postsContext)
                                                                  .posts[index]
                                                                  .caption ??
                                                              "",
                                                          maxLines: 10,
                                                          textAlign:
                                                              TextAlign.right,
                                                        ),
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                        itemCount:
                                            GetPostsCubit.get(postsContext)
                                                .posts
                                                .length,
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                      ),
                                      20.verticalSpace,
                                      (GetPostsCubit.get(postsContext).url ==
                                                  "null" ||
                                              GetPostsCubit.get(postsContext)
                                                      .url ==
                                                  null)
                                          ? const SizedBox()
                                          : Column(
                                              children: [
                                                postsState
                                                        is GetMorePostsStateLoading
                                                    ? const LoadingWidget(
                                                        isCircle: true,
                                                      )
                                                    : InkWell(
                                                        onTap: () {
                                                          GetPostsCubit.get(
                                                                  postsContext)
                                                              .loadMoreFbPosts(
                                                                  url: GetPostsCubit
                                                                          .get(
                                                                              postsContext)
                                                                      .url,
                                                                  context:
                                                                      postsContext);
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              ShapeDecoration(
                                                            gradient: Constants
                                                                .defGradient,
                                                            shape:
                                                                RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          38),
                                                            ),
                                                            shadows: const [
                                                              BoxShadow(
                                                                color: Color(
                                                                    0x19000000),
                                                                blurRadius: 22,
                                                                offset: Offset(
                                                                    0, 4),
                                                                spreadRadius: 0,
                                                              )
                                                            ],
                                                          ),
                                                          child: Padding(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    vertical:
                                                                        8.sp,
                                                                    horizontal:
                                                                        14.sp),
                                                            child: Text(
                                                              'Load More'.tr,
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 12.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                // Column(
                                                //   children: [
                                                //     CustomText(
                                                //       text: 'post-ad action'.tr,
                                                //       color: AppColors.mainColor,
                                                //       fontSize: 16,
                                                //       fontWeight: FontWeight.w500,
                                                //     ),
                                                //     const SizedBox(height: 25),
                                                //     SizedBox(
                                                //       height: 80.h,
                                                //       child: Material(
                                                //         elevation: 0,
                                                //         color: Colors.transparent,
                                                //         child: ListView.builder(
                                                //           scrollDirection:
                                                //               Axis.horizontal,
                                                //           itemCount:
                                                //               destinationType.length,
                                                //           itemBuilder: (context, index) {
                                                //             final e =
                                                //                 destinationType[index];
                                                //
                                                //             // Update the destination index
                                                //             CreateAdCubit.get(context)
                                                //                 .destinationIndex = index;
                                                //
                                                //             // Determine if the destination is selected
                                                //             CreateAdCubit.get(context)
                                                //                     .isSelectedDestination =
                                                //                 CreateAdCubit.get(context)
                                                //                         .destinationIndex ==
                                                //                     CreateAdCubit.get(
                                                //                             context)
                                                //                         .selectedDestinationIndex;
                                                //
                                                //             return Padding(
                                                //               padding: const EdgeInsets
                                                //                   .symmetric(
                                                //                   horizontal: 5.0),
                                                //               child:
                                                //                   DestinationTypeWidget(
                                                //                 isSelected: CreateAdCubit
                                                //                             .get(context)
                                                //                         .isSelectedDestination ??
                                                //                     false,
                                                //                 callback:
                                                //                     (selectedIndex) {
                                                //                   CreateAdCubit.get(
                                                //                               context)
                                                //                           .webSiteLink =
                                                //                       TextEditingController();
                                                //                   CreateAdCubit.get(
                                                //                               context)
                                                //                           .linkDesc =
                                                //                       TextEditingController();
                                                //                   CreateAdCubit.get(
                                                //                               context)
                                                //                           .headline =
                                                //                       TextEditingController();
                                                //                   // CreateAdCubit.get(context).message =
                                                //                   //     TextEditingController();
                                                //                   // CreateAdCubit.get(context)
                                                //                   //     .isAdCreativeProcess1Updated = false;
                                                //                   // CreateAdCubit.get(context)
                                                //                   //     .isAdCreativeProcess2Updated = false;
                                                //                   // CreateAdCubit.get(context)
                                                //                   //     .isAdCreativeProcess3Updated = false;
                                                //                   // CreateAdCubit.get(context)
                                                //                   //     .isAdCreativeProcess4Updated = false;
                                                //                   // CreateAdCubit.get(context)
                                                //                   //     .isAdCreativeProcess5Updated = false;
                                                //                   // CreateAdCubit.get(context)
                                                //                   //     .isAdCreativeProcess6Updated = false;
                                                //                   setState(() {
                                                //                     CreateAdCubit.get(
                                                //                                 context)
                                                //                             .selectedDestinationIndex =
                                                //                         selectedIndex;
                                                //
                                                //                     // Update destination type based on index
                                                //                     if (e == "Whatsapp") {
                                                //                       CreateAdCubit.get(
                                                //                                   context)
                                                //                               .destinationType =
                                                //                           "WHATSAPP";
                                                //                     } else if (e ==
                                                //                         "Messenger") {
                                                //                       CreateAdCubit.get(
                                                //                                   context)
                                                //                               .destinationType =
                                                //                           "MESSENGER";
                                                //                     } else if (e ==
                                                //                         "Website") {
                                                //                       CreateAdCubit.get(
                                                //                                   context)
                                                //                               .destinationType =
                                                //                           "WEBSITE";
                                                //                     } else {
                                                //                       CreateAdCubit.get(
                                                //                                   context)
                                                //                               .destinationType =
                                                //                           "INSTAGRAM_DIRECT";
                                                //                     }
                                                //
                                                //                     print("destinationType" +
                                                //                         CreateAdCubit.get(
                                                //                                 context)
                                                //                             .destinationType);
                                                //                   });
                                                //                 },
                                                //                 name: e,
                                                //                 index: index,
                                                //               ),
                                                //             );
                                                //           },
                                                //         ),
                                                //       ),
                                                //     ),
                                                //   ],
                                                // ),
                                                // 20.verticalSpace,
                                              ],
                                            ),
                                      if (CreateAdCubit.get(context)
                                              .optimization
                                              ?.actualName !=
                                          "POST_ENGAGEMENT") ...[
                                        20.verticalSpace,
                                        DestinationWidget(
                                          selectedTab: selectTab,
                                          isPrevPost: true,
                                        ),
                                      ],
                                      const SizedBox(height: 25),
                                      if (GetPostsCubit.get(postsContext).url !=
                                              "null" ||
                                          GetPostsCubit.get(postsContext).url !=
                                              null)
                                        SizedBox(
                                          width: 235.w,
                                          child: ButtonWidget(
                                            text: "Save".tr,
                                            onTap: () async {
                                              if (CreateAdCubit.get(context)
                                                      .postId ==
                                                  null) {
                                                print('c 1');
                                                showErrorToast(
                                                    "please select the post");
                                              } else if (CreateAdCubit.get(
                                                          context)
                                                      .postId !=
                                                  null) {
                                                CreateAdCubit.get(context)
                                                        .adModel =
                                                    CreateAdCubit.get(context)
                                                        .adModel
                                                        .copyWith(
                                                          objectStoryId:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .postId,
                                                          // video: CreateAdCubit.get(context)
                                                          //     .adVideo,
                                                          // thumb: CreateAdCubit.get(context)
                                                          //     .videoImage,
                                                          adCreativeName:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .adCreativeName
                                                                  .text,
                                                          description:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .headline
                                                                  .text,
                                                          message:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .message
                                                                  .text,
                                                          webSiteLinkMain:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .webSiteLink
                                                                  .text,

                                                          linkDescription:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .linkDesc
                                                                  .text,
                                                          type: CreateAdCubit.get(
                                                                          context)
                                                                      .destinationType ==
                                                                  "WEBSITE"
                                                              ? CreateAdCubit.get(
                                                                      context)
                                                                  .type
                                                              : CreateAdCubit.get(
                                                                              context)
                                                                          .destinationType ==
                                                                      "ON_AD"
                                                                  ? "SIGN_UP"
                                                                  : CreateAdCubit.get(context)
                                                                              .destinationType ==
                                                                          "INSTAGRAM_DIRECT"
                                                                      ? "INSTAGRAM_MESSAGE"
                                                                      : "MESSAGE_PAGE",
                                                          destinationType:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .destinationType,
                                                          // CreateAdCubit.get(context)
                                                          //             .optimization
                                                          //             ?.actualName
                                                          //             .toString() ==
                                                          //         'CONVERSATIONS'
                                                          //     ? "MESSENGER"
                                                          //     : "WEBSITE",
                                                          instAccId: instance<
                                                                  HiveHelper>()
                                                              .getUser()
                                                              ?.instAccId
                                                              .toString(),

                                                          link: CreateAdCubit.get(
                                                                          context)
                                                                      .destinationType ==
                                                                  "MESSENGER"
                                                              ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
                                                              : CreateAdCubit.get(
                                                                              context)
                                                                          .destinationType ==
                                                                      "INSTAGRAM_DIRECT"
                                                                  ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                                                  : CreateAdCubit.get(context)
                                                                              .destinationType ==
                                                                          "WHATSAPP"
                                                                      ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
                                                                      : null,
                                                        );
                                                print(
                                                    'c 2 ${CreateAdCubit.get(context).destinationType} ${CreateAdCubit.get(context).type}');
                                                CreateAdCubit.get(context)
                                                    .isAdCreativeCreated = true;
                                                Constants
                                                    .adCreativeExpansionTileKey
                                                    .currentState
                                                    ?.collapse();
                                              } else if (CreateAdCubit.get(
                                                      context)
                                                  .adCreativeVideoFormKey
                                                  .currentState!
                                                  .validate()) {
                                                print('c 3');
                                                if (CreateAdCubit.get(context)
                                                    .adVideo
                                                    .isEmpty) {
                                                  showErrorToast(
                                                      "Please select your ad video");
                                                }
                                                // else if (CreateAdCubit.get(
                                                //         context)
                                                //     .videoImage
                                                //     .isEmpty) {
                                                //   showErrorToast(
                                                //       "Please select your ad video image");
                                                // }
                                                else {
                                                  // else if (await validateImage(
                                                  //     CreateAdCubit.get(context).videoImage)) {
                                                  CreateAdCubit.get(context)
                                                          .adModel =
                                                      CreateAdCubit.get(context)
                                                          .adModel
                                                          .copyWith(
                                                            video: CreateAdCubit
                                                                    .get(
                                                                        context)
                                                                .adVideo,
                                                            // thumb: CreateAdCubit
                                                            //         .get(
                                                            //             context)
                                                            //     .videoImage,
                                                            adCreativeName:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .adCreativeName
                                                                    .text,
                                                            description:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .headline
                                                                    .text,
                                                            message:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .message
                                                                    .text,
                                                            webSiteLinkMain:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .webSiteLink
                                                                    .text,

                                                            linkDescription:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .linkDesc
                                                                    .text,
                                                            type: CreateAdCubit.get(
                                                                            context)
                                                                        .destinationType ==
                                                                    "WEBSITE"
                                                                ? CreateAdCubit.get(
                                                                        context)
                                                                    .type
                                                                : CreateAdCubit.get(context)
                                                                            .destinationType ==
                                                                        "ON_AD"
                                                                    ? "SIGN_UP"
                                                                    : CreateAdCubit.get(context).destinationType ==
                                                                            "INSTAGRAM_DIRECT"
                                                                        ? "INSTAGRAM_MESSAGE"
                                                                        : "MESSAGE_PAGE",
                                                            destinationType:
                                                                CreateAdCubit.get(
                                                                        context)
                                                                    .destinationType,
                                                            // CreateAdCubit.get(context)
                                                            //             .optimization
                                                            //             ?.actualName
                                                            //             .toString() ==
                                                            //         'CONVERSATIONS'
                                                            //     ? "MESSENGER"
                                                            //     : "WEBSITE",
                                                            instAccId: instance<
                                                                    HiveHelper>()
                                                                .getUser()
                                                                ?.instAccId
                                                                .toString(),

                                                            link: CreateAdCubit.get(
                                                                            context)
                                                                        .destinationType ==
                                                                    "MESSENGER"
                                                                ? "https://m.me/${instance<HiveHelper>().getUser()?.defaultPageId}"
                                                                : CreateAdCubit.get(context)
                                                                            .destinationType ==
                                                                        "INSTAGRAM_DIRECT"
                                                                    ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                                                    : CreateAdCubit.get(context).destinationType ==
                                                                            "WHATSAPP"
                                                                        ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
                                                                        : null,
                                                          );

                                                  CreateAdCubit.get(context)
                                                          .isAdCreativeCreated =
                                                      true;
                                                  Constants
                                                      .adCreativeExpansionTileKey
                                                      .currentState
                                                      ?.collapse();
                                                }
                                                // }
                                              } else {
                                                print('c 4');
                                                // Show error toast if the form is invalid
                                                showErrorToast(
                                                    "Please fill in all the required fields correctly.");
                                              }
                                              // CreateAdCubit.get(context)
                                              //     .adModel
                                              //     .toJson()
                                              //     .then((value) =>
                                              //         print("create campaign ${value}"));
                                            },
                                          ),
                                        ),
                                      // if (CreateAdCubit.get(context).optimization?.id !=
                                      //     5)
                                      // const DestinationWidget(),
                                      // SizedBox(
                                      //   width: 235.w,
                                      //   child: ButtonWidget(
                                      //     text: "Save".tr,
                                      //     onTap: () async {
                                      //       if (CreateAdCubit.get(context).postId ==
                                      //           null) {
                                      //         showErrorToast("please select the post");
                                      //       } else if (CreateAdCubit.get(context)
                                      //               .postId !=
                                      //           null) {
                                      //         CreateAdCubit.get(context).adModel =
                                      //             CreateAdCubit.get(context)
                                      //                 .adModel
                                      //                 .copyWith(
                                      //                   objectStoryId:
                                      //                       CreateAdCubit.get(context)
                                      //                           .postId,
                                      //                 );
                                      //         Constants
                                      //             .adCreativeExpansionTileKey.currentState
                                      //             ?.collapse();
                                      //       } else if (CreateAdCubit.get(context)
                                      //           .adCreativeVideoFormKey
                                      //           .currentState!
                                      //           .validate()) {
                                      //         if (CreateAdCubit.get(context)
                                      //             .adVideo
                                      //             .first
                                      //             .path
                                      //             .isEmpty) {
                                      //           showErrorToast(
                                      //               "Please select your ad video");
                                      //         } else if (CreateAdCubit.get(context)
                                      //             .videoImage
                                      //             .first
                                      //             .path
                                      //             .isEmpty) {
                                      //           showErrorToast(
                                      //               "Please select your ad video image");
                                      //         } else {
                                      //           // else if (await validateImage(
                                      //           //     CreateAdCubit.get(context).videoImage)) {
                                      //           CreateAdCubit.get(context)
                                      //               .adModel = CreateAdCubit.get(
                                      //                   context)
                                      //               .adModel
                                      //               .copyWith(
                                      //                 video: CreateAdCubit.get(context)
                                      //                     .adVideo,
                                      //                 thumb: CreateAdCubit.get(context)
                                      //                     .videoImage,
                                      //                 adCreativeName:
                                      //                     CreateAdCubit.get(context)
                                      //                         .adCreativeName
                                      //                         .text,
                                      //                 description:
                                      //                     CreateAdCubit.get(context)
                                      //                         .headline
                                      //                         .text,
                                      //                 message: CreateAdCubit.get(context)
                                      //                     .message
                                      //                     .text,
                                      //                 webSiteLinkMain:
                                      //                     CreateAdCubit.get(context)
                                      //                         .webSiteLink
                                      //                         .text,
                                      //
                                      //                 linkDescription:
                                      //                     CreateAdCubit.get(context)
                                      //                         .linkDesc
                                      //                         .text,
                                      //                 type: CreateAdCubit.get(context)
                                      //                             .destinationType ==
                                      //                         "WEBSITE"
                                      //                     ? CreateAdCubit.get(context)
                                      //                         .type
                                      //                     : CreateAdCubit.get(context)
                                      //                                 .destinationType ==
                                      //                             "ON_AD"
                                      //                         ? "SIGN_UP"
                                      //                         : CreateAdCubit.get(context)
                                      //                                     .destinationType ==
                                      //                                 "INSTAGRAM_DIRECT"
                                      //                             ? "INSTAGRAM_MESSAGE"
                                      //                             : "MESSAGE_PAGE",
                                      //                 destinationType:
                                      //                     CreateAdCubit.get(context)
                                      //                                 .optimization
                                      //                                 ?.actualName
                                      //                                 .toString() ==
                                      //                             'CONVERSATIONS'
                                      //                         ? "MESSENGER"
                                      //                         : "WEBSITE",
                                      //                 instAccId: instance<HiveHelper>()
                                      //                     .getUser()
                                      //                     ?.instAccId
                                      //                     .toString(),
                                      //
                                      //                 // link: CreateAdCubit.get(context)
                                      //                 //             .destinationType ==
                                      //                 //         "MESSENGER"
                                      //                 //     ? "https://m.me/${instance<HiveHelper>().getUser()?.pageUserName}"
                                      //                 //     : CreateAdCubit.get(context)
                                      //                 //                 .destinationType ==
                                      //                 //             "INSTAGRAM_DIRECT"
                                      //                 //         ? "https://www.instagram.com/direct/t/${instance<HiveHelper>().getUser()?.instUserName}"
                                      //                 //         : CreateAdCubit.get(context)
                                      //                 //                     .destinationType ==
                                      //                 //                 "WHATSAPP"
                                      //                 //             ? "https://wa.me/${instance<HiveHelper>().getUser()?.whatsNumber}"
                                      //                 //             : null,
                                      //               );
                                      //
                                      //           Constants.adCreativeExpansionTileKey
                                      //               .currentState
                                      //               ?.collapse();
                                      //
                                      //           print(
                                      //               "create campaign ${CreateAdCubit.get(context).adModel.toJson()}");
                                      //         }
                                      //         // }
                                      //       } else {
                                      //         // Show error toast if the form is invalid
                                      //         showErrorToast(
                                      //             "Please fill in all the required fields correctly.");
                                      //       }
                                      //     },
                                      //   ),
                                      // ),
                                      // SizedBox(
                                      //   width: 235.w,
                                      //   child: ButtonWidget(
                                      //     text: "Save".tr,
                                      //     onTap: () async {
                                      //       if (CreateAdCubit.get(context).postId ==
                                      //           null) {
                                      //         showErrorToast("please select the post");
                                      //       } else {
                                      //         CreateAdCubit.get(context).adModel =
                                      //             CreateAdCubit.get(context)
                                      //                 .adModel
                                      //                 .copyWith(
                                      //                   objectStoryId:
                                      //                       CreateAdCubit.get(context)
                                      //                           .postId,
                                      //                 );
                                      //         Constants.adCreativeExpansionTileKey
                                      //             .currentState
                                      //             ?.collapse();
                                      //       }
                                      //     },
                                      //   ),
                                      // ),
                                    ],
                                  );
                                }
                              },
                            ),
                    ],
                  ),
        const SizedBox(height: 25),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../utils/res/app_assets.dart';
import '../../../../utils/res/constants.dart';
import '../../data/models/chart_data.dart';

part 'campaign_home_state.dart';

class CampaignHomeCubit extends Cubit<CampaignHomeState> {
  CampaignHomeCubit() : super(CampaignHomeInitial());

  static CampaignHomeCubit get(context) => BlocProvider.of(context);

  List<String> banners = [
    AppAssets.dummy,
    AppAssets.dummy1,
    AppAssets.dummy2,
    AppAssets.dummy3,
    AppAssets.dummy4,
  ];
  int bannerIndex = 0;
  int selectedIndex = 0;

  void setSelectedIndex(int index) {
    selectedIndex = index;
    emit(ChangeSelectedIndex());
  }

  void setBannerIndex(int index) {
    bannerIndex = index;
    emit(ChangeBannerIndex());
  }

  List<StackedColumnSeries<ChartData, String>> getSeries() {
    final List<ChartData> chartData = [
      ChartData(
        'Mon',
        5,
      ),
      ChartData(
        'Tue',
        4,
      ),
      ChartData(
        'Wed',
        6,
      ),
      ChartData(
        'Thu',
        3,
      ),
      ChartData(
        'Fri',
        7,
      ),
      ChartData(
        'Sat',
        8,
      ),
      ChartData(
        'Sun',
        6,
      ),
    ];

    return <StackedColumnSeries<ChartData, String>>[
      StackedColumnSeries<ChartData, String>(
        dataSource: chartData,
        width: 0.4,
        gradient: Constants.secGradient,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(3.sp),
          topLeft: Radius.circular(3.sp),
        ),
        xValueMapper: (ChartData sales, _) => sales.day,
        yValueMapper: (ChartData sales, _) => sales.value1,
        name: 'Campaign',
      ),
    ];
  }
}

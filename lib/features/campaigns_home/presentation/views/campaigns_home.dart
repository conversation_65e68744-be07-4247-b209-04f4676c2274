import 'package:ads_dv/features/campaigns_home/presentation/controllers/campaign_home_cubit.dart';
import 'package:ads_dv/features/campaigns_home/presentation/views/widgets/campaign_details.dart';
import 'package:ads_dv/features/campaigns_home/presentation/views/widgets/insights_widget.dart';
import 'package:ads_dv/features/campaigns_home/presentation/views/widgets/occasions_widget.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/controllers/get_add_accounts/get_add_accounts_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/appbar.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../../../utils/network/urls/services_urls.dart';
import '../../../../utils/res/router/routes.dart';
import '../../../../widgets/decoration_container.dart';
import '../../../sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';

class CampaignsHomeScreen extends StatefulWidget {
  const CampaignsHomeScreen({super.key});

  @override
  State<CampaignsHomeScreen> createState() => _CampaignsHomeScreenState();
}

class _CampaignsHomeScreenState extends State<CampaignsHomeScreen>
    with TickerProviderStateMixin {
  final GlobalKey _key = GlobalKey();

  late AnimationController _animationController;
  late Animation<double> _jumpAnimation;

  @override
  void initState() {
    super.initState();
    // Initialize the AnimationController
    _animationController = AnimationController(
      duration: const Duration(seconds: 1), // Duration of one jump
      vsync: this,
    );

    // Define the jumping animation (goes from 0 to -20 to 0, creating a bouncing effect)
    _jumpAnimation = Tween<double>(begin: 0.0, end: -20.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut, // Smooth easing in and out
      ),
    );

    // Loop the animation to make it "jump" repeatedly
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose(); // Always dispose of controllers when done
    super.dispose();
  }

  // @override
  // void initState() {
  //   print(
  //       'photoUrlxcxcz ${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.balance!.toInt() / 100}');
  //   super.initState();
  // }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CampaignHomeCubit, CampaignHomeState>(
      builder: (context, state) {
        return Stack(
          children: [
            Scaffold(
              appBar: CustomAppBar(
                height: 180.h,
                logo: AppAssets.mainLogo,
                actions: [
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, Routes.notifications);
                    },
                    child: CustomSvgWidget(
                      svg: AppAssets.notifications,
                      height: 30.h,
                      width: 30.h,
                    ),
                  )
                ],
                leading: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: InkWell(
                    onTap: () {
                      Constants.scaffoldKey.currentState!.openDrawer();
                    },
                    child: CustomSvgWidget(
                      svg: AppAssets.drawer,
                      height: 4.h,
                      width: 12.h,
                    ),
                  ),
                ),
              ),
              floatingActionButton: IconButton(
                  onPressed: () async {
                    await Navigator.of(context)
                        .pushNamedAndRemoveUntil(Routes.chat, (route) => false);
                  },
                  icon: const Icon(Icons.add)),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.startDocked,
            ),
            Padding(
              padding: EdgeInsets.only(top: 160.h, left: 12.sp, right: 12.sp),
              child: DecoratedContainer(
                boxShadow: const BoxShadow(
                  color: Color(0x4C000000),
                  blurRadius: 61.90,
                  offset: Offset(0, 4),
                  spreadRadius: 0,
                ),
                color: Colors.white,
                radius: 35.sp,
                widget: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 32.sp),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        if (instance<HiveHelper>()
                                .getUser()
                                ?.defaultAccountName !=
                            null)
                          Column(
                            children: [
                              5.verticalSpace,
                              Showcase(
                                key: _key,
                                targetBorderRadius: const BorderRadius.all(
                                    Radius.elliptical(100, 40)),
                                targetPadding: const EdgeInsets.symmetric(
                                    vertical: 20, horizontal: 10),
                                // tooltipPosition: TooltipPosition.top,
                                description:
                                    '${"Now you are using".tr} ${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}',
                                scaleAnimationAlignment: Alignment.topCenter,
                                textColor: Colors.black,
                                child: InkWell(
                                  onTap: () {
                                    ShowCaseWidget.of(context)
                                        .startShowCase([_key]);
                                  },
                                  child: AccountHintText(
                                    isDefaultHint: true,
                                    hint:
                                        "${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}",
                                  ),
                                ),
                              ),
                              20.verticalSpace,
                            ],
                          )
                        else
                          const SizedBox(),
                        Row(
                          children: [
                            if (instance<HiveHelper>().getUser() != null)
                              (instance<HiveHelper>().getUser()?.photo ==
                                          null ||
                                      instance<HiveHelper>().getUser()!.photo ==
                                          "")
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.circular(50),
                                      child: Container(
                                        height: 70.h,
                                        //   width: 70.h,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                        child: CachedImageWidget(
                                          assetsImage: AppAssets.dummyProfile,
                                          fit: BoxFit.fill,
                                          height: 70.h,
                                        ),
                                      ),
                                    )
                                  : ClipRRect(
                                      borderRadius: BorderRadius.circular(50),
                                      child: Container(
                                        height: 70.h,
                                        width: 70.h,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                        child: CachedImageWidget(
                                          image: ServicesURLs.assetsUrl +
                                              instance<HiveHelper>()
                                                  .getUser()!
                                                  .photo
                                                  .toString(),
                                          height: 70.h,
                                        ),
                                      ),
                                    ),
                            10.horizontalSpace,
                            Row(
                              children: [
                                CustomText(
                                  text: "Hello, ".tr,
                                  fontSize: 18.sp,
                                  color: Colors.black,
                                ),
                                CustomText(
                                  text:
                                      instance<HiveHelper>().getUser()?.name ??
                                          "",
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.black,
                                  maxLines: 2,
                                ),
                              ],
                            ),
                          ],
                        ),
                        20.verticalSpace,
                        BlocBuilder<GetAdAccountsCubit, GetAdAccountsState>(
                          builder: (context, walletState) {
                            return Padding(
                              padding: EdgeInsets.symmetric(horizontal: 12.sp),
                              child: SizedBox(
                                height: 260.h,
                                width: double.infinity,
                                child: PageView(
                                  children: [
                                    Container(
                                      height: 250.h,
                                      width: double.infinity,
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x33000000),
                                            blurRadius: 30,
                                            offset: Offset(0, 4),
                                            spreadRadius: -6,
                                          )
                                        ],
                                      ),
                                      child: Center(
                                        child: Stack(
                                          children: [
                                            Center(
                                              child: walletState
                                                      is GetAdAccountsStateLoading
                                                  ? CustomText(
                                                      text: 'Loading ...'.tr,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    )
                                                  : CircularPercentIndicator(
                                                      radius: 80.sp,
                                                      animation: true,
                                                      animationDuration: 1200,
                                                      lineWidth: 13.sp,
                                                      percent: 0.65,
                                                      center: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          const CustomSvgWidget(
                                                              svg: AppAssets
                                                                  .wallet1),
                                                          6.verticalSpace,
                                                          ShaderMask(
                                                            shaderCallback:
                                                                (Rect bounds) {
                                                              return Constants
                                                                  .defGradient
                                                                  .createShader(
                                                                      bounds);
                                                            },
                                                            child: SizedBox(
                                                              width: 110.h,
                                                              child: GetAdAccountsCubit
                                                                          .get(
                                                                              context)
                                                                      .adAccounts
                                                                      .where((element) =>
                                                                          element
                                                                              .id ==
                                                                          instance<HiveHelper>()
                                                                              .getUser()
                                                                              ?.defaultAccountId)
                                                                      .isEmpty
                                                                  ? CustomText(
                                                                      text:
                                                                          "0 AED",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    )
                                                                  : CustomText(
                                                                      text:
                                                                          "${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.balance.toInt() / 100 ?? ""} ${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.currency ?? ""}",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      reverse: true,
                                                      circularStrokeCap:
                                                          CircularStrokeCap
                                                              .round,
                                                      linearGradient:
                                                          Constants.secGradient,
                                                      backgroundColor:
                                                          const Color(
                                                                  0xFFFB533E)
                                                              .withOpacity(0.1),
                                                    ),
                                            ),
                                            Positioned(
                                              top: 10,
                                              left: 10,
                                              child: CustomText(
                                                text: "Total Balance".tr,
                                                fontWeight: FontWeight.w700,
                                                fontSize: 16.sp,
                                                alignment: AlignmentDirectional
                                                    .centerStart,
                                                color: Colors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Container(
                                      height: 250.h,
                                      width: double.infinity,
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x33000000),
                                            blurRadius: 30,
                                            offset: Offset(0, 4),
                                            spreadRadius: -6,
                                          )
                                        ],
                                      ),
                                      child: Center(
                                        child: Stack(
                                          children: [
                                            Center(
                                              child: walletState
                                                      is GetAdAccountsStateLoading
                                                  ? CustomText(
                                                      text: 'Loading ...'.tr,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    )
                                                  : CircularPercentIndicator(
                                                      radius: 80.sp,
                                                      animation: true,
                                                      animationDuration: 1200,
                                                      lineWidth: 13.sp,
                                                      percent: 0.65,
                                                      center: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          const CustomSvgWidget(
                                                              svg: AppAssets
                                                                  .wallet1),
                                                          6.verticalSpace,
                                                          ShaderMask(
                                                            shaderCallback:
                                                                (Rect bounds) {
                                                              return Constants
                                                                  .defGradient
                                                                  .createShader(
                                                                      bounds);
                                                            },
                                                            child: SizedBox(
                                                              width: 110.h,
                                                              child: GetAdAccountsCubit
                                                                          .get(
                                                                              context)
                                                                      .adAccounts
                                                                      .where((element) =>
                                                                          element
                                                                              .id ==
                                                                          instance<HiveHelper>()
                                                                              .getUser()
                                                                              ?.defaultAccountId)
                                                                      .isEmpty
                                                                  ? CustomText(
                                                                      text:
                                                                          "0 AED",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    )
                                                                  : CustomText(
                                                                      text:
                                                                          "${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.amountSpent.toInt() / 100 ?? ""} ${GetAdAccountsCubit.get(context).adAccounts.where((element) => element.id == instance<HiveHelper>().getUser()?.defaultAccountId).first.currency ?? ""}",
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          25.sp,
                                                                      alignment:
                                                                          AlignmentDirectional
                                                                              .center,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      reverse: true,
                                                      circularStrokeCap:
                                                          CircularStrokeCap
                                                              .round,
                                                      linearGradient:
                                                          Constants.secGradient,
                                                      backgroundColor:
                                                          const Color(
                                                                  0xFFFB533E)
                                                              .withOpacity(0.1),
                                                    ),
                                            ),
                                            Positioned(
                                              top: 10,
                                              left: 10,
                                              child: CustomText(
                                                text: "Amount Spent".tr,
                                                fontWeight: FontWeight.w700,
                                                fontSize: 16.sp,
                                                alignment: AlignmentDirectional
                                                    .centerStart,
                                                color: Colors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                        25.verticalSpace,
                        CustomText(
                          text: "Insights".tr,
                          fontWeight: FontWeight.w700,
                          fontSize: 20.sp,
                          alignment: AlignmentDirectional.centerStart,
                          color: Colors.black,
                        ),
                        25.verticalSpace,
                        InsightsWidget(cubit: CampaignHomeCubit.get(context)),
                        25.verticalSpace,
                        CampaignDetails(cubit: CampaignHomeCubit.get(context)),
                        25.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 280.w,
                              child: CustomText(
                                alignment: AlignmentDirectional.centerStart,
                                maxLines: 2,
                                text:
                                    "Choose an Occasion to launch your campaign"
                                        .tr,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                        10.verticalSpace,
                        OccasionsWidget(cubit: CampaignHomeCubit.get(context)),
                        80.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 100.0,
              right: 20.0,
              child: GestureDetector(
                onTap: () async {
                  await Navigator.of(context)
                      .pushNamedAndRemoveUntil(Routes.chat, (route) => false);
                },
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _jumpAnimation.value),
                      // Move the button up and down
                      child: child,
                    );
                  },
                  child: Image.asset(
                      "assets/images/chat.png"), // Your chat button image
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

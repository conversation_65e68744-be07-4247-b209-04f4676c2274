import 'package:ads_dv/features/campaigns_home/presentation/controllers/campaign_home_cubit.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/media_query_config.dart';
import '../../../../../utils/res/responsive_utils.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/svg_widget.dart';

class CampaignDetails extends StatelessWidget {
  CampaignHomeCubit cubit;
  CampaignDetails({super.key, required this.cubit});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.sp),
      child: DecoratedContainer(
        width: double.infinity,
        color: Colors.white,
        radius: 10.sp,
        boxShadow: const BoxShadow(
          color: Color(0x33000000),
          blurRadius: 30,
          offset: Offset(0, 4),
          spreadRadius: -6,
        ),
        widget: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              CustomText(
                text: "Details of Last Campaign",
                fontWeight: FontWeight.w700,
                fontSize: 16.sp,
                alignment: AlignmentDirectional.centerStart,
                color: Colors.black,
              ),
              15.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 40.83.sp,
                    decoration: ShapeDecoration(
                      color: const Color(0xFFF1F1F1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(49),
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.sp),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomSvgWidget(
                            svg: AppAssets.calender,
                            width: 15.h,
                            height: 15.h,
                          ),
                          6.horizontalSpace,
                          CustomText(
                            alignment: AlignmentDirectional.center,
                            text: '24 Mar, 2024',
                            textAlign: TextAlign.center,
                            color: const Color(0xFF0B0F26),
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                          )
                        ],
                      ),
                    ),
                  ),
                  StatefulBuilder(builder: (context, setState) {
                    return Center(
                      child: Container(
                        height: 40,
                        padding: EdgeInsets.zero,
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                                width: 1, color: Color(0xFFF1F1F5)),
                            borderRadius: BorderRadius.circular(49),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            CampaignHomeCubit.get(context).selectedIndex == 0
                                ? SizedBox(
                                    width: SizeConfig.widthr(55, context),
                                    child: Container(
                                      padding: ResponsiveUtils.padding(
                                          context: context,
                                          width: 8,
                                          height: 2),
                                      height: 40,
                                      decoration: ShapeDecoration(
                                        gradient: const LinearGradient(
                                          begin: Alignment(-0.99, -0.10),
                                          end: Alignment(0.99, 0.1),
                                          colors: [
                                            Color(0xFF0B0F26),
                                            Color(0xFF1C4294)
                                          ],
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                        ),
                                      ),
                                      child: Center(
                                        child: CustomText(
                                          text: "Day",
                                          alignment:
                                              AlignmentDirectional.center,
                                          fontSize: 12.sp,
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  )
                                : SizedBox(
                                    width: SizeConfig.widthr(55, context),
                                    child: InkWell(
                                      onTap: () {
                                        CampaignHomeCubit.get(context)
                                            .setSelectedIndex(0);
                                      },
                                      child: Center(
                                        child: CustomText(
                                          text: "Day",
                                          alignment:
                                              AlignmentDirectional.center,
                                          fontSize: 12.sp,
                                          color: AppColors.mainColor,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                            CampaignHomeCubit.get(context).selectedIndex == 1
                                ? SizedBox(
                                    width: SizeConfig.widthr(55, context),
                                    child: Container(
                                      padding: ResponsiveUtils.padding(
                                          context: context,
                                          width: 8,
                                          height: 2),
                                      height: 40,
                                      decoration: ShapeDecoration(
                                        gradient: const LinearGradient(
                                          begin: Alignment(-0.99, -0.10),
                                          end: Alignment(0.99, 0.1),
                                          colors: [
                                            Color(0xFF0B0F26),
                                            Color(0xFF1C4294)
                                          ],
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                        ),
                                      ),
                                      child: Center(
                                        child: CustomText(
                                          text: "Week",
                                          alignment:
                                              AlignmentDirectional.center,
                                          fontSize: 12.sp,
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  )
                                : SizedBox(
                                    width: SizeConfig.widthr(55, context),
                                    child: InkWell(
                                      onTap: () {
                                        CampaignHomeCubit.get(context)
                                            .setSelectedIndex(1);
                                      },
                                      child: Center(
                                        child: CustomText(
                                          text: "Week",
                                          alignment:
                                              AlignmentDirectional.center,
                                          fontSize: 12.sp,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                            CampaignHomeCubit.get(context).selectedIndex == 2
                                ? SizedBox(
                                    width: SizeConfig.widthr(55, context),
                                    child: Container(
                                      padding: ResponsiveUtils.padding(
                                          context: context,
                                          width: 8,
                                          height: 2),
                                      height: 40,
                                      decoration: ShapeDecoration(
                                        gradient: const LinearGradient(
                                          begin: Alignment(-0.99, -0.10),
                                          end: Alignment(0.99, 0.1),
                                          colors: [
                                            Color(0xFF0B0F26),
                                            Color(0xFF1C4294)
                                          ],
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                        ),
                                      ),
                                      child: Center(
                                        child: CustomText(
                                          text: "Month",
                                          alignment:
                                              AlignmentDirectional.center,
                                          fontSize: 12.sp,
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  )
                                : SizedBox(
                                    width: SizeConfig.widthr(55, context),
                                    child: InkWell(
                                      onTap: () {
                                        CampaignHomeCubit.get(context)
                                            .setSelectedIndex(2);
                                      },
                                      child: Center(
                                        child: CustomText(
                                          text: "Month",
                                          alignment:
                                              AlignmentDirectional.center,
                                          fontSize: 12.sp,
                                          color: AppColors.mainColor,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    );
                  }),
                ],
              ),
              25.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 30,
                            offset: Offset(0, 4),
                            spreadRadius: -5,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomSvgWidget(svg: AppAssets.views),
                            4.verticalSpace,
                            CustomText(
                              text: 'Views',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w400,
                              alignment: AlignmentDirectional.center,
                            ),
                            4.verticalSpace,
                            CustomText(
                              text: '2.453',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              alignment: AlignmentDirectional.center,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w700,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  10.horizontalSpace,
                  Expanded(
                    child: Container(
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 30,
                            offset: Offset(0, 4),
                            spreadRadius: -5,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomSvgWidget(svg: AppAssets.click),
                            4.verticalSpace,
                            CustomText(
                              text: 'Clicks',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w400,
                              alignment: AlignmentDirectional.center,
                            ),
                            4.verticalSpace,
                            CustomText(
                              text: '6.455',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              alignment: AlignmentDirectional.center,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w700,
                            )
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
              10.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      // width: 144.71,
                      // height: 78.45,
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 30,
                            offset: Offset(0, 4),
                            spreadRadius: -5,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomSvgWidget(svg: AppAssets.reach),
                            4.verticalSpace,
                            CustomText(
                              text: 'Reach',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w400,
                              alignment: AlignmentDirectional.center,
                            ),
                            4.verticalSpace,
                            CustomText(
                              text: '435.380',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              alignment: AlignmentDirectional.center,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w700,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  10.horizontalSpace,
                  Expanded(
                    child: Container(
                      //  width: 144.71,
                      //  height: 78.45,
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x33000000),
                            blurRadius: 30,
                            offset: Offset(0, 4),
                            spreadRadius: -5,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomSvgWidget(svg: AppAssets.dollar),
                            4.verticalSpace,
                            CustomText(
                              text: 'Cost',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w400,
                              alignment: AlignmentDirectional.center,
                            ),
                            4.verticalSpace,
                            CustomText(
                              text: '\$300.00',
                              textAlign: TextAlign.center,
                              color: Constants.primaryTextColor,
                              alignment: AlignmentDirectional.center,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w700,
                            )
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
              25.verticalSpace,
              SizedBox(
                width: 120.w,
                height: 40.h,
                child: ButtonWidget(
                  text: "Show more",
                  onTap: () {},
                ),
              ),
              10.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }
}

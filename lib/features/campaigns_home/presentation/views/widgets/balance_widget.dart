import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/decoration_container.dart';
import '../../../../../widgets/svg_widget.dart';

class BalanceWidget extends StatelessWidget {
  String balance;
  double percentage;
   BalanceWidget({super.key,required this.balance,required this.percentage});

  @override
  Widget build(BuildContext context) {
    return  Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.sp),
      child: DecoratedContainer(
        height: 250.h,
        width: double.infinity,
        boxShadow: const BoxShadow(
          color: Color(0x33000000),
          blurRadius: 30,
          offset: Offset(0, 4),
          spreadRadius: -6,
        ),
        color: Colors.white,
        radius: 10.sp,
        widget: Center(
          child: Stack(
            children: [
              Center(
                child: CircularPercentIndicator(
                  radius: 90.0,
                  animation: true,
                  animationDuration: 1200,
                  lineWidth: 13.sp,
                  percent: percentage,
                  center: Column(
                    mainAxisAlignment:
                    MainAxisAlignment.center,
                    crossAxisAlignment:
                    CrossAxisAlignment.center,
                    children: [
                      const CustomSvgWidget(
                          svg: AppAssets.wallet1),
                      6.verticalSpace,
                      ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return Constants.defGradient
                              .createShader(bounds);
                        },
                        child: CustomText(
                          text: balance,
                          fontWeight: FontWeight.w700,
                          fontSize: 25.sp,
                          alignment:
                          AlignmentDirectional.center,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  reverse: true,
                  circularStrokeCap:
                  CircularStrokeCap.round,
                  linearGradient: Constants.secGradient,
                  backgroundColor: const Color(0xFFFB533E)
                      .withOpacity(0.1),
                ),
              ),
              Positioned(
                top: 10,
                left: 10,
                child: CustomText(
                  text: "Total Balance",
                  fontWeight: FontWeight.w700,
                  fontSize: 16.sp,
                  alignment:
                  AlignmentDirectional.centerStart,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

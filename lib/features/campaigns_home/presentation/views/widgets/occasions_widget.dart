import 'package:ads_dv/features/campaigns_home/presentation/controllers/campaign_home_cubit.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../widgets/cached__image.dart';

class OccasionsWidget extends StatelessWidget {
  CampaignHomeCubit cubit;
  OccasionsWidget({super.key, required this.cubit});

  @override
  Widget build(BuildContext context) {
    return CarouselSlider(
      items: List.generate(cubit.banners.length, (index) {
        return InkWell(
          onTap: () {},
          child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Opacity(
                opacity: cubit.bannerIndex != index ? 0.5 : 1.0,
                child: CachedImageWidget(
                  assetsImage: cubit.banners[index],
                  fit: BoxFit.fitWidth,
                  width: double.infinity,
                ),
              )),
        );
      }),
      options: CarouselOptions(
        onPageChanged: (index, reason) {
          cubit.setBannerIndex(index);
        },
        scrollPhysics: const BouncingScrollPhysics(),
        height: 130.h,
        enlargeCenterPage: true,
        initialPage: 0,
        viewportFraction: 0.57,
        enlargeStrategy: CenterPageEnlargeStrategy.scale,
        enableInfiniteScroll: true,
        autoPlay: true,
        reverse: false,
        autoPlayInterval: const Duration(seconds: 3),
        autoPlayAnimationDuration: const Duration(seconds: 1),
        autoPlayCurve: Curves.fastOutSlowIn,
        scrollDirection: Axis.horizontal,
      ),
    );
  }
}

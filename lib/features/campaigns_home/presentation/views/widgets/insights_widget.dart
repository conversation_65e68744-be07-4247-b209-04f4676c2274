import 'package:ads_dv/features/campaigns_home/presentation/controllers/campaign_home_cubit.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/svg_widget.dart';

class InsightsWidget extends StatelessWidget {
  CampaignHomeCubit cubit;
   InsightsWidget({super.key,required this.cubit});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.sp),
      child: DecoratedContainer(
        width: double.infinity,
        color: Colors.white,
        radius: 10.sp,
        boxShadow: const BoxShadow(
          color: Color(0x33000000),
          blurRadius: 30,
          offset: Offset(0, 4),
          spreadRadius: -6,
        ),

        widget: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomSvgWidget(
                    svg: AppAssets.refresh,
                    height: 15.h,
                    width: 15.h,
                  ),
                  6.horizontalSpace,
                  CustomText(
                    text: "Last refresh since 3 min",
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: Constants.gray,
                  ),
                ],
              ),
              12.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomText(
                    alignment: AlignmentDirectional.center,
                    text: "Views",
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                  3.horizontalSpace,
                  CustomText(
                    alignment: AlignmentDirectional.center,
                    text: "of your campaign increased",
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ],
              ),
              6.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomText(
                    alignment: AlignmentDirectional.center,
                    text: "by",
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                  3.horizontalSpace,
                  CustomText(
                    alignment: AlignmentDirectional.center,
                    text: "(15 %)",
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Constants.greenTextColor,
                  ),
                  3.horizontalSpace,
                  CustomText(
                    alignment: AlignmentDirectional.center,
                    text: "from yesterday",
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ],
              ),
              15.verticalSpace,
              Stack(
                children: [
                  SizedBox(
                    //width: 270.w,
                    //height: 200.h,
                    child: SfCartesianChart(
                      primaryXAxis: const CategoryAxis(),
                      // primaryYAxis: CategoryAxis(
                      //   isVisible: false,
                      // ),
                      series: CampaignHomeCubit.get(context).getSeries(),
                      tooltipBehavior: TooltipBehavior(enable: true),
                    ),
                  ),
                  Positioned(
                    bottom: 190.sp,
                    left: 100.sp,
                    child: Container(
                      // width: 134,
                      // height: 30,
                      decoration: ShapeDecoration(
                        color: const Color(0xFF2AC927),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(37),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0xFF2AC927),
                            blurRadius: 20,
                            offset: Offset(0, 0),
                            spreadRadius: -2,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              // width: 18,
                              // height: 18,
                              decoration: const ShapeDecoration(
                                color: Color(0xFF74DB72),
                                shape: OvalBorder(),
                              ),
                              child: const Icon(
                                Icons.arrow_upward,
                                color: Colors.white,
                                size: 12,
                              ),
                            ),
                            4.horizontalSpace,
                            CustomText(
                              text: '125.565 views',
                              alignment: AlignmentDirectional.center,
                              textAlign: TextAlign.center,
                              color: Colors.white,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w700,
                            )
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
              10.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }
}

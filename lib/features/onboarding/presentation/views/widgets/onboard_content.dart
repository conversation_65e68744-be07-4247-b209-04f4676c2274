import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/res/lang/lang_cubit.dart';
import '../../../../../widgets/shader_mask.dart';

class OnBoardContent extends StatelessWidget {
  OnBoardContent({
    super.key,
    this.gif,
    this.image,
    required this.title,
    required this.description,
  });

  String? image;
  String? gif;
  String title;
  String description;

  @override
  Widget build(BuildContext context) {
    // print('i`m in 4 $image $gif');
    return Stack(
      children: [
        Positioned(
          top: 0,
          right: 0,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned(
                  // top: 0,
                  // right: 0,
                  child: Image.asset(
                "assets/images/onBoardingAppBar.png",
                width: MediaQuery.of(context).size.width,
                fit: BoxFit.cover,
              )),
              Positioned(
                  top: 60.0,
                  // right: 0,
                  left: 15,
                  child: Image.asset(
                    "assets/images/onBoardingLogo.png",
                    // width: 50.w,
                    fit: BoxFit.cover,
                  )),
              if (gif == null) ...[
                Positioned(
                    top: 150.0,
                    // right: 0,
                    // left: 0,
                    child: Image.asset(
                      image!,
                      // width: 50.w,
                      fit: BoxFit.cover,
                    )),
                if (image == "assets/images/newPerson1.png")
                  Positioned(
                    top: 60.0,
                    // right: 0,
                    right: 15,
                    child: Row(
                      children: [
                        Text(
                          'English'.tr,
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400,
                              color: Colors.white),
                        ),
                        IconButton(
                            onPressed: () {
                              if (instance<HiveHelper>().getLang() == 'ar') {
                                LangCubit.get(context)
                                    .changeLang('en', context: context);
                              } else {
                                LangCubit.get(context)
                                    .changeLang('ar', context: context);
                              }
                            },
                            icon: const Icon(
                              Icons.refresh,
                              color: Colors.white,
                            ))
                      ],
                    ),
                  )
              ],
              if (gif != null)
                Positioned(
                    top: 30.0,
                    // right: 0,
                    // left: 0,
                    child: Lottie.asset(
                      gif!,
                      // width: 50.w,
                      fit: BoxFit.cover,
                    )),
            ],
          ),
        ),
        // Positioned(
        //   top: 20.h,
        //   left: 0,
        //   right: 0,
        //   child: CachedImageWidget(assetsImage: image,height:400.h,width:150.h),
        // ),
        Positioned(
          top: 550.h,
          right: 0,
          left: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ShaderMaskWidget(
                gradient: Constants.secGradient,
                widget: CustomText(
                  text: title.tr,
                  color: Colors.white,
                  fontSize: 30.sp,
                  textAlign: TextAlign.center,
                  maxLines: 3,
                  alignment: AlignmentDirectional.center,
                  fontWeight: FontWeight.bold,
                ),
              ),
              30.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.center,
                text: description.tr,
                textAlign: TextAlign.center,
                maxLines: 3,
                color: Constants.primaryTextColor,
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

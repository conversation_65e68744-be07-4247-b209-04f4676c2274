import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';

class DotIndicator extends StatelessWidget {
  const DotIndicator({
    this.isActive = false,
    super.key,
  });

  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 6,
      width: isActive ? 24 : 8,
      decoration: isActive
          ? const BoxDecoration(
              gradient: Constants.secGradient,
              borderRadius: BorderRadius.all(
                Radius.circular(12),
              ),
            )
          : const BoxDecoration(
              color: Constants.gray,
              borderRadius: BorderRadius.all(
                Radius.circular(12),
              ),
            ),
    );
  }
}

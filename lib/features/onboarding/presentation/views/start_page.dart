import 'dart:async';

import 'package:ads_dv/features/auth/presentation/login/controllers/login/login_cubit.dart';
import 'package:ads_dv/features/auth/presentation/login/views/login_screen.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/widgets/custom_container.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/shader_mask.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../../../utils/res/constants.dart';
import '../../../../widgets/custom_text.dart';

class StartPage extends StatefulWidget {
  const StartPage({super.key});

  @override
  State<StartPage> createState() => _StartPageState();
}

class _StartPageState extends State<StartPage> {
  int _counter = 0; // Initial counter value
  late Timer _timer; // Timer to increment the counter

  // Target value for the counter
  final int _targetValue = 10000;

  void _startCounterAnimation() {
    _timer = Timer.periodic(const Duration(milliseconds: 1), (timer) {
      if (_counter < _targetValue) {
        setState(() {
          _counter += 10; // Increment by 10 each time (adjust to control speed)
        });
      } else {
        _timer
            .cancel(); // Stop the timer when the counter reaches the target value
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _startCounterAnimation();
  }

  @override
  void dispose() {
    _timer.cancel(); // Ensure the timer is canceled when the widget is disposed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: Scaffold(
        body: Container(
          color: Colors.white,
          height: SizeConfig.screenHeight(context),
          width: SizeConfig.screenWidth(context),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Image.asset(
              //   "assets/images/counterCircle.png",
              //   // height: 200.h,
              //   // width: 500.w,
              //   fit: BoxFit.cover,
              // ),
              Positioned(
                  top: 160.0,
                  // right: 0,
                  left: 30.0,
                  child: Container(
                    // width: 10.0.w,
                    padding: const EdgeInsets.all(5.0),
                    decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: Constants.secGradient),
                    child: Container(
                      alignment: Alignment.center,
                      height: 80.0.h,
                      width: 80.0.w,
                      // padding: const EdgeInsets.all(20.0),
                      decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: Constants.defGradient),
                      child: Text(
                        _counter == _targetValue
                            ? "${(_counter / 1000).toStringAsFixed(0)}k"
                            : "$_counter",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 25.0.sp,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  )),
              Positioned(
                  top: 5.0,
                  right: -150,
                  // left: 40,
                  child: Lottie.asset(
                    "assets/lottie/chart.json",
                    height: 650.h,
                    width: 673.w,
                    fit: BoxFit.cover,
                  )),

              // Positioned(
              //   top: 30,
              //   left: 30,
              //   right: 30,
              //   child: CachedImageWidget(
              //     assetsImage: AppAssets.person4,
              //     height: 450.h,
              //   ),
              // ),
              Positioned(
                top: 550.h,
                right: 0,
                left: 0,
                child: Column(
                  children: [
                    ShaderMaskWidget(
                      gradient: Constants.secGradient,
                      widget: CustomText(
                        text: "Get Started".tr,
                        color: Colors.white,
                        fontSize: 30.sp,
                        textAlign: TextAlign.center,
                        maxLines: 3,
                        alignment: AlignmentDirectional.center,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    30.verticalSpace,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40.0),
                      child: CustomText(
                        alignment: AlignmentDirectional.center,
                        text:
                            "With just one click, you’re a step closer to your customers create your ad account now"
                                .tr,
                        textAlign: TextAlign.center,
                        maxLines: 3,
                        color: Constants.primaryTextColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    90.verticalSpace,
                    BlocBuilder<LoginCubit, LoginState>(
                      builder: (context, state) {
                        if (state is StoreFCMTokenLoading) {
                          return const LoadingWidget(
                            isCircle: true,
                          );
                        } else {
                          return InkWell(
                            onTap: () {
                              LoginCubit.get(context)
                                  .storeFcmToken(
                                      token: instance<HiveHelper>()
                                          .getFcmToken()
                                          .toString(),
                                      context: context)
                                  .then((value) {
                                Navigator.of(context).pushAndRemoveUntil(
                                  PageRouteBuilder(
                                    transitionDuration:
                                        const Duration(seconds: 1),
                                    pageBuilder: (_, __, ___) =>
                                        const LoginScreen(),
                                    transitionsBuilder:
                                        (_, animation, __, child) {
                                      return FadeTransition(
                                        opacity: animation,
                                        child: child,
                                      );
                                    },
                                  ),
                                  (route) => false,
                                );
                              });
                            },
                            child: CustomContainer(
                              width: 156.w,
                              height: 47.h,
                              text: "Start".tr,
                              textColor: Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 16.sp,
                              gradient: Constants.secGradient,
                              radius: 53.sp,
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
              // Positioned(
              //   top: 0,
              //   right: 0,
              //   child: Stack(
              //     children: [
              //       Positioned(
              //           // top: 0,
              //           // right: 0,
              //           child: Image.asset(
              //         "assets/images/onBoardingAppBar.png",
              //         width: MediaQuery.of(context).size.width,
              //         fit: BoxFit.cover,
              //       )),
              //       Positioned(child: Lottie.asset("assets/lottie/chart.json")),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}

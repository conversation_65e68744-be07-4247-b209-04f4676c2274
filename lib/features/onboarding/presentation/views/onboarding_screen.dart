import 'package:ads_dv/features/onboarding/presentation/controllers/on_boarding_cubit.dart';
import 'package:ads_dv/features/onboarding/presentation/views/start_page.dart';
import 'package:ads_dv/features/onboarding/presentation/views/widgets/dot_indicator.dart';
import 'package:ads_dv/features/onboarding/presentation/views/widgets/onboard_content.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../utils/res/app_assets.dart';

class OnBoardingScreen extends StatelessWidget {
  const OnBoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnBoardingCubit, OnBoardingState>(
      builder: (context, state) {
        return Scaffold(
          body: Container(
            // padding: const EdgeInsets.symmetric(horizontal: 16),
            // Background gradient
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(OnBoardingCubit.get(context).pageIndex == 0
                    ? AppAssets.background1
                    : OnBoardingCubit.get(context).pageIndex == 1
                        ? AppAssets.background2
                        : AppAssets.background3),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              children: [
                // Carousel area
                Expanded(
                  child: PageView.builder(
                    onPageChanged: (index) {
                      OnBoardingCubit.get(context).updatePageIndex(index);
                    },
                    itemCount: OnBoardingCubit.get(context).demoData.length,
                    controller: OnBoardingCubit.get(context).pageController,
                    itemBuilder: (context, index) => OnBoardContent(
                      title: OnBoardingCubit.get(context).demoData[index].title,
                      description: OnBoardingCubit.get(context)
                          .demoData[index]
                          .description,
                      image: index == 3
                          ? null
                          : OnBoardingCubit.get(context).demoData[index].image,
                      gif: index != 3
                          ? null
                          : OnBoardingCubit.get(context).demoData[index].gif,
                    ),
                  ),
                ),
                // Indicator area
                Padding(
                  padding:
                      EdgeInsets.only(bottom: 20.h, right: 15.sp, left: 15.sp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          OnBoardingCubit.get(context).dispose();
                          Navigator.of(context).pushReplacement(
                              SlidePageRoute(page: const StartPage()));
                          instance<HiveHelper>().setIsFirst(false);
                        },
                        child: CustomText(
                          text: 'Skip'.tr,
                          color: Constants.primaryTextColor,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ...List.generate(
                            OnBoardingCubit.get(context).demoData.length,
                            (index) => Padding(
                              padding: const EdgeInsets.only(right: 4),
                              child: DotIndicator(
                                isActive: index ==
                                    OnBoardingCubit.get(context).pageIndex,
                              ),
                            ),
                          ),
                        ],
                      ),
                      InkWell(
                        onTap: () {
                          if (OnBoardingCubit.get(context).pageIndex < 3) {
                            OnBoardingCubit.get(context).pageIndex++;
                          } else {
                            OnBoardingCubit.get(context).dispose();
                            Navigator.of(context).pushReplacement(
                                SlidePageRoute(page: const StartPage()));
                            instance<HiveHelper>().setIsFirst(false);
                          }

                          OnBoardingCubit.get(context)
                              .pageController
                              .animateToPage(
                                OnBoardingCubit.get(context).pageIndex,
                                duration: const Duration(milliseconds: 100),
                                curve: Curves.easeIn,
                              );
                        },
                        child: Container(
                          height: 41.h,
                          width: 41.h,
                          decoration: const ShapeDecoration(
                            gradient: Constants.defGradient,
                            shape: OvalBorder(),
                          ),
                          child: const Icon(
                            Icons.keyboard_arrow_right,
                            color: Colors.white,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                // Privacy policy area
              ],
            ),
          ),
        );
      },
    );
  }
}

class SlidePageRoute extends PageRouteBuilder {
  final Widget page;

  SlidePageRoute({required this.page})
      : super(
          pageBuilder: (_, __, ___) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var begin = const Offset(1.0, 0.0);
            var end = Offset.zero;
            var curve = Curves.ease;

            var tween =
                Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

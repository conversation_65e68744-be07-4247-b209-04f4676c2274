import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../data/models/onboard.dart';

part 'on_boarding_state.dart';

class OnBoardingCubit extends Cubit<OnBoardingState> {
  OnBoardingCubit() : super(OnBoardingInitial());

  static OnBoardingCubit get(context) => BlocProvider.of(context);

  // Variables
  late PageController pageController;
  int pageIndex = 0;
  Timer? timer;

  // OnBoarding content list
  final List<OnBoard> demoData = [
    OnBoard(
      image: "assets/images/newPerson1.png",
      title: "Register and Choose Your Platforms".tr,
      description:
          "Register quickly and select the platforms you want to advertise on—Meta, TikTok, Snapchat, and more!"
              .tr,
    ),
    OnBoard(
        image: "assets/images/newPerson2.png",
        title: "Set Your Campaign Goal".tr,
        description:
            "Decide what you want to achieve, more clicks, engagement, or sales? It's all up to you! Choose your goal, and Leave the rest to us!"
                .tr),
    OnBoard(
      image: "assets/images/newPerson3.png",
      title: "Launch in a Minute!".tr,
      description:
          "Simply set your budget, customize a few details, and click “Start.” Your campaign will be live and ready to make an impact in no time!"
              .tr,
    ),
    OnBoard(
      // image: "assets/images/newPerson3.png",
      gif: "assets/lottie/gift.json",
      title: "Your Campaign is Live! 🎉".tr,
      description:
          "Sit back and track your results in real-time with our analytics dashboard."
              .tr,
    ),
  ];

  void updatePageIndex(int index) {
    pageIndex = index;
    emit(UpdateIndexState());
  }

  void initial() {
    pageController = PageController(initialPage: 0);
    // Automatic scroll behaviour
    timer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      if (pageIndex < 3) {
        pageIndex++;
      } else {
        pageIndex = 0;
      }

      pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeIn,
      );
    });
  }

  void dispose() {
    pageController.dispose();
    timer?.cancel();
  }
}

import 'package:ads_dv/features/sidebar/ad_accounts/data/models/default_account.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/meta_page.dart';
import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../models/ad_account.dart';
import '../models/check_default_accounts_response.dart';
import '../models/get_default_accounts_response.dart';

class MetaDataSource {
  Future<AdAccountResponse> getAdAccounts() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAdAccounts,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return AdAccountResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<GetDefaultAccountsResponse> getDefaultAccounts() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getDefaultAccounts,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return GetDefaultAccountsResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> checkAccessToken({String? accessToken}) async {
    try {
      print('checkDefaultAccountsNewszax $accessToken');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.checkAccessToken,
        params: {
          "access_token": accessToken,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return response['check'];
    } catch (error) {
      rethrow;
    }
  }

  Future<CheckDefaultAccountResponse> checkDefaultAccounts() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.checkDefaultAccounts,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      print('checkDefaultAccountsNewszax $response');
      return CheckDefaultAccountResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<AdAccountResponse> loadMoreAdAccounts({String? url}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.pagination,
        params: {
          if (url != null) "url": url,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return AdAccountResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<MetaPageResponse> getFbPages({required String adAccountId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.addAccountsPages,
        params: {"ad_account_id": adAccountId},
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return MetaPageResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<MetaPageResponse> loadMoreFbPages({String? url}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.pagination,
        params: {
          if (url != null) "url": url,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return MetaPageResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<DefaultAccount> storeAdAccount(
      {String? accountId,
      int? pageId,
      String? pageAccessToken,
      String? accountName,
        required String currency,
      String? pageUserName,
      required bool isCreate}) async {
    try {
      print('whatIsCreate $isCreate');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        isCreate ? EndPoints.storeAccount : EndPoints.updateAccount,
        params: isCreate
            ? {
                "account_id": accountId,
                "page_id": pageId,
                "page_access_token": pageAccessToken,
                "account_name": accountName,
                "page_user_name": pageUserName,
          "currency": currency,
              }
            : {
                if (accountId != null) "account_id": accountId,
                if (pageId != null) "page_id": pageId,
                if (accountName != null) "account_name": accountName,
                if (pageAccessToken != null)
                  "page_access_token": pageAccessToken,
                if (pageUserName != null) "page_user_name": pageUserName,
                if (currency != null) "currency": currency,
              },
        queryParameters: {
          if (!isCreate) "_method": "put",
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return DefaultAccount.fromJson(response['data']);
    } catch (error) {
      rethrow;
    }
  }

  Future<DefaultAccount> updateAdAccount({
    String? accountId,
    String? pageId,
    String? pageAccessToken,
  }) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.storeAccount,
        params: {
          if (accountId != null) "account_id": accountId,
          if (pageId != null) "page_id": pageId,
          if (pageAccessToken != null) "page_access_token": pageAccessToken,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return DefaultAccount.fromJson(response['data']);
    } catch (error) {
      rethrow;
    }
  }
}

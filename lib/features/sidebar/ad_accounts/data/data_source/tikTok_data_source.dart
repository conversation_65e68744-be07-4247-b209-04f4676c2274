import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../models/tiktok_ad_accounts_response.dart';

class TikTokDataSource {
  Future<TikTokAdAccountsResponse> getAdAccounts() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getTikTokAdAccounts,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return TikTokAdAccountsResponse.fromJson(response);
    } catch (error) {
      print('flayer ${error.hashCode}');
      rethrow;
    }
  }

  Future<String> disconnect() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.tikTokDisconnect,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return response["message"];
    } catch (error) {
      // print('flayer ${error.hashCode}');
      rethrow;
    }
  }

  Future<TikTokAdAccountsResponse> getDefaultTiktokAccounts() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getDefaultTiktokAccounts,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return TikTokAdAccountsResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<TikTokAdAccountModel> storeTikTokAdAccount({
    String? accountId,
    String? accountName,
    required String currency,
    // int? pageId,
    // String? pageAccessToken,
    // String? accountName,
    // String? pageUserName,
    // required bool isCreate
  }) async {
    try {
      // print('whatIsCreate ${isCreate}');
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.storeTikTokAdAccount,
        params: {
          "account_id": accountId,
          "account_name": accountName,
          "currency": currency,
        },
        // queryParameters: {
        //   if (!isCreate) "_method": "put",
        // },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return TikTokAdAccountModel.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }
}

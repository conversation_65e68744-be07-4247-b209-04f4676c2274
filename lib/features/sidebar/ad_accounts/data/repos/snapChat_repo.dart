import 'package:ads_dv/features/sidebar/ad_accounts/data/data_source/snapChat_data_source.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../models/get_snapChat_default_accounts_response.dart';
import '../models/snapChat_ad_accounts_response.dart';
import '../models/store_ad_response.dart';

class SnapChatRepo {
  NetworkInfo networkInfo;
  SnapChatDataSource snapChatDataSource;

  SnapChatRepo({required this.networkInfo, required this.snapChatDataSource});

  Future<Either<Failure, SnapChatAdAccountsResponse>> getAdAccounts() {
    return FailureHelper.instance(
      method: () async {
        print('slayer');
        return await snapChatDataSource.getAdAccounts();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> disconnect() {
    return FailureHelper.instance(
      method: () async {
        print('slayer');
        return await snapChatDataSource.disconnect();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, SnapChatStoreAdAccountsResponse>>
      storeSnapChatAdAccount({
    required String accountId,
    required String accountName,
    required String currency,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await snapChatDataSource.storeSnapChatAdAccount(
            accountId: accountId, accountName: accountName, currency: currency);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, SnapChatDefaultAccountsResponse>>
      getDefaultSnapChatAccounts() {
    return FailureHelper.instance(
      method: () async {
        return await snapChatDataSource.getDefaultSnapChatAccounts();
      },
      networkInfo: networkInfo,
    );
  }

// Future<Either<Failure, TikTokAdAccountModel>> storeTikTokAdAccount({
//   required String accountId,
//   required String accountName,
// }) {
//   return FailureHelper.instance(
//     method: () async {
//       return await tikTokDataSource.storeTikTokAdAccount(
//           accountId: accountId, accountName: accountName);
//     },
//     networkInfo: networkInfo,
//   );
// }
}

import 'package:ads_dv/features/sidebar/ad_accounts/data/models/default_account.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/meta_page.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../data_source/meta_data_source.dart';
import '../models/ad_account.dart';
import '../models/check_default_accounts_response.dart';
import '../models/get_default_accounts_response.dart';

class MetaRepo {
  NetworkInfo networkInfo;
  MetaDataSource metaDataSource;
  MetaRepo({required this.networkInfo, required this.metaDataSource});

  Future<Either<Failure, AdAccountResponse>> getAdAccounts() {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.getAdAccounts();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, GetDefaultAccountsResponse>> getDefaultAccounts() {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.getDefaultAccounts();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, CheckDefaultAccountResponse>> checkDefaultAccounts() {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.checkDefaultAccounts();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> checkAccessToken({String? accessToken}) {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.checkAccessToken(accessToken: accessToken);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, AdAccountResponse>> loadMoreAdAccounts({String? url}) {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.loadMoreAdAccounts(url: url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, MetaPageResponse>> getFbPages(
      {required String adAccountId}) {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.getFbPages(adAccountId: adAccountId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, MetaPageResponse>> loadMoreFbPages(
      { String? url}) {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.loadMoreFbPages(url: url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, DefaultAccount>> storeAdAccount({
    required String accountId,
    required int pageId,
    required String pageAccessToken,
    required String accountName,
    required String currency,
    required  String pageUserName,

    required bool isCreate,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.storeAdAccount(
            accountId: accountId,
            pageId: pageId,
            pageUserName: pageUserName,
            pageAccessToken: pageAccessToken,
            accountName: accountName,
            currency: currency,
            isCreate: isCreate);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, DefaultAccount>> updateAdAccount({
    required String accountId,
    required String pageId,
    required String pageAccessToken,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await metaDataSource.updateAdAccount(
            accountId: accountId,
            pageId: pageId,
            pageAccessToken: pageAccessToken);
      },
      networkInfo: networkInfo,
    );
  }
}

import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../data_source/tikTok_data_source.dart';
import '../models/tiktok_ad_accounts_response.dart';

class TikTokRepo {
  NetworkInfo networkInfo;
  TikTokDataSource tikTokDataSource;

  TikTokRepo({required this.networkInfo, required this.tikTokDataSource});

  Future<Either<Failure, TikTokAdAccountsResponse>> getAdAccounts() {
    return FailureHelper.instance(
      method: () async {
        print('slayer');
        return await tikTokDataSource.getAdAccounts();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, String>> disconnect() {
    return FailureHelper.instance(
      method: () async {
        print('slayer');
        return await tikTokDataSource.disconnect();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, TikTokAdAccountsResponse>> getDefaultTiktokAccounts() {
    return FailureHelper.instance(
      method: () async {
        return await tikTokDataSource.getDefaultTiktokAccounts();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, TikTokAdAccountModel>> storeTikTokAdAccount({
    required String accountId,
    required String accountName,
    required String currency,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await tikTokDataSource.storeTikTokAdAccount(
            accountId: accountId, accountName: accountName,currency: currency);
      },
      networkInfo: networkInfo,
    );
  }
}

class Pages {
  String? id;
  String? name;
  String? accessToken;
  Picture? picture;
  Cover? cover;

  Pages({this.id, this.name, this.accessToken, this.picture, this.cover});

  Pages.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    accessToken = json['access_token'];
    picture =
    json['picture'] != null ? Picture.fromJson(json['picture']) : null;
    cover = json['cover'] != null ? Cover.fromJson(json['cover']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['access_token'] = accessToken;
    if (picture != null) {
      data['picture'] = picture!.toJson();
    }
    if (cover != null) {
      data['cover'] = cover!.toJson();
    }
    return data;
  }
}

class Picture {
  Data? data;

  Picture({this.data});

  Picture.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? url;

  Data({this.url});

  Data.fromJson(Map<String, dynamic> json) {
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['url'] = url;
    return data;
  }
}

class Cover {
  String? source;
  String? id;

  Cover({this.source, this.id});

  Cover.fromJson(Map<String, dynamic> json) {
    source = json['source'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['source'] = source;
    data['id'] = id;
    return data;
  }
}
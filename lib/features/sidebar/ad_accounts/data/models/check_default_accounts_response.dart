class CheckDefaultAccountResponse {
  bool? success;
  String? message;
  String? tiktokToken;
  String? snapChatToken;
  bool? subscribed;
  bool? result;
  bool? tiktok;
  bool? snapChat;

  CheckDefaultAccountResponse({
    this.success,
    this.message,
    this.tiktokToken,
    this.snapChatToken,
    this.subscribed,
    this.result,
    this.tiktok,
    this.snapChat,
  });

  CheckDefaultAccountResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    subscribed = json['subscribed'];
    result = json['result'];
    tiktok = json['tiktok'];
    snapChat = json['snapchat'];
    tiktokToken = json['tiktok_token'];
    snapChatToken = json['snapchat_token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    data['subscribed'] = subscribed;
    data['result'] = result;
    data['tiktok'] = tiktok;
    data['snapchat_token'] = snapChatToken;
    data['snapchat'] = snapChat;
    return data;
  }
}

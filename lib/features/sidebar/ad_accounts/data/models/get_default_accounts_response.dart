import 'dart:convert';

import 'default_account.dart';

DefaultAccount adAccountFromJson(String str) =>
    DefaultAccount.fromJson(json.decode(str));

String adAccountToJson(DefaultAccount data) => json.encode(data.toJson());

class GetDefaultAccountsResponse {
  int? status;
  List<DefaultAccount>? data;
  String? message;

  GetDefaultAccountsResponse({this.status, this.data, this.message});

  GetDefaultAccountsResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != []) {
      data = <DefaultAccount>[];
      json['data'].forEach((v) {
        data!.add(DefaultAccount.fromJson(v));
      });
    }
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['message'] = message;
    return data;
  }
}

// class Data {
//   int? id;
//   int? userId;
//   String? accountId;
//   Null? accountName;
//   String? pageId;
//   String? pageAccessToken;
//   String? pageUserName;
//   Null? instaUserName;
//   Null? instaAccId;
//   Null? whatsNumber;
//   String? createdAt;
//   String? updatedAt;
//   int? status;
//
//   Data(
//       {this.id,
//         this.userId,
//         this.accountId,
//         this.accountName,
//         this.pageId,
//         this.pageAccessToken,
//         this.pageUserName,
//         this.instaUserName,
//         this.instaAccId,
//         this.whatsNumber,
//         this.createdAt,
//         this.updatedAt,
//         this.status});
//
//   Data.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     userId = json['user_id'];
//     accountId = json['account_id'];
//     accountName = json['account_name'];
//     pageId = json['page_id'];
//     pageAccessToken = json['page_access_token'];
//     pageUserName = json['page_user_name'];
//     instaUserName = json['insta_user_name'];
//     instaAccId = json['insta_acc_id'];
//     whatsNumber = json['whats_number'];
//     createdAt = json['created_at'];
//     updatedAt = json['updated_at'];
//     status = json['status'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['user_id'] = this.userId;
//     data['account_id'] = this.accountId;
//     data['account_name'] = this.accountName;
//     data['page_id'] = this.pageId;
//     data['page_access_token'] = this.pageAccessToken;
//     data['page_user_name'] = this.pageUserName;
//     data['insta_user_name'] = this.instaUserName;
//     data['insta_acc_id'] = this.instaAccId;
//     data['whats_number'] = this.whatsNumber;
//     data['created_at'] = this.createdAt;
//     data['updated_at'] = this.updatedAt;
//     data['status'] = this.status;
//     return data;
//   }
// }

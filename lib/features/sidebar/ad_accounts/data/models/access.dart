import 'package:ads_dv/features/sidebar/ad_accounts/data/models/ad_account.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/page.dart';

class AccessedUser {
  int? id;
  int? userId;
  String? email;
  String? name;

  int? managedUser;
  String? role;
  String? createdAt;
  String? updatedAt;
  List<AdAccount>? accounts;
  List<Pages>? pages;
  AccessedUser(
      {this.id,
        this.userId,
        this.email,
        this.managedUser,
        this.role,
        this.name,

        this.createdAt,
        this.updatedAt,
        this.accounts,
        this.pages});

  AccessedUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    email = json['email'];
    name = json['name'];

    managedUser = json['managed_user'];
    role = json['role'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    if (json['accounts'] != null) {
      accounts = <AdAccount>[];
      json['accounts'].forEach((v) {
        accounts!.add(AdAccount.fromJson(v));
      });
    }
    if (json['pages'] != null) {
      pages = <Pages>[];
      json['pages'].forEach((v) {
        pages!.add(Pages.fromJson(v));
      });
    }

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['email'] = email;
    data['name'] = name;

    data['managed_user'] = managedUser;
    data['role'] = role;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;


    if (accounts != null) {
      data['accounts'] = accounts!.map((v) => v.toJson()).toList();
    }
    if (pages != null) {
      data['pages'] = pages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
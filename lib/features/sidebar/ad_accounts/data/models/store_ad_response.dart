class SnapChatStoreAdAccountsResponse {
  bool? success;
  String? message;
  StoreAdAccountModel? result;

  SnapChatStoreAdAccountsResponse({this.success, this.message, this.result});

  SnapChatStoreAdAccountsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    result = json['result'] != null
        ? StoreAdAccountModel.fromJson(json['result'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.toJson();
    }
    return data;
  }
}

class StoreAdAccountModel {
  int? userId;
  String? accountId;
  String? accountName;
  String? type;
  String? pageId;
  String? pageAccessToken;
  String? updatedAt;
  String? createdAt;
  int? id;

  StoreAdAccountModel(
      {this.userId,
      this.accountId,
      this.accountName,
      this.type,
      this.pageId,
      this.pageAccessToken,
      this.updatedAt,
      this.createdAt,
      this.id});

  StoreAdAccountModel.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    accountId = json['account_id'];
    accountName = json['account_name'];
    type = json['type'];
    pageId = json['page_id'];
    pageAccessToken = json['page_access_token'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['account_id'] = accountId;
    data['account_name'] = accountName;
    data['type'] = type;
    data['page_id'] = pageId;
    data['page_access_token'] = pageAccessToken;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}

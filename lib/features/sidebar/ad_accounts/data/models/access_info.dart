import 'package:ads_dv/features/sidebar/ad_accounts/data/models/access.dart';

class AccessInfo {
  List<AccessedUser>? canAccess;
  List<AccessedUser>? accessedBy;

  AccessInfo({this.canAccess, this.accessedBy});

  AccessInfo.fromJson(Map<String, dynamic> json) {
    if (json['can_access'] != null) {
      canAccess = <AccessedUser>[];
      json['can_access'].forEach((v) {
        canAccess!.add(AccessedUser.fromJson(v));
      });
    }
    if (json['accessed_by'] != null) {
      accessedBy = <AccessedUser>[];
      json['accessed_by'].forEach((v) {
        accessedBy!.add(AccessedUser.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (canAccess != null) {
      data['can_access'] = canAccess!.map((v) => v.toJson()).toList();
    }
    if (accessedBy != null) {
      data['accessed_by'] = accessedBy!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

import 'dart:convert';

AdAccount adAccountFromJson(String str) => AdAccount.fromJson(json.decode(str));

String adAccountToJson(AdAccount data) => json.encode(data.toJson());

class AdAccountResponse {

  List<AdAccount>? result;
  String? next;

  AdAccountResponse({ this.result, this.next});

  AdAccountResponse.fromJson(Map<String, dynamic> json) {

    if (json['result'] != null) {
      result = <AdAccount>[];
      json['result'].forEach((v) {
        result!.add(AdAccount.fromJson(v));
      });
    }
    next = json['next'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    return data;
  }
}
class AdAccount {
  String? name;
  String? id;
  String? accountId;
  String? balance;
  String? amountSpent;
  String? currency;
  int? accountStatus;
  int? disableReason;

  AdAccount(
      {this.name,
        this.id,
        this.accountId,
        this.balance,
        this.amountSpent,
        this.accountStatus,
        this.disableReason,
        this.currency});

  AdAccount.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    id = json['id'];
    accountId = json['account_id'];
    balance = json['balance'];
    amountSpent = json['amount_spent'];
    currency = json['currency'];
    accountStatus = json['account_status'];
    disableReason = json['disable_reason'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['id'] = id;
    data['account_id'] = accountId;
    data['balance'] = balance;
    data['amount_spent'] = amountSpent;
    data['currency'] = currency;
    data['account_status'] = accountStatus;
    data['disable_reason'] = disableReason;
    return data;
  }
}


enum AccountStatus {
  ACTIVE(1, "Active"),
  DISABLED(2, "Disabled"),
  UNSETTLED(3, "Unsettled"),
  PENDING_RISK_REVIEW(7, "Pending risk review"),
  PENDING_SETTLEMENT(8, "Pending settlement"),
  IN_GRACE_PERIOD(9, "In grace period"),
  PENDING_CLOSURE(100, "Pending closure"),
  CLOSED(101, "Closed"),
  ANY_ACTIVE(201, "Any active"),
  ANY_CLOSED(202, "Any closed");

  final int value;
  final String description;

  const AccountStatus(this.value, this.description);

  static AccountStatus? fromValue(int? value) {
    return AccountStatus.values.firstWhere(
          (status) => status.value == value,
    );
  }
}

enum DisableReason {
  NONE(0, "None"),
  ADS_INTEGRITY_POLICY(1, "Ads integrity policy"),
  ADS_IP_REVIEW(2, "Ads IP review"),
  RISK_PAYMENT(3, "Risk payment"),
  GRAY_ACCOUNT_SHUT_DOWN(4, "Gray account shut down"),
  ADS_AFC_REVIEW(5, "Ads afc review"),
  BUSINESS_INTEGRITY_RAR(6, "Business integrity rar"),
  PERMANENT_CLOSE(7, "Permanent close"),
  UNUSED_RESELLER_ACCOUNT(8, "Unused reseller account"),
  UNUSED_ACCOUNT(9, "Unused account"),
  UMBRELLA_AD_ACCOUNT(10, "Umbra ad account"),
  BUSINESS_MANAGER_INTEGRITY_POLICY(11, "Business manager integrity policy"),
  MISREPRESENTED_AD_ACCOUNT(12, "Misrepresented ad account"),
  AOAB_DESHARE_LEGAL_ENTITY(13, "Aoab deshare legal entity"),
  CTX_THREAD_REVIEW(14, "Ctx thread review"),
  COMPROMISED_AD_ACCOUNT(15, "Compromised ad account");

  final int value;
  final String description;

  const DisableReason(this.value, this.description);

  static DisableReason? fromValue(int? value) {
    return DisableReason.values.firstWhere(
          (reason) => reason.value == value,
    );
  }
}
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/ad_account.dart';

class Accounts {
  List<AdAccount>? userAccount;

  Accounts({ this.userAccount});

  Accounts.fromJson(Map<String, dynamic> json) {

    if (json['user_account'] != null) {
      userAccount = <AdAccount>[];
      json['user_account'].forEach((v) {
        userAccount!.add(AdAccount.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (userAccount != null) {
      data['user_account'] = userAccount!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}






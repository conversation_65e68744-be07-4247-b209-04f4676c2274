class SnapChatAdAccountsResponse {
  bool? success;
  String? message;
  List<SnapChatAdAccountsResult>? result;

  SnapChatAdAccountsResponse({this.success, this.message, this.result});

  SnapChatAdAccountsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <SnapChatAdAccountsResult>[];
      json['result'].forEach((v) {
        result!.add(SnapChatAdAccountsResult.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SnapChatAdAccountsResult {
  String? subRequestStatus;
  SnapChatAdAccount? adaccount;

  SnapChatAdAccountsResult({this.subRequestStatus, this.adaccount});

  SnapChatAdAccountsResult.fromJson(Map<String, dynamic> json) {
    subRequestStatus = json['sub_request_status'];
    adaccount = json['adaccount'] != null
        ? SnapChatAdAccount.fromJson(json['adaccount'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sub_request_status'] = subRequestStatus;
    if (adaccount != null) {
      data['adaccount'] = adaccount!.toJson();
    }
    return data;
  }
}

class SnapChatAdAccount {
  String? id;
  String? updatedAt;
  String? createdAt;
  String? name;
  String? type;
  String? status;
  String? organizationId;
  String? currency;
  String? timezone;
  String? advertiserOrganizationId;
  String? billingCenterId;
  String? billingType;
  bool? agencyRepresentingClient;
  bool? clientPayingInvoices;
  String? advertiser;
  int? lifetimeSpendCapMicro;

  SnapChatAdAccount(
      {this.id,
      this.updatedAt,
      this.createdAt,
      this.name,
      this.type,
      this.status,
      this.organizationId,
      this.currency,
      this.timezone,
      this.advertiserOrganizationId,
      this.billingCenterId,
      this.billingType,
      this.agencyRepresentingClient,
      this.clientPayingInvoices,
      this.advertiser,
      this.lifetimeSpendCapMicro});

  SnapChatAdAccount.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    name = json['name'];
    type = json['type'];
    status = json['status'];
    organizationId = json['organization_id'];
    currency = json['currency'];
    timezone = json['timezone'];
    advertiserOrganizationId = json['advertiser_organization_id'];
    billingCenterId = json['billing_center_id'];
    billingType = json['billing_type'];
    agencyRepresentingClient = json['agency_representing_client'];
    clientPayingInvoices = json['client_paying_invoices'];
    advertiser = json['advertiser'];
    lifetimeSpendCapMicro = json['lifetime_spend_cap_micro'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['name'] = name;
    data['type'] = type;
    data['status'] = status;
    data['organization_id'] = organizationId;
    data['currency'] = currency;
    data['timezone'] = timezone;
    data['advertiser_organization_id'] = advertiserOrganizationId;
    data['billing_center_id'] = billingCenterId;
    data['billing_type'] = billingType;
    data['agency_representing_client'] = agencyRepresentingClient;
    data['client_paying_invoices'] = clientPayingInvoices;
    data['advertiser'] = advertiser;
    data['lifetime_spend_cap_micro'] = lifetimeSpendCapMicro;
    return data;
  }
}

// class SnapChatAdAccountsResponse {
//   bool? success;
//   String? message;
//   List<SnapChatAdAccountsResult>? result;
//
//   SnapChatAdAccountsResponse({this.success, this.message, this.result});
//
//   SnapChatAdAccountsResponse.fromJson(Map<String, dynamic> json) {
//     success = json['success'];
//     message = json['message'];
//     if (json['result'] != null) {
//       result = <SnapChatAdAccountsResult>[];
//       json['result'].forEach((v) {
//         result!.add(new SnapChatAdAccountsResult.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['success'] = this.success;
//     data['message'] = this.message;
//     if (this.result != null) {
//       data['result'] = this.result!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }
//
// class SnapChatAdAccountsResult {
//   String? subRequestStatus;
//   SnapChatAdAccount? adaccount;
//
//   SnapChatAdAccountsResult({this.subRequestStatus, this.adaccount});
//
//   SnapChatAdAccountsResult.fromJson(Map<String, dynamic> json) {
//     subRequestStatus = json['sub_request_status'];
//     adaccount = json['adaccount'] != null
//         ? new SnapChatAdAccount.fromJson(json['adaccount'])
//         : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['sub_request_status'] = this.subRequestStatus;
//     if (this.adaccount != null) {
//       data['adaccount'] = this.adaccount!.toJson();
//     }
//     return data;
//   }
// }
//
// class SnapChatAdAccount {
//   String? id;
//   String? updatedAt;
//   String? createdAt;
//   String? name;
//   String? type;
//   String? status;
//   String? organizationId;
//   String? currency;
//   String? timezone;
//   String? advertiserOrganizationId;
//   String? billingCenterId;
//   String? billingType;
//   bool? agencyRepresentingClient;
//   bool? clientPayingInvoices;
//   int? advertiser;
//   int? lifetimeSpendCapMicro;
//
//   SnapChatAdAccount(
//       {this.id,
//       this.updatedAt,
//       this.createdAt,
//       this.name,
//       this.type,
//       this.status,
//       this.organizationId,
//       this.currency,
//       this.timezone,
//       this.advertiserOrganizationId,
//       this.billingCenterId,
//       this.billingType,
//       this.agencyRepresentingClient,
//       this.clientPayingInvoices,
//       this.advertiser,
//       this.lifetimeSpendCapMicro});
//
//   SnapChatAdAccount.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     updatedAt = json['updated_at'];
//     createdAt = json['created_at'];
//     name = json['name'];
//     type = json['type'];
//     status = json['status'];
//     organizationId = json['organization_id'];
//     currency = json['currency'];
//     timezone = json['timezone'];
//     advertiserOrganizationId = json['advertiser_organization_id'];
//     billingCenterId = json['billing_center_id'];
//     billingType = json['billing_type'];
//     agencyRepresentingClient = json['agency_representing_client'];
//     clientPayingInvoices = json['client_paying_invoices'];
//     advertiser = json['advertiser'];
//     lifetimeSpendCapMicro = json['lifetime_spend_cap_micro'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['updated_at'] = this.updatedAt;
//     data['created_at'] = this.createdAt;
//     data['name'] = this.name;
//     data['type'] = this.type;
//     data['status'] = this.status;
//     data['organization_id'] = this.organizationId;
//     data['currency'] = this.currency;
//     data['timezone'] = this.timezone;
//     data['advertiser_organization_id'] = this.advertiserOrganizationId;
//     data['billing_center_id'] = this.billingCenterId;
//     data['billing_type'] = this.billingType;
//     data['agency_representing_client'] = this.agencyRepresentingClient;
//     data['client_paying_invoices'] = this.clientPayingInvoices;
//     data['advertiser'] = this.advertiser;
//     data['lifetime_spend_cap_micro'] = this.lifetimeSpendCapMicro;
//     return data;
//   }
// }


import 'dart:convert';

MetaPages pagesFromJson(String str) => MetaPages.fromJson(json.decode(str));

String pagesModelTo<PERSON>son(MetaPages data) => json.encode(data.toJson());

class MetaPageResponse {

  List<MetaPages>? result;
  String? next;

  MetaPageResponse({ this.result, this.next});

  MetaPageResponse.fromJson(Map<String, dynamic> json) {

    if (json['result'] != null) {
      result = <MetaPages>[];
      json['result'].forEach((v) {
        result!.add(MetaPages.fromJson(v));
      });
    }
    next = json['next'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    return data;
  }
}
class MetaPages {
  String? id;
  String? userName;
  String? name;
  String? accessToken;
  String? profilePic;
  String? cover;

  MetaPages({
    this.id,
    this.userName,
    this.name,
    this.accessToken,
    this.profilePic,
    this.cover,
  });
  MetaPages.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    userName = json['username']?.toString();
    name = json['name']?.toString();
    accessToken = json['access_token']?.toString();
    profilePic = json['profile_pic']?.toString();
    cover = json['cover']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = userName;
    data['name'] = name;
    data['access_token'] = accessToken;
    data['profile_pic'] = profilePic;
    data['cover'] = cover;
    return data;
  }
}
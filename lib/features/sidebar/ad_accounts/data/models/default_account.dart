class DefaultAccount {
  String? accountId;
  String? accountName;
  String? pageId;
  String? pageAccessToken;
  String? pagePic;
  String? pageUserName;
  String? instUserName;
  String? instAccId;
  String? instUserId;
  String? whatsNumber;

  DefaultAccount({
    this.accountId,
    this.instUserId,
    this.pageId,
    this.accountName,
    this.pageAccessToken,
    this.pagePic,
    this.pageUserName,
    this.instUserName,
    this.instAccId,
    this.whatsNumber,
  });

  DefaultAccount.fromJson(Map<String, dynamic> json) {
    accountId = json['account_id'].toString();
    pageId = json['page_id'].toString();
    accountName = json['account_name'].toString();
    pageAccessToken = json['page_access_token'].toString();
    pagePic = json['page_pic'].toString();
    pageUserName = json['page_user_name'].toString();
    instUserName = json['insta_user_name'].toString();
    instAccId = json['insta_acc_id'].toString();
    whatsNumber = json['whats_number'].toString();
    instUserId = json['instagram_user_id'].toString();
    // print('getAddAccountErrordfzxcx ${pagePic}');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['account_id'] = accountId;
    data['page_id'] = pageId;
    data['page_access_token'] = pageAccessToken;
    data['account_name'] = accountName;
    data['page_user_name'] = pageUserName;
    data['insta_user_name'] = instUserName;
    data['insta_acc_id'] = instAccId;
    data['whats_number'] = whatsNumber;
    data['instagram_user_id'] = instUserId;

    return data;
  }
}

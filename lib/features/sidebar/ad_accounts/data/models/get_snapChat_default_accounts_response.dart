class SnapChatDefaultAccountsResponse {
  bool? success;
  String? message;
  List<DefaultSnapChatAccountModel>? result;

  SnapChatDefaultAccountsResponse({this.success, this.message, this.result});

  SnapChatDefaultAccountsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['result'] != null) {
      result = <DefaultSnapChatAccountModel>[];
      json['result'].forEach((v) {
        result!.add(DefaultSnapChatAccountModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DefaultSnapChatAccountModel {
  int? id;
  int? userId;
  String? accountId;
  String? type;
  String? accountName;
  String? pageId;
  String? pageAccessToken;
  String? currency;
  int? instagramUserId;
  String? pageUserName;
  String? pagePic;
  String? instaUserName;
  int? instaAccId;
  String? whatsNumber;
  String? createdAt;
  String? updatedAt;
  int? status;

  DefaultSnapChatAccountModel(
      {this.id,
      this.userId,
      this.accountId,
      this.type,
      this.accountName,
      this.pageId,
      this.pageAccessToken,
      this.currency,
      this.instagramUserId,
      this.pageUserName,
      this.pagePic,
      this.instaUserName,
      this.instaAccId,
      this.whatsNumber,
      this.createdAt,
      this.updatedAt,
      this.status});

  DefaultSnapChatAccountModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    accountId = json['account_id'];
    type = json['type'];
    accountName = json['account_name'];
    pageId = json['page_id'];
    pageAccessToken = json['page_access_token'];
    currency = json['currency'];
    instagramUserId = json['instagram_user_id'];
    pageUserName = json['page_user_name'];
    pagePic = json['page_pic'];
    instaUserName = json['insta_user_name'];
    instaAccId = json['insta_acc_id'];
    whatsNumber = json['whats_number'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['account_id'] = accountId;
    data['type'] = type;
    data['account_name'] = accountName;
    data['page_id'] = pageId;
    data['page_access_token'] = pageAccessToken;
    data['instagram_user_id'] = instagramUserId;
    data['page_user_name'] = pageUserName;
    data['page_pic'] = pagePic;
    data['insta_user_name'] = instaUserName;
    data['insta_acc_id'] = instaAccId;
    data['whats_number'] = whatsNumber;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['status'] = status;
    return data;
  }
}

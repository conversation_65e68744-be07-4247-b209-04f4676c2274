part of 'store_tiktok_ad_account_cubit.dart';

@immutable
sealed class StoreTiktokAdAccountState {}

final class StoreTiktokAdAccountInitial extends StoreTiktokAdAccountState {}

final class StoreTiktokAdAccountLoading extends StoreTiktokAdAccountState {}

class StoreTiktokAdAccountError extends StoreTiktokAdAccountState {
  final String message;

  StoreTiktokAdAccountError(this.message);

  @override
  List<Object?> get props => [message];

  StoreTiktokAdAccountError copyWith({
    String? message,
  }) {
    return StoreTiktokAdAccountError(
      message ?? this.message,
    );
  }
}

class StoreTikTokAdAccount extends StoreTiktokAdAccountState {
  final TikTokAdAccountModel? addedAccounts;

  StoreTikTokAdAccount(this.addedAccounts);

  @override
  List<Object?> get props => [addedAccounts];

  StoreTikTokAdAccount copyWith({
    TikTokAdAccountModel? addedAccounts,
  }) {
    return StoreTikTokAdAccount(
      addedAccounts ?? this.addedAccounts,
    );
  }
}

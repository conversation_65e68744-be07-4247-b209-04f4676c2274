import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/tiktok_ad_accounts_response.dart';
import '../../../data/repos/tikTok_repo.dart';

part 'store_tiktok_ad_account_state.dart';

class StoreTiktokAdAccountCubit extends Cubit<StoreTiktokAdAccountState> {
  StoreTiktokAdAccountCubit() : super(StoreTiktokAdAccountInitial());

  static StoreTiktokAdAccountCubit get(context) => BlocProvider.of(context);

  Future storeAdAccount({
    required String accountId,
    required String accountName,
    required String currency,
    required BuildContext context,
  }) async {
    emit(StoreTiktokAdAccountLoading());
    await instance<TikTokRepo>()
        .storeTikTokAdAccount(
      accountId: accountId,
      accountName: accountName,
      currency: currency,
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(StoreTiktokAdAccountError(l.message));
      }, (r) async {
        print("kakfkajklStoreAdAccount  ${r.toJson()}");
        // Navigator.of(context).pop();
        // Navigator.pushNamedAndRemoveUntil(
        //     context, Routes.home, (route) => false);
        // Navigator.pushNamedAndRemoveUntil(
        //     context, Routes.splash, (route) => false);
        emit(StoreTikTokAdAccount(r));
      });
    });
  }
}

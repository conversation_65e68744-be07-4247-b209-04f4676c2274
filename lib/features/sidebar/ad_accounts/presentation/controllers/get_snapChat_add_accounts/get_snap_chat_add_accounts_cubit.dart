import 'package:ads_dv/features/sidebar/ad_accounts/data/repos/snapChat_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/get_snapChat_default_accounts_response.dart';
import '../../../data/models/snapChat_ad_accounts_response.dart';

part 'get_snap_chat_add_accounts_state.dart';

class GetSnapChatAddAccountsCubit extends Cubit<GetSnapChatAddAccountsState> {
  GetSnapChatAddAccountsCubit() : super(GetSnapChatAddAccountsInitial());

  static GetSnapChatAddAccountsCubit get(context) => BlocProvider.of(context);

  List<SnapChatAdAccountsResult> snapChatAdAccounts = [];
  bool isAccountSelected = false;
  SnapChatAdAccountsResult? addedAdAccount;
  List<DefaultSnapChatAccountModel> defaultSnapChatAccounts = [];

  DefaultSnapChatAccountModel? selectedDefaultSnapChatAccount;

  Future<void> getAdAccounts({
    required BuildContext context,
  }) async {
    emit(GetMoreAdAccountsStateLoading());
    instance<SnapChatRepo>().getAdAccounts().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        print('getSnapChatAdAccountsResponseass ${l.message}');
        emit(GetSnapChatAdAccountsStateError(l.message));
      }, (r) async {
        // adAccounts = r.result ?? [];
        // await setSelectedUrl(r.next);
        // await checkDefaultAccounts(context: context);
        snapChatAdAccounts = r.result ?? [];
        print(
            'getSnapChatAdAccountsResponseass ${r.result?.first.adaccount?.toJson()}');
        emit(GetSnapChatAdAccountsStateLoaded(r));
      });
    });
  }

  Future<void> disconnect({
    required BuildContext context,
  }) async {
    emit(GetMoreAdAccountsStateLoading());
    instance<SnapChatRepo>().disconnect().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        print('getSnapChatAdAccountsResponseass ${l.message}');
        emit(GetSnapChatAdAccountsStateError(l.message));
      }, (r) async {
        // adAccounts = r.result ?? [];
        // await setSelectedUrl(r.next);
        // await checkDefaultAccounts(context: context);
        // snapChatAdAccounts = r.result ?? [];
        // print(
        //     'getSnapChatAdAccountsResponseass ${r.result?.first.adaccount?.toJson()}');
        emit(UpdateStatus());
      });
    });
  }

  Future<void> getDefaultAccounts({required BuildContext context}) async {
    try {
      // emit(GetAdAccountsStateLoading());
      await instance<SnapChatRepo>()
          .getDefaultSnapChatAccounts()
          .then((value) => value.fold((l) {}, (r) async {
                defaultSnapChatAccounts = r.result ?? [];
                // print('getAddAccountsxczfjd ${r.data?.first.instUserId}');
                // await changeUserData(index: 0, context: context);
                print('getSnapChatAddAccountErrordfzxcx ${r.result}');

                emit(GetDefaultSnapChatAdAccountsStateLoaded(
                    data: r.result ?? []));
              }));

      // result.fold(
      //   (l) {
      //     FailureHelper.instance.handleFailures(l, context);
      //     emit(GetAdAccountsStateError(l.message));
      //   },
      //   (r) {
      //     defaultAccounts = r.data ?? [];
      //     print('getAddAccountErrordfzxcx ${r.data?.first.pagePic}');
      //
      //     emit(CheckDefaultsAccountsStateLoaded(data: r.data ?? []));
      //   },
      // );
    } catch (e) {
      print('Error while fetching default accounts: $e');
      emit(GetSnapChatAdAccountsStateError(e.toString()));
    }
  }

  setSelectedAdAccount(bool status, SnapChatAdAccountsResult addedAdAcc) {
    isAccountSelected = status;
    // addedAdAccounts?.add(addedAdAcc);
    addedAdAccount = addedAdAcc;
    // emit(GetTikTokAdAccountsAdded(addedAdAccounts));
  }

  setSelectedDefaultAccount(DefaultSnapChatAccountModel sDefault) {
    // addedAdAccounts?.add(addedAdAcc);
    selectedDefaultSnapChatAccount = sDefault;
    // emit(GetTikTokAdAccountsAdded(addedAdAccounts));
  }
}

part of 'get_snap_chat_add_accounts_cubit.dart';

@immutable
sealed class GetSnapChatAddAccountsState {}

final class GetSnapChatAddAccountsInitial extends GetSnapChatAddAccountsState {}

final class UpdateStatus extends GetSnapChatAddAccountsState {}

class GetMoreAdAccountsStateLoading extends GetSnapChatAddAccountsState {}

class GetSnapChatAdAccountsStateLoaded extends GetSnapChatAddAccountsState {
  final SnapChatAdAccountsResponse data;

  GetSnapChatAdAccountsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetSnapChatAdAccountsStateLoaded copyWith({
    SnapChatAdAccountsResponse? data,
  }) {
    return GetSnapChatAdAccountsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetDefaultSnapChatAdAccountsStateLoaded
    extends GetSnapChatAddAccountsState {
  final List<DefaultSnapChatAccountModel>? data;

  GetDefaultSnapChatAdAccountsStateLoaded({required this.data});

  @override
  List<Object?> get props => [data];

  GetDefaultSnapChatAdAccountsStateLoaded copyWith({
    List<DefaultSnapChatAccountModel>? data,
  }) {
    return GetDefaultSnapChatAdAccountsStateLoaded(
      data: data ?? this.data,
    );
  }
}

class GetSnapChatAdAccountsStateError extends GetSnapChatAddAccountsState {
  final String message;

  GetSnapChatAdAccountsStateError(this.message);

  @override
  List<Object?> get props => [message];

  GetSnapChatAdAccountsStateError copyWith({
    String? message,
  }) {
    return GetSnapChatAdAccountsStateError(
      message ?? this.message,
    );
  }
}

part of 'tiktok_accounts_cubit.dart';

@immutable
sealed class TiktokAccountsState {}

final class TiktokAccountsInitial extends TiktokAccountsState {}

final class UpdateStatus extends TiktokAccountsState {}

class GetTikTokAdAccountsStateLoading extends TiktokAccountsState {}

class GetTikTokAdAccountsStateLoaded extends TiktokAccountsState {
  final List<TikTokAdAccountModel>? data;

  GetTikTokAdAccountsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetTikTokAdAccountsStateLoaded copyWith({
    List<TikTokAdAccountModel>? data,
  }) {
    return GetTikTokAdAccountsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetTikTokAdAccountsAdded extends TiktokAccountsState {
  final List<TikTokAdAccountModel>? addedAccounts;

  GetTikTokAdAccountsAdded(this.addedAccounts);

  @override
  List<Object?> get props => [addedAccounts];

  GetTikTokAdAccountsAdded copyWith({
    List<TikTokAdAccountModel>? addedAccounts,
  }) {
    return GetTikTokAdAccountsAdded(
      addedAccounts ?? this.addedAccounts,
    );
  }
}

class GetTikTokAdAccountsStateError extends TiktokAccountsState {
  final String message;

  GetTikTokAdAccountsStateError(this.message);

  @override
  List<Object?> get props => [message];

  GetTikTokAdAccountsStateError copyWith({
    String? message,
  }) {
    return GetTikTokAdAccountsStateError(
      message ?? this.message,
    );
  }
}

part of 'update_ad_account_cubit.dart';

@immutable
abstract class UpdateAdAccountState {
  const UpdateAdAccountState();
  List<Object?> get props => [];
}
final class UpdateAdAccountInitial extends UpdateAdAccountState {}


final class UpdateAdAccountLoading extends UpdateAdAccountState {}


final class UpdateAdAccountSuccess extends UpdateAdAccountState {
  final DefaultAccount data;

  const UpdateAdAccountSuccess(this.data);

  @override
  List<Object?> get props => [data];

  UpdateAdAccountSuccess copyWith({
    DefaultAccount? data,
  }) {
    return UpdateAdAccountSuccess(
      data ?? this.data,
    );
  }
}


class UpdateAdAccountError extends UpdateAdAccountState {
  final Failure message;

  const UpdateAdAccountError(this.message);

  @override
  List<Object?> get props => [message];
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../auth/data/models/user.dart';
import '../../../data/models/default_account.dart';
import '../../../data/repos/meta_repo.dart';

part 'update_ad_account_state.dart';

class UpdateAdAccountCubit extends Cubit<UpdateAdAccountState> {
  UpdateAdAccountCubit() : super(UpdateAdAccountInitial());

  static UpdateAdAccountCubit get(context) => BlocProvider.of(context);

  updateAdAccount({
    required String accountId,
    required String pageId,
    required String pageAccessToken,
    required BuildContext context,
  }) async {
    emit(UpdateAdAccountLoading());
    instance<MetaRepo>()
        .updateAdAccount(
            accountId: accountId,
            pageAccessToken: pageAccessToken,
            pageId: pageId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(UpdateAdAccountError(l));
      }, (r) async {
        UserData userData = UserData().copyWith();
        await instance<HiveHelper>().setUserModel(userData.copyWith(
          accessToken: instance<HiveHelper>().getUser()?.accessToken ?? '',
          defaultAccountId: r.accountId ?? '',
          defaultPageAccessToken: r.pageAccessToken ?? '',
          defaultPageId: r.pageId ?? '',
          id: instance<HiveHelper>().getUser()?.id ?? 0,
          name: instance<HiveHelper>().getUser()?.name ?? '',
          email: instance<HiveHelper>().getUser()?.email ?? '',
          userId: instance<HiveHelper>().getUser()?.userId ?? '',
          createdAt: instance<HiveHelper>().getUser()?.createdAt ?? '',
          updatedAt: instance<HiveHelper>().getUser()?.updatedAt ?? '',
          phone: instance<HiveHelper>().getUser()?.phone ?? '',
          photo: instance<HiveHelper>().getUser()?.photo ?? '',
          emailVerifiedAt:
              instance<HiveHelper>().getUser()?.emailVerifiedAt ?? '',
          token: instance<HiveHelper>().getUser()?.token ?? '',
          instUserId: instance<HiveHelper>().getUser()?.instUserId,
          snapChatToken: instance<HiveHelper>().getUser()?.snapChatToken,
          tiktokToken: instance<HiveHelper>().getUser()?.tiktokToken,
        ));
        Navigator.of(context).pop();

        emit(UpdateAdAccountSuccess(r));
      });
    });
  }
}

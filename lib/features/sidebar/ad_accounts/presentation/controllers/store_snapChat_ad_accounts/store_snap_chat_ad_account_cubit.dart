import 'package:ads_dv/features/sidebar/ad_accounts/data/repos/snapChat_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/store_ad_response.dart';

part 'store_snap_chat_ad_account_state.dart';

class StoreSnapChatAdAccountCubit extends Cubit<StoreSnapChatAdAccountState> {
  StoreSnapChatAdAccountCubit() : super(StoreSnapChatAdAccountInitial());

  static StoreSnapChatAdAccountCubit get(context) => BlocProvider.of(context);

  Future storeAdAccount({
    required String accountId,
    required String accountName,
    required String currency,
    required BuildContext context,
  }) async {
    emit(StoreSnapChatAdAccountLoading());
    await instance<SnapChatRepo>()
        .storeSnapChatAdAccount(
      accountId: accountId,
      accountName: accountName,
      currency: currency,
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(StoreSnapChatAdAccountError(l.message));
      }, (r) async {
        print("kakfkajklStoreAdAccount  ${r.toJson()}");
        // Navigator.of(context).pop();
        // Navigator.pushNamedAndRemoveUntil(
        //     context, Routes.home, (route) => false);
        // Navigator.pushNamedAndRemoveUntil(
        //     context, Routes.splash, (route) => false);
        emit(StoreSnapChatAdAccount(r));
      });
    });
  }
}

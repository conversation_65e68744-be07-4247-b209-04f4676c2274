part of 'store_snap_chat_ad_account_cubit.dart';

@immutable
sealed class StoreSnapChatAdAccountState {}

final class StoreSnapChatAdAccountInitial extends StoreSnapChatAdAccountState {}

final class StoreSnapChatAdAccountLoading extends StoreSnapChatAdAccountState {}

class StoreSnapChatAdAccountError extends StoreSnapChatAdAccountState {
  final String message;

  StoreSnapChatAdAccountError(this.message);

  @override
  List<Object?> get props => [message];

  StoreSnapChatAdAccountError copyWith({
    String? message,
  }) {
    return StoreSnapChatAdAccountError(
      message ?? this.message,
    );
  }
}

class StoreSnapChatAdAccount extends StoreSnapChatAdAccountState {
  final SnapChatStoreAdAccountsResponse? addedAccounts;

  StoreSnapChatAdAccount(this.addedAccounts);

  @override
  List<Object?> get props => [addedAccounts];

  StoreSnapChatAdAccount copyWith({
    SnapChatStoreAdAccountsResponse? addedAccounts,
  }) {
    return StoreSnapChatAdAccount(
      addedAccounts ?? this.addedAccounts,
    );
  }
}

import 'package:ads_dv/features/sidebar/ad_accounts/data/repos/meta_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/meta_page.dart';

part 'get_fb_pages_state.dart';

class GetFbPagesCubit extends Cubit<GetFbPagesState> {
  GetFbPagesCubit() : super(GetFbPagesInitial());

  static GetFbPagesCubit get(context) => BlocProvider.of(context);


  List<MetaPages>  metaPages = [];

  getFbPages({
    required BuildContext context,
    required String addAccountId
  }) async {
    emit(GetFbPagesStateLoading());
    instance<MetaRepo>().getFbPages(adAccountId: addAccountId).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetFbPagesStateError(l));
      }, (r) {
        metaPages = r.result ?? [];
        emit(GetFbPagesStateLoaded(r));
      });
    });
  }

  loadMoreFbPages({
    required BuildContext context,
     String? url
  }) async {
    emit(GetFbPagesStateLoading());
    instance<MetaRepo>().loadMoreFbPages(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetFbPagesStateError(l));
      }, (r) {
        metaPages.addAll(r.result?.map((e) => e).toList() ?? []);
        emit(GetFbPagesStateLoaded(r));
      });
    });
  }
}
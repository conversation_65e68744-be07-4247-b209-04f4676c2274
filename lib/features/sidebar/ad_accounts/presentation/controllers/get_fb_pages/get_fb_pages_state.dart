part of 'get_fb_pages_cubit.dart';

@immutable
abstract class GetFbPagesState {
  const GetFbPagesState();
  List<Object?> get props => [];
}

class GetFbPagesInitial extends GetFbPagesState {}

class GetFbPagesStateLoading extends GetFbPagesState {}

class GetFbPagesStateLoaded extends GetFbPagesState {
  final MetaPageResponse data;

  const GetFbPagesStateLoaded(this.data);
  @override
  List<Object?> get props => [data];

  GetFbPagesStateLoaded copyWith({
    MetaPageResponse? data,
  }) {
    return GetFbPagesStateLoaded(
      data ?? this.data,
    );
  }
}

class GetFbPagesStateError extends GetFbPagesState {
  final Failure message;

  const GetFbPagesStateError(this.message);

  @override
  List<Object?> get props => [message];
}
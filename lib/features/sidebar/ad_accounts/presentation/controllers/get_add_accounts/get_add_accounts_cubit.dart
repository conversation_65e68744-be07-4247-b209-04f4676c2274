import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../auth/data/models/user.dart';
import '../../../../../auth/presentation/login/controllers/facebook_disconnect/facebook_disconnect_cubit.dart';
import '../../../data/models/ad_account.dart';
import '../../../data/models/check_default_accounts_response.dart';
import '../../../data/models/default_account.dart';
import '../../../data/repos/meta_repo.dart';

part 'get_add_accounts_state.dart';

class GetAdAccountsCubit extends Cubit<GetAdAccountsState> {
  GetAdAccountsCubit() : super(GetAdAccountsInitial());

  static GetAdAccountsCubit get(context) => BlocProvider.of(context);

  List<AdAccount> adAccounts = [];
  List<DefaultAccount> defaultAccounts = [];
  DefaultAccount? account;
  String? url;
  CheckDefaultAccountResponse? checkDefaultAccountResponse;

  setSelectedUrl(String? selectedUrl) {
    url = selectedUrl;
    emit(UpdateStatus());
  }

  Future<void> getAdAccounts({
    required BuildContext context,
  }) async {
    emit(GetAdAccountsStateLoading());
    await instance<MetaRepo>().getAdAccounts().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAdAccountsStateError(l.message));
      }, (r) async {
        adAccounts = r.result ?? [];
        // await setSelectedUrl(r.next);
        // await checkDefaultAccounts(context: context);
        print('getAdAccountsResponseass $adAccounts');
        emit(GetAdAccountsStateLoaded(r));
      });
    });
  }

  Future<void> getDefaultAccounts({required BuildContext context}) async {
    try {
      // emit(GetAdAccountsStateLoading());
      await instance<MetaRepo>()
          .getDefaultAccounts()
          .then((value) => value.fold((l) {}, (r) async {
                defaultAccounts = r.data ?? [];
                // print('getAddAccountsxczfjd ${r.data?.first.instUserId}');
                // await changeUserData(index: 0, context: context);
                print('getAddAccountErrordfzxcx ${r.data?[2].accountId}');
                // ***************
                // ***************
                // ***************
                ////////////////////////
                // act_2303380910012286
                // act_2303380910012286
                // act_1756175435175763
                emit(GetDefaultAdAccountsStateLoaded(data: r.data ?? []));
              }));

      // result.fold(
      //   (l) {
      //     FailureHelper.instance.handleFailures(l, context);
      //     emit(GetAdAccountsStateError(l.message));
      //   },
      //   (r) {
      //     defaultAccounts = r.data ?? [];
      //     print('getAddAccountErrordfzxcx ${r.data?.first.pagePic}');
      //
      //     emit(CheckDefaultsAccountsStateLoaded(data: r.data ?? []));
      //   },
      // );
    } catch (e) {
      print('Error while fetching default accounts: $e');
      emit(GetAdAccountsStateError(e.toString()));
    }
  }

  Future<void> changeUserData(
      {required int index, required BuildContext context}) async {
    final user = instance<HiveHelper>().getUser();
    account = GetAdAccountsCubit.get(context).defaultAccounts[index];

    if (account == null) {
      print("Error: Account at index $index is null.");
      return;
    }

    print('Before update - userInstaId: ${user?.instUserId}');

    UserData userData = UserData().copyWith(
      id: user?.id,
      name: user?.name,
      email: user?.email,
      phone: user?.phone,
      photo: user?.photo,
      emailVerifiedAt: user?.emailVerifiedAt,
      token: user?.token,
      accessToken: user?.accessToken,
      userId: user?.userId,
      createdAt: user?.createdAt,
      updatedAt: user?.updatedAt,
      pageUserName: account?.pageUserName,
      pagePic: account?.pagePic,
      defaultPageAccessToken: account?.pageAccessToken,
      defaultAccountName: account?.accountName,
      whatsNumber: account?.whatsNumber,
      instAccId: account?.instAccId,
      instUserName: account?.instUserName,
      defaultAccountId: account?.accountId,
      defaultPageId: account?.pageId,
      instUserId: account?.instUserId,
      snapChatToken: instance<HiveHelper>().getUser()?.snapChatToken,
      tiktokToken: instance<HiveHelper>().getUser()?.tiktokToken,
    );

    await instance<HiveHelper>().setUserModel(userData);

    // Force a refresh to get the latest data
    final updatedUser = instance<HiveHelper>().getUser();

    print(
        'Updated userInstaId: ${account?.instUserId} -> ${updatedUser?.instUserId}');

    if (updatedUser?.instUserId == null) {
      print("Error: instUserId is still null after update!");
    }

    // emit(UpdateStatus());
  }

  Future<void> checkDefaultAccounts({
    required BuildContext context,
  }) async {
    emit(GetAdAccountsStateLoading());
    await instance<MetaRepo>().checkDefaultAccounts().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAdAccountsStateError(l.message));
      }, (r) {
        checkDefaultAccountResponse = r;
        print(
            'checkDefaultsAccounts ${checkDefaultAccountResponse?.toJson()}');
        // await getDefaultAccounts(context: context);
        emit(CheckDefaultsAccountsStateLoaded(checkDefaultAccountResponse: r));
      });
    });
  }
  Future<void> checkAccessToken({
    required BuildContext context,
    required String accessToken,
  }) async {
    emit(GetAdAccountsStateLoading());
    await instance<MetaRepo>().checkAccessToken(accessToken: accessToken).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAdAccountsStateError(l.message));
      }, (r) {
        if(r == false){
          FacebookDisconnectCubit.get(context)
              .facebookDisconnect(
            userId:
            instance<HiveHelper>().getUserId().toString(),
            context: context,
          );
          instance<HiveHelper>().deleteAdAccount();
          instance<HiveHelper>().deleteMetaPages();
          instance<HiveHelper>().setUserModel(
            UserData(
                id: instance<HiveHelper>().getUser()?.id,
                name: instance<HiveHelper>().getUser()?.name,
                email: instance<HiveHelper>().getUser()?.email,
                phone: instance<HiveHelper>().getUser()?.phone,
                photo: instance<HiveHelper>().getUser()?.photo,
                pageName:
                instance<HiveHelper>().getUser()?.pageName,
                pagePic:
                instance<HiveHelper>().getUser()?.pagePic,
                emailVerifiedAt: instance<HiveHelper>()
                    .getUser()
                    ?.emailVerifiedAt,
                token: instance<HiveHelper>().getUser()?.token,
                accessToken: null,
                userId:
                instance<HiveHelper>().getUser()?.userId,
                createdAt:
                instance<HiveHelper>().getUser()?.createdAt,
                updatedAt:
                instance<HiveHelper>().getUser()?.updatedAt,
                defaultAccountId: null,
                defaultPageId: null,
                defaultPageAccessToken: null,
                defaultAccountName: instance<HiveHelper>()
                    .getUser()
                    ?.defaultAccountName,
                pageUserName: instance<HiveHelper>()
                    .getUser()
                    ?.pageUserName,
                instUserName: instance<HiveHelper>()
                    .getUser()
                    ?.instUserName,
                instAccId:
                instance<HiveHelper>().getUser()?.instAccId,
                whatsNumber: instance<HiveHelper>()
                    .getUser()
                    ?.whatsNumber,
                instUserId: instance<HiveHelper>()
                    .getUser()
                    ?.instUserId,
                snapChatToken: instance<HiveHelper>()
                    .getUser()
                    ?.snapChatToken,tiktokToken: instance<HiveHelper>().getUser()?.tiktokToken),
          );
        }

        // checkDefaultAccountResponse = r;
        // print(
        //     'checkDefaultsAccounts ${checkDefaultAccountResponse?.snapChatToken} ${checkDefaultAccountResponse?.snapChat}');
        // await getDefaultAccounts(context: context);
        // emit(CheckDefaultsAccountsStateLoaded(checkDefaultAccountResponse: r));
      });
    });
  }

  loadMoreAdAccounts({required BuildContext context, String? url}) async {
    emit(GetMoreAdAccountsStateLoading());
    instance<MetaRepo>().loadMoreAdAccounts(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAdAccountsStateError(l.message));
      }, (r) {
        adAccounts.addAll(r.result?.map((e) => e).toList() ?? []);
        setSelectedUrl(r.next);

        emit(GetAdAccountsStateLoaded(r));
      });
    });
  }
}

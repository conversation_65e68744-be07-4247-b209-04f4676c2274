part of 'get_add_accounts_cubit.dart';

@immutable
abstract class GetAdAccountsState {
  const GetAdAccountsState();

  List<Object?> get props => [];
}

class GetAdAccountsInitial extends GetAdAccountsState {}

class GetAdAccountsStateLoading extends GetAdAccountsState {}

class GetMoreAdAccountsStateLoading extends GetAdAccountsState {}

class GetAdAccountsStateLoaded extends GetAdAccountsState {
  final AdAccountResponse data;

  const GetAdAccountsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetAdAccountsStateLoaded copyWith({
    AdAccountResponse? data,
  }) {
    return GetAdAccountsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetDefaultAdAccountsStateLoaded extends GetAdAccountsState {
  final List<DefaultAccount>? data;

  const GetDefaultAdAccountsStateLoaded({required this.data});

  @override
  List<Object?> get props => [data];

  GetDefaultAdAccountsStateLoaded copyWith({
    List<DefaultAccount>? data,
  }) {
    return GetDefaultAdAccountsStateLoaded(
      data: data ?? this.data,
    );
  }
}

class CheckDefaultsAccountsStateLoaded extends GetAdAccountsState {
  final CheckDefaultAccountResponse? checkDefaultAccountResponse;

  const CheckDefaultsAccountsStateLoaded({
    this.checkDefaultAccountResponse,
    // this.data,
  });

  @override
  List<Object?> get props => [
        checkDefaultAccountResponse,
        // data,
      ];

  CheckDefaultsAccountsStateLoaded copyWith({
    CheckDefaultAccountResponse? checkDefaultAccountResponse,
    // List<DefaultAccount>? data,
  }) {
    return CheckDefaultsAccountsStateLoaded(
      checkDefaultAccountResponse:
          checkDefaultAccountResponse ?? this.checkDefaultAccountResponse,
      // data: data ?? this.data,
    );
  }
}

class GetAdAccountsStateError extends GetAdAccountsState {
  final String message;

  const GetAdAccountsStateError(this.message);

  @override
  List<Object?> get props => [message];

  GetAdAccountsStateError copyWith({
    String? message,
  }) {
    return GetAdAccountsStateError(
      message ?? this.message,
    );
  }
}

class UpdateStatus extends GetAdAccountsState {}

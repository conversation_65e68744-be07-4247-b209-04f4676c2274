import 'package:ads_dv/features/sidebar/ad_accounts/data/models/default_account.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../auth/data/models/user.dart';
import '../../../data/repos/meta_repo.dart';

part 'store_ad_account_state.dart';

class StoreAdAccountCubit extends Cubit<StoreAdAccountState> {
  StoreAdAccountCubit() : super(StoreAdAccountInitial());

  static StoreAdAccountCubit get(context) => BlocProvider.of(context);

  Future storeAdAccount({
    required String accountId,
    required int pageId,
    required String pageAccessToken,
    required String accountName,
    required String pageUserName,
    required String currency,
    required bool isCreate,
    required BuildContext context,
  }) async {
    emit(StoreAdAccountLoading());
    await instance<MetaRepo>()
        .storeAdAccount(
            accountName: accountName,
            accountId: accountId,
            pageUserName: pageUserName,
            pageAccessToken: pageAccessToken,
            currency: currency,
            pageId: pageId,
            isCreate: isCreate)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(StoreAdAccountError(l));
      }, (r) async {
        UserData userData = UserData().copyWith();
        await instance<HiveHelper>().setUserModel(
          userData.copyWith(
            accessToken: instance<HiveHelper>().getUser()?.accessToken ?? '',
            defaultAccountId: r.accountId ?? '',
            defaultPageAccessToken: r.pageAccessToken ?? '',
            defaultPageId: r.pageId ?? '',
            defaultAccountName: r.accountName ?? "",
            pageUserName: r.pageUserName ?? '',
            instUserName: r.instUserName ?? '',
            instAccId: r.instAccId ?? "",
            whatsNumber: r.whatsNumber ?? '',
            id: instance<HiveHelper>().getUser()?.id ?? 0,
            name: instance<HiveHelper>().getUser()?.name ?? '',
            email: instance<HiveHelper>().getUser()?.email ?? '',
            userId: instance<HiveHelper>().getUser()?.userId ?? '',
            createdAt: instance<HiveHelper>().getUser()?.createdAt ?? '',
            updatedAt: instance<HiveHelper>().getUser()?.updatedAt ?? '',
            phone: instance<HiveHelper>().getUser()?.phone ?? '',
            photo: instance<HiveHelper>().getUser()?.photo ?? '',
            emailVerifiedAt:
                instance<HiveHelper>().getUser()?.emailVerifiedAt ?? '',
            token: instance<HiveHelper>().getUser()?.token ?? '',
            instUserId: instance<HiveHelper>().getUser()?.instUserId,
            snapChatToken: instance<HiveHelper>().getUser()?.snapChatToken,
            tiktokToken: instance<HiveHelper>().getUser()?.tiktokToken,
            // pageUserName: instance<HiveHelper>().getUser()?.pageUserName ?? '',
            // instUserName: instance<HiveHelper>().getUser()?.instUserName ?? '',
            // instAccId: instance<HiveHelper>().getUser()?.instAccId ?? "",
            // whatsNumber: instance<HiveHelper>().getUser()?.whatsNumber ?? '',
          ),
        );
        print("kakfkajklStoreAdAccount  ${r.toJson()}");
        // Navigator.pushNamedAndRemoveUntil(
        //     context, Routes.home, (route) => false);
        emit(StoreAdAccountSuccess(r));
      });
    });
  }
}

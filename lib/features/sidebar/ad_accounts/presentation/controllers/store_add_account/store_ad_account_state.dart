part of 'store_ad_account_cubit.dart';

@immutable
abstract class StoreAdAccountState {
  const StoreAdAccountState();
  List<Object?> get props => [];
}
final class StoreAdAccountInitial extends StoreAdAccountState {}


final class StoreAdAccountLoading extends StoreAdAccountState {}


final class StoreAdAccountSuccess extends StoreAdAccountState {
  final DefaultAccount data;

  const StoreAdAccountSuccess(this.data);

  @override
  List<Object?> get props => [data];

  StoreAdAccountSuccess copyWith({
    DefaultAccount? data,
  }) {
    return StoreAdAccountSuccess(
      data ?? this.data,
    );
  }
}


class StoreAdAccountError extends StoreAdAccountState {
  final Failure message;

   const StoreAdAccountError(this.message);

  @override
  List<Object?> get props => [message];
}

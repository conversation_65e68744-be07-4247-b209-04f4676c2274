import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';

import '../../../../../../widgets/custom_text.dart';

class AccountHintText extends StatelessWidget {
  final String hint;
  bool isDefaultHint;

  AccountHintText({super.key, required this.hint, required this.isDefaultHint});

  @override
  Widget build(BuildContext context) {
    return isDefaultHint
        ? Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: CustomText(
                  text: hint,
                  color: Constants.darkColor,
                  fontSize: 14,
                  alignment: AlignmentDirectional.center,
                  textAlign: TextAlign.center,
                  fontFamily: 'Helvetica Now Display',
                  fontWeight: FontWeight.w700,
                  maxLines: 3,
                ),
              ),
              const CustomText(
                text: '*',
                color: Color(0xFFFD235A),
                fontSize: 14,
                fontFamily: 'Helvetica Now Display',
                fontWeight: FontWeight.w700,
              ),
            ],
          )
        : Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: hint,
                  style: const TextStyle(
                    color: Constants.darkColor,
                    fontSize: 14,
                    fontFamily: 'Helvetica Now Display',
                    fontWeight: FontWeight.w700,
                    height: 0.24,
                    letterSpacing: -0.35,
                  ),
                ),
                const TextSpan(
                  text: '*',
                  style: TextStyle(
                    color: Color(0xFFFD235A),
                    fontSize: 14,
                    fontFamily: 'Helvetica Now Display',
                    fontWeight: FontWeight.w700,
                    height: 0.24,
                    letterSpacing: -0.35,
                  ),
                ),
              ],
            ),
          );
  }
}

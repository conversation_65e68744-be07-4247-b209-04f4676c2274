import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../../../../widgets/appbar.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/res/router/routes.dart';

class TiktokConnectionWebViewScreen extends StatefulWidget {
  const TiktokConnectionWebViewScreen({super.key});

  @override
  State<TiktokConnectionWebViewScreen> createState() =>
      _TiktokConnectionWebViewScreenState();
}

class _TiktokConnectionWebViewScreenState
    extends State<TiktokConnectionWebViewScreen> {
  // late WebViewController controller;

  late WebViewController controller;

  @override
  void initState() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {
            print('startedUrl $url');
          },
          // onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('https://flutter.dev')) {
              // Navigator.pop(context)';'
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) async {
            // print('finishedUrl $url');
            if (url.startsWith(
                'https://dvadsstage.devdigitalvibes.com/public/api/tiktok/access_token')) {
              print('finishedUrl $url');
              Navigator.pop(context);
              await Navigator.pushNamed(context, Routes.tiktokAccounts);
              return;
            }
            // Navigator.pop(context);
            // print('finishedUrl ahmed $url');
          },
        ),
      )
      ..loadRequest(Uri.parse(
          'https://business-api.tiktok.com/portal/auth?app_id=7412831026029789200&state=${instance.get<HiveHelper>().getToken()}&redirect_uri=https://dvadsstage.devdigitalvibes.com/public/api/tiktok/access_token'));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: "Tiktok Accounts",
        showBackButton: true,
        hasDrawer: true,
      ),
      body: WebViewWidget(controller: controller),
    );
  }
}

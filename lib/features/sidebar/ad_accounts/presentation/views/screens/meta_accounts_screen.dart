import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/controllers/get_fb_pages/get_fb_pages_cubit.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/controllers/store_add_account/store_ad_account_cubit.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../utils/res/custom_widgets.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/handle_error_widget.dart';
import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/res/colors.dart';
import '../../../../../auth/data/models/user.dart';
import '../../../data/models/ad_account.dart';
import '../../controllers/get_add_accounts/get_add_accounts_cubit.dart';

class MetaAccountsScreen extends StatefulWidget {
  const MetaAccountsScreen({super.key});

  @override
  State<MetaAccountsScreen> createState() => _MetaAccountsScreenState();
}

class _MetaAccountsScreenState extends State<MetaAccountsScreen> {
  // GetAdAccountsCubit? getAdAccountsCubit;

  @override
  void initState() {
    // getAdAccountsCubit = GetAdAccountsCubit.get(context);
    print(
        'metaLocalStorage ${instance.get<HiveHelper>().getAdAccount()?.toJson()} ${instance.get<HiveHelper>().getMetaPages()?.toJson()} ${instance.get<HiveHelper>().getUser()?.toJson()}');
    Future.microtask(() async {
      await GetAdAccountsCubit.get(context).getAdAccounts(
        context: context,
      );
      await GetAdAccountsCubit.get(context)
          .checkDefaultAccounts(context: context);
      await instance<HiveHelper>().setUserModel(
        UserData(
          id: instance<HiveHelper>().getUser()?.id,
          name: instance<HiveHelper>().getUser()?.name,
          email: instance<HiveHelper>().getUser()?.email,
          phone: instance<HiveHelper>().getUser()?.phone,
          photo: instance<HiveHelper>().getUser()?.photo,
          pageName: instance<HiveHelper>().getUser()?.pageName,
          pagePic: instance<HiveHelper>().getUser()?.pagePic,
          emailVerifiedAt: instance<HiveHelper>().getUser()?.emailVerifiedAt,
          token: instance<HiveHelper>().getUser()?.token,
          accessToken: instance<HiveHelper>().getUser()?.accessToken,
          userId: instance<HiveHelper>().getUser()?.userId,
          createdAt: instance<HiveHelper>().getUser()?.createdAt,
          updatedAt: instance<HiveHelper>().getUser()?.updatedAt,
          defaultAccountId: instance<HiveHelper>().getUser()?.defaultAccountId,
          defaultPageId: instance<HiveHelper>().getUser()?.defaultPageId,
          defaultPageAccessToken:
          instance<HiveHelper>().getUser()?.defaultPageAccessToken,
          defaultAccountName:
          instance<HiveHelper>().getUser()?.defaultAccountName,
          pageUserName: instance<HiveHelper>().getUser()?.pageUserName,
          instUserName: instance<HiveHelper>().getUser()?.instUserName,
          instAccId: instance<HiveHelper>().getUser()?.instAccId,
          whatsNumber: instance<HiveHelper>().getUser()?.whatsNumber,
          instUserId: instance<HiveHelper>().getUser()?.instUserId,
          tiktokToken: GetAdAccountsCubit.get(context)
              .checkDefaultAccountResponse
              ?.tiktokToken,
          snapChatToken: GetAdAccountsCubit.get(context)
              .checkDefaultAccountResponse
              ?.snapChatToken,
        ),
      );
      if (instance.get<HiveHelper>().getAdAccount() != null) {
        await GetFbPagesCubit.get(context).getFbPages(
          context: context,
          addAccountId:
              instance.get<HiveHelper>().getAdAccount()!.accountId.toString(),
        );
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetAdAccountsCubit, GetAdAccountsState>(
        bloc: GetAdAccountsCubit.get(context),
        builder: (context, state) {
          return BlocBuilder<CreateAdCubit, CreateAdState>(
            bloc: CreateAdCubit.get(context),
            builder: (ctx, adState) {
              return Scaffold(
                  backgroundColor: Colors.white,
                  appBar: const CustomAppBar(
                    title: "Meta Account",
                    showBackButton: true,
                    hasDrawer: true,
                  ),
                  body: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (state is GetAdAccountsStateLoading)
                          SizedBox(
                            height: SizeConfig.screenHeight(context) * 0.8,
                            child: const Center(
                              child: LoadingWidget(
                                isCircle: true,
                              ),
                            ),
                          )
                        else if (state is GetAdAccountsStateError)
                          SizedBox(
                            height: SizeConfig.screenHeight(context) * 0.8,
                            child: Center(
                              child: HandleErrorWidget(
                                  fun: () {
                                    GetAdAccountsCubit.get(context)
                                        .getAdAccounts(
                                      context: context,
                                    );
                                  },
                                  failure: state),
                            ),
                          )
                        else
                          Padding(
                            padding: const EdgeInsets.all(24.0),
                            child: Column(
                              children: [
                                Column(
                                  children: [
                                    ExpansionTileItem(
                                      expansionKey: Constants.accountsTileKey,
                                      onExpansionChanged: (val) {
                                        print('onAdAccountChanged $val');
                                      },
                                      childrenPadding: EdgeInsets.zero,
                                      iconColor: AppColors.secondColor,
                                      collapsedIconColor: AppColors.secondColor,
                                      expandedAlignment: Alignment.center,
                                      expandedCrossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      trailing: const Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(left: 6.0),
                                            child: Icon(
                                              Icons.expand_more,
                                              size: 40.0,
                                              color: Constants.darkColor,
                                            ),
                                          )
                                        ],
                                      ),
                                      title: instance
                                                  .get<HiveHelper>()
                                                  .getAdAccount() ==
                                              null
                                          ? CreateAdCubit.get(context)
                                                      .adAccount !=
                                                  null
                                              ? Row(
                                                  children: [
                                                    ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              25),
                                                      child: CircleAvatar(
                                                        child:
                                                            CachedImageWidget(
                                                          assetsImage: AppAssets
                                                              .dummyProfile,
                                                          height: 40.h,
                                                        ),
                                                      ),
                                                    ),
                                                    10.horizontalSpace,
                                                    Expanded(
                                                      child: CustomText(
                                                          text: CreateAdCubit.get(
                                                                      context)
                                                                  .adAccount
                                                                  ?.name ??
                                                              ""),
                                                    ),
                                                  ],
                                                )
                                              : AccountHintText(
                                                  isDefaultHint: false,
                                                  hint:
                                                      'Choose your ads account ',
                                                )
                                          : Row(
                                              children: [
                                                ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(25),
                                                  child: CircleAvatar(
                                                    child: CachedImageWidget(
                                                      assetsImage: AppAssets
                                                          .dummyProfile,
                                                      height: 40.h,
                                                    ),
                                                  ),
                                                ),
                                                10.horizontalSpace,
                                                Expanded(
                                                  child: CustomText(
                                                      text: instance
                                                              .get<HiveHelper>()
                                                              .getAdAccount()
                                                              ?.name ??
                                                          ""),
                                                ),
                                              ],
                                            ),
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x3F000000),
                                            blurRadius: 40,
                                            offset: Offset(0, 0),
                                            spreadRadius: -10,
                                          )
                                        ],
                                      ),
                                      children: [
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemBuilder: (item, index) {
                                            return InkWell(
                                              onTap: () {
                                                final adAccount =
                                                    GetAdAccountsCubit.get(
                                                            context)
                                                        .adAccounts[index];

                                                if (adAccount.accountStatus ==
                                                    1) {
                                                  CreateAdCubit.get(context)
                                                      .setSelectedAccount(
                                                          adAccount);
                                                  GetFbPagesCubit.get(context)
                                                      .getFbPages(
                                                    context: context,
                                                    addAccountId:
                                                        CreateAdCubit.get(
                                                                context)
                                                            .adAccount!
                                                            .id
                                                            .toString(),
                                                  );
                                                  Constants.accountsTileKey
                                                      .currentState
                                                      ?.collapse();
                                                } else {
                                                  // Get the status and reason using the enums
                                                  final status =
                                                      AccountStatus.fromValue(
                                                          adAccount
                                                              .accountStatus);
                                                  final reason =
                                                      DisableReason.fromValue(
                                                          adAccount
                                                              .disableReason);

                                                  // Prepare the description
                                                  String description = reason
                                                              ?.value ==
                                                          0
                                                      ? ""
                                                      : "Reason: ${reason?.description ?? "No reason provided"}";

                                                  AwesomeDialog(
                                                    context: context,
                                                    dialogType:
                                                        DialogType.error,
                                                    animType:
                                                        AnimType.rightSlide,
                                                    headerAnimationLoop: false,
                                                    title:
                                                        status?.description ??
                                                            "Unknown Status",
                                                    // Set the title to the status description
                                                    desc: description,
                                                    // Set the description based on the reason
                                                    btnOkColor: Colors.red,
                                                  ).show();
                                                }
                                              },
                                              child: Container(
                                                decoration: BoxDecoration(
                                                    color: Constants.gray
                                                        .withOpacity(0.15),
                                                    border: Border.symmetric(
                                                        horizontal: BorderSide(
                                                            color: Constants
                                                                .gray
                                                                .withOpacity(
                                                                    0.3)))),
                                                child: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 18,
                                                      horizontal: 20),
                                                  child: Column(
                                                    children: [
                                                      Row(
                                                        children: [
                                                          ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        25),
                                                            child: CircleAvatar(
                                                              child:
                                                                  CachedImageWidget(
                                                                assetsImage:
                                                                    AppAssets
                                                                        .dummyProfile,
                                                                height: 40.h,
                                                              ),
                                                            ),
                                                          ),
                                                          10.horizontalSpace,
                                                          Expanded(
                                                            child: CustomText(
                                                                maxLines: 3,
                                                                text: GetAdAccountsCubit.get(
                                                                            context)
                                                                        .adAccounts[
                                                                            index]
                                                                        .name ??
                                                                    ""),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                          itemCount:
                                              GetAdAccountsCubit.get(context)
                                                  .adAccounts
                                                  .length,
                                        ),
                                        20.verticalSpace,
                                        (GetAdAccountsCubit.get(context).url ==
                                                    "null" ||
                                                GetAdAccountsCubit.get(context)
                                                        .url ==
                                                    null)
                                            ? const SizedBox()
                                            : Column(
                                                children: [
                                                  state is GetMoreAdAccountsStateLoading
                                                      ? const LoadingWidget(
                                                          isCircle: true,
                                                        )
                                                      : InkWell(
                                                          onTap: () {
                                                            GetAdAccountsCubit
                                                                    .get(
                                                                        context)
                                                                .loadMoreAdAccounts(
                                                                    url: GetAdAccountsCubit.get(
                                                                            context)
                                                                        .url,
                                                                    context:
                                                                        context);
                                                          },
                                                          child: Container(
                                                            decoration:
                                                                ShapeDecoration(
                                                              gradient: Constants
                                                                  .defGradient,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            38),
                                                              ),
                                                              shadows: const [
                                                                BoxShadow(
                                                                  color: Color(
                                                                      0x19000000),
                                                                  blurRadius:
                                                                      22,
                                                                  offset:
                                                                      Offset(
                                                                          0, 4),
                                                                  spreadRadius:
                                                                      0,
                                                                )
                                                              ],
                                                            ),
                                                            child: Padding(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      vertical:
                                                                          8.sp,
                                                                      horizontal:
                                                                          14.sp),
                                                              child: Text(
                                                                'Load More',
                                                                style:
                                                                    TextStyle(
                                                                  color: Colors
                                                                      .white,
                                                                  fontSize:
                                                                      12.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                  20.verticalSpace,
                                                ],
                                              ),
                                      ],
                                    ),
                                    30.verticalSpace,
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 12.sp),
                                      child:
                                          const Divider(color: Constants.gray),
                                    ),
                                    30.verticalSpace,
                                    ExpansionTileItem(
                                      expansionKey: Constants.pagesTileKey,
                                      onExpansionChanged: (val) {},
                                      childrenPadding: EdgeInsets.zero,
                                      iconColor: AppColors.secondColor,
                                      collapsedIconColor: AppColors.secondColor,
                                      expandedAlignment: Alignment.center,
                                      expandedCrossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      trailing: const Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(left: 6.0),
                                            child: Icon(
                                              Icons.expand_more,
                                              size: 40.0,
                                              color: Constants.darkColor,
                                            ),
                                          )
                                        ],
                                      ),
                                      title: instance
                                                  .get<HiveHelper>()
                                                  .getMetaPages() ==
                                              null
                                          ? CreateAdCubit.get(context)
                                                      .metaPages !=
                                                  null
                                              ? Row(
                                                  children: [
                                                    CircleAvatar(
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(20),
                                                        child:
                                                            CachedImageWidget(
                                                          image:
                                                              CreateAdCubit.get(
                                                                      context)
                                                                  .metaPages
                                                                  ?.profilePic,
                                                          height: 30.h,
                                                        ),
                                                      ),
                                                    ),
                                                    10.horizontalSpace,
                                                    SizedBox(
                                                      width: 120.h,
                                                      child: FittedBox(
                                                        child: CustomText(
                                                            text: CreateAdCubit.get(
                                                                        context)
                                                                    .metaPages
                                                                    ?.name ??
                                                                ""),
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              : AccountHintText(
                                                  hint: 'Choose your page ',
                                                  isDefaultHint: false,
                                                )
                                          : Row(
                                              children: [
                                                CircleAvatar(
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                    child: CachedImageWidget(
                                                      image: instance
                                                          .get<HiveHelper>()
                                                          .getMetaPages()
                                                          ?.profilePic,
                                                      height: 30.h,
                                                    ),
                                                  ),
                                                ),
                                                10.horizontalSpace,
                                                SizedBox(
                                                  width: 120.h,
                                                  child: FittedBox(
                                                    child: CustomText(
                                                        text: instance
                                                                .get<
                                                                    HiveHelper>()
                                                                .getMetaPages()
                                                                ?.name ??
                                                            ""),
                                                  ),
                                                ),
                                              ],
                                            ),
                                      decoration: ShapeDecoration(
                                        color: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x3F000000),
                                            blurRadius: 40,
                                            offset: Offset(0, 0),
                                            spreadRadius: -10,
                                          )
                                        ],
                                      ),
                                      children: [
                                        BlocBuilder<GetFbPagesCubit,
                                            GetFbPagesState>(
                                          bloc: GetFbPagesCubit.get(context),
                                          builder: (ctx, pageState) {
                                            return pageState
                                                    is GetFbPagesStateLoading
                                                ? SizedBox(
                                                    height: 100.h,
                                                    child: const LoadingWidget(
                                                      isCircle: true,
                                                    ),
                                                  )
                                                : pageState
                                                        is GetFbPagesStateLoaded
                                                    ? GetFbPagesCubit.get(ctx)
                                                            .metaPages
                                                            .isNotEmpty
                                                        ? ListView.builder(
                                                            shrinkWrap: true,
                                                            physics:
                                                                const NeverScrollableScrollPhysics(),
                                                            itemBuilder:
                                                                (item, index) {
                                                              return InkWell(
                                                                onTap: () {
                                                                  CreateAdCubit
                                                                          .get(
                                                                              ctx)
                                                                      .setSelectedPage(
                                                                          GetFbPagesCubit.get(ctx)
                                                                              .metaPages[index]);
                                                                  Constants
                                                                      .pagesTileKey
                                                                      .currentState
                                                                      ?.collapse();
                                                                },
                                                                child:
                                                                    Container(
                                                                  decoration: BoxDecoration(
                                                                      color: Constants
                                                                          .gray
                                                                          .withOpacity(
                                                                              0.15),
                                                                      border: Border.symmetric(
                                                                          horizontal:
                                                                              BorderSide(color: Constants.gray.withOpacity(0.3)))),
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        vertical:
                                                                            18,
                                                                        horizontal:
                                                                            20),
                                                                    child:
                                                                        Column(
                                                                      children: [
                                                                        Row(
                                                                          children: [
                                                                            CircleAvatar(
                                                                              child: ClipRRect(
                                                                                borderRadius: BorderRadius.circular(20),
                                                                                child: CachedImageWidget(
                                                                                  image: GetFbPagesCubit.get(ctx).metaPages[index].profilePic,
                                                                                  height: 40.h,
                                                                                ),
                                                                              ),
                                                                            ),
                                                                            10.horizontalSpace,
                                                                            Expanded(
                                                                              child: CustomText(text: GetFbPagesCubit.get(ctx).metaPages[index].name ?? ""),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                            itemCount:
                                                                GetFbPagesCubit
                                                                        .get(
                                                                            ctx)
                                                                    .metaPages
                                                                    .length,
                                                          )
                                                        : const Center(
                                                            child: CustomText(
                                                                alignment:
                                                                    AlignmentDirectional
                                                                        .center,
                                                                text:
                                                                    "No associated pages with this account"))
                                                    : const Center(
                                                        child: CustomText(
                                                            alignment:
                                                                AlignmentDirectional
                                                                    .center,
                                                            text:
                                                                "No Ad Account Selected until now"));
                                          },
                                        ),
                                        20.verticalSpace,
                                      ],
                                    ),
                                  ],
                                ),
                                50.verticalSpace,
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 50.sp),
                                  child: SizedBox(
                                    width: 235.w,
                                    child: BlocBuilder<StoreAdAccountCubit,
                                        StoreAdAccountState>(
                                      bloc: StoreAdAccountCubit.get(context),
                                      builder: (context3, accState) {
                                        return accState is StoreAdAccountLoading
                                            ? const LoadingWidget(
                                                isCircle: true,
                                              )
                                            : ButtonWidget(
                                                text: "Save",
                                                onTap: () async{
                                                  if (CreateAdCubit.get(context)
                                                          .adAccount ==
                                                      null) {
                                                    showErrorToast(
                                                        "please select your ad account");
                                                  } else if (CreateAdCubit.get(
                                                              context)
                                                          .metaPages ==
                                                      null) {
                                                    showErrorToast(
                                                        "please select your page");
                                                  } else {
                                                    CreateAdCubit.get(context).adModel = CreateAdCubit.get(context).adModel.copyWith(
                                                        adAccountId: instance<HiveHelper>()
                                                                .getUser()
                                                                ?.defaultAccountId ??
                                                            CreateAdCubit.get(context)
                                                                .adAccount
                                                                ?.id ??
                                                            "",
                                                        pageAccessToken: instance<HiveHelper>()
                                                                .getUser()
                                                                ?.defaultPageAccessToken ??
                                                            CreateAdCubit.get(context)
                                                                .metaPages!
                                                                .accessToken ??
                                                            "",
                                                        pageId: int.parse(
                                                            instance<HiveHelper>()
                                                                    .getUser()
                                                                    ?.defaultPageId ??
                                                                CreateAdCubit.get(context)
                                                                    .metaPages
                                                                    ?.id ??
                                                                "0"));

                                                    await StoreAdAccountCubit.get(
                                                            context)
                                                        .storeAdAccount(
                                                      isCreate: (GetAdAccountsCubit
                                                                          .get(
                                                                              context)
                                                                      .checkDefaultAccountResponse
                                                                      ?.subscribed ==
                                                                  true &&
                                                              GetAdAccountsCubit
                                                                          .get(
                                                                              context)
                                                                      .checkDefaultAccountResponse
                                                                      ?.result ==
                                                                  true)
                                                          ? true
                                                          : false,
                                                      pageUserName:
                                                          CreateAdCubit.get(
                                                                      context)
                                                                  .metaPages
                                                                  ?.userName ??
                                                              "",
                                                      accountId:
                                                          CreateAdCubit.get(
                                                                      context)
                                                                  .adAccount
                                                                  ?.id ??
                                                              "",
                                                      accountName:
                                                          CreateAdCubit.get(
                                                                      context)
                                                                  .adAccount
                                                                  ?.name ??
                                                              "",
                                                      context: context3,
                                                      pageAccessToken:
                                                          CreateAdCubit.get(
                                                                      context)
                                                                  .metaPages
                                                                  ?.accessToken ??
                                                              "",
                                                      pageId: int.parse(
                                                        CreateAdCubit.get(
                                                                    context)
                                                                .metaPages
                                                                ?.id ??
                                                            "0",
                                                      ),
                                                      currency: CreateAdCubit.get(
                                                                      context)
                                                                  .adAccount
                                                                  ?.currency ??
                                                              "",
                                                    );
                                                    await GetAdAccountsCubit.get(context)
                                                        .getDefaultAccounts(context: context);
                                                    await GetAdAccountsCubit.get(context)
                                                        .checkDefaultAccounts(context: context);
                                                    Navigator.of(context).pop();
                                                  }
                                                },
                                              );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ));
            },
          );
        });
  }
}

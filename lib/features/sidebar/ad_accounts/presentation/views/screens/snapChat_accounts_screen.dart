import 'package:ads_dv/features/auth/data/models/user.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/controllers/get_snapChat_add_accounts/get_snap_chat_add_accounts_cubit.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/views/widgets/account_hint_text.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';

import '../../../../../../utils/res/colors.dart';
import '../../controllers/get_add_accounts/get_add_accounts_cubit.dart';
import '../../controllers/store_snapChat_ad_accounts/store_snap_chat_ad_account_cubit.dart';

class SnapChatAccountsScreen extends StatefulWidget {
  const SnapChatAccountsScreen({super.key});

  @override
  State<SnapChatAccountsScreen> createState() => _SnapChatAccountsScreenState();
}

class _SnapChatAccountsScreenState extends State<SnapChatAccountsScreen> {
  @override
  void initState() {
    Future.delayed(const Duration(), () async {
      await GetSnapChatAddAccountsCubit.get(context)
          .getAdAccounts(context: context);
      await GetAdAccountsCubit.get(context)
          .checkDefaultAccounts(context: context);
      await instance<HiveHelper>().setUserModel(
        UserData(
          id: instance<HiveHelper>().getUser()?.id,
          name: instance<HiveHelper>().getUser()?.name,
          email: instance<HiveHelper>().getUser()?.email,
          phone: instance<HiveHelper>().getUser()?.phone,
          photo: instance<HiveHelper>().getUser()?.photo,
          pageName: instance<HiveHelper>().getUser()?.pageName,
          pagePic: instance<HiveHelper>().getUser()?.pagePic,
          emailVerifiedAt: instance<HiveHelper>().getUser()?.emailVerifiedAt,
          token: instance<HiveHelper>().getUser()?.token,
          accessToken: instance<HiveHelper>().getUser()?.accessToken,
          userId: instance<HiveHelper>().getUser()?.userId,
          createdAt: instance<HiveHelper>().getUser()?.createdAt,
          updatedAt: instance<HiveHelper>().getUser()?.updatedAt,
          defaultAccountId: instance<HiveHelper>().getUser()?.defaultAccountId,
          defaultPageId: instance<HiveHelper>().getUser()?.defaultPageId,
          defaultPageAccessToken:
              instance<HiveHelper>().getUser()?.defaultPageAccessToken,
          defaultAccountName:
              instance<HiveHelper>().getUser()?.defaultAccountName,
          pageUserName: instance<HiveHelper>().getUser()?.pageUserName,
          instUserName: instance<HiveHelper>().getUser()?.instUserName,
          instAccId: instance<HiveHelper>().getUser()?.instAccId,
          whatsNumber: instance<HiveHelper>().getUser()?.whatsNumber,
          instUserId: instance<HiveHelper>().getUser()?.instUserId,
          tiktokToken: GetAdAccountsCubit.get(context)
              .checkDefaultAccountResponse
              ?.tiktokToken,
          snapChatToken: GetAdAccountsCubit.get(context)
              .checkDefaultAccountResponse
              ?.snapChatToken,
        ),
      );
      print(
          'ghjghjhjkjklbnmbm ${GetAdAccountsCubit.get(context).checkDefaultAccountResponse?.snapChatToken}');
    });
    // TiktokAccountsCubit.get(context).setSelectedAccount(false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: "SnapChat Accounts",
        showBackButton: true,
        hasDrawer: true,
      ),
      body:
          BlocBuilder<GetSnapChatAddAccountsCubit, GetSnapChatAddAccountsState>(
        builder: (snapChatContext, snapChatState) {
          return Stack(
            children: [
              // (TiktokAccountsCubit.get(tiktokContext).isPageSelected &&
              //         TiktokAccountsCubit.get(tiktokContext).isAccountSelected)
              //     ?
              // :
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      ExpansionTileBorderItem(
                        expansionKey: Constants.tiktokAccountsTileKey,
                        onExpansionChanged: (val) {},
                        childrenPadding: EdgeInsets.zero,
                        iconColor: AppColors.secondColor,
                        collapsedIconColor: AppColors.secondColor,
                        expandedAlignment: Alignment.center,
                        expandedCrossAxisAlignment: CrossAxisAlignment.center,
                        trailing: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(left: 6.0),
                              child: Icon(
                                Icons.expand_more,
                                size: 40.0,
                                color: Constants.darkColor,
                              ),
                            )
                          ],
                        ),
                        title: GetSnapChatAddAccountsCubit.get(snapChatContext)
                                .isAccountSelected
                            ? Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(25),
                                    child: CircleAvatar(
                                      child: CachedImageWidget(
                                        assetsImage: AppAssets.dummyProfile,
                                        height: 40.h,
                                      ),
                                    ),
                                  ),
                                  10.horizontalSpace,
                                  Expanded(
                                    child: CustomText(
                                        text: GetSnapChatAddAccountsCubit.get(
                                                    snapChatContext)
                                                .addedAdAccount
                                                ?.adaccount
                                                ?.name ??
                                            ""),
                                  ),
                                ],
                              )
                            : AccountHintText(
                                isDefaultHint: false,
                                hint: 'Choose your ads account ',
                              ),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          shadows: const [
                            BoxShadow(
                              color: Color(0x3F000000),
                              blurRadius: 40,
                              offset: Offset(0, 0),
                              spreadRadius: -10,
                            )
                          ],
                        ),
                        children: [
                          if (snapChatState is GetSnapChatAdAccountsStateLoaded)
                            ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (item, index) {
                                return InkWell(
                                  onTap: () {
                                    GetSnapChatAddAccountsCubit.get(
                                            snapChatContext)
                                        .setSelectedAdAccount(true,
                                            snapChatState.data.result![index]);
                                    Constants.tiktokAccountsTileKey.currentState
                                        ?.collapse();
                                    setState(() {});
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Constants.gray.withOpacity(0.15),
                                      border: Border.symmetric(
                                        horizontal: BorderSide(
                                          color:
                                              Constants.gray.withOpacity(0.3),
                                        ),
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 18, horizontal: 20),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(25),
                                                child: CircleAvatar(
                                                  child: CachedImageWidget(
                                                    assetsImage:
                                                        AppAssets.dummyProfile,
                                                    height: 40.h,
                                                  ),
                                                ),
                                              ),
                                              10.horizontalSpace,
                                              Expanded(
                                                child: CustomText(
                                                    maxLines: 3,
                                                    text: snapChatState
                                                            .data
                                                            .result![index]
                                                            .adaccount
                                                            ?.name ??
                                                        ""),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                              itemCount: snapChatState.data.result?.length ?? 0,
                            ),
                        ],
                      ),
                      // 30.verticalSpace,
                      // Padding(
                      //   padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      //   child: const Divider(color: Constants.gray),
                      // ),
                      // 30.verticalSpace,
                      // ExpansionTileBorderItem(
                      //   expansionKey: Constants.tiktokPagesTileKey,
                      //   onExpansionChanged: (val) {},
                      //   childrenPadding: EdgeInsets.zero,
                      //   iconColor: AppColors.secondColor,
                      //   collapsedIconColor: AppColors.secondColor,
                      //   expandedAlignment: Alignment.center,
                      //   expandedCrossAxisAlignment:
                      //       CrossAxisAlignment.center,
                      //   trailing: const Row(
                      //     mainAxisSize: MainAxisSize.min,
                      //     children: [
                      //       Padding(
                      //         padding: EdgeInsets.only(left: 6.0),
                      //         child: Icon(
                      //           Icons.expand_more,
                      //           size: 40.0,
                      //           color: Constants.darkColor,
                      //         ),
                      //       )
                      //     ],
                      //   ),
                      //   title: TiktokAccountsCubit.get(tiktokContext)
                      //           .isPageSelected
                      //       ? Row(
                      //           children: [
                      //             ClipRRect(
                      //               borderRadius:
                      //                   BorderRadius.circular(25),
                      //               child: CircleAvatar(
                      //                 child: CachedImageWidget(
                      //                   assetsImage:
                      //                       AppAssets.dummyProfile,
                      //                   height: 40.h,
                      //                 ),
                      //               ),
                      //             ),
                      //             10.horizontalSpace,
                      //             const Expanded(
                      //               child: CustomText(text: "Page Name"),
                      //             ),
                      //           ],
                      //         )
                      //       : AccountHintText(
                      //           hint: 'Choose your another ad account ',
                      //           isDefaultHint: false,
                      //         ),
                      //   decoration: ShapeDecoration(
                      //     color: Colors.white,
                      //     shape: RoundedRectangleBorder(
                      //       borderRadius: BorderRadius.circular(20),
                      //     ),
                      //     shadows: const [
                      //       BoxShadow(
                      //         color: Color(0x3F000000),
                      //         blurRadius: 40,
                      //         offset: Offset(0, 0),
                      //         spreadRadius: -10,
                      //       )
                      //     ],
                      //   ),
                      //   children: [
                      //     ListView.builder(
                      //       shrinkWrap: true,
                      //       physics: const NeverScrollableScrollPhysics(),
                      //       itemBuilder: (item, index) {
                      //         return InkWell(
                      //           onTap: () {
                      //             TiktokAccountsCubit.get(tiktokContext)
                      //                 .setSelectedPage(true);
                      //             Constants
                      //                 .tiktokPagesTileKey.currentState
                      //                 ?.collapse();
                      //           },
                      //           child: Container(
                      //             decoration: BoxDecoration(
                      //                 color: Constants.gray
                      //                     .withOpacity(0.15),
                      //                 border: Border.symmetric(
                      //                     horizontal: BorderSide(
                      //                         color: Constants.gray
                      //                             .withOpacity(0.3)))),
                      //             child: Padding(
                      //               padding: const EdgeInsets.symmetric(
                      //                   vertical: 18, horizontal: 20),
                      //               child: Column(
                      //                 children: [
                      //                   Row(
                      //                     children: [
                      //                       CircleAvatar(
                      //                         child: ClipRRect(
                      //                           borderRadius:
                      //                               BorderRadius.circular(
                      //                                   20),
                      //                           child: CachedImageWidget(
                      //                             assetsImage: AppAssets
                      //                                 .dummyProfile,
                      //                             height: 40.h,
                      //                           ),
                      //                         ),
                      //                       ),
                      //                       10.horizontalSpace,
                      //                       const Expanded(
                      //                         child: CustomText(
                      //                             text: "Page Name"),
                      //                       ),
                      //                     ],
                      //                   ),
                      //                 ],
                      //               ),
                      //             ),
                      //           ),
                      //         );
                      //       },
                      //       itemCount: 15,
                      //     ),
                      //     20.verticalSpace,
                      //   ],
                      // ),
                    ],
                  ),
                ),
              ),
              // (
              // TiktokAccountsCubit.get(tiktokContext).isAccountSelected &&
              // TiktokAccountsCubit.get(tiktokContext).isAccountSelected
              //     // )
              //     ? Align(
              //         alignment: AlignmentDirectional.bottomCenter,
              //         child: Padding(
              //           padding: EdgeInsets.symmetric(
              //               horizontal: 50.sp, vertical: 20.sp),
              //           child: SizedBox(
              //             width: 235.w,
              //             child: ButtonWidget(
              //               text: "Done",
              //               onTap: () {
              //                 Navigator.of(context).pop();
              //               },
              //             ),
              //           ),
              //         ),
              //       )
              //     :
              Align(
                alignment: AlignmentDirectional.bottomCenter,
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 50.sp, vertical: 20.sp),
                  child: SizedBox(
                    width: 235.w,
                    child: ButtonWidget(
                      text: "Save",
                      onTap: () async {
                        // print(
                        //     'snapChatUserAfterSave ${instance.get<GetAdAccountsCubit>().checkDefaultAccountResponse?.tiktokToken} ${instance<HiveHelper>().getUser()?.toJson()}');
                        await StoreSnapChatAdAccountCubit.get(context)
                            .storeAdAccount(
                                accountId: GetSnapChatAddAccountsCubit.get(
                                            snapChatContext)
                                        .addedAdAccount
                                        ?.adaccount!
                                        .id
                                        .toString() ??
                                    "",
                                accountName: GetSnapChatAddAccountsCubit.get(
                                            snapChatContext)
                                        .addedAdAccount
                                        ?.adaccount
                                        ?.name ??
                                    "",
                                currency: GetSnapChatAddAccountsCubit.get(
                                            snapChatContext)
                                        .addedAdAccount
                                        ?.adaccount!
                                        .currency ??
                                    "",
                                context: context);
                        await GetAdAccountsCubit.get(context)
                            .checkDefaultAccounts(context: context);
                        await GetSnapChatAddAccountsCubit.get(context)
                            .getDefaultAccounts(context: context);
                        // await TiktokAccountsCubit.get(context)
                        //     .getDefaultTiktokAccounts(context: context);
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

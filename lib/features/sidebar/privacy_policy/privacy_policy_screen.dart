import 'package:ads_dv/utils/res/common_utils.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../widgets/appbar.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: "Privacy & Policy".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.sp),
        child: SingleChildScrollView(
          child: Column(
            children: [
              10.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '1. Introduction'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "DV Ads Manager is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our application. Please read this Privacy Policy carefully. By using the application, you agree to the collection and use of information in accordance with this policy.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '2. Information We Collect'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "While using our application, we may collect the following personal information:".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "- Name".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- Email address".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- Facebook profile information".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '3. How We Use Your Information'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "We use the collected information to:".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "- Create and manage advertising campaigns on your connected Facebook ad accounts".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- Tailor the content and features of our app to better suit your interests and needs".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    textAlign: TextAlign.justify,

                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- Provide you with relevant updates, notifications, and communications about our service".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    textAlign: TextAlign.justify,

                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '4. How We Share Your Information'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "We may share your information with third parties in the following situations:".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "- With service providers who perform services on our behalf".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- With Meta Platforms, Inc. to facilitate the integration and functionality of the application".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- To comply with legal obligations and protect our rights".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- With your consent or at your direction".tr,
                    color: const Color(0xFF131534),
                    textAlign: TextAlign.justify,

                    fontSize: 12.sp,
                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '5. Data Security'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "We implement appropriate technical and organizational measures to protect your personal data against unauthorized access, alteration, disclosure, or destruction.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '6. Your Data Protection Rights'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "Depending on your location, you may have the following rights regarding your personal data:".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "-  The right to access – You have the right to request copies of your personal data.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- The right to rectification – You have the right to request correction of any information you believe is inaccurate or incomplete.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- The right to erasure – You have the right to request the deletion of your personal data under certain conditions.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- The right to restrict processing – You have the right to request restriction of the processing of your personal data under certain conditions.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- The right to object to processing – You have the right to object to the processing of your personal data under certain conditions.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  5.verticalSpace,
                  CustomText(
                    text:
                    "- The right to data portability – You have the right to request the transfer of the data that we have collected to another organization, or directly to you, under certain conditions.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    fontFamily: 'Helvetica Now Display'.tr,
                    textAlign: TextAlign.justify,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '7. Changes to This Privacy Policy'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page. You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                ],
              ),
              20.verticalSpace,
              Column(
                children: [
                  CustomText(
                    text: '8. Contact Us'.tr,
                    color: const Color(0xFF131534),
                    fontSize: 16.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w600,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  CustomText(
                    text:
                    "If you have any questions about this Privacy Policy, please contact us:".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  10.verticalSpace,
                  Row(
                    children: [
                      CustomText(
                        text:
                        "- By email:".tr,
                        color: const Color(0xFF131534),
                        fontSize: 12.sp,
                        textAlign: TextAlign.justify,

                        fontFamily: 'Helvetica Now Display'.tr,
                        fontWeight: FontWeight.w400,
                        maxLines: 10,
                      ),
                      10.horizontalSpace,
                      InkWell(
                        onTap: (){
                          CommonUtils.emailTo("<EMAIL>".tr);
                        },
                        child: CustomText(
                          text:
                          "<EMAIL>".tr,
                          color: Colors.blue,
                          fontSize: 12.sp,
                          textAlign: TextAlign.justify,

                          fontFamily: 'Helvetica Now Display'.tr,
                          fontWeight: FontWeight.w600,
                          maxLines: 10,
                        ),
                      ),
                    ],
                  ),
                  5.verticalSpace,
                  Row(
                    children: [
                      Expanded(
                        child: CustomText(
                          text:
                          "- By visiting our website:".tr,
                          color: const Color(0xFF131534),
                          fontSize: 12.sp,
                          textAlign: TextAlign.justify,
                        
                          fontFamily: 'Helvetica Now Display'.tr,
                          fontWeight: FontWeight.w400,
                          maxLines: 10,
                        ),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: (){
                            CommonUtils.launchInWebViewWithoutJavaScript("https://connect.devdigitalvibes.com/".tr);
                          },
                          child: CustomText(
                            text:
                            "https://connect.devdigitalvibes.com/".tr,
                            color: Colors.blue,
                            fontSize: 12.sp,
                            textAlign: TextAlign.justify,
                        
                            fontFamily: 'Helvetica Now Display'.tr,
                            fontWeight: FontWeight.w600,
                            maxLines: 10,
                          ),
                        ),
                      ),
                    ],
                  ),

                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

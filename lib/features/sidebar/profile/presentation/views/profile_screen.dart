import 'dart:io';

import 'package:ads_dv/features/sidebar/profile/presentation/controllers/edit_profile_cubit.dart';
import 'package:ads_dv/utils/network/urls/services_urls.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/text_field_widget.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p;
import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/appbar.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/svg_widget.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

final _formKey = GlobalKey<FormState>();
final TextEditingController _nameController = TextEditingController(
    text: instance<HiveHelper>().getUser()!.name.toString());
final TextEditingController _emailController = TextEditingController(
    text: instance<HiveHelper>().getUser()!.email.toString());
final TextEditingController _phoneController = TextEditingController(
    text: instance<HiveHelper>().getUser()!.phone.toString());
final TextEditingController _passwordController = TextEditingController();
final TextEditingController _passwordConfirmController =
    TextEditingController();

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EditProfileCubit(),
      child: BlocListener<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileLoaded) {
            _passwordController.text = "";
            _passwordConfirmController.text = "";
          }
          // TODO: implement listener
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: CustomAppBar(
            height: 180.h,
            logo: AppAssets.mainLogo,
            actions: [
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, Routes.notifications);

                },
                child: CustomSvgWidget(
                  svg: AppAssets.notifications,
                  height: 30.h,
                  width: 30.h,
                ),
              )
            ],
            leading: Padding(
              padding: const EdgeInsets.all(8.0),
              child: InkWell(
                onTap: () {
                  Constants.scaffoldKey.currentState!.openDrawer();
                },
                child: CustomSvgWidget(
                  svg: AppAssets.drawer,
                  height: 4.h,
                  width: 12.h,
                ),
              ),
            ),
          ),
          body: BlocBuilder<EditProfileCubit, EditProfileState>(
            builder: (context, state) {
              return Padding(
                padding:
                    EdgeInsets.only(right: 24.sp, left: 24.sp, bottom: 24.sp),
                child: SingleChildScrollView(
                  clipBehavior: Clip.none,
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Stack(
                          children: [
                            Stack(
                              children: [
                                Center(
                                  child: CachedImageWidget(
                                    assetsImage: AppAssets.success,
                                    height: 120.h,
                                  ),
                                ),
                                Positioned(
                                    left: 0,
                                    right: 0,
                                    top: 10.h,
                                    bottom: 10.h,
                                    child: Center(
                                      child: (EditProfileCubit.get(context)
                                              .profilePic
                                              .path
                                              .isEmpty)
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(50),
                                              child: Container(
                                                height: 85.h,
                                                width: 85.h,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(50),
                                                ),
                                                child: (instance<HiveHelper>()
                                                                .getUser()!
                                                                .photo ==
                                                            null ||
                                                        instance<HiveHelper>()
                                                                .getUser()!
                                                                .photo ==
                                                            "")
                                                    ? CachedImageWidget(
                                                        assetsImage: AppAssets
                                                            .dummyProfile,
                                                        fit: BoxFit.fill,
                                                        height: 70.h,
                                                      )
                                                    : CachedImageWidget(
                                                        image: ServicesURLs
                                                                .assetsUrl +
                                                            instance<
                                                                    HiveHelper>()
                                                                .getUser()!
                                                                .photo
                                                                .toString(),
                                                        height: 70.h,
                                                      ),
                                              ),
                                            )
                                          : ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(50),
                                              child: Container(
                                                height: 85.h,
                                                width: 85.h,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(50),
                                                ),
                                                child: Image.file(
                                                    EditProfileCubit.get(
                                                            context)
                                                        .profilePic,
                                                    fit: BoxFit.fill),
                                              ),
                                            ),
                                    )),
                              ],
                            ),
                            Positioned(
                                left: 70.h,
                                right: 0,
                                top: 70.h,
                                bottom: 0,
                                child: InkWell(
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      builder: (BuildContext ctx) {
                                        return AlertDialog(
                                          title: const Center(
                                            child: Text("Images source"),
                                          ),
                                          content: const Text(
                                              "Choose images source"),
                                          actionsAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          actions: [
                                            TextButton(
                                              onPressed: () async {
                                                Navigator.of(context).pop();

                                                FilePickerResult? result =
                                                    await FilePicker.platform
                                                        .pickFiles(
                                                  type: FileType.image,
                                                  allowMultiple: false,
                                                  allowCompression: true,
                                                );

                                                if (result != null &&
                                                    result.files.isNotEmpty) {
                                                  File imageFile = File(result
                                                      .files.single.path!);
                                                  final fileExtension =
                                                      p.extension(
                                                          imageFile.path);
                                                  if (!['.jpg', '.jpeg', '.png']
                                                      .contains(fileExtension
                                                          .toLowerCase())) {
                                                    const snackBar = SnackBar(
                                                      elevation: 0,
                                                      behavior: SnackBarBehavior
                                                          .floating,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      content:
                                                          AwesomeSnackbarContent(
                                                        title: 'On Snap!',
                                                        message:
                                                            'Please select an image file',
                                                        contentType:
                                                            ContentType.failure,
                                                      ),
                                                    );

                                                    ScaffoldMessenger.of(
                                                        context)
                                                      ..hideCurrentSnackBar()
                                                      ..showSnackBar(snackBar);
                                                  } else {
                                                    // Reduce image size

                                                    EditProfileCubit.get(
                                                            context)
                                                        .profilePic = imageFile;

                                                    EditProfileCubit.get(
                                                            context)
                                                        .updateStatus();
                                                  }
                                                }
                                              },
                                              child: const Text("Gallery"),
                                            ),
                                            TextButton(
                                              onPressed: () async {
                                                Navigator.of(context).pop();

                                                // If file picker is not supported, capture image from camera
                                                final XFile? capturedImage =
                                                    await ImagePicker()
                                                        .pickImage(
                                                  source: ImageSource.camera,
                                                );

                                                if (capturedImage != null) {
                                                  EditProfileCubit.get(context)
                                                          .profilePic =
                                                      File(capturedImage.path);

                                                  EditProfileCubit.get(context)
                                                      .updateStatus();
                                                }
                                              },
                                              child: const Text("Camera"),
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  },
                                  child: CustomSvgWidget(
                                    svg: AppAssets.updateProfile,
                                    height: 70.h,
                                  ),
                                ))
                          ],
                        ),
                        CustomText(
                          text:
                              instance<HiveHelper>().getUser()!.name.toString(),
                          textAlign: TextAlign.center,
                          color: Constants.darkColor,
                          fontSize: 20,
                          alignment: AlignmentDirectional.center,
                          fontWeight: FontWeight.w700,
                        ),
                        25.verticalSpace,
                        Column(
                          children: [
                            Column(
                              children: [
                                CustomText(
                                  text: 'Name',
                                  color: Constants.darkColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                10.verticalSpace,
                                Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.sp),
                                  width: double.infinity,
                                  height: 60.h,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 150.20,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const CustomSvgWidget(
                                        svg: AppAssets.userG,
                                        height: 25,
                                      ),
                                      10.horizontalSpace,
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 10.sp),
                                        child: const VerticalDivider(),
                                      ),
                                      Expanded(
                                          child: CustomTextField(
                                        borderColor: Colors.transparent,
                                        controller: _nameController,
                                      ))
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Column(
                              children: [
                                CustomText(
                                  text: 'Email',
                                  color: Constants.darkColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                10.verticalSpace,
                                Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.sp),
                                  width: double.infinity,
                                  height: 60.h,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 150.20,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const CustomSvgWidget(
                                        svg: AppAssets.mailG,
                                        height: 20,
                                      ),
                                      10.horizontalSpace,
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 10.sp),
                                        child: const VerticalDivider(),
                                      ),
                                      Expanded(
                                          child: CustomTextField(
                                        borderColor: Colors.transparent,
                                        isEditable: false,
                                        controller: _emailController,
                                      )),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Column(
                              children: [
                                CustomText(
                                  text: 'Phone Number',
                                  color: Constants.darkColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                10.verticalSpace,
                                Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.sp),
                                  width: double.infinity,
                                  height: 60.h,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 150.20,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const CustomSvgWidget(
                                        svg: AppAssets.phone,
                                        hasGradient: true,
                                        color: Colors.white,
                                        height: 25,
                                      ),
                                      10.horizontalSpace,
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 10.sp),
                                        child: const VerticalDivider(),
                                      ),
                                      Expanded(
                                          child: CustomTextField(
                                        borderColor: Colors.transparent,
                                        controller: _phoneController,
                                      ))
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Column(
                              children: [
                                CustomText(
                                  text: 'Password',
                                  color: Constants.darkColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                10.verticalSpace,
                                Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.sp),
                                  width: double.infinity,
                                  height: 60.h,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 150.20,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const CustomSvgWidget(
                                        svg: AppAssets.lockG,
                                        height: 25,
                                      ),
                                      10.horizontalSpace,
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 10.sp),
                                        child: const VerticalDivider(),
                                      ),
                                      Expanded(
                                          child: CustomTextField(
                                        hasPassword: true,
                                        borderColor: Colors.transparent,
                                        controller: _passwordController,
                                        validator: (value) =>
                                            AppValidator.validPassword(value),
                                      )),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Column(
                              children: [
                                CustomText(
                                  text: 'Confirm Password',
                                  color: Constants.darkColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                10.verticalSpace,
                                Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.sp),
                                  width: double.infinity,
                                  height: 60.h,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 150.20,
                                        offset: Offset(0, 0),
                                        spreadRadius: 0,
                                      )
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const CustomSvgWidget(
                                        svg: AppAssets.lockG,
                                        height: 25,
                                      ),
                                      10.horizontalSpace,
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 10.sp),
                                        child: const VerticalDivider(),
                                      ),
                                      Expanded(
                                          child: CustomTextField(
                                        hasPassword: true,
                                        borderColor: Colors.transparent,
                                        validator: (value) => AppValidator
                                            .validateConfirmPassword(value,
                                                _passwordController.text),
                                        controller: _passwordConfirmController,
                                      )),
                                    ],
                                  ),
                                ),
                                40.verticalSpace,
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 75.sp),
                                  child: SizedBox(
                                    width: 150.w,
                                    child: state is EditProfileLoading
                                        ? const LoadingWidget(
                                            isCircle: true,
                                          )
                                        : ButtonWidget(
                                            text: "Save",
                                      onTap: () {
                                        if (_passwordController.text.isNotEmpty) {
                                          if (_formKey.currentState!.validate()) {
                                            // Check if the phone number has been changed
                                            if (_phoneController.text != instance<HiveHelper>().getUser()?.phone) {
                                              if (EditProfileCubit.get(context).profilePic.path.isEmpty) {
                                                EditProfileCubit.get(context).updateProfile(
                                                  context: context,
                                                  name: _nameController.text,
                                                  phone: _phoneController.text,
                                                  password: _passwordController.text,
                                                );
                                              } else {
                                                EditProfileCubit.get(context).updateProfile(
                                                  context: context,
                                                  name: _nameController.text,
                                                  phone: _phoneController.text,
                                                  password: _passwordController.text,
                                                  photo: EditProfileCubit.get(context).profilePic,
                                                );
                                              }
                                            } else {
                                              // Phone number has not been changed, so don't send it in the update request
                                              if (EditProfileCubit.get(context).profilePic.path.isEmpty) {
                                                EditProfileCubit.get(context).updateProfile(
                                                  context: context,
                                                  name: _nameController.text,
                                                  password: _passwordController.text,
                                                );
                                              } else {
                                                EditProfileCubit.get(context).updateProfile(
                                                  context: context,
                                                  name: _nameController.text,
                                                  password: _passwordController.text,
                                                  photo: EditProfileCubit.get(context).profilePic,
                                                );
                                              }
                                            }
                                          }
                                        } else {
                                          // Check if the phone number has been changed
                                          if (_phoneController.text != instance<HiveHelper>().getUser()?.phone) {
                                            if (EditProfileCubit.get(context).profilePic.path.isEmpty) {
                                              EditProfileCubit.get(context).updateProfile(
                                                context: context,
                                                name: _nameController.text,
                                                phone: _phoneController.text,
                                                password: _passwordController.text,
                                              );
                                            } else {
                                              EditProfileCubit.get(context).updateProfile(
                                                context: context,
                                                name: _nameController.text,
                                                phone: _phoneController.text,
                                                password: _passwordController.text,
                                                photo: EditProfileCubit.get(context).profilePic,
                                              );
                                            }
                                          } else {
                                            // Phone number has not been changed, so don't send it in the update request
                                            if (EditProfileCubit.get(context).profilePic.path.isEmpty) {
                                              EditProfileCubit.get(context).updateProfile(
                                                context: context,
                                                name: _nameController.text,
                                                password: _passwordController.text,
                                              );
                                            } else {
                                              EditProfileCubit.get(context).updateProfile(
                                                context: context,
                                                name: _nameController.text,
                                                password: _passwordController.text,
                                                photo: EditProfileCubit.get(context).profilePic,
                                              );
                                            }
                                          }
                                        }
                                      },
                                          ),
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                        100.verticalSpace,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

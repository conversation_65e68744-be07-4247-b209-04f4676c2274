import 'dart:io';

import 'package:ads_dv/features/sidebar/profile/data/repos/edit_profile_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/custom_widgets.dart';

part 'edit_profile_state.dart';

class EditProfileCubit extends Cubit<EditProfileState> {
  EditProfileCubit() : super(EditProfileInitial());
  static EditProfileCubit get(context) => BlocProvider.of(context);

  updateProfile(
      {String? password,
      String? name,
      String? phone,
      File? photo,
      required BuildContext context}) async {
    emit(EditProfileLoading());
    instance<EditProfileRepo>()
        .updateProfile(
      name: name,
      phone: phone,
      photo: photo,
      password: password,
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(EditProfileError(l));
      }, (r) async {
        if (r != null) {
          await instance<HiveHelper>().setUserModel(r);
          await instance<HiveHelper>().setToken(r.token ?? "");
        }
        showSuccessToast("Profile updated successfully");

        emit(EditProfileLoaded());
      });
    });
  }

  late File profilePic = File('');

  void updateStatus() {
    emit(UpdateStates());
  }
}

part of 'edit_profile_cubit.dart';

@immutable
abstract class EditProfileState {
  const EditProfileState();
  List<Object?> get props => [];
}

class EditProfileInitial extends EditProfileState {}

class EditProfileLoading extends EditProfileState {}

class EditProfileLoaded extends EditProfileState {
}

class EditProfileError extends EditProfileState {
  final Failure message;

  const EditProfileError(this.message);

  @override
  List<Object?> get props => [message];
}

final class UpdateStates extends EditProfileState {}

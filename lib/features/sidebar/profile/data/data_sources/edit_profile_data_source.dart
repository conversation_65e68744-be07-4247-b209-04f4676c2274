import 'dart:io';

import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../../../../auth/data/models/user.dart';

class EditProfileDataSource {
  Future<UserData?> updateProfile(
      {String? name, String? phone, String? password, File? photo}) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.updateToken,
              params: FormData.fromMap(
                {
                  if (name != null) 'name': name,
                  if (phone != null) 'phone': phone,
                  if (password != null) 'password': password,
                  if (photo != null)
                    "image": await MultipartFile.fromFile(photo.path)
                },
              ),
              options: Options(method: Method.post.name,headers: {
                "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
              },));
      return UserData.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }
}

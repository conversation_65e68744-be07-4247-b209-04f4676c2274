import 'dart:io';

import 'package:ads_dv/features/sidebar/profile/data/data_sources/edit_profile_data_source.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../auth/data/models/user.dart';

class EditProfileRepo {
  NetworkInfo networkInfo;
  EditProfileDataSource editProfileDataSource;

  EditProfileRepo(
      {required this.networkInfo, required this.editProfileDataSource});

  Future<Either<Failure, UserData?>> updateProfile(
      {String? name, String? phone, String? password, File? photo}) {
    return FailureHelper.instance(
        method: () async {
          return await editProfileDataSource.updateProfile(
              name: name, phone: phone, password: password, photo: photo);
        },
        networkInfo: networkInfo);
  }
}

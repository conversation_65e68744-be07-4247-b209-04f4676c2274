import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../widgets/appbar.dart';
import '../../../utils/res/common_utils.dart';

class TermsConditionsScreen extends StatelessWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: "Terms and conditions".tr,
        showBackButton: true,
        hasDrawer: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.sp),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
          
            children: [
              10.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '1. By using this App, you agree to be bound by these terms and conditions.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,

              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '2. The content of this App is for general information and use only. It is subject to change without notice.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '3. Your use of any information or materials on this App is entirely at your own risk, for which we shall not be liable.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '4. This App contains material which is owned by or licensed to us. This material includes, but is not limited to, the design, layout, look, appearance, and graphics.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '5. Unauthorized use of this App may give rise to a claim for damages and/or be a criminal offense.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '6. From time to time, this App may also include links to other websites. These links are provided for your convenience to provide further information. They do not signify that we endorse the website(s). We have no responsibility for the content of the linked website(s).'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '7. Your use of this App and any dispute arising out of such use of the website is subject to the laws of Egypt.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '8. All trademarks reproduced in this App, which is not the property of, or licensed to the operator, are acknowledged on the website.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '9. Unauthorized use of this App may give rise to a claim for damages and/or be a criminal offense.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                alignment: AlignmentDirectional.centerStart,
                text: '10. This App is subject to the laws of Egypt.'.tr,
                color: const Color(0xFF131534),
                fontSize: 16.sp,
                maxLines: 6,
                fontWeight: FontWeight.w700,
              ),
              20.verticalSpace,
              CustomText(
                text:
                "If you have any questions about this Privacy Policy, please contact us:".tr,
                color: const Color(0xFF131534),
                fontSize: 12.sp,
                textAlign: TextAlign.justify,

                fontFamily: 'Helvetica Now Display'.tr,
                fontWeight: FontWeight.w400,
                maxLines: 10,
              ),
              10.verticalSpace,
              Row(
                children: [
                  CustomText(
                    text:
                    "- By email:".tr,
                    color: const Color(0xFF131534),
                    fontSize: 12.sp,
                    textAlign: TextAlign.justify,

                    fontFamily: 'Helvetica Now Display'.tr,
                    fontWeight: FontWeight.w400,
                    maxLines: 10,
                  ),
                  10.horizontalSpace,
                  InkWell(
                    onTap: (){
                      CommonUtils.emailTo("<EMAIL>".tr);
                    },
                    child: CustomText(
                      text:
                      "<EMAIL>".tr,
                      color: Colors.blue,
                      fontSize: 12.sp,
                      textAlign: TextAlign.justify,

                      fontFamily: 'Helvetica Now Display'.tr,
                      fontWeight: FontWeight.w600,
                      maxLines: 10,
                    ),
                  ),
                ],
              ),
              5.verticalSpace,
              Row(
                children: [
                  Expanded(
                    child: CustomText(
                      text:
                      "- By visiting our website:".tr,
                      color: const Color(0xFF131534),
                      fontSize: 12.sp,
                      textAlign: TextAlign.justify,

                      fontFamily: 'Helvetica Now Display',
                      fontWeight: FontWeight.w400,
                      maxLines: 10,
                    ),
                  ),
                  Expanded(
                    child: InkWell(
                      onTap: (){
                        CommonUtils.launchInWebViewWithoutJavaScript("https://connect.devdigitalvibes.com/".tr);
                      },
                      child: CustomText(
                        text:
                        "https://connect.devdigitalvibes.com/".tr,
                        color: Colors.blue,
                        fontSize: 12.sp,
                        textAlign: TextAlign.justify,

                        fontFamily: 'Helvetica Now Display'.tr,
                        fontWeight: FontWeight.w600,
                        maxLines: 10,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../data/repos/wallet_repo.dart';

part 'renew_package_state.dart';

class RenewPackageCubit extends Cubit<RenewPackageState> {
  RenewPackageCubit() : super(RenewPackageInitial());
  static RenewPackageCubit get(context) => BlocProvider.of(context);

  renewPackage({required String packageId, required BuildContext context}) async {
    emit(RenewPackageLoading());
    instance<WalletRepo>().renewPackage(packageId: packageId).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(RenewPackageError(l));
      }, (r) async {
        showSuccessToast("Subscription Renewed successfully");

        emit(RenewPackageLoaded(r));
      });
    });
  }
}

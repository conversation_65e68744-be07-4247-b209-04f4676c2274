part of 'renew_package_cubit.dart';

@immutable
abstract class RenewPackageState {
  const RenewPackageState();
  List<Object?> get props => [];
}

class RenewPackageInitial extends RenewPackageState {}

class RenewPackageLoading extends RenewPackageState {}

class RenewPackageLoaded extends RenewPackageState {
  final bool data;

  const RenewPackageLoaded(this.data);

  @override
  List<Object?> get props => [data];

  RenewPackageLoaded copyWith({
    bool? data,
  }) {
    return RenewPackageLoaded(
      data ?? this.data,
    );
  }
}

class RenewPackageError extends RenewPackageState {
  final Failure message;

  const RenewPackageError(this.message);

  @override
  List<Object?> get props => [message];
}
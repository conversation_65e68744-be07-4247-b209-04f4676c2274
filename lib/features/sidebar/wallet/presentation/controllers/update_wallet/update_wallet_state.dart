part of 'update_wallet_cubit.dart';

@immutable
abstract class UpdateWalletState {
  const UpdateWalletState();
  List<Object?> get props => [];
}

class UpdateWalletInitial extends UpdateWalletState {}

class UpdateWalletLoading extends UpdateWalletState {}

class UpdateWalletLoaded extends UpdateWalletState {
  final bool data;

  const UpdateWalletLoaded(this.data);

  @override
  List<Object?> get props => [data];

  UpdateWalletLoaded copyWith({
    bool? data,
  }) {
    return UpdateWalletLoaded(
      data ?? this.data,
    );
  }
}

class UpdateWalletError extends UpdateWalletState {
  final Failure message;

  const UpdateWalletError(this.message);

  @override
  List<Object?> get props => [message];
}
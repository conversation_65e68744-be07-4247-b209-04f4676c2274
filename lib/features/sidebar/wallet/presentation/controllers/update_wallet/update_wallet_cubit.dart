import 'package:ads_dv/features/sidebar/wallet/data/repos/wallet_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../../utils/res/custom_widgets.dart';

part 'update_wallet_state.dart';

class UpdateWalletCubit extends Cubit<UpdateWalletState> {
  UpdateWalletCubit() : super(UpdateWalletInitial());

  static UpdateWalletCubit get(context) => BlocProvider.of(context);

  updateWallet({required String amount, required BuildContext context}) async {
    emit(UpdateWalletLoading());
    instance<WalletRepo>().updateWallet(amount: amount).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(UpdateWalletError(l));
      }, (r) async {
        showSuccessToast("Wallet Charged successfully");

        emit(UpdateWalletLoaded(r));
      });
    });
  }
}

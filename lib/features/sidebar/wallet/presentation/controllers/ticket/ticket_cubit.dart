import 'package:ads_dv/utils/di/injection.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../data/models/ticket_model.dart';
import '../../../data/repos/ticket_repo.dart';

part 'ticket_state.dart';

class TicketCubit extends Cubit<TicketState> {
  TicketCubit() : super(TicketInitial());

  Future<void> sendTicket({String? title, String? description}) async {
    emit(SenDTicketLoadingState());
    instance
        .get<TicketRepo>()
        .sendTicket(title: title, description: description)
        .then((value) => value.fold((l) {}, (r) async {
              await getTicketsHistory();
            }));
    emit(SenDTicketState());
  }

  Future<void> getTicketsHistory() async {
    emit(SenDTicketLoadingState());
    instance
        .get<TicketRepo>()
        .getTicketsHistory()
        .then((value) => value.fold((l) {}, (r) {
              emit(GetTicketsHistoryState(tickets: r));
            }));
  }
}

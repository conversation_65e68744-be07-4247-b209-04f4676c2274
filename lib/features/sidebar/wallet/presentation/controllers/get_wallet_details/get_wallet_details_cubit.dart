import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/wallet.dart';
import '../../../data/repos/wallet_repo.dart';

part 'get_wallet_details_state.dart';

class GetWalletDetailsCubit extends Cubit<GetWalletDetailsState> {
  GetWalletDetailsCubit() : super(GetWalletDetailsInitial());

  static GetWalletDetailsCubit get(context) => BlocProvider.of(context);

  Wallet? wallet;
  getWalletDetails({required BuildContext context}) async {
    emit(GetWalletDetailsLoading());
    instance<WalletRepo>().getWalletDetails().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetWalletDetailsError(l));
      }, (r) async {
        wallet = r;

        emit(GetWalletDetailsLoaded(r));
      });
    });
  }
}

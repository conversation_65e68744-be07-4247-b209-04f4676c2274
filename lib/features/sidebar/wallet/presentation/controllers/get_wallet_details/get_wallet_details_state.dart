part of 'get_wallet_details_cubit.dart';

@immutable
abstract class GetWalletDetailsState {
  const GetWalletDetailsState();
  List<Object?> get props => [];
}

class GetWalletDetailsInitial extends GetWalletDetailsState {}

class GetWalletDetailsLoading extends GetWalletDetailsState {}

class GetWalletDetailsLoaded extends GetWalletDetailsState {
  final Wallet data;

  const GetWalletDetailsLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetWalletDetailsLoaded copyWith({
    Wallet? data,
  }) {
    return GetWalletDetailsLoaded(
      data ?? this.data,
    );
  }
}

class GetWalletDetailsError extends GetWalletDetailsState {
  final Failure message;

  const GetWalletDetailsError(this.message);

  @override
  List<Object?> get props => [message];
}
import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/ticket/ticket_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/common_utils.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/custom_text_field.dart';
import '../../../../../widgets/svg_widget.dart';

class ComplaintsWidget extends StatefulWidget {
  const ComplaintsWidget({super.key});

  @override
  State<ComplaintsWidget> createState() => _ComplaintsWidgetState();
}

class _ComplaintsWidgetState extends State<ComplaintsWidget> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionsController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionsController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TicketCubit(),
      child: BlocListener<TicketCubit, TicketState>(
        listener: (context, state) {
          if (state is SenDTicketState) {
            _titleController.text = "";
            _descriptionsController.text = "";
          }
        },
        child: BlocBuilder<TicketCubit, TicketState>(
          builder: (context, state) {
            return SizedBox(
              height: 50.h,
              width: 50.w,
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(50.r),
                clipBehavior: Clip.antiAlias,
                child: InkWell(
                  onTap: () {
                    CommonUtils.showBottomDialog(
                      context,
                      Form(
                        key: _formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: EdgeInsets.all(24.sp),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  CustomText(
                                    text: 'Send Ticket'.tr,
                                    color: Constants.primaryTextColor,
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  InkWell(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Icon(
                                      Icons.close,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 24.sp),
                              child: CustomText(
                                text: 'title'.tr,
                                color: Constants.primaryTextColor,
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            10.verticalSpace,
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 24.sp),
                              child: CustomTextFormField(
                                borderRadius: 20.sp,
                                hintText: "title".tr,
                                controller: _titleController,
                                validator: (value) =>
                                    AppValidator.validateIdentity(
                                        value, context),
                              ),
                            ),
                            15.verticalSpace,
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 24.sp),
                              child: CustomText(
                                text: 'description'.tr,
                                color: Constants.primaryTextColor,
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            10.verticalSpace,
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 24.sp),
                              child: CustomTextFormField(
                                borderRadius: 20.sp,
                                hintText: "description".tr,
                                controller: _descriptionsController,
                                validator: (value) =>
                                    AppValidator.validateIdentity(
                                        value, context),
                              ),
                            ),
                            20.verticalSpace,
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 24.sp),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: InkWell(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Container(
                                        decoration: ShapeDecoration(
                                          color: Colors.grey.withOpacity(0.2),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 20.sp,
                                              horizontal: 14.sp),
                                          child: CustomText(
                                            text: 'Close'.tr,
                                            color: Constants.primaryTextColor,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w500,
                                            alignment:
                                                AlignmentDirectional.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  20.horizontalSpace,
                                  Expanded(
                                    child: state is SenDTicketLoadingState
                                        ? const LoadingWidget(
                                            isCircle: true,
                                          )
                                        : InkWell(
                                            onTap: () async {
                                              if (_formKey.currentState!
                                                  .validate()) {
                                                Navigator.of(context).pop();
                                                await instance
                                                    .get<TicketCubit>()
                                                    .sendTicket(
                                                      title:
                                                          _titleController.text,
                                                      description:
                                                          _descriptionsController
                                                              .text,
                                                    );
                                                // SendComplaintsCubit.get(context)
                                                //     .sendComplaints(
                                                //         title: _titleController
                                                //             .text,
                                                //         descriptions:
                                                //             _descriptionsController
                                                //                 .text,
                                                //         context: context);
                                              }
                                            },
                                            child: Container(
                                              decoration: ShapeDecoration(
                                                gradient: Constants.defGradient,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x33000000),
                                                    blurRadius: 30,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: -10,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 15.sp,
                                                    horizontal: 14.sp),
                                                child: CustomText(
                                                  text: 'Confirm'.tr,
                                                  color: Colors.white,
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w500,
                                                  alignment:
                                                      AlignmentDirectional
                                                          .center,
                                                ),
                                              ),
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                            ),
                            40.verticalSpace,
                          ],
                        ),
                      ),
                    );
                  },
                  child: CustomSvgWidget(
                    svg: AppAssets.support,
                    width: 50.w,
                    height: 50.h,
                    //    color: Colors.white,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

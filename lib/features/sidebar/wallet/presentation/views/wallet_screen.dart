import 'package:ads_dv/features/sidebar/payment/presentation/controllers/payment_controller/payment_cubit.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/controllers/transactions_controller/transaction_controller_cubit.dart';
import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/get_wallet_details/get_wallet_details_cubit.dart';
import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/renew_package/renew_package_cubit.dart';
import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/update_wallet/update_wallet_cubit.dart';
import 'package:ads_dv/utils/res/common_utils.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/media_query_config.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../utils/res/validations.dart';
import '../../../../../widgets/appbar.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../../../widgets/custom_text_field.dart';
import '../../../../../widgets/handle_error_widget.dart';
import '../../../../../widgets/svg_widget.dart';
import '../../../ad_accounts/presentation/views/widgets/account_hint_text.dart';

class WalletScreen extends StatefulWidget {
  final bool isWallet;

  const WalletScreen({super.key, required this.isWallet});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  final TextEditingController _amountController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  GetWalletDetailsCubit getWalletDetailsCubit = GetWalletDetailsCubit();

  @override
  void dispose() {
    _amountController.dispose();
    getWalletDetailsCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.isWallet
        ? MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) =>
                    GetTransactionsCubit()..getTransactions(context: context),
              ),
              BlocProvider(
                create: (context) =>
                    getWalletDetailsCubit..getWalletDetails(context: context),
              ),
              BlocProvider(
                create: (context) => PaymentCubit(),
              ),
              BlocProvider(
                create: (context) => UpdateWalletCubit(),
              ),
              BlocProvider(
                create: (context) => RenewPackageCubit(),
              ),
            ],
            child: BlocListener<PaymentCubit, PaymentState>(
              listener: (context, state) {
                if (state is PaymentLoaded) {
                  UpdateWalletCubit.get(context).updateWallet(
                      amount: _amountController.text, context: context);
                  _amountController.text = "";
                }
                // TODO: implement listener
              },
              child: BlocListener<UpdateWalletCubit, UpdateWalletState>(
                listener: (context1, state) {
                  if (state is UpdateWalletLoaded) {
                    getWalletDetailsCubit.getWalletDetails(context: context);
                  }
                  // TODO: implement listener
                },
                child: BlocListener<RenewPackageCubit, RenewPackageState>(
                  listener: (context2, state) {
                    if (state is RenewPackageLoaded) {
                      getWalletDetailsCubit.getWalletDetails(context: context);
                    }
                    // TODO: implement listener
                  },
                  child: BlocBuilder<RenewPackageCubit, RenewPackageState>(
                    builder: (context2, renewState) {
                      return Scaffold(
                        backgroundColor: Colors.white,
                        appBar: CustomAppBar(
                          height: 180.h,
                          logo: AppAssets.mainLogo,
                          actions: [
                            InkWell(
                              onTap: () {
                                Navigator.pushNamed(
                                    context, Routes.notifications);
                              },
                              child: CustomSvgWidget(
                                svg: AppAssets.notifications,
                                height: 30.h,
                                width: 30.h,
                              ),
                            )
                          ],
                          leading: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: InkWell(
                              onTap: () {
                                Constants.scaffoldKey.currentState!
                                    .openDrawer();
                              },
                              child: CustomSvgWidget(
                                svg: AppAssets.drawer,
                                height: 4.h,
                                width: 12.h,
                              ),
                            ),
                          ),
                        ),
                        body: BlocBuilder<GetWalletDetailsCubit,
                            GetWalletDetailsState>(
                          builder: (context, state) {
                            return state is GetWalletDetailsLoading ||
                                    renewState is RenewPackageLoading
                                ? const Center(
                                    child: LoadingWidget(
                                      isCircle: true,
                                    ),
                                  )
                                : state is GetWalletDetailsError
                                    ? Center(
                                        child: HandleErrorWidget(
                                            fun: () {
                                              getWalletDetailsCubit
                                                  .getWalletDetails(
                                                context: context,
                                              );
                                            },
                                            failure: state.message),
                                      )
                                    : state is GetWalletDetailsLoaded
                                        ? Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              if (instance<HiveHelper>()
                                                      .getUser()
                                                      ?.defaultAccountName !=
                                                  null)
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    20.verticalSpace,
                                                    AccountHintText(
                                                      isDefaultHint: true,
                                                      hint:
                                                          '${"Now you are using".tr} ${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}',
                                                    ),
                                                    20.verticalSpace,
                                                  ],
                                                )
                                              else
                                                const SizedBox(),
                                              50.verticalSpace,
                                              Padding(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 34.sp),
                                                child: Container(
                                                  decoration: ShapeDecoration(
                                                    color: Colors.white,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              23),
                                                    ),
                                                    shadows: const [
                                                      BoxShadow(
                                                        color:
                                                            Color(0x33000000),
                                                        blurRadius: 30,
                                                        offset: Offset(0, 0),
                                                        spreadRadius: -10,
                                                      )
                                                    ],
                                                  ),
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 34.sp,
                                                            vertical: 16.sp),
                                                    child: Column(
                                                      children: [
                                                        CustomSvgWidget(
                                                          svg:
                                                              AppAssets.wallet1,
                                                          height: 40.h,
                                                          width: 40.h,
                                                        ),
                                                        20.verticalSpace,
                                                        CustomText(
                                                          text: 'Total Balance'
                                                              .tr,
                                                          color: Constants
                                                              .darkColor,
                                                          fontSize: 14.sp,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          alignment:
                                                              AlignmentDirectional
                                                                  .center,
                                                        ),
                                                        20.verticalSpace,
                                                        CustomText(
                                                          text:
                                                              '${state.data.amount ?? "0"} ${state.data.currency ?? "AED"}',
                                                          color: Constants
                                                              .darkColor,
                                                          fontSize: 40.sp,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          alignment:
                                                              AlignmentDirectional
                                                                  .center,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              40.verticalSpace,
                                              BlocBuilder<PaymentCubit,
                                                  PaymentState>(
                                                builder:
                                                    (context1, paymentState) {
                                                  return InkWell(
                                                    onTap: () {
                                                      CommonUtils
                                                          .showBottomDialog(
                                                        context,
                                                        Form(
                                                          key: _formKey,
                                                          child: Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Padding(
                                                                padding:
                                                                    EdgeInsets
                                                                        .all(24
                                                                            .sp),
                                                                child: Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .spaceBetween,
                                                                  children: [
                                                                    CustomText(
                                                                      text: 'Recharge Wallet'
                                                                          .tr,
                                                                      color: Constants
                                                                          .primaryTextColor,
                                                                      fontSize:
                                                                          18.sp,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w400,
                                                                    ),
                                                                    InkWell(
                                                                      onTap:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                      child:
                                                                          const Icon(
                                                                        Icons
                                                                            .close,
                                                                        color: Colors
                                                                            .grey,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                              Padding(
                                                                padding: EdgeInsets
                                                                    .symmetric(
                                                                        horizontal:
                                                                            24.sp),
                                                                child:
                                                                    CustomText(
                                                                  text: 'Amount'
                                                                      .tr,
                                                                  color: Constants
                                                                      .primaryTextColor,
                                                                  fontSize:
                                                                      14.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                ),
                                                              ),
                                                              10.verticalSpace,
                                                              Padding(
                                                                padding: EdgeInsets
                                                                    .symmetric(
                                                                        horizontal:
                                                                            24.sp),
                                                                child: CustomTextFormField
                                                                    .withPrefixSVG(
                                                                  borderRadius:
                                                                      20.sp,
                                                                  svgURL:
                                                                      AppAssets
                                                                          .dollar,
                                                                  hintText:
                                                                      "Amount"
                                                                          .tr,
                                                                  controller:
                                                                      _amountController,
                                                                  validator: (value) =>
                                                                      AppValidator.validateIdentity(
                                                                          value,
                                                                          context),
                                                                ),
                                                              ),
                                                              20.verticalSpace,
                                                              Padding(
                                                                padding: EdgeInsets
                                                                    .symmetric(
                                                                        horizontal:
                                                                            24.sp),
                                                                child: Row(
                                                                  children: [
                                                                    Expanded(
                                                                      child:
                                                                          InkWell(
                                                                        onTap:
                                                                            () {
                                                                          Navigator.of(context)
                                                                              .pop();
                                                                        },
                                                                        child:
                                                                            Container(
                                                                          decoration:
                                                                              ShapeDecoration(
                                                                            color:
                                                                                Colors.grey.withOpacity(0.2),
                                                                            shape:
                                                                                RoundedRectangleBorder(
                                                                              borderRadius: BorderRadius.circular(10),
                                                                            ),
                                                                          ),
                                                                          child:
                                                                              Padding(
                                                                            padding:
                                                                                EdgeInsets.symmetric(vertical: 20.sp, horizontal: 14.sp),
                                                                            child:
                                                                                CustomText(
                                                                              text: 'Close'.tr,
                                                                              color: Constants.primaryTextColor,
                                                                              fontSize: 14.sp,
                                                                              fontWeight: FontWeight.w500,
                                                                              alignment: AlignmentDirectional.center,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    20.horizontalSpace,
                                                                    Expanded(
                                                                      child:
                                                                          InkWell(
                                                                        onTap:
                                                                            () {
                                                                          if (_formKey
                                                                              .currentState!
                                                                              .validate()) {
                                                                            Navigator.of(context).pop();
                                                                            PaymentCubit.get(context1).makePayment(
                                                                                amount: int.parse(_amountController.text),
                                                                                currency: "AED",
                                                                                context: context1);
                                                                          }
                                                                        },
                                                                        child:
                                                                            Container(
                                                                          decoration:
                                                                              ShapeDecoration(
                                                                            gradient:
                                                                                Constants.defGradient,
                                                                            shape:
                                                                                RoundedRectangleBorder(
                                                                              borderRadius: BorderRadius.circular(10),
                                                                            ),
                                                                            shadows: const [
                                                                              BoxShadow(
                                                                                color: Color(0x33000000),
                                                                                blurRadius: 30,
                                                                                offset: Offset(0, 0),
                                                                                spreadRadius: -10,
                                                                              )
                                                                            ],
                                                                          ),
                                                                          child:
                                                                              Padding(
                                                                            padding:
                                                                                EdgeInsets.symmetric(vertical: 15.sp, horizontal: 14.sp),
                                                                            child:
                                                                                CustomText(
                                                                              text: 'Confirm'.tr,
                                                                              color: Colors.white,
                                                                              fontSize: 14.sp,
                                                                              fontWeight: FontWeight.w500,
                                                                              alignment: AlignmentDirectional.center,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                              20.verticalSpace,
                                                            ],
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                    child: Container(
                                                      decoration:
                                                          ShapeDecoration(
                                                        gradient: Constants
                                                            .secGradient,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(44),
                                                        ),
                                                        shadows: const [
                                                          BoxShadow(
                                                            color: Color(
                                                                0x33000000),
                                                            blurRadius: 30,
                                                            offset:
                                                                Offset(0, 0),
                                                            spreadRadius: -10,
                                                          )
                                                        ],
                                                      ),
                                                      child: Padding(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                vertical: 10.sp,
                                                                horizontal:
                                                                    14.sp),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            CustomSvgWidget(
                                                              svg: AppAssets
                                                                  .refresh,
                                                              color:
                                                                  Colors.white,
                                                              height: 18.h,
                                                            ),
                                                            6.horizontalSpace,
                                                            CustomText(
                                                              text:
                                                                  'Top up wallet'
                                                                      .tr,
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 20.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              alignment:
                                                                  AlignmentDirectional
                                                                      .center,
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                              40.verticalSpace,
                                              BlocBuilder<GetTransactionsCubit,
                                                  GetTransactionsState>(
                                                builder: (context4, state) {
                                                  return (GetTransactionsCubit
                                                              .get(context4)
                                                          .trans
                                                          .isNotEmpty)
                                                      ? InkWell(
                                                          onTap: () {
                                                            showDialog(
                                                              context: context,
                                                              builder:
                                                                  (BuildContext
                                                                      context) {
                                                                return AlertDialog(
                                                                  title: Text(
                                                                      'Renew Subscription'
                                                                          .tr),
                                                                  content: Text(
                                                                      'Would you like to renew your current subscription?'
                                                                          .tr),
                                                                  actions: [
                                                                    TextButton(
                                                                      child:
                                                                          Text(
                                                                        'Cancel'
                                                                            .tr,
                                                                        style: const TextStyle(
                                                                            color:
                                                                                Constants.primaryTextColor),
                                                                      ),
                                                                      onPressed:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                    ),
                                                                    ElevatedButton(
                                                                      onPressed:
                                                                          () {
                                                                        // Implement your subscription renewal logic here
                                                                        RenewPackageCubit.get(context2)
                                                                            .renewPackage(
                                                                          packageId: GetTransactionsCubit.get(context4)
                                                                              .trans
                                                                              .first
                                                                              .packageId
                                                                              .toString(),
                                                                          context:
                                                                              context2,
                                                                        );
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                      style: ElevatedButton
                                                                          .styleFrom(
                                                                        // Set the gradient background using the `backgroundColor` property
                                                                        backgroundColor:
                                                                            Constants.primaryTextColor,
                                                                      ),
                                                                      child:
                                                                          Text(
                                                                        'Renew'
                                                                            .tr,
                                                                        style: const TextStyle(
                                                                            color:
                                                                                Colors.white),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                );
                                                              },
                                                            );
                                                          },
                                                          child: Container(
                                                            width: 60.h,
                                                            height: 30.h,
                                                            decoration:
                                                                ShapeDecoration(
                                                              gradient:
                                                                  const LinearGradient(
                                                                begin:
                                                                    Alignment(
                                                                        -0.87,
                                                                        -0.50),
                                                                end: Alignment(
                                                                    0.87, 0.5),
                                                                colors: [
                                                                  Color(
                                                                      0xFFFF006F),
                                                                  Color(
                                                                      0xFFF6BA00)
                                                                ],
                                                              ),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            29),
                                                              ),
                                                              shadows: const [
                                                                BoxShadow(
                                                                  color: Color(
                                                                      0x33000000),
                                                                  blurRadius:
                                                                      30,
                                                                  offset:
                                                                      Offset(
                                                                          0, 0),
                                                                  spreadRadius:
                                                                      -10,
                                                                )
                                                              ],
                                                            ),
                                                            child: CustomText(
                                                              text: 'Renew'.tr,
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 10.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              alignment:
                                                                  AlignmentDirectional
                                                                      .center,
                                                            ),
                                                          ),
                                                        )
                                                      : const SizedBox();
                                                },
                                              ),
                                            ],
                                          )
                                        : const SizedBox();
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          )
        : MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) =>
                    GetTransactionsCubit()..getTransactions(context: context),
              ),
              BlocProvider(
                create: (context) =>
                    getWalletDetailsCubit..getWalletDetails(context: context),
              ),
              BlocProvider(
                create: (context) => PaymentCubit(),
              ),
              BlocProvider(
                create: (context) => UpdateWalletCubit(),
              ),
              BlocProvider(
                create: (context) => RenewPackageCubit(),
              ),
            ],
            child: BlocListener<PaymentCubit, PaymentState>(
              listener: (context, state) {
                if (state is PaymentLoaded) {
                  UpdateWalletCubit.get(context).updateWallet(
                      amount: _amountController.text, context: context);
                  _amountController.text = "";
                }
                // TODO: implement listener
              },
              child: BlocListener<UpdateWalletCubit, UpdateWalletState>(
                listener: (context1, state) {
                  if (state is UpdateWalletLoaded) {
                    getWalletDetailsCubit.getWalletDetails(context: context);
                  }
                  // TODO: implement listener
                },
                child: BlocListener<RenewPackageCubit, RenewPackageState>(
                  listener: (context2, state) {
                    if (state is RenewPackageLoaded) {
                      getWalletDetailsCubit.getWalletDetails(context: context);
                    }
                    // TODO: implement listener
                  },
                  child: BlocBuilder<RenewPackageCubit, RenewPackageState>(
                    builder: (context2, renewState) {
                      return BlocBuilder<GetWalletDetailsCubit,
                          GetWalletDetailsState>(
                        builder: (context, state) {
                          return state is GetWalletDetailsLoading ||
                                  renewState is RenewPackageLoading
                              ? SizedBox(
                                  height:
                                      SizeConfig.screenHeight(context) * 0.6,
                                  child: const Center(
                                    child: LoadingWidget(
                                      isCircle: true,
                                    ),
                                  ),
                                )
                              : state is GetWalletDetailsError
                                  ? SizedBox(
                                      height: SizeConfig.screenHeight(context) *
                                          0.6,
                                      child: Center(
                                        child: HandleErrorWidget(
                                            fun: () {
                                              getWalletDetailsCubit
                                                  .getWalletDetails(
                                                context: context,
                                              );
                                            },
                                            failure: state.message),
                                      ),
                                    )
                                  : state is GetWalletDetailsLoaded
                                      ? Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            if (instance<HiveHelper>()
                                                    .getUser()
                                                    ?.defaultAccountName !=
                                                null)
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  20.verticalSpace,
                                                  AccountHintText(
                                                    isDefaultHint: true,
                                                    hint:
                                                        '${"Now you are using".tr} ${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}',
                                                  ),
                                                  20.verticalSpace,
                                                ],
                                              )
                                            else
                                              const SizedBox(),
                                            50.verticalSpace,
                                            Padding(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 34.sp),
                                              child: Container(
                                                decoration: ShapeDecoration(
                                                  color: Colors.white,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            23),
                                                  ),
                                                  shadows: const [
                                                    BoxShadow(
                                                      color: Color(0x33000000),
                                                      blurRadius: 30,
                                                      offset: Offset(0, 0),
                                                      spreadRadius: -10,
                                                    )
                                                  ],
                                                ),
                                                child: Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 34.sp,
                                                      vertical: 16.sp),
                                                  child: Column(
                                                    children: [
                                                      CustomSvgWidget(
                                                        svg: AppAssets.wallet1,
                                                        height: 40.h,
                                                        width: 40.h,
                                                      ),
                                                      20.verticalSpace,
                                                      CustomText(
                                                        text:
                                                            'Total Balance'.tr,
                                                        color:
                                                            Constants.darkColor,
                                                        fontSize: 14.sp,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        alignment:
                                                            AlignmentDirectional
                                                                .center,
                                                      ),
                                                      20.verticalSpace,
                                                      CustomText(
                                                        text:
                                                            '${state.data.amount ?? "0"} ${state.data.currency ?? "AED"}',
                                                        color:
                                                            Constants.darkColor,
                                                        fontSize: 40.sp,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        alignment:
                                                            AlignmentDirectional
                                                                .center,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            40.verticalSpace,
                                            BlocBuilder<PaymentCubit,
                                                PaymentState>(
                                              builder:
                                                  (context1, paymentState) {
                                                return InkWell(
                                                  onTap: () {
                                                    CommonUtils
                                                        .showBottomDialog(
                                                      context,
                                                      Form(
                                                        key: _formKey,
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  EdgeInsets
                                                                      .all(24
                                                                          .sp),
                                                              child: Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  CustomText(
                                                                    text:
                                                                        'Recharge Wallet'
                                                                            .tr,
                                                                    color: Constants
                                                                        .primaryTextColor,
                                                                    fontSize:
                                                                        18.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w400,
                                                                  ),
                                                                  InkWell(
                                                                    onTap: () {
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    },
                                                                    child:
                                                                        const Icon(
                                                                      Icons
                                                                          .close,
                                                                      color: Colors
                                                                          .grey,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                            Padding(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          24.sp),
                                                              child: CustomText(
                                                                text:
                                                                    'Amount'.tr,
                                                                color: Constants
                                                                    .primaryTextColor,
                                                                fontSize: 14.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                              ),
                                                            ),
                                                            10.verticalSpace,
                                                            Padding(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          24.sp),
                                                              child: CustomTextFormField
                                                                  .withPrefixSVG(
                                                                borderRadius:
                                                                    20.sp,
                                                                svgURL:
                                                                    AppAssets
                                                                        .dollar,
                                                                hintText:
                                                                    "Amount".tr,
                                                                controller:
                                                                    _amountController,
                                                                validator: (value) =>
                                                                    AppValidator
                                                                        .validateIdentity(
                                                                            value,
                                                                            context),
                                                              ),
                                                            ),
                                                            20.verticalSpace,
                                                            Padding(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          24.sp),
                                                              child: Row(
                                                                children: [
                                                                  Expanded(
                                                                    child:
                                                                        InkWell(
                                                                      onTap:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        decoration:
                                                                            ShapeDecoration(
                                                                          color: Colors
                                                                              .grey
                                                                              .withOpacity(0.2),
                                                                          shape:
                                                                              RoundedRectangleBorder(
                                                                            borderRadius:
                                                                                BorderRadius.circular(10),
                                                                          ),
                                                                        ),
                                                                        child:
                                                                            Padding(
                                                                          padding: EdgeInsets.symmetric(
                                                                              vertical: 20.sp,
                                                                              horizontal: 14.sp),
                                                                          child:
                                                                              CustomText(
                                                                            text:
                                                                                'Close'.tr,
                                                                            color:
                                                                                Constants.primaryTextColor,
                                                                            fontSize:
                                                                                14.sp,
                                                                            fontWeight:
                                                                                FontWeight.w500,
                                                                            alignment:
                                                                                AlignmentDirectional.center,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  20.horizontalSpace,
                                                                  Expanded(
                                                                    child:
                                                                        InkWell(
                                                                      onTap:
                                                                          () {
                                                                        if (_formKey
                                                                            .currentState!
                                                                            .validate()) {
                                                                          Navigator.of(context)
                                                                              .pop();
                                                                          PaymentCubit.get(context1).makePayment(
                                                                              amount: int.parse(_amountController.text),
                                                                              currency: "AED",
                                                                              context: context1);
                                                                        }
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        decoration:
                                                                            ShapeDecoration(
                                                                          gradient:
                                                                              Constants.defGradient,
                                                                          shape:
                                                                              RoundedRectangleBorder(
                                                                            borderRadius:
                                                                                BorderRadius.circular(10),
                                                                          ),
                                                                          shadows: const [
                                                                            BoxShadow(
                                                                              color: Color(0x33000000),
                                                                              blurRadius: 30,
                                                                              offset: Offset(0, 0),
                                                                              spreadRadius: -10,
                                                                            )
                                                                          ],
                                                                        ),
                                                                        child:
                                                                            Padding(
                                                                          padding: EdgeInsets.symmetric(
                                                                              vertical: 15.sp,
                                                                              horizontal: 14.sp),
                                                                          child:
                                                                              CustomText(
                                                                            text:
                                                                                'Confirm'.tr,
                                                                            color:
                                                                                Colors.white,
                                                                            fontSize:
                                                                                14.sp,
                                                                            fontWeight:
                                                                                FontWeight.w500,
                                                                            alignment:
                                                                                AlignmentDirectional.center,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                            20.verticalSpace,
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  child: Container(
                                                    decoration: ShapeDecoration(
                                                      gradient:
                                                          Constants.secGradient,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(44),
                                                      ),
                                                      shadows: const [
                                                        BoxShadow(
                                                          color:
                                                              Color(0x33000000),
                                                          blurRadius: 30,
                                                          offset: Offset(0, 0),
                                                          spreadRadius: -10,
                                                        )
                                                      ],
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 10.sp,
                                                              horizontal:
                                                                  14.sp),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          CustomSvgWidget(
                                                            svg: AppAssets
                                                                .refresh,
                                                            color: Colors.white,
                                                            height: 18.h,
                                                          ),
                                                          6.horizontalSpace,
                                                          CustomText(
                                                            text:
                                                                'Top up wallet'
                                                                    .tr,
                                                            color: Colors.white,
                                                            fontSize: 20.sp,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            alignment:
                                                                AlignmentDirectional
                                                                    .center,
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                            40.verticalSpace,
                                            BlocBuilder<GetTransactionsCubit,
                                                GetTransactionsState>(
                                              builder: (context4, state) {
                                                return GetTransactionsCubit.get(
                                                            context4)
                                                        .trans
                                                        .isNotEmpty
                                                    ? InkWell(
                                                        onTap: () {
                                                          showDialog(
                                                            context: context,
                                                            builder:
                                                                (BuildContext
                                                                    context) {
                                                              return AlertDialog(
                                                                title: Text(
                                                                    'Renew Subscription'
                                                                        .tr),
                                                                content: Text(
                                                                    'Would you like to renew your current subscription?'
                                                                        .tr),
                                                                actions: [
                                                                  TextButton(
                                                                    child:
                                                                        const Text(
                                                                      'Cancel',
                                                                      style: TextStyle(
                                                                          color:
                                                                              Constants.primaryTextColor),
                                                                    ),
                                                                    onPressed:
                                                                        () {
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    },
                                                                  ),
                                                                  ElevatedButton(
                                                                    onPressed:
                                                                        () {
                                                                      // Implement your subscription renewal logic here
                                                                      RenewPackageCubit.get(
                                                                              context2)
                                                                          .renewPackage(
                                                                        packageId: GetTransactionsCubit.get(context4)
                                                                            .trans
                                                                            .first
                                                                            .packageId
                                                                            .toString(),
                                                                        context:
                                                                            context2,
                                                                      );
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                    },
                                                                    style: ElevatedButton
                                                                        .styleFrom(
                                                                      // Set the gradient background using the `backgroundColor` property
                                                                      backgroundColor:
                                                                          Constants
                                                                              .primaryTextColor,
                                                                    ),
                                                                    child: Text(
                                                                      'Renew'
                                                                          .tr,
                                                                      style: const TextStyle(
                                                                          color:
                                                                              Colors.white),
                                                                    ),
                                                                  ),
                                                                ],
                                                              );
                                                            },
                                                          );
                                                        },
                                                        child: Container(
                                                          width: 60.h,
                                                          height: 30.h,
                                                          decoration:
                                                              ShapeDecoration(
                                                            gradient:
                                                                const LinearGradient(
                                                              begin: Alignment(
                                                                  -0.87, -0.50),
                                                              end: Alignment(
                                                                  0.87, 0.5),
                                                              colors: [
                                                                Color(
                                                                    0xFFFF006F),
                                                                Color(
                                                                    0xFFF6BA00)
                                                              ],
                                                            ),
                                                            shape:
                                                                RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          29),
                                                            ),
                                                            shadows: const [
                                                              BoxShadow(
                                                                color: Color(
                                                                    0x33000000),
                                                                blurRadius: 30,
                                                                offset: Offset(
                                                                    0, 0),
                                                                spreadRadius:
                                                                    -10,
                                                              )
                                                            ],
                                                          ),
                                                          child: CustomText(
                                                            text: 'Renew'.tr,
                                                            color: Colors.white,
                                                            fontSize: 10.sp,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            alignment:
                                                                AlignmentDirectional
                                                                    .center,
                                                          ),
                                                        ),
                                                      )
                                                    : const SizedBox();
                                              },
                                            ),
                                          ],
                                        )
                                      : const SizedBox();
                        },
                      );
                    },
                  ),
                ),
              ),
            ),
          );
  }
}

import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/ticket/ticket_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/appbar.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../widgets/svg_widget.dart';

class TicketsHistoryScreen extends StatefulWidget {
  const TicketsHistoryScreen({
    super.key,
  });

  @override
  State<TicketsHistoryScreen> createState() => _TicketsHistoryScreenState();
}

class _TicketsHistoryScreenState extends State<TicketsHistoryScreen> {
  bool showDescription = false;

  @override
  void initState() {
    Future.microtask(() async {
      await instance.get<TicketCubit>().getTicketsHistory();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        height: 180.h,
        logo: AppAssets.mainLogo,
        showBackButton: true,
        // actions: const [
        //   ComplaintsWidget(),
        // ],
        leading: Padding(
          padding: const EdgeInsets.all(8.0),
          child: InkWell(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: CustomSvgWidget(
              svg: AppAssets.back,
              height: 4.h,
              width: 12.h,
            ),
          ),
        ),
      ),
      body: BlocBuilder<TicketCubit, TicketState>(
        bloc: instance.get<TicketCubit>(),
        builder: (context, state) {
          return Card(
            color: Constants.backgroundCardsGray,
            margin:
                const EdgeInsets.symmetric(horizontal: 15.0, vertical: 20.0),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0)),
            child: state is GetTicketsHistoryState
                ? ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: state.tickets?.length ?? 0,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 8.0),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () {
                                setState(() {
                                  showDescription = !showDescription;
                                  state.tickets?[index].showDescription =
                                      !showDescription;
                                });
                              },
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const CustomText(
                                    text: 'Tickets History',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold,
                                    color: Constants.darkColor,
                                    // bgColor: Constants.mainColor,
                                  ),
                                  20.verticalSpace,
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20.0),
                                    child: Text(
                                      state.tickets?[index].createdAt
                                              ?.split('T')[0]
                                              .toString() ??
                                          "",
                                      style: const TextStyle(
                                          fontSize: 12.0,
                                          fontWeight: FontWeight.w300),
                                    ),
                                  ),
                                  Card(
                                    color: Constants.backgroundCardsGray,
                                    margin:
                                        const EdgeInsets.symmetric(horizontal: 8.0),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(15.0)),
                                    child: Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            state.tickets?[index].title ?? "",
                                            style: const TextStyle(
                                                fontSize: 16.0,
                                                fontWeight: FontWeight.w200),
                                          ),
                                          Column(
                                            children: [
                                              state.tickets?[index].status ==
                                                      "completed"
                                                  ? Image.asset(
                                                      'assets/images/Approval.png')
                                                  : state.tickets?[index]
                                                              .status ==
                                                          "open"
                                                      ? Image.asset(
                                                          'assets/images/inprogressHolder.png')
                                                      : Image.asset(
                                                          'assets/images/Stopwatch.png'),
                                              Text(
                                                state.tickets?[index].status ??
                                                    "",
                                                style: const TextStyle(
                                                    fontSize: 10.0,
                                                    fontWeight:
                                                        FontWeight.w100),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            state.tickets?[index].showDescription == true
                                ? SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    // height: 50.0.h,
                                    child: Card(
                                      color: Constants.backgroundCardsGray,
                                      // margin: const EdgeInsets.symmetric(
                                      //                             vertical: 10.0, horizontal: 8.0),
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(15.0)),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 15.0, horizontal: 10.0),
                                        child: Text(
                                            state.tickets?[index].description ??
                                                ""),
                                      ),
                                    ),
                                  )
                                : const SizedBox(),
                          ],
                        ),
                      );
                    })
                : const Center(
                    child: CircularProgressIndicator(),
                  ),
          );
        },
      ),
    );
  }
}

import 'package:ads_dv/features/sidebar/wallet/data/data_sources/wallet_data_source.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../models/wallet.dart';

class WalletRepo {
  NetworkInfo networkInfo;
  WalletDataSource walletDataSource;

  WalletRepo({required this.networkInfo, required this.walletDataSource});

  Future<Either<Failure, bool>> updateWallet({required String amount}) {
    return FailureHelper.instance(
        method: () async {
          return await walletDataSource.updateWallet(amount: amount);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, bool>> renewPackage({required String packageId}) {
    return FailureHelper.instance(
        method: () async {
          return await walletDataSource.renewPackage(packageId: packageId);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, Wallet>> getWalletDetails() {
    return FailureHelper.instance(
        method: () async {
          return await walletDataSource.getWalletDetails();
        },
        networkInfo: networkInfo);
  }
}

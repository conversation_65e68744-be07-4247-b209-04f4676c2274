import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../data_sources/tickets_data_source.dart';
import '../models/ticket_model.dart';

class TicketRepo {
  NetworkInfo networkInfo;
  TicketDataSource ticketDataSource;

  TicketRepo({required this.networkInfo, required this.ticketDataSource});

  Future<Either<Failure, String>> sendTicket(
      {String? title, String? description}) {
    return FailureHelper.instance(
        method: () async {
          return await ticketDataSource.sendTicket(
              title: title, description: description);
        },
        networkInfo: networkInfo);
  }

  Future<Either<Failure, List<TicketModel>>> getTicketsHistory() {
    return FailureHelper.instance(
        method: () async {
          return await ticketDataSource.getTicketsHistory();
        },
        networkInfo: networkInfo);
  }
}

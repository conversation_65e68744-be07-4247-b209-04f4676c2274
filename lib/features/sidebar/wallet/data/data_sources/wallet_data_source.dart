import 'package:ads_dv/features/sidebar/wallet/data/models/wallet.dart';
import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';

class WalletDataSource {
  Future<Wallet> getWalletDetails() async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.wallet,
              options: Options(
                method: Method.get.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return Wallet.fromJson(response['data']);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> updateWallet(
      {required String amount}) async {
    try {
      Map<String, dynamic> response =
      await instance<NetworkCall>().request(EndPoints.wallet,
          params: FormData.fromMap(
            {
             'amount': amount,

            },
          ),
          options: Options(method: Method.post.name,headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },));
      return true;
    } catch (error) {
      rethrow;
    }
  }
  Future<bool> renewPackage(
      {required String packageId}) async {
    try {
      Map<String, dynamic> response =
      await instance<NetworkCall>().request(EndPoints.renewPackage,
          params: FormData.fromMap(
            {
              'package_id': packageId,

            },
          ),
          options: Options(method: Method.post.name,headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },));
      return true;
    } catch (error) {
      rethrow;
    }
  }
}

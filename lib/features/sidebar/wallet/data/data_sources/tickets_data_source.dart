import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../models/ticket_model.dart';

class TicketDataSource {
  Future<String> sendTicket({String? title, String? description}) async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request(EndPoints.sendTicket,
              params: {
                "title": title,
                "description": description,
              },
              options: Options(
                method: Method.post.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      return response['message'];
    } catch (error) {
      rethrow;
    }
  }

  Future<List<TicketModel>> getTicketsHistory() async {
    try {
      Map<String, dynamic> response =
          await instance<NetworkCall>().request("${EndPoints.sendTicket}/",
              // params: {
              //   "title": title,
              //   "description": description,
              // },
              options: Options(
                method: Method.get.name,
                headers: {
                  "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
                },
              ));
      List<dynamic> data = response['result'];
      List<TicketModel> tickets =
          data.map((ticket) => TicketModel.fromJson(ticket)).toList();
      return tickets;
    } catch (error) {
      rethrow;
    }
  }
}

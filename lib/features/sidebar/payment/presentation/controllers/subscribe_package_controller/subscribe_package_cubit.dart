import 'package:ads_dv/features/sidebar/payment/data/repos/payment_repo.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';

part 'subscribe_package_state.dart';

class SubscribePackageCubit extends Cubit<SubscribePackageState> {
  SubscribePackageCubit() : super(SubscribePackageInitial());

  static SubscribePackageCubit get(context) => BlocProvider.of(context);

  subscribePackage(
      {required String transactionId,
      required String packageId,
        required String amount,
        required String currency,
        required String transactionDate,
        required String invoiceUrl,
        required BuildContext context}) async {
    emit(SubscribePackageLoading());

    instance<PaymentRepo>()
        .subscribePackage(
      transactionId: transactionId,
      amount: amount,
      packageId: packageId,
      currency: currency,
      transactionDate: transactionDate,
      invoiceUrl: invoiceUrl
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(SubscribePackageError(l));
      }, (r) {
        showSuccessToast("Package Subscribed Successfully");
        emit(SubscribePackageLoaded());
      });
    });
  }
}

part of 'subscribe_package_cubit.dart';

@immutable
abstract class SubscribePackageState {
  const SubscribePackageState();
  List<Object?> get props => [];
}

class SubscribePackageInitial extends SubscribePackageState {}

class SubscribePackageLoading extends SubscribePackageState {}

class SubscribePackageLoaded extends SubscribePackageState {
}

class SubscribePackageError extends SubscribePackageState {
  final Failure message;

  const SubscribePackageError(this.message);

  @override
  List<Object?> get props => [message];
}

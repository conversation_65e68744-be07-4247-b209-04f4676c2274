part of 'packages_cubit.dart';

@immutable
abstract class GetPackagesState {
  const GetPackagesState();
  List<Object?> get props => [];
}

class GetPackagesInitial extends GetPackagesState {}

class GetPackagesLoading extends GetPackagesState {}

class GetPackagesLoaded extends GetPackagesState {
  final Package data;

  const GetPackagesLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetPackagesLoaded copyWith({
    Package? data,
  }) {
    return GetPackagesLoaded(
      data ?? this.data,
    );
  }
}

class GetPackagesError extends GetPackagesState {
  final Failure message;

  const GetPackagesError(this.message);

  @override
  List<Object?> get props => [message];
}
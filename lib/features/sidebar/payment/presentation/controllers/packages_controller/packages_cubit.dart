import 'package:ads_dv/features/sidebar/payment/data/models/package.dart';
import 'package:ads_dv/features/sidebar/payment/data/repos/payment_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';

part 'packages_state.dart';

class GetPackagesCubit extends Cubit<GetPackagesState> {
  GetPackagesCubit() : super(GetPackagesInitial());

  static GetPackagesCubit get(context) => BlocProvider.of(context);


  getPackages({
    required BuildContext context,
  }) async {
    emit(GetPackagesLoading());
    instance<PaymentRepo>().getPackages().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetPackagesError(l));
      }, (r) {
        emit(GetPackagesLoaded(r));
      });
    });
  }
}
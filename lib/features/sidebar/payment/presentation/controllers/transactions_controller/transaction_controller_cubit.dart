import 'package:ads_dv/features/sidebar/payment/data/repos/payment_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/models/transaction.dart';

part 'transaction_controller_state.dart';

class GetTransactionsCubit extends Cubit<GetTransactionsState> {
  GetTransactionsCubit() : super(GetTransactionsInitial());

  static GetTransactionsCubit get(context) => BlocProvider.of(context);

  List<Transaction> trans =[ ];


  getTransactions({
    required BuildContext context,
  }) async {
    emit(GetTransactionsStateLoading());
    instance<PaymentRepo>().getTransactions().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetTransactionsStateError(l));
      }, (r) {
        trans = r;
        emit(GetTransactionsStateLoaded(r));
      });
    });
  }
}
part of 'transaction_controller_cubit.dart';

@immutable
abstract class GetTransactionsState {
  const GetTransactionsState();
  List<Object?> get props => [];
}

class GetTransactionsInitial extends GetTransactionsState {}

class GetTransactionsStateLoading extends GetTransactionsState {}

class GetTransactionsStateLoaded extends GetTransactionsState {
  final List<Transaction> data;

  const GetTransactionsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetTransactionsStateLoaded copyWith({
    List<Transaction>? data,
  }) {
    return GetTransactionsStateLoaded(
      data ?? this.data,
    );
  }
}

class GetTransactionsStateError extends GetTransactionsState {
  final Failure message;

  const GetTransactionsStateError(this.message);

  @override
  List<Object?> get props => [message];
}


part of 'payment_cubit.dart';

@immutable
sealed class PaymentState {}

class PaymentInitial extends PaymentState {}

class ChangePaymentTab extends PaymentState {}

class ChangeIndicatorIndex extends PaymentState {}
class SubscriptionViewState extends PaymentState {}
class SuccessViewState extends PaymentState {}

class PaymentLoading extends PaymentState {}

class PaymentLoaded extends PaymentState {
  final PaymentResponse data;

  PaymentLoaded(this.data);

  @override
  List<Object?> get props => [data];

  PaymentLoaded copyWith({
    PaymentResponse? data,
  }) {
    return PaymentLoaded(
      data ?? this.data,
    );
  }
}

class PaymentError extends PaymentState {}

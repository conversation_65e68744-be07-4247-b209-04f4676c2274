import 'package:ads_dv/features/sidebar/payment/data/models/payment_response.dart';
import 'package:ads_dv/features/sidebar/payment/data/repos/payment_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../../utils/res/router/routes.dart';

part 'payment_state.dart';

class PaymentCubit extends Cubit<PaymentState> {
  PaymentCubit() : super(PaymentInitial());

  static PaymentCubit get(context) => BlocProvider.of(context);

  int planIndex = 0;
  int selectedTab = 0;
  bool isSuccess = false;

  void changePaymentTabIndex(int index) {
    selectedTab = index;
    emit(ChangePaymentTab());
  }

  void changeIndicatorIndex(int index) {
    planIndex = index;
    emit(ChangeIndicatorIndex());
  }

  void changeToSuccessView(BuildContext context) {
    isSuccess = true;
    emit(SuccessViewState());
  }

  void moveFromToSuccessView(BuildContext context) {
    isSuccess = false;
    emit(SuccessViewState());
    Navigator.of(context).pushNamedAndRemoveUntil(Routes.home, (route) => false);
  }


  makePayment(
      {required int amount,
      required String currency,
      required BuildContext context}) async {
    emit(PaymentLoading());

    print("isloading");
    instance<PaymentRepo>()
        .makePayment(amount: amount, currency: currency)
        .then((value) {
      value.fold((l) {
        StripeException stripeException = const StripeException(
          error: LocalizedErrorMessage(
            code: FailureCode.Canceled,
            localizedMessage: 'The payment flow has been canceled',
            message: 'The payment flow has been canceled',
            stripeErrorCode: null,
            declineCode: null,
            type: null,
          ),
        );

        String localizedMessage = stripeException.error.localizedMessage ?? "";
        List<String> splitResult = localizedMessage.split(": ");

// Check if the split result has at least two elements
        String extractedMessage =
            splitResult.length >= 2 ? splitResult[1] : localizedMessage;
        print(extractedMessage); // Output: The payment_controller flow has been canceled
        FailureHelper.instance.handleStripeFailures(extractedMessage, context);
        emit(PaymentError());
      }, (r) {

        emit(PaymentLoaded(r));
        print("transactionID${r.amount}");
      });
    });
  }
}

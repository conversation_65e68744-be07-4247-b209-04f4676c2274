import 'package:ads_dv/features/onboarding/presentation/views/widgets/dot_indicator.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/controllers/packages_controller/packages_cubit.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/controllers/transactions_controller/transaction_controller_cubit.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/widgets/subscription/subscription_widget.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/widgets/subscription/success_widget.dart';
import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/renew_package/renew_package_cubit.dart';
import 'package:ads_dv/features/sidebar/wallet/presentation/views/wallet_screen.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../widgets/appbar.dart';
import '../../../../../widgets/handle_error_widget.dart';
import '../../../../create_campaigns/presentation/views/widgets/create_campaign/new_campaign/tabs_widget.dart';
import '../controllers/payment_controller/payment_cubit.dart';
import '../widgets/plans/plans_widget.dart';

class PaymentScreen extends StatelessWidget {
  const PaymentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => PaymentCubit(),
        ),
        BlocProvider(
          create: (context) => RenewPackageCubit(),
        ),
        BlocProvider(
          create: (context) =>
          GetTransactionsCubit()
            ..getTransactions(context: context),
        ),
        BlocProvider(
          create: (context) =>
          GetPackagesCubit()
            ..getPackages(context: context),
        ),
      ],
      child: BlocListener<RenewPackageCubit, RenewPackageState>(
        listener: (context, state) {
          if (state is RenewPackageLoading) {
            GetTransactionsCubit.get(context).getTransactions(context: context);
          }
          // TODO: implement listener
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: CustomAppBar(
            title: "packages options".tr,
            showBackButton: true,
            hasDrawer: true,
          ),
          body: BlocBuilder<PaymentCubit, PaymentState>(
            builder: (context, state) {
              return BlocBuilder<GetTransactionsCubit, GetTransactionsState>(
                builder: (context1, transState) {
                  return transState is GetTransactionsStateLoading
                      ? const LoadingWidget(
                    isCircle: true,
                  )
                      : transState is GetTransactionsStateError
                      ? HandleErrorWidget(
                      fun: () {
                        GetTransactionsCubit.get(context1)
                            .getTransactions(
                          context: context,
                        );
                      },
                      failure: transState.message)
                      : transState is GetTransactionsStateLoaded
                      ? Padding(
                    padding: EdgeInsets.all(24.sp),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          20.verticalSpace,
                          DecoratedContainer(
                            radius: 40,
                            height: 50.h,
                            color: Colors.white,
                            boxShadow: const BoxShadow(
                              color: Color(0x3D000000),
                              blurRadius: 13.93,
                              offset: Offset(0, 0),
                              spreadRadius: -3.80,
                            ),
                            widget: transState.data.isNotEmpty
                                ? TabsWidget(
                              fontSize:
                              transState.data.isNotEmpty
                                  ? 14
                                  : 20,
                              newObject:
                              transState.data.isEmpty
                                  ? 'packages'.tr
                                  : 'package details'.tr,
                              existObject: 'Wallet'.tr,
                              thirdObject: 'packages'.tr,
                              selectedTab:
                              PaymentCubit
                                  .get(context)
                                  .selectedTab,
                              onTabChanged: (tab) {
                                if (PaymentCubit
                                    .get(
                                    context)
                                    .selectedTab !=
                                    tab) {
                                  PaymentCubit.get(context)
                                      .changePaymentTabIndex(
                                      tab);
                                }
                              },
                              radius: 40,
                            )
                                : TabsWidget(
                              fontSize: 20,
                              newObject:
                              transState.data.isEmpty
                                  ? 'Plans'.tr
                                  : 'buy package'.tr,
                              existObject: 'Wallet'.tr,
                              selectedTab:
                              PaymentCubit
                                  .get(context)
                                  .selectedTab,
                              onTabChanged: (tab) {
                                if (PaymentCubit
                                    .get(
                                    context)
                                    .selectedTab !=
                                    tab) {
                                  PaymentCubit.get(context)
                                      .changePaymentTabIndex(
                                      tab);
                                }
                              },
                              radius: 40,
                            ),
                          ),
                          40.verticalSpace,
                          (PaymentCubit
                              .get(context)
                              .selectedTab ==
                              0)
                              ? PaymentCubit
                              .get(context)
                              .isSuccess
                              ? SuccessWidget(
                            context: context,
                            getTransactionsCubit:
                            GetTransactionsCubit
                                .get(context1),
                            paymentCubit:
                            PaymentCubit.get(
                                context),
                          )
                              : transState.data.isEmpty
                              ? BlocBuilder<
                              GetPackagesCubit,
                              GetPackagesState>(
                            builder: (context,
                                packagesState) {
                              if (packagesState
                              is GetPackagesLoading) {
                                return SizedBox(
                                  height: 400.h,
                                  child:
                                  const Center(
                                    child:
                                    LoadingWidget(
                                      isCircle:
                                      true,
                                    ),
                                  ),
                                );
                              } else {
                                return packagesState
                                is GetPackagesError
                                    ? SizedBox(
                                  height:
                                  400.h,
                                  child:
                                  Center(
                                    child: HandleErrorWidget(
                                        fun: () {
                                          GetPackagesCubit.get(context)
                                              .getPackages(
                                            context: context,
                                          );
                                        },
                                        failure: packagesState.message),
                                  ),
                                )
                                    : packagesState
                                is GetPackagesLoaded
                                    ? Column(
                                  children: [
                                    PlansWidget(
                                      getTransactionsCubit:
                                      GetTransactionsCubit.get(context1),
                                      packages:
                                      packagesState.data.adsPackages ?? [],
                                      planIndex:
                                      PaymentCubit
                                          .get(context)
                                          .planIndex,
                                      paymentCubit:
                                      PaymentCubit.get(context),
                                      ctx:
                                      context,
                                      state:
                                      state,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      children: [
                                        ...List.generate(
                                          packagesState.data.adsPackages!
                                              .length,
                                              (index) =>
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 4),
                                                child: DotIndicator(
                                                  isActive: index ==
                                                      PaymentCubit
                                                          .get(context)
                                                          .planIndex,
                                                ),
                                              ),
                                        ),
                                      ],
                                    ),
                                  ],
                                )
                                    : const SizedBox();
                              }
                            },
                          )
                              : SubscriptionWidget(
                            transaction:
                            transState.data,
                          )
                              : (PaymentCubit
                              .get(context)
                              .selectedTab ==
                              1)
                              ? const WalletScreen(
                            isWallet: false,
                          )
                              : BlocBuilder<GetPackagesCubit,
                              GetPackagesState>(
                            builder: (context,
                                packagesState) {
                              if (packagesState
                              is GetPackagesLoading) {
                                return SizedBox(
                                  height: 400.h,
                                  child: const Center(
                                    child:
                                    LoadingWidget(
                                      isCircle: true,
                                    ),
                                  ),
                                );
                              } else {
                                return packagesState
                                is GetPackagesError
                                    ? SizedBox(
                                  height: 400.h,
                                  child: Center(
                                    child: HandleErrorWidget(
                                        fun: () {
                                          GetPackagesCubit.get(context)
                                              .getPackages(
                                            context:
                                            context,
                                          );
                                        },
                                        failure: packagesState.message),
                                  ),
                                )
                                    : packagesState
                                is GetPackagesLoaded
                                    ? Column(
                                  children: [
                                    PlansWidget(
                                      getTransactionsCubit:
                                      GetTransactionsCubit.get(context1),
                                      packages:
                                      packagesState.data.adsPackages ??
                                          [],
                                      planIndex:
                                      PaymentCubit
                                          .get(context)
                                          .planIndex,
                                      paymentCubit:
                                      PaymentCubit.get(context),
                                      ctx:
                                      context,
                                      state:
                                      state,
                                    ),
                                    // SizedBox(height: 20.0.h),
                                    Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      children: [
                                        ...List
                                            .generate(
                                          packagesState.data.adsPackages!
                                              .length,
                                              (index) =>
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 4),
                                                child: DotIndicator(
                                                  isActive: index ==
                                                      PaymentCubit
                                                          .get(context)
                                                          .planIndex,
                                                ),
                                              ),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(height: 40.0.h),
                                  ],
                                )
                                    : const SizedBox();
                              }
                            },
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                        ],
                      ),
                    ),
                  )
                      : const SizedBox();
                },
              );
            },
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/svg_widget.dart';

class WalletWidget extends StatelessWidget {
  const WalletWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        50.verticalSpace,
        CustomSvgWidget(
          svg: AppAssets.wallet1,
          height: 40.h,
          width: 40.h,
        ),
        20.verticalSpace,
        CustomText(
          text: 'Total Balance',
          color: Constants.darkColor,
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
          alignment: AlignmentDirectional.center,
        ),
        20.verticalSpace,
        CustomText(
          text: '0.00 AED',
          color: Constants.darkColor,
          fontSize: 40.sp,
          fontWeight: FontWeight.w500,
          alignment: AlignmentDirectional.center,
        ),
        60.verticalSpace,
        Container(
          decoration: ShapeDecoration(
            gradient: Constants.secGradient,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(44),
            ),
            shadows: const [
              BoxShadow(
                color: Color(0x33000000),
                blurRadius: 30,
                offset: Offset(0, 0),
                spreadRadius: -10,
              )
            ],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: 10.sp, horizontal: 14.sp),
            child: Row(
              mainAxisAlignment:
              MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomSvgWidget(
                  svg: AppAssets.refresh,
                  color: Colors.white,
                  height: 18.h,
                ),
                6.horizontalSpace,
                CustomText(
                  text: 'Top up wallet',
                  color: Colors.white,
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w500,
                  alignment: AlignmentDirectional.center,
                )
              ],
            ),
          ),
        ),
        40.verticalSpace,
        Container(
          width: 60.h,
          height: 30.h,
          decoration: ShapeDecoration(
            gradient: const LinearGradient(
              begin: Alignment(-0.87, -0.50),
              end: Alignment(0.87, 0.5),
              colors: [
                Color(0xFFFF006F),
                Color(0xFFF6BA00)
              ],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(29),
            ),
            shadows: const [
              BoxShadow(
                color: Color(0x33000000),
                blurRadius: 30,
                offset: Offset(0, 0),
                spreadRadius: -10,
              )
            ],
          ),
          child: CustomText(
            text: 'Renew',
            color: Colors.white,
            fontSize: 10.sp,
            fontWeight: FontWeight.w500,
            alignment: AlignmentDirectional.center,
          ),
        ),
      ],
    );
  }
}

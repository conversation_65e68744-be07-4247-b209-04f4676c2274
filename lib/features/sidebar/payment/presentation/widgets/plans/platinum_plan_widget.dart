import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/loading_widget.dart';
import '../../controllers/payment_controller/payment_cubit.dart';

class PlatinumPlanWidget extends StatelessWidget {
  PaymentCubit paymentCubit;
  BuildContext ctx;
  PaymentState state;
  PlatinumPlanWidget({super.key, required this.paymentCubit,required this.ctx,required this.state});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 100.h,
          decoration: const ShapeDecoration(
            color: Color(0xFF0977FF),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25.43),
                topRight: Radius.circular(25.43),
              ),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: CustomText(
              alignment: AlignmentDirectional.topCenter,
              text: 'Most Popular',
              color: Colors.white,
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          top: 30,
          child: Container(
            width: double.infinity,
            height: 100.h,
            decoration: const ShapeDecoration(
              gradient: Constants.defGradient,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25.43),
                  topRight: Radius.circular(25.43),
                ),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.sp),
              child: Column(
                children: [
                  const CustomText(
                    text: 'Platinum',
                    color: Colors.white,
                    alignment: AlignmentDirectional.center,
                    fontSize: 33.91,
                    fontWeight: FontWeight.w400,
                  ),
                  10.verticalSpace,
                  const CustomText(
                    text: 'PER MONTH',
                    color: Colors.white,
                    fontSize: 12,
                    alignment: AlignmentDirectional.center,
                    fontFamily: 'IBM Plex Sans Devanagari',
                    fontWeight: FontWeight.w300,
                  )
                ],
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          left: 0,
          top: 130.0,
          child: Container(
            width: double.infinity,
            //height: 200.34,
            decoration: const ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(25.43),
                  bottomRight: Radius.circular(25.43),
                ),
              ),
              shadows: [
                BoxShadow(
                  color: Color(0x33000000),
                  blurRadius: 30,
                  offset: Offset(0, 4),
                  spreadRadius: -6,
                )
              ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                  vertical: 16.sp, horizontal: 16.sp),
              child: Column(
                children: [
                  const CustomText(
                    alignment: AlignmentDirectional.center,
                    text: '1200 AED',
                    color: Constants.darkColor,
                    fontSize: 33.91,
                    fontWeight: FontWeight.w700,
                  ),
                  20.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.check,
                        color: Constants.darkColor,
                        size: 20,
                      ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Lorem Ipsum is simply dummy text.',
                        color: Constants.darkColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                  15.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.check,
                        color: Constants.darkColor,
                        size: 20,
                      ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Lorem Ipsum is simply dummy text.',
                        color: Constants.darkColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                  15.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.check,
                        color: Constants.darkColor,
                        size: 20,
                      ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Lorem Ipsum is simply dummy text.',
                        color: Constants.darkColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                  15.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.check,
                        color: Constants.darkColor,
                        size: 20,
                      ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Lorem Ipsum is simply dummy text.',
                        color: Constants.darkColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                  15.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.check,
                        color: Constants.darkColor,
                        size: 20,
                      ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Lorem Ipsum is simply dummy text.',
                        color: Constants.darkColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                  15.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.check,
                        color: Constants.darkColor,
                        size: 20,
                      ),
                      10.horizontalSpace,
                      CustomText(
                        text: 'Lorem Ipsum is simply dummy text.',
                        color: Constants.darkColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                  20.verticalSpace,
                  state is PaymentLoading
                      ? const LoadingWidget(
                    isCircle: true,
                  )
                      : InkWell(
                    onTap: () {
                      paymentCubit.makePayment(
                          amount: 1200,
                          currency: "AED",
                          context: ctx);
                    },
                    child: Container(
                      width: 127.99,
                      height: 32.21,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: const BorderSide(
                              width: 1, color: Color(0xFF0B0F26)),
                          borderRadius: BorderRadius.circular(44.08),
                        ),
                      ),
                      child: CustomText(
                        text: 'Subscribe',
                        alignment: AlignmentDirectional.center,
                        color: Constants.darkColor,
                        fontSize: 13.56.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}

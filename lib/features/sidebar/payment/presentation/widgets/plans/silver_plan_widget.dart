import 'package:ads_dv/features/sidebar/payment/data/models/feature.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/controllers/subscribe_package_controller/subscribe_package_cubit.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/controllers/transactions_controller/transaction_controller_cubit.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/radius_only_container.dart';
import '../../controllers/payment_controller/payment_cubit.dart';

class SilverPlanWidget extends StatefulWidget {
  PaymentCubit paymentCubit;
  GetTransactionsCubit getTransactionsCubit;
  BuildContext ctx;
  String title;
  int price;
  String packageId;
  PaymentState state;
  List<Features> features;

  SilverPlanWidget(
      {super.key,
      required this.paymentCubit,
      required this.state,
      required this.ctx,
      required this.title,
      required this.price,
      required this.getTransactionsCubit,
      required this.features,
      required this.packageId});

  @override
  State<SilverPlanWidget> createState() => _SilverPlanWidgetState();
}

class _SilverPlanWidgetState extends State<SilverPlanWidget> {
  // Future<void> _startApplePay() async {
  //   try {
  //     await Stripe.instance.presentApplePay(
  //       ApplePayPresentParams(
  //         cartItems: [
  //           ApplePayCartItem(
  //             label: "Product 1",
  //             amount: "10.00",
  //           ),
  //           ApplePayCartItem(
  //             label: "Tax",
  //             amount: "2.00",
  //           ),
  //         ],
  //         country: "US",
  //         currency: "USD",
  //       ),
  //     );
  //
  //     // Confirm the payment
  //     final paymentMethod = await Stripe.instance.createPaymentMethod(
  //       const PaymentMethodParams.applePay(),
  //     );
  //
  //     print("Payment Successful: ${paymentMethod.id}");
  //   } catch (e) {
  //     print("Error: $e");
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SubscribePackageCubit(),
      child: BlocListener<SubscribePackageCubit, SubscribePackageState>(
        listener: (context, subscribeState) {
          if (subscribeState is SubscribePackageLoaded) {
            widget.paymentCubit.changeToSuccessView(widget.ctx);
          }
        },
        child: StreamBuilder<PaymentState>(
          stream: widget.paymentCubit.stream,
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              final paymentState = snapshot.data!;
              if (paymentState is PaymentLoaded) {
                SubscribePackageCubit.get(context).subscribePackage(
                  transactionId: paymentState.data.id ?? "",
                  packageId: widget.packageId,
                  amount: (paymentState.data.amount! ~/ 100).toString(),
                  // Integer division to remove the decimal part
                  currency: paymentState.data.currency ?? "AED",
                  transactionDate: paymentState.data.created.toString(),
                  invoiceUrl: paymentState.data.receiptUrl ?? "",
                  context: context,
                );
              }
            }
            return SingleChildScrollView(
              child: Column(
                // clipBehavior: Clip.none,
                children: [
                  RadiusOnlyContainer(
                    color: const Color(0xFFF3F3F3),
                    height: 100.h,
                    rectangleBorder: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(25.43),
                        topRight: Radius.circular(25.43),
                      ),
                    ),
                    widget: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12.sp),
                      child: Column(
                        children: [
                          CustomText(
                            text: widget.title,
                            color: Constants.darkColor,
                            alignment: AlignmentDirectional.center,
                            fontSize: 33.91,
                            fontWeight: FontWeight.w400,
                          ),
                          10.verticalSpace,
                          CustomText(
                            text: 'PER MONTH'.tr,
                            color: Constants.darkColor,
                            fontSize: 12,
                            alignment: AlignmentDirectional.center,
                            fontFamily: 'IBM Plex Sans Devanagari',
                            fontWeight: FontWeight.w300,
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    decoration: const ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(25.43),
                          bottomRight: Radius.circular(25.43),
                        ),
                      ),
                      shadows: [
                        BoxShadow(
                          color: Color(0x33000000),
                          blurRadius: 30,
                          offset: Offset(0, 4),
                          spreadRadius: -6,
                        )
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: 16.sp, horizontal: 16.sp),
                      child: Column(
                        children: [
                          CustomText(
                            alignment: AlignmentDirectional.center,
                            text: '${widget.price} USD',
                            color: Constants.darkColor,
                            fontSize: 33.91,
                            fontWeight: FontWeight.w700,
                          ),
                          // sdsfdf
                          15.verticalSpace,
                          ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: widget.features.length,
                              itemBuilder: (item, index) {
                                return Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 8.0),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.check,
                                        color: Constants.darkColor,
                                        size: 20,
                                      ),
                                      10.horizontalSpace,
                                      CustomText(
                                        text: widget.features[index].feature ??
                                            "",
                                        color: Constants.darkColor,
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w400,
                                      )
                                    ],
                                  ),
                                );
                              }),

                          20.verticalSpace,
                          (widget.state is PaymentLoading)
                              ? const LoadingWidget(
                                  isCircle: true,
                                )
                              : InkWell(
                                  onTap: () {
                                    widget.paymentCubit.makePayment(
                                        amount: widget.price,
                                        currency: "USD",
                                        context: widget.ctx);
                                  },
                                  child: Container(
                                    width: 127.99.w,
                                    height: 32.21.h,
                                    decoration: ShapeDecoration(
                                      shape: RoundedRectangleBorder(
                                        side: const BorderSide(
                                            width: 1, color: Color(0xFF0B0F26)),
                                        borderRadius:
                                            BorderRadius.circular(44.08),
                                      ),
                                    ),
                                    child: CustomText(
                                      text: 'Buy'.tr,
                                      alignment: AlignmentDirectional.center,
                                      color: Constants.darkColor,
                                      fontSize: 13.56.sp,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                )
                        ],
                      ),
                    ),
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

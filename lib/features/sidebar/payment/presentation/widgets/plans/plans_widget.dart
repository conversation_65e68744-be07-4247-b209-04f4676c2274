import 'package:ads_dv/features/sidebar/payment/data/models/ad_package.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/controllers/transactions_controller/transaction_controller_cubit.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/widgets/plans/silver_plan_widget.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../controllers/payment_controller/payment_cubit.dart';

class PlansWidget extends StatelessWidget {
  List<AdsPackages> packages;
  int planIndex;
GetTransactionsCubit getTransactionsCubit;
  PaymentCubit paymentCubit;
  BuildContext ctx;
  PaymentState state;
  PlansWidget(
      {super.key,
      required this.planIndex,
      required this.paymentCubit,
      required this.ctx,
        required this.getTransactionsCubit,
      required this.state,
      required this.packages});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PaymentCubit(),
      child: CarouselSlider(
        items: List.generate(packages.length, (index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: /*index == 0
                ?*/
                SilverPlanWidget(
                  getTransactionsCubit: getTransactionsCubit,
              packageId: packages[index].id.toString(),
              title: packages[index].title ?? "",
              price: packages[index].price ?? 0,
              features: packages[index].features ?? [],
              paymentCubit: PaymentCubit.get(context),
              state: state,
              ctx: ctx,
            )
            /*: index == 1
                    ? PlatinumPlanWidget(
                        paymentCubit: PaymentCubit.get(context),
                        state: state,
                        ctx: ctx,
                      )
                    : GoldPlanWidget(
                        paymentCubit: PaymentCubit.get(context),
                        state: state,
                        ctx: ctx,
                      )*/
            ,
          );
        }),
        options: CarouselOptions(
          onPageChanged: (index, reason) {
            paymentCubit.changeIndicatorIndex(index);
          },
          scrollPhysics: const BouncingScrollPhysics(),
          height: 500.h,
          enlargeCenterPage: true,
          initialPage: 0,
          viewportFraction: 1,
          enableInfiniteScroll: true,
          autoPlay: false,
          reverse: false,
          autoPlayInterval: const Duration(seconds: 3),
          autoPlayAnimationDuration: const Duration(seconds: 1),
          autoPlayCurve: Curves.fastOutSlowIn,
          scrollDirection: Axis.horizontal,
        ),
      ),
    );
  }
}

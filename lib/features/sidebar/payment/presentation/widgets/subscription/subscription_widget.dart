import 'package:ads_dv/features/sidebar/wallet/presentation/controllers/renew_package/renew_package_cubit.dart';
import 'package:ads_dv/utils/res/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/cached__image.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../../../../widgets/svg_widget.dart';
import '../../../data/models/transaction.dart';

class SubscriptionWidget extends StatelessWidget {
  List<Transaction> transaction;
  SubscriptionWidget({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
  create: (context) => RenewPackageCubit(),
  child: Column(
      children: [
        FittedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CustomSvgWidget(
                        svg: AppAssets.date,
                        height: 15.h,
                      ),
                      6.horizontalSpace,
                      CustomText(
                        text: 'Date of renewal :'.tr,
                        color: const Color(0xFF0B0F26),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w700,
                      )
                    ],
                  ),
                  10.verticalSpace,
                  CustomText(
                    text: CommonUtils.convertDateString(
                        transaction.first.subscription?.renewDate ?? ""),
                    color: const Color(0x7F0B0F26),
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                  )
                ],
              ),
              20.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CustomSvgWidget(
                        svg: AppAssets.cal,
                        height: 15.h,
                      ),
                      6.horizontalSpace,
                      CustomText(
                        text: 'Days Left :'.tr,
                        color: const Color(0xFF0B0F26),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w700,
                      )
                    ],
                  ),
                  10.verticalSpace,
                  CustomText(
                    text: '${transaction.first.subscription?.daysLeft} Day',
                    color: const Color(0x7F0B0F26),
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                  )
                ],
              ),
              20.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      CustomSvgWidget(
                        svg: AppAssets.date,
                        height: 15.h,
                      ),
                      6.horizontalSpace,
                      CustomText(
                        text: 'Renew Plan :'.tr,
                        color: const Color(0xFF0B0F26),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w700,
                      )
                    ],
                  ),
                  10.verticalSpace,
                  BlocBuilder<RenewPackageCubit, RenewPackageState>(
                    builder: (context1, state) {
                      return InkWell(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: Text('Renew Subscription'.tr),
                                content: Text(
                                    'Would you like to renew your current subscription?'.tr),
                                actions: [
                                  TextButton(
                                    child: Text(
                                      'Cancel'.tr,
                                      style: const TextStyle(
                                          color: Constants.primaryTextColor),
                                    ),
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                  ),
                                  ElevatedButton(
                                    onPressed: () {
                                      // Implement your subscription renewal logic here
                                      RenewPackageCubit.get(context1)
                                          .renewPackage(
                                        packageId: transaction.first.packageId
                                            .toString(),
                                        context: context1,
                                      );
                                      Navigator.of(context).pop();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      // Set the gradient background using the `backgroundColor` property
                                      backgroundColor:
                                          Constants.primaryTextColor,
                                    ),
                                    child: Text(
                                      'Renew'.tr,
                                      style: const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                        child: Container(
                          decoration: ShapeDecoration(
                            gradient: Constants.secGradient,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(2)),
                            shadows: const [
                              BoxShadow(
                                color: Color(0x33000000),
                                blurRadius: 30,
                                offset: Offset(0, 0),
                                spreadRadius: -10,
                              )
                            ],
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                vertical: 4.sp, horizontal: 8.sp),
                            child: CustomText(
                              text: 'Renew'.tr,
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              alignment: AlignmentDirectional.center,
                            ),
                          ),
                        ),
                      );
                    },
                  )
                ],
              ),
            ],
          ),
        ),
        20.verticalSpace,
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 6.0),
          child: Divider(color: Constants.textColor),
        ),
        20.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(
              text: 'Transaction'.tr,
              color: Constants.darkColor,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
            CustomText(
              text: 'View All'.tr,
              color: Constants.darkColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            )
          ],
        ),
        20.verticalSpace,
        ListView.builder(
            itemCount: transaction.length,
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (item, index) {
              return Padding(
                padding: EdgeInsets.symmetric(
                  vertical: 12.sp,
                ),
                child: Container(
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(14),
                    ),
                    shadows: const [
                      BoxShadow(
                        color: Color(0x33000000),
                        blurRadius: 30,
                        offset: Offset(0, 0),
                        spreadRadius: -10,
                      )
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 20.sp, horizontal: 12.sp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            CachedImageWidget(
                              assetsImage: AppAssets.logo,
                              height: 25.h,
                            ),
                            10.horizontalSpace,
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: 180,
                                  child: CustomText(
                                    textOverflow: TextOverflow.fade,
                                    text: '${transaction[index].transactionId}',
                                    color: const Color(0xFFB0B0B0),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                10.verticalSpace,
                                ShaderMask(
                                  shaderCallback: (Rect bound) {
                                    return Constants.secGradient
                                        .createShader(bound);
                                  },
                                  child: CustomText(
                                    text: '${transaction[index].amount} USD',
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontFamily: 'IBM Plex Sans Devanagari',
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            transaction[index].subscription?.subscriptionDate !=
                                    null
                                ? CustomText(
                                    text: CommonUtils.convertDateString(
                                        transaction[index]
                                                .subscription
                                                ?.subscriptionDate ??
                                            ""),
                                    color: Constants.darkColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400,
                                  )
                                : const SizedBox(),
                            10.verticalSpace,
                            Container(
                              width: 64.h,
                              height: 32.h,
                              decoration: ShapeDecoration(
                                color: const Color(0x3F52FF83),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(7)),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  const Icon(
                                    Icons.check,
                                    color: Constants.greenColor,
                                    size: 15,
                                  ),
                                  CustomText(
                                    text: 'Paid'.tr,
                                    color: Constants.greenColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    alignment: AlignmentDirectional.center,
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
      ],
    ),
);
  }
}

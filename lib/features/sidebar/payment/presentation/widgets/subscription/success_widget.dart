import 'package:ads_dv/features/sidebar/payment/presentation/controllers/payment_controller/payment_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/app_assets.dart';
import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/cached__image.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../controllers/transactions_controller/transaction_controller_cubit.dart';

class SuccessWidget extends StatelessWidget {
  PaymentCubit paymentCubit;
  BuildContext context;
  GetTransactionsCubit getTransactionsCubit;

  SuccessWidget(
      {super.key,
      required this.paymentCubit,
      required this.getTransactionsCubit,
      required this.context});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const CachedImageWidget(
          assetsImage: AppAssets.success,
          height: 150,
        ),
        20.verticalSpace,
        CustomText(
          text: 'Success'.tr,
          color: Constants.darkColor,
          fontSize: 24.sp,
          alignment: AlignmentDirectional.center,
          fontFamily: 'IBM Plex Sans Devanagari',
          fontWeight: FontWeight.w700,
        ),
        20.verticalSpace,
        SizedBox(
          width: 273,
          child: CustomText(
            text: 'paymentSuccess'.tr,
            textAlign: TextAlign.center,
            maxLines: 2,
            color: const Color(0xFFABABAB),
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        60.verticalSpace,
        InkWell(
          onTap: () {
            paymentCubit.moveFromToSuccessView(context);

            getTransactionsCubit.getTransactions(context: context);
          },
          child: Container(
            width: 151.h,
            height: 38.h,
            decoration: ShapeDecoration(
              gradient: Constants.secGradient,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(52),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x23000000),
                  blurRadius: 22,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                )
              ],
            ),
            child: CustomText(
              text: 'Continue'.tr,
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w400,
              alignment: AlignmentDirectional.center,
            ),
          ),
        )
      ],
    );
  }
}

import 'package:ads_dv/features/sidebar/payment/data/data_sources/payment_data_source.dart';
import 'package:ads_dv/features/sidebar/payment/data/models/package.dart';
import 'package:ads_dv/features/sidebar/payment/data/models/payment_response.dart';
import 'package:ads_dv/features/sidebar/payment/data/models/transaction.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';

class PaymentRepo {
  NetworkInfo networkInfo;
  PaymentDataSource paymentDataSource;

  PaymentRepo({required this.networkInfo, required this.paymentDataSource});

  Future<Either<Failure, PaymentResponse>> makePayment(
      {required int amount, required String currency}) async {
    return FailureHelper.instance(
      method: () async {
        return await paymentDataSource.makePayment(amount, currency);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, Package>> getPackages() {
    return FailureHelper.instance(
      method: () async {
        return await paymentDataSource.getPackages();
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> subscribePackage({
    required String packageId,
    required String transactionId,
    required String amount,
    required String currency,
    required String transactionDate,
    required String invoiceUrl,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await paymentDataSource.subscribePackage(
          packageId: packageId,
          transactionId: transactionId,
          amount: amount,
          currency: currency,
          transactionDate: transactionDate,
          invoiceUrl: invoiceUrl
        );
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, List<Transaction>>> getTransactions() {
    return FailureHelper.instance(
      method: () async {
        return await paymentDataSource.getTransactions();
      },
      networkInfo: networkInfo,
    );
  }
}

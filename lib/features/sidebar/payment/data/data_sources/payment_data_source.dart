import 'package:ads_dv/features/sidebar/payment/data/models/package.dart';
import 'package:ads_dv/features/sidebar/payment/data/models/payment_response.dart';
import 'package:ads_dv/features/sidebar/payment/data/models/transaction.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/urls/end_points.dart';

class PaymentDataSource {
  Future<PaymentResponse> makePayment(int amount, String currency) async {
    final eitherClientSecret =
    await getClientSecret((amount * 100).toString(), currency);
    return eitherClientSecret.fold(
          (failure) {
        print("fail1: ${failure.message}");
        throw ServerFailure(message: failure.message.toString());
      },
          (clientSecret) async {
        final eitherInitialized =
        await initializePaymentSheet(clientSecret.clientSecret ?? "");
        return eitherInitialized.fold(
              (failure) {
            print("fail2: ${failure.message}");
            throw ServerFailure(message: failure.message.toString());
          },
              (_) async {
            await Stripe.instance.presentPaymentSheet();
            return clientSecret;
          },
        );
      },
    );
  }

  Future<Either<ServerFailure, void>> initializePaymentSheet(
      String clientSecret) async {
    try {
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: clientSecret,
          merchantDisplayName: "DV",
          googlePay: const PaymentSheetGooglePay(
            merchantCountryCode: "US",
            currencyCode: "USD",
            testEnv: false, // Set to false for production
          ),
          // style: ThemeMode.light,
          // Optional: Can be dark or system
          allowsDelayedPaymentMethods: true, // Enable if needed
        ),
      );
      return const Right(null);
    } catch (error) {
      print("PaymentSheet Initialization Error: $error");
      return Left(ServerFailure(message: error.toString()));
    }
  }

  Future<Either<ServerFailure, PaymentResponse>> getClientSecret(String amount,
      String currency) async {
    try {
      final dio = Dio();
      final response = await dio.post(
        'https://api.stripe.com/v1/payment_intents',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${Constants.secretKey}',
            'Content-Type': 'application/x-www-form-urlencoded'
          },
        ),
        data: {
          'amount': amount,
          'currency': currency,
        },
      );
      print("responsepay$response");
      return Right(PaymentResponse.fromJson(response.data));
    } catch (error) {
      print("fail4$error");

      return Left(ServerFailure(message: error.toString()));
    }
  }

  Future<Package> getPackages() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getPackages,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return Package.fromJson(response['result']);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> subscribePackage({
    required String packageId,
    required String transactionId,
    required String amount,
    required String currency,
    required String transactionDate,
    required String invoiceUrl,
  }) async {
    try {
      var response = await instance<NetworkCall>().request(
        params: FormData.fromMap({
          "package_id": packageId,
          "transaction_id": transactionId,
          "amount": amount,
          "currency": currency,
          "transaction_date": transactionDate,
          "invoice_url": invoiceUrl,
        }),
        EndPoints.subscribePackage,
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<Transaction>> getTransactions() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.subscribePackage,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      List<dynamic> data = response['data'];
      List<Transaction> trans =
      data.map((trans) => Transaction.fromJson(trans)).toList();
      return trans;
    } catch (error) {
      rethrow;
    }
  }
}

import 'package:ads_dv/features/sidebar/payment/data/models/ad_package.dart';

class Package {
  List<AdsPackages>? adsPackages;
  List<AdsPackages>? daysPackages;

  Package({this.adsPackages, this.daysPackages});

  Package.fromJson(Map<String, dynamic> json) {
    if (json['ads_packages'] != null) {
      adsPackages = <AdsPackages>[];
      json['ads_packages'].forEach((v) {
        adsPackages!.add(AdsPackages.fromJson(v));
      });
    }
    if (json['days_packages'] != null) {
      daysPackages = <AdsPackages>[];
      json['days_packages'].forEach((v) {
        daysPackages!.add(AdsPackages.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (adsPackages != null) {
      data['ads_packages'] = adsPackages!.map((v) => v.toJson()).toList();
    }
    if (daysPackages != null) {
      data['days_packages'] =
          daysPackages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}


import 'package:ads_dv/features/sidebar/payment/data/models/feature.dart';

class AdsPackages {
  int? id;
  String? title;
  int? type;
  int? number;
  int? price;
  String? createdAt;
  String? updatedAt;
  String? color;
  List<Features>? features;
  AdsPackages(
      {this.id,
        this.title,
        this.type,
        this.number,
        this.price,
        this.createdAt,
        this.updatedAt,
        this.features,
        this.color,
      });

  AdsPackages.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    type = json['type'];
    number = json['number'];
    price = json['price'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    color = json['color'];
    if (json['features'] != null) {
      features = <Features>[];
      json['features'].forEach((v) {
        features!.add(Features.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['type'] = type;
    data['number'] = number;
    data['price'] = price;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['color'] = color;
    if (features != null) {
      data['features'] = features!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
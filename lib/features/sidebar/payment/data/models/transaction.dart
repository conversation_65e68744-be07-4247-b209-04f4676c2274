class Transaction {
  int? id;
  int? userId;
  int? packageId;
  String? transactionId;
  String? amount;
  String? paymentStatus;
  Subscription? subscription;

  Transaction(
      {this.id,
        this.userId,
        this.packageId,
        this.transactionId,
        this.amount,
        this.paymentStatus,
        this.subscription});

  Transaction.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    packageId = json['package_id'];
    transactionId = json['transaction_id'];
    amount = json['amount'];
    paymentStatus = json['payment_status'];
    subscription = json['subscription'] != "null"
        ? Subscription.fromJson(json['subscription'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['package_id'] = packageId;
    data['transaction_id'] = transactionId;
    data['amount'] = amount;
    data['payment_status'] = paymentStatus;
    if (subscription != "null") {
      data['subscription'] = subscription!.toJson();
    }
    return data;
  }
}

class Subscription {
  String? amount;
  String? subscriptionDate;
  String? renewDate;
  int? daysLeft;

  Subscription(
      {this.amount, this.subscriptionDate, this.renewDate, this.daysLeft});

  Subscription.fromJson(Map<String, dynamic> json) {
    amount = json['amount'];
    subscriptionDate = json['subscription_date'];
    renewDate = json['renew_date'];
    daysLeft = json['days left'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['amount'] = amount;
    data['subscription_date'] = subscriptionDate;
    data['renew_date'] = renewDate;
    data['days left'] = daysLeft;
    return data;
  }
}
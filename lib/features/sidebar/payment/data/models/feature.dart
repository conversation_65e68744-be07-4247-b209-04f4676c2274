class Features {
  int? id;
  int? packageId;
  String? feature;
  String? createdAt;
  String? updatedAt;

  Features(
      {this.id, this.packageId, this.feature, this.createdAt, this.updatedAt});

  Features.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    packageId = json['package_id'];
    feature = json['feature'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['package_id'] = packageId;
    data['feature'] = feature;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
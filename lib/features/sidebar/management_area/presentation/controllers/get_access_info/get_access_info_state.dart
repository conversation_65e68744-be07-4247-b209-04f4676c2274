part of 'get_access_info_cubit.dart';

@immutable
abstract class GetAccessInfoState {
  const GetAccessInfoState();
  List<Object?> get props => [];
}

class GetAccessInfoInitial extends GetAccessInfoState {}

class GetAccessInfoStateLoading extends GetAccessInfoState {}

class GetAccessInfoStateLoaded extends GetAccessInfoState {
  final AccessInfo data;

  const GetAccessInfoStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  GetAccessInfoStateLoaded copyWith({
    AccessInfo? data,
  }) {
    return GetAccessInfoStateLoaded(
      data ?? this.data,
    );
  }
}

class GetAccessInfoStateError extends GetAccessInfoState {
  final Failure message;

  const GetAccessInfoStateError(this.message);

  @override
  List<Object?> get props => [message];
}

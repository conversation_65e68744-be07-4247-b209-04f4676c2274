import 'package:ads_dv/features/sidebar/ad_accounts/data/models/access_info.dart';
import 'package:ads_dv/features/sidebar/management_area/data/repos/management_area_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';

part 'get_access_info_state.dart';

class GetAccessInfoCubit extends Cubit<GetAccessInfoState> {
  GetAccessInfoCubit() : super(GetAccessInfoInitial());

  static GetAccessInfoCubit get(context) => BlocProvider.of(context);


  getAccessInfo({
    required BuildContext context,
  }) async {
    emit(GetAccessInfoStateLoading());
    instance<ManagementAreaRepo>().getAccessInfo().then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(GetAccessInfoStateError(l));
      }, (r) {
        emit(GetAccessInfoStateLoaded(r));
      });
    });
  }
}
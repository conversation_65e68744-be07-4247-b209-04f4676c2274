import 'package:ads_dv/features/sidebar/management_area/data/repos/management_area_repo.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';

part 'edit_access_state.dart';

class EditAccessCubit extends Cubit<EditAccessState> {
  EditAccessCubit() : super(EditAccessInitial());

  static EditAccessCubit get(context) => BlocProvider.of(context);

  String? selectedRole;

  updateRole(
      {
        required String role,
        required var accessId,
        required BuildContext context}) async {
    emit(EditAccessLoading());
    instance<ManagementAreaRepo>()
        .updateRole(role: role,accessId: accessId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(EditAccessError(l));
      }, (r) {
        AwesomeDialog(
          context: context,
          animType: AnimType.leftSlide,
          headerAnimationLoop: false,
          dialogType: DialogType.success,
          showCloseIcon: true,
          title: 'Success',
          desc: 'Access updated successfully',
          btnOkOnPress: () {
            Navigator.of(context).pop(true);
          },
          btnOkIcon: Icons.check_circle,
        ).show();

        emit(EditAccessLoaded());
      });
    });
  }

  void setSelectedRole(String role) {
    selectedRole = role;
    emit(SetSelectedRole());
  }
}

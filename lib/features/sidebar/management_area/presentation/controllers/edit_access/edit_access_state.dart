part of 'edit_access_cubit.dart';

@immutable
abstract class EditAccessState {
  const EditAccessState();
  List<Object?> get props => [];
}

class EditAccessInitial extends EditAccessState {}

class EditAccessLoading extends EditAccessState {}

class EditAccessLoaded extends EditAccessState {
}

class EditAccessError extends EditAccessState {
  final Failure message;

  const EditAccessError(this.message);

  @override
  List<Object?> get props => [message];
}

class SetSelectedRole extends EditAccessState {}

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/management_area_repo.dart';

part 'add_access_state.dart';

class AddAccessCubit extends Cubit<AddAccessState> {
  AddAccessCubit() : super(AddAccessInitial());

  static AddAccessCubit get(context) => BlocProvider.of(context);

  String? selectedRole;

  addAccess(
      {required String name,
      required String phone,
      required String email,
      required String role,
      required BuildContext context}) async {
    emit(AddAccessLoading());
    instance<ManagementAreaRepo>()
        .addAccess(email: email, phone: phone, name: name, role: role)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(AddAccessError(l));
      }, (r) {
        AwesomeDialog(
          context: context,
          animType: AnimType.leftSlide,
          headerAnimationLoop: false,
          dialogType: DialogType.success,
          showCloseIcon: true,
          title: 'Success',
          desc: 'Access added successfully',
          btnOkOnPress: () {
            Navigator.of(context).pop(true);
          },
          btnOkIcon: Icons.check_circle,
        ).show();

        emit(AddAccessLoaded());
      });
    });
  }

  void setSelectedRole(String role) {
    selectedRole = role;
    emit(SetSelectedRole());
  }
}

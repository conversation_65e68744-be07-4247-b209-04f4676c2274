part of 'add_access_cubit.dart';

@immutable
abstract class AddAccessState {
  const AddAccessState();
  List<Object?> get props => [];
}

class AddAccessInitial extends AddAccessState {}

class AddAccessLoading extends AddAccessState {}

class AddAccessLoaded extends AddAccessState {
}

class AddAccessError extends AddAccessState {
  final Failure message;

  const AddAccessError(this.message);

  @override
  List<Object?> get props => [message];
}

class SetSelectedRole extends AddAccessState {}

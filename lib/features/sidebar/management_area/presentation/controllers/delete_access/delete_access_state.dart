part of 'delete_access_cubit.dart';

@immutable
abstract class DeleteAccessState {
  const DeleteAccessState();
  List<Object?> get props => [];
}

class DeleteAccessInitial extends DeleteAccessState {}

class DeleteAccessLoading extends DeleteAccessState {}

class DeleteAccessLoaded extends DeleteAccessState {
}

class DeleteAccessError extends DeleteAccessState {
  final Failure message;

  const DeleteAccessError(this.message);

  @override
  List<Object?> get props => [message];
}
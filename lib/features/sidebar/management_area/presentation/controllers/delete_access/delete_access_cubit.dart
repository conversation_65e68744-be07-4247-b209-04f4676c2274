import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../data/repos/management_area_repo.dart';

part 'delete_access_state.dart';

class DeleteAccessCubit extends Cubit<DeleteAccessState> {
  DeleteAccessCubit() : super(DeleteAccessInitial());

  static DeleteAccessCubit get(context) => BlocProvider.of(context);

  String? selectedRole;

  deleteAccess(
      {

        required var accessId,
        required BuildContext context}) async {
    emit(DeleteAccessLoading());
    instance<ManagementAreaRepo>()
        .deleteAccess(accessId: accessId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(DeleteAccessError(l));
      }, (r) {
        showSuccessToast('Access deleted successfully');


        emit(DeleteAccessLoaded());
      });
    });
  }


}

import 'package:ads_dv/features/sidebar/management_area/presentation/controllers/edit_access/edit_access_cubit.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../utils/res/validations.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../ad_accounts/data/models/access.dart';
import '../../../widgets/role_widget.dart';

class EditAccessScreen extends StatefulWidget {
  AccessedUser accessedUser;
  EditAccessScreen({super.key, required this.accessedUser});

  @override
  State<EditAccessScreen> createState() => _EditAccessScreenState();
}

class _EditAccessScreenState extends State<EditAccessScreen> {
  var nameController = TextEditingController();
  var emailController = TextEditingController();
  var phoneController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    nameController = TextEditingController(text: widget.accessedUser.email);
    emailController = TextEditingController(text: widget.accessedUser.email);
    phoneController = TextEditingController(text: widget.accessedUser.email);

    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EditAccessCubit(),
      child: Scaffold(
        appBar: const CustomAppBar(
          title: "Management Area",
          showBackButton: true,
          hasDrawer: true,
        ),
        body: BlocBuilder<EditAccessCubit, EditAccessState>(
          builder: (context, state) {
            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const CustomSvgWidget(svg: AppAssets.invite),
                        RSizedBox.horizontal(10.w),
                        CustomText(
                          text: "Edit Partner",
                          color: Colors.black,
                          fontSize: 22.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ],
                    ),
                    RSizedBox.vertical(12.h),
                    CustomText(
                      maxLines: 3,
                      textAlign: TextAlign.left,
                      text:
                          "Enter and name and email address for the person you'd like to grant account accass. Then, select an access level and click Edit.",
                      color: Colors.black,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    RSizedBox.vertical(16.h),
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          CustomTextFormField.withPrefixSVG(
                            controller: nameController,
                            svgURL: AppAssets.user,
                            hintText: "Name",
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                          ),
                          16.hSpace,
                          CustomTextFormField.withPrefixSVG(
                            controller: emailController,
                            svgURL: AppAssets.mail,
                            hintText: "E-Mail",
                            validator: (value) =>
                                AppValidator.validateEmail(value),
                          ),
                          16.hSpace,
                          CustomTextFormField.withPrefixSVG(
                            hintText: "Phone",
                            svgURL: AppAssets.phone,
                            controller: phoneController,
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                          ),
                        ],
                      ),
                    ),
                    RSizedBox.vertical(16.h),
                    Row(
                      children: [
                        CustomText(
                          text: "Access level requested",
                          color: Colors.black,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w700,
                        ),
                        RSizedBox.horizontal(2.w),
                        CustomText(
                          text: "*",
                          color: Constants.redColor,
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ],
                    ),
                    RSizedBox.vertical(16.h),
                    InkWell(
                        onTap: () {
                          EditAccessCubit.get(context)
                              .setSelectedRole("Super Admin");
                        },
                        child: RoleWidget(
                            role: "Super Admin",
                            description:
                                "Access to manage products and domains Only.",
                            isSelected:
                                (EditAccessCubit.get(context).selectedRole ==
                                            "Super Admin" ||
                                        (widget.accessedUser.role ==
                                                "Super Admin" &&
                                            EditAccessCubit.get(context)
                                                    .selectedRole ==
                                                null))
                                    ? true
                                    : false)),
                    RSizedBox.vertical(16.h),
                    InkWell(
                        onTap: () {
                          EditAccessCubit.get(context).setSelectedRole("Admin");
                        },
                        child: RoleWidget(
                            role: "Admin",
                            description:
                                "Access to manage products and domains Only.",
                            isSelected:
                                (EditAccessCubit.get(context).selectedRole ==
                                            "Admin" ||
                                        (widget.accessedUser.role == "Admin" &&
                                            EditAccessCubit.get(context)
                                                    .selectedRole ==
                                                null))
                                    ? true
                                    : false)),
                    RSizedBox.vertical(16.h),
                    InkWell(
                      onTap: () {
                        EditAccessCubit.get(context).setSelectedRole("Member");
                      },
                      child: RoleWidget(
                          role: "Team Member",
                          description:
                              "Access to manage products and domains Only.",
                          isSelected:
                              (EditAccessCubit.get(context).selectedRole ==
                                          "Member" ||
                                      (widget.accessedUser.role == "Member" &&
                                          EditAccessCubit.get(context)
                                                  .selectedRole ==
                                              null))
                                  ? true
                                  : false),
                    ),
                    RSizedBox.vertical(32.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 100.sp),
                      child: SizedBox(
                        width: 200.w,
                        child: state is EditAccessLoading
                            ? const LoadingWidget(
                                isCircle: true,
                              )
                            : ButtonWidget(
                                text: "Update",
                                onTap: () {
                                  if (_formKey.currentState!.validate()) {
                                    EditAccessCubit.get(context).updateRole(
                                        accessId: widget.accessedUser.id ?? 0,
                                        role: EditAccessCubit.get(context)
                                                .selectedRole ??
                                            widget.accessedUser.role ??
                                            "",
                                        context: context);
                                  }
                                },
                              ),
                      ),
                    )
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../utils/res/validations.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../../../../../widgets/custom_text_field.dart';
import '../../../widgets/role_widget.dart';
import '../controllers/add_access/add_access_cubit.dart';

class InviteAccessScreen extends StatefulWidget {
  const InviteAccessScreen({super.key});

  @override
  State<InviteAccessScreen> createState() => _InviteAccessScreenState();
}

class _InviteAccessScreenState extends State<InviteAccessScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    // TODO: implement dispose
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AddAccessCubit(),
      child: Scaffold(
        appBar: const CustomAppBar(
          title: "Management Area",
          showBackButton: true,
          hasDrawer: true,
        ),
        body: BlocBuilder<AddAccessCubit, AddAccessState>(
          builder: (context, state) {
            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const CustomSvgWidget(svg: AppAssets.invite),
                        RSizedBox.horizontal(10.w),
                        CustomText(
                          text: "Invite Partner",
                          color: Colors.black,
                          fontSize: 22.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ],
                    ),
                    RSizedBox.vertical(12.h),
                    CustomText(
                      maxLines: 3,
                      textAlign: TextAlign.left,
                      text:
                          "Enter and name and email address for the person you'd like to grant account accass. Then, select an access level and click Invite.",
                      color: Colors.black,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w300,
                    ),
                    RSizedBox.vertical(16.h),
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          CustomTextFormField.withPrefixSVG(
                            controller: nameController,
                            svgURL: AppAssets.user,
                            hintText: "Name",
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                          ),
                          16.hSpace,
                          CustomTextFormField.withPrefixSVG(
                            controller: emailController,
                            svgURL: AppAssets.mail,
                            hintText: "E-Mail",
                            validator: (value) =>
                                AppValidator.validateEmail(value),
                          ),
                          16.hSpace,
                          CustomTextFormField.withPrefixSVG(
                            hintText: "Phone",
                            svgURL: AppAssets.phone,
                            controller: phoneController,
                            validator: (value) =>
                                AppValidator.validateIdentity(value, context),
                          ),
                        ],
                      ),
                    ),
                    RSizedBox.vertical(16.h),
                    Row(
                      children: [
                        CustomText(
                          text: "Access level requested",
                          color: Colors.black,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w700,
                        ),
                        RSizedBox.horizontal(2.w),
                        CustomText(
                          text: "*",
                          color: Constants.redColor,
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ],
                    ),
                    RSizedBox.vertical(16.h),
                    InkWell(
                        onTap: () {
                          AddAccessCubit.get(context).setSelectedRole("Super Admin");
                        },
                        child: RoleWidget(
                            role: "Super Admin",
                            description:
                                "Access to manage products and domains Only.",
                            isSelected:
                                AddAccessCubit.get(context).selectedRole ==
                                        "Super Admin"
                                    ? true
                                    : false)),
                    RSizedBox.vertical(16.h),
                    InkWell(
                        onTap: () {
                          AddAccessCubit.get(context).setSelectedRole("Admin");
                        },
                        child: RoleWidget(
                            role: "Admin",
                            description:
                            "Access to manage products and domains Only.",
                            isSelected:
                            AddAccessCubit.get(context).selectedRole ==
                                "Admin"
                                ? true
                                : false)),
                    RSizedBox.vertical(16.h),
                    InkWell(
                      onTap: () {
                        AddAccessCubit.get(context).setSelectedRole("Member");
                      },
                      child: RoleWidget(
                          role: "Team Member",
                          description:
                              "Access to manage products and domains Only.",
                          isSelected:
                              AddAccessCubit.get(context).selectedRole ==
                                      "Member"
                                  ? true
                                  : false),
                    ),
                    RSizedBox.vertical(32.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 100.sp),
                      child: SizedBox(
                        width: 200.w,
                        child: state is AddAccessLoading
                            ? const LoadingWidget(
                                isCircle: true,
                              )
                            : ButtonWidget(
                                text: "Invite",
                                onTap: () {
                                  if (_formKey.currentState!.validate()) {
                                    if(AddAccessCubit.get(context).selectedRole == null){
                                      showErrorToast("Please Select Access Role");
                                    }else{
                                      AddAccessCubit.get(context).addAccess(
                                          name: nameController.text,
                                          phone: phoneController.text,
                                          email: emailController.text,
                                          role: AddAccessCubit.get(context)
                                              .selectedRole ??
                                              "",
                                          context: context);
                                    }

                                  }
                                },
                              ),
                      ),
                    )
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/custom_widgets.dart';
import '../../../../../widgets/appbar.dart';
import '../../../../../widgets/button_widget.dart';
import '../../../../../widgets/cached__image.dart';
import '../../../../../widgets/custom_text.dart';
import '../../../ad_accounts/data/models/ad_account.dart';
import '../../../ad_accounts/data/models/page.dart';
import '../../../ad_accounts/presentation/views/widgets/account_hint_text.dart';



class AccessedAccountsScreen extends StatelessWidget {
  List<AdAccount> userAccount;
  List<Pages> metaPages;

  AccessedAccountsScreen(
      {super.key, required this.userAccount, required this.metaPages});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: const CustomAppBar(
            title: "Meta Accessed Accounts",
            showBackButton: true,
            hasDrawer: true,
          ),
          body: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Stack(
              children: [
                Column(
                  children: [
                    ExpansionTileItem(
                      expansionKey: Constants.accountsTileKey,
                      onExpansionChanged: (val) {},
                      childrenPadding: EdgeInsets.zero,
                      iconColor: AppColors.secondColor,
                      collapsedIconColor: AppColors.secondColor,
                      expandedAlignment: Alignment.center,
                      expandedCrossAxisAlignment: CrossAxisAlignment.center,
                      trailing: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 6.0),
                            child: Icon(
                              Icons.expand_more,
                              size: 40.0,
                              color: Constants.darkColor,
                            ),
                          )
                        ],
                      ),
                      title: CreateAdCubit.get(context).adAccount != null
                          ? Row(
                              children: [
                                CircleAvatar(
                                  child: CachedImageWidget(
                                    assetsImage: AppAssets.dummyProfile,
                                    height: 40.h,
                                  ),
                                ),
                                10.horizontalSpace,
                                SizedBox(
                                  width: 120.h,

                                  child: FittedBox(
                                    child: CustomText(
                                        text: CreateAdCubit.get(context)
                                                .adAccount
                                                ?.name ??
                                            ""),
                                  ),
                                ),
                              ],
                            )
                          :  AccountHintText(
                        isDefaultHint: false,

                        hint: 'Choose your ads account ',
                            ),
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x3F000000),
                            blurRadius: 40,
                            offset: Offset(0, 0),
                            spreadRadius: -10,
                          )
                        ],
                      ),
                      children: [
                       userAccount.isEmpty ? const Center(
                         child: CustomText(
                             alignment: AlignmentDirectional.center,
                             text:
                             "No ad accounts until now"),
                       ): ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (item, index) {
                            return InkWell(
                              onTap: () {
                                CreateAdCubit.get(context)
                                    .setSelectedAccount(userAccount[index]);
                                print("asdasasda${CreateAdCubit.get(context).adAccount!.accountId}");

                                Constants.accountsTileKey.currentState
                                    ?.collapse();
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Constants.gray.withOpacity(0.15),
                                    border: Border.symmetric(
                                        horizontal: BorderSide(
                                            color: Constants.gray
                                                .withOpacity(0.3)))),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 18, horizontal: 20),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          CircleAvatar(
                                            child: CachedImageWidget(
                                              assetsImage: AppAssets.dummyProfile,
                                              height: 40.h,
                                            ),
                                          ),
                                          10.horizontalSpace,
                                          CustomText(
                                              text: userAccount[index].name ??
                                                  ""),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                          itemCount: userAccount.length,
                        ),
                        20.verticalSpace,
                      ],
                    ),
                    30.verticalSpace,
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      child: const Divider(color: Constants.gray),
                    ),
                    30.verticalSpace,
                    ExpansionTileItem(
                      expansionKey: Constants.pagesTileKey,
                      onExpansionChanged: (val) {},
                      childrenPadding: EdgeInsets.zero,
                      iconColor: AppColors.secondColor,
                      collapsedIconColor: AppColors.secondColor,
                      expandedAlignment: Alignment.center,
                      expandedCrossAxisAlignment: CrossAxisAlignment.center,
                      trailing: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 6.0),
                            child: Icon(
                              Icons.expand_more,
                              size: 40.0,
                              color: Constants.darkColor,
                            ),
                          )
                        ],
                      ),
                      title: CreateAdCubit.get(context).accessedMetaPages != null
                          ? Row(
                              children: [
                                CircleAvatar(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20),
                                    child: CachedImageWidget(
                                      image: CreateAdCubit.get(context)
                                          .accessedMetaPages
                                          ?.picture?.data?.url ?? "",
                                      height: 30.h,
                                    ),
                                  ),
                                ),
                                10.horizontalSpace,
                                SizedBox(
                                  width: 120.h,
                                  child: FittedBox(
                                    child: CustomText(
                                        text: CreateAdCubit.get(context)
                                                .accessedMetaPages
                                                ?.name ??
                                            ""),
                                  ),
                                ),
                              ],
                            )
                          :  AccountHintText(
                        isDefaultHint: false,

                        hint: 'Choose your page',
                            ),
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x3F000000),
                            blurRadius: 40,
                            offset: Offset(0, 0),
                            spreadRadius: -10,
                          )
                        ],
                      ),
                      children: [
                        metaPages.isNotEmpty
                            ? ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemBuilder: (item, index) {
                                  return InkWell(
                                    onTap: () {
                                      CreateAdCubit.get(context)
                                          .setSelectedAccessedPage(metaPages[index]);
                                      print("asdas${CreateAdCubit.get(context).accessedMetaPages!.accessToken}");
                                      Constants.pagesTileKey.currentState
                                          ?.collapse();
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color:
                                              Constants.gray.withOpacity(0.15),
                                          border: Border.symmetric(
                                              horizontal: BorderSide(
                                                  color: Constants.gray
                                                      .withOpacity(0.3)))),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 18, horizontal: 20),
                                        child: Column(
                                          children: [
                                            Row(
                                              children: [
                                                CircleAvatar(
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                    child: CachedImageWidget(
                                                      image: metaPages[index]
                                                          .picture?.data?.url ?? "",
                                                      height: 40.h,
                                                    ),
                                                  ),
                                                ),
                                                10.horizontalSpace,
                                                CustomText(
                                                    text:
                                                        metaPages[index].name ??
                                                            ""),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                                itemCount: metaPages.length,
                              )
                            : const Center(
                                child: CustomText(
                                    alignment: AlignmentDirectional.center,
                                    text:
                                        "No associated pages with this account"),
                              ),
                        20.verticalSpace,
                      ],
                    ),
                  ],
                ),
                Positioned(
                  bottom: 15.h,
                  left: 20.w,
                  right: 20.w,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 50.sp),
                    child: SizedBox(
                      width: 235.w,
                      child: ButtonWidget(
                        text: "Save",
                        onTap: () {
                          if (CreateAdCubit.get(context).adAccount == null) {
                            showErrorToast("please select your ad account");
                          } else if (CreateAdCubit.get(context).accessedMetaPages ==
                              null) {
                            showErrorToast("please select your page");
                          } else {
                            CreateAdCubit.get(context).adModel =
                                CreateAdCubit.get(context).adModel.copyWith(
                                    adAccountId: CreateAdCubit.get(context)
                                            .adAccount
                                            ?.id ??
                                        "",
                                    pageAccessToken: CreateAdCubit.get(context)
                                            .accessedMetaPages
                                            ?.accessToken ??
                                        "",
                                    pageId: int.parse(CreateAdCubit.get(context)
                                            .accessedMetaPages
                                            ?.id ??
                                        ""));

                            Navigator.of(context).pop();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

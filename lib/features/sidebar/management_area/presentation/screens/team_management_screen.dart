import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../utils/res/router/routes.dart';
import '../../../../../../../widgets/appbar.dart';
import '../../../../../../../widgets/button_widget.dart';
import '../../../../../widgets/handle_error_widget.dart';
import '../../../widgets/access_widget.dart';
import '../../../widgets/edit_permissions_widget.dart';
import '../controllers/get_access_info/get_access_info_cubit.dart';

class TeamManagementScreen extends StatelessWidget {
  const TeamManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          GetAccessInfoCubit()..getAccessInfo(context: context),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          title: "Management Area".tr,
          showBackButton: true,
          hasDrawer: true,
        ),
        body: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height - 150.h,
          ),
          child: Stack(
            children: [
              BlocBuilder<GetAccessInfoCubit, GetAccessInfoState>(
                builder: (context, state) {
                  return state is GetAccessInfoStateLoading
                      ? const LoadingWidget(
                          isCircle: true,
                        )
                      : state is GetAccessInfoStateError
                          ? Center(
                              child: HandleErrorWidget(
                                  fun: () {
                                    GetAccessInfoCubit.get(context)
                                        .getAccessInfo(context: context);
                                  },
                                  failure: state.message),
                            )
                          : state is GetAccessInfoStateLoaded
                              ? Padding(
                                  padding: const EdgeInsets.all(24.0),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            const CustomSvgWidget(
                                                svg: AppAssets.access),
                                            RSizedBox.horizontal(10.w),
                                            CustomText(
                                              text: "Delegate Access".tr,
                                              color: Colors.black,
                                              fontSize: 22.sp,
                                              fontWeight: FontWeight.w400,
                                            )
                                          ],
                                        ),
                                        RSizedBox.vertical(24.h),
                                        CustomText(
                                          text: "access account".tr,
                                          color: Colors.black,
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        RSizedBox.vertical(12.h),
                                        Container(
                                          height: state
                                                      .data.canAccess!.length ==
                                                  1
                                              ? 200.h
                                              : state.data.canAccess!.isNotEmpty
                                                  ? 350.h
                                                  : 50.h,
                                          decoration: ShapeDecoration(
                                            color: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                            ),
                                            shadows: const [
                                              BoxShadow(
                                                color: Color(0x3F000000),
                                                blurRadius: 40,
                                                offset: Offset(0, 0),
                                                spreadRadius: -10,
                                              )
                                            ],
                                          ),
                                          child: Padding(
                                            padding: EdgeInsets.all(16.sp),
                                            child: state
                                                    .data.canAccess!.isNotEmpty
                                                ? ListView.builder(
                                                    clipBehavior: Clip.hardEdge,
                                                    itemCount: state
                                                        .data.canAccess!.length,
                                                    itemBuilder: (item, index) {
                                                      return Column(
                                                        children: [
                                                          Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        4.0,
                                                                    vertical:
                                                                        4),
                                                            child: AccessWidget(
                                                              userAccount: state
                                                                      .data
                                                                      .canAccess![
                                                                          index]
                                                                      .accounts??
                                                                  [],
                                                              metaPages: state
                                                                      .data
                                                                      .canAccess?[
                                                                          index].pages ??
                                                                  [],
                                                              accountName: state
                                                                  .data
                                                                  .canAccess![
                                                                      index]
                                                                  .name??"",
                                                              permission: state
                                                                      .data
                                                                      .canAccess![
                                                                          index]
                                                                      .role ??
                                                                  "",
                                                            ),
                                                          ),
                                                          (index !=
                                                                  state
                                                                          .data
                                                                          .canAccess!
                                                                          .length -
                                                                      1)
                                                              ? Padding(
                                                                  padding: EdgeInsets.symmetric(
                                                                      horizontal:
                                                                          40.sp,
                                                                      vertical:
                                                                          16.sp),
                                                                  child: const Divider(
                                                                      color: Constants
                                                                          .gray),
                                                                )
                                                              : const SizedBox(),
                                                        ],
                                                      );
                                                    },
                                                  )
                                                :  CustomText(
                                                    text:
                                                        "There is no available accounts".tr,
                                                    alignment:
                                                        AlignmentDirectional
                                                            .center,
                                                  ),
                                          ),
                                        ),
                                        RSizedBox.vertical(24.h),
                                        CustomText(
                                          text:
                                              "People who can access my account".tr,
                                          color: Colors.black,
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        RSizedBox.vertical(12.h),
                                        Container(
                                            height: state.data.accessedBy!
                                                        .length ==
                                                    1
                                                ? 200.h
                                                : state.data.accessedBy!
                                                        .isNotEmpty
                                                    ? 350.h
                                                    : 50.h, // Set the desired height here

                                            decoration: ShapeDecoration(
                                              color: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                              shadows: const [
                                                BoxShadow(
                                                  color: Color(0x3F000000),
                                                  blurRadius: 40,
                                                  offset: Offset(0, 0),
                                                  spreadRadius: -10,
                                                )
                                              ],
                                            ),
                                            child: Padding(
                                              padding: EdgeInsets.all(16.sp),
                                              child:
                                                  state.data.accessedBy!
                                                          .isNotEmpty
                                                      ? ListView.builder(
                                                          clipBehavior:
                                                              Clip.hardEdge,
                                                          itemCount: state
                                                              .data
                                                              .accessedBy!
                                                              .length,
                                                          itemBuilder:
                                                              (item, index) {
                                                            return Column(
                                                              children: [
                                                                Padding(
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          4.0,
                                                                      vertical:
                                                                          4),
                                                                  child:
                                                                      EditPermissionWidget(
                                                                        
                                                                    accountName: state
                                                                        .data
                                                                        .accessedBy![
                                                                            index]
                                                                        .name
                                                                        ,
                                                                    permission: state
                                                                            .data
                                                                            .accessedBy![index]
                                                                            .role ??
                                                                        "",
                                                                        accessedUser: state.data.accessedBy![index],
                                                                         getAccessInfoCubit: GetAccessInfoCubit.get(context),
                                                                  ),
                                                                ),
                                                                (index !=
                                                                        state.data.accessedBy!.length -
                                                                            1)
                                                                    ? Padding(
                                                                        padding: EdgeInsets.symmetric(
                                                                            horizontal:
                                                                                40.sp,
                                                                            vertical: 16.sp),
                                                                        child: const Divider(
                                                                            color:
                                                                                Constants.gray),
                                                                      )
                                                                    : const SizedBox(),
                                                              ],
                                                            );
                                                          },
                                                        )
                                                      : CustomText(
                                                          text:
                                                              "There is no available accounts".tr,
                                                          alignment:
                                                              AlignmentDirectional
                                                                  .center,
                                                        ),
                                            )),
                                        RSizedBox.vertical(100.h),
                                      ],
                                    ),
                                  ),
                                )
                              : const SizedBox();
                },
              ),
              BlocBuilder<GetAccessInfoCubit, GetAccessInfoState>(
                builder: (context, state) {
                  return Positioned(
                    bottom: 45.h,
                    left: 20.w,
                    right: 20.w,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 75.sp),
                      child: SizedBox(
                        width: 200.w,
                        child: ButtonWidget(
                          iconData: Icons.add,
                          text: "Invite to Access".tr,
                          onTap: () {
                            Navigator.pushNamed(context, Routes.invite).then(
                              (value) {
                                if (value == true) {
                                  GetAccessInfoCubit.get(context)
                                      .getAccessInfo(context: context);
                                }
                              },
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:ads_dv/features/sidebar/management_area/data/data_source/management_area_data_source.dart';
import 'package:ads_dv/utils/network/failure_helper.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../ad_accounts/data/models/access_info.dart';

class ManagementAreaRepo {
  NetworkInfo networkInfo;
  ManagementAreaDataSource managementAreaDataSource;
  ManagementAreaRepo(
      {required this.networkInfo, required this.managementAreaDataSource});

  Future<Either<Failure, bool>> deleteAccess({
    required int accessId,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await managementAreaDataSource.deleteAccess(accessId: accessId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> addAccess({
    required String name,
    required String phone,
    required String email,
    required String role,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await managementAreaDataSource.addAccess(
            name: name, phone: phone, email: email, role: role);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> updateRole({
    required String role,
    required int accessId
  }) {
    return FailureHelper.instance(
      method: () async {
        return await managementAreaDataSource.updateRole(role: role,accessId: accessId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, AccessInfo>> getAccessInfo() {
    return FailureHelper.instance(
      method: () async {
        return await managementAreaDataSource.getAccessInfo();
      },
      networkInfo: networkInfo,
    );
  }
}

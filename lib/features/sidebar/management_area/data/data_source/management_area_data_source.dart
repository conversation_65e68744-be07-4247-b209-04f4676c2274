import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';
import '../../../ad_accounts/data/models/access_info.dart';

class ManagementAreaDataSource {
  Future<bool> deleteAccess({
    required int accessId,
  }) async {
    try {
      var response = await instance<NetworkCall>().request(
        EndPoints.deleteAccess(accessId),
        options: Options(
          method: Method.delete.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<AccessInfo> getAccessInfo() async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.addAccess,
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return AccessInfo.fromJson(response['data']);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> addAccess({
    required String name,
    required String phone,
    required String email,
    required String role,
  }) async {
    try {
      var response = await instance<NetworkCall>().request(
        params: FormData.fromMap({
          "name": name,
          "phone": phone,
          "email": email,
          "role": role,
        }),
        EndPoints.addAccess,
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> updateRole({
    required int accessId,
    required String role,
  }) async {
    try {
      var response = await instance<NetworkCall>().request(
        params: FormData.fromMap({
          "role": role,
        }),
        queryParameters: {
          '_method': 'put',
        },
        '${EndPoints.addAccess}/$accessId',
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );

      return true;
    } catch (error) {
      rethrow;
    }
  }
}

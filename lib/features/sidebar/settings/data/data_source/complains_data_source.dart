import 'package:ads_dv/utils/network/urls/end_points.dart';
import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';

class ComplainsDataSource{
  Future<bool> sendComplains(
      {required String title,required String description}) async {
    try {
      Map<String, dynamic> response =
      await instance<NetworkCall>().request(EndPoints.complaint,
          params: FormData.fromMap(
            {
              'title': title,
              'description': description,

            },
          ),
          options: Options(method: Method.post.name,headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },));
      return true;
    } catch (error) {
      rethrow;
    }
  }
}
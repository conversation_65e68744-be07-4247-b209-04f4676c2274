import 'package:ads_dv/features/sidebar/settings/data/data_source/complains_data_source.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/connection/network_info.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';

class ComplaintsRepo {
  NetworkInfo networkInfo;
  ComplainsDataSource complainsDataSource;

  ComplaintsRepo({required this.networkInfo, required this.complainsDataSource});

  Future<Either<Failure, bool>> sendComplains({required String title,required String description}) {
    return FailureHelper.instance(
        method: () async {
          return await complainsDataSource.sendComplains(title: title,description: description);
        },
        networkInfo: networkInfo);
  }


}

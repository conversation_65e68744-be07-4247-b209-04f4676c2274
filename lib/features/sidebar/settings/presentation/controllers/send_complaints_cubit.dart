import 'package:ads_dv/features/sidebar/settings/data/repos/complains_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';
import '../../../../../utils/res/custom_widgets.dart';

part 'send_complaints_state.dart';

class SendComplaintsCubit extends Cubit<SendComplaintsState> {
  SendComplaintsCubit() : super(SendComplaintsInitial());
  static SendComplaintsCubit get(context) => BlocProvider.of(context);

  sendComplaints({required String title,required String descriptions, required BuildContext context}) async {
    emit(SendComplaintsLoading());
    instance<ComplaintsRepo>().sendComplains(title: title,description: descriptions).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(SendComplaintsError(l));
      }, (r) async {
        showSuccessToast("Complaint sent successfully");

        emit(SendComplaintsLoaded(r));
      });
    });
  }
}

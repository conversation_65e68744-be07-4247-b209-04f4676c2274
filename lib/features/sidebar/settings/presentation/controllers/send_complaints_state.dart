part of 'send_complaints_cubit.dart';

@immutable
abstract class SendComplaintsState {
  const SendComplaintsState();
  List<Object?> get props => [];
}

class SendComplaintsInitial extends SendComplaintsState {}

class SendComplaintsLoading extends SendComplaintsState {}

class SendComplaintsLoaded extends SendComplaintsState {
  final bool data;

  const SendComplaintsLoaded(this.data);

  @override
  List<Object?> get props => [data];

  SendComplaintsLoaded copyWith({
    bool? data,
  }) {
    return SendComplaintsLoaded(
      data ?? this.data,
    );
  }
}

class SendComplaintsError extends SendComplaintsState {
  final Failure message;

  const SendComplaintsError(this.message);

  @override
  List<Object?> get props => [message];
}
import 'package:ads_dv/features/notifications/presentation/controllers/change_notifications_status/change_notifications_status_cubit.dart';
import 'package:ads_dv/features/notifications/presentation/controllers/get_notifications_status/get_notifications_status_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/decoration_container.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../../widgets/appbar.dart';
import '../../../../../../../../widgets/switch.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/lang/lang_cubit.dart';
import '../../../../../widgets/custom_dialogs.dart';
import '../../../../../widgets/handle_error_widget.dart';

class SettingScreen extends StatelessWidget {
  const SettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => GetNotificationsStatusCubit()
            ..getNotificationsStatus(context: context),
        ),
        BlocProvider(
          create: (context) => ChangeNotificationsStatusCubit(),
        ),
      ],
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          title: "Setting".tr,
          showBackButton: true,
          hasDrawer: true,
        ),
        body: BlocBuilder<GetNotificationsStatusCubit,
            GetNotificationsStatusState>(
          builder: (context, state) {
            return state is GetNotificationsStatusLoading
                ? const Center(
                    child: LoadingWidget(
                      isCircle: true,
                    ),
                  )
                : state is GetNotificationsStatusError
                    ? Center(
                        child: HandleErrorWidget(
                            fun: () {
                              GetNotificationsStatusCubit.get(context)
                                  .getNotificationsStatus(
                                context: context,
                              );
                            },
                            failure: state.message),
                      )
                    : state is GetNotificationsStatusLoaded
                        ? Padding(
                            padding: EdgeInsets.all(24.sp),
                            child: Column(
                              children: [
                                20.verticalSpace,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        const CustomSvgWidget(
                                            svg: AppAssets.changeLang),
                                        10.horizontalSpace,
                                        CustomText(
                                          text: "Change Language".tr,
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                          alignment:
                                              AlignmentDirectional.center,
                                        ),
                                      ],
                                    ),
                                    DecoratedContainer(
                                      radius: 47,
                                      color: Colors.white,
                                      boxShadow: const BoxShadow(
                                        color: Color(0x33000000),
                                        blurRadius: 20,
                                        offset: Offset(0, 4),
                                        spreadRadius: 0,
                                      ),
                                      widget: SlidingSwitch(
                                        value: instance<HiveHelper>().getLang() == 'ar' ? true : false,
                                        width: 55.h,
                                        onChanged: (bool value) {
                                          if(instance<HiveHelper>().getLang() == 'ar'){
                                            LangCubit.get(context).changeLang('en',context: context);
                                          }else {
                                            LangCubit.get(context).changeLang('ar',context: context);
                                          }
                                        },
                                        height: 25.h,
                                        animationDuration:
                                            const Duration(milliseconds: 400),
                                        onTap: () {},
                                        onDoubleTap: () {},
                                        onSwipe: () {},
                                        textOff: "Ar".tr,
                                        textOn: "En".tr,
                                        contentSize: 12,
                                        colorOn: Colors.white,
                                        colorOff: Colors.white,
                                        background: Colors.white,
                                        buttonColor: Colors.red,
                                        inactiveColor: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                                30.verticalSpace,
                                Row(
                                  children: [
                                    Row(
                                      children: [
                                        const CustomSvgWidget(
                                            svg: AppAssets.checkUpdate),
                                        10.horizontalSpace,
                                        CustomText(
                                          text: "Check for Update".tr,
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black,
                                          alignment:
                                              AlignmentDirectional.center,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                35.verticalSpace,
                                DecoratedContainer(
                                  radius: 16,
                                  color: Colors.white,
                                  boxShadow: const BoxShadow(
                                    color: Color(0x33000000),
                                    blurRadius: 40,
                                    offset: Offset(0, 0),
                                    spreadRadius: -10,
                                  ),
                                  widget: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 16.sp, vertical: 20.sp),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            const CustomSvgWidget(
                                                svg: AppAssets.not),
                                            10.horizontalSpace,
                                            CustomText(
                                              text: "Notifications".tr,
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w700,
                                              color: Colors.black,
                                              alignment:
                                                  AlignmentDirectional.center,
                                            ),
                                          ],
                                        ),
                                        20.verticalSpace,
                                        // Row(
                                        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        //   children: [
                                        //     CustomText(
                                        //       text: "Promotional Emails",
                                        //       fontSize: 12.sp,
                                        //       fontWeight: FontWeight.w400,
                                        //       color: Colors.black,
                                        //       alignment: AlignmentDirectional.center,
                                        //     ),
                                        //     DecoratedContainer(
                                        //       color: Colors.white,
                                        //       radius: 47,
                                        //       boxShadow: const BoxShadow(
                                        //         color: Color(0x33000000),
                                        //         blurRadius: 20,
                                        //         offset: Offset(0, 4),
                                        //         spreadRadius: 0,
                                        //       ),
                                        //       widget: SlidingSwitch(
                                        //         value: false,
                                        //         width: 55.h,
                                        //         onChanged: (bool value) {
                                        //           print(value);
                                        //         },
                                        //         height: 27.h,
                                        //         animationDuration:
                                        //         const Duration(milliseconds: 400),
                                        //         onTap: () {},
                                        //         onDoubleTap: () {},
                                        //         onSwipe: () {},
                                        //         textOff: "",
                                        //         textOn: "",
                                        //         contentSize: 12,
                                        //         colorOn: Colors.white,
                                        //         colorOff: Colors.white,
                                        //         background: Colors.white,
                                        //         buttonColor: Colors.red,
                                        //         inactiveColor: Colors.white,
                                        //       ),
                                        //     ),
                                        //   ],
                                        // ),
                                        // 15.verticalSpace,
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            // CustomText(
                                            //   text: "Promotional Notifications",
                                            //   fontSize: 12.sp,
                                            //   fontWeight: FontWeight.w400,
                                            //   color: Colors.black,
                                            //   alignment:
                                            //       AlignmentDirectional.center,
                                            // ),
                                            DecoratedContainer(
                                              color: Colors.white,
                                              radius: 47,
                                              boxShadow: const BoxShadow(
                                                color: Color(0x33000000),
                                                blurRadius: 20,
                                                offset: Offset(0, 4),
                                                spreadRadius: 0,
                                              ),
                                              widget: BlocBuilder<
                                                  ChangeNotificationsStatusCubit,
                                                  ChangeNotificationsStatusState>(
                                                builder: (context1,
                                                    changeStatusState) {
                                                  return SlidingSwitch(
                                                    value: state.data == 0
                                                        ? false
                                                        : true,
                                                    width: 55.h,
                                                    onChanged: (bool value) {
                                                      ChangeNotificationsStatusCubit
                                                              .get(context)
                                                          .changeNotificationStatus(
                                                              context:
                                                                  context1);
                                                    },
                                                    height: 27.h,
                                                    animationDuration:
                                                        const Duration(
                                                            milliseconds: 400),
                                                    onTap: () {},
                                                    onDoubleTap: () {},
                                                    onSwipe: () {},
                                                    textOff: "Off".tr,
                                                    textOn: "On".tr,
                                                    contentSize: 12,
                                                    colorOn: Colors.white,
                                                    colorOff: Colors.white,
                                                    background: Colors.white,
                                                    buttonColor: Colors.red,
                                                    inactiveColor: Colors.white,
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                20.verticalSpace,
                                ListTile(
                                  leading: ShaderMask(
                                    shaderCallback: (Rect bound) {
                                      return Constants.secGradient
                                          .createShader(bound);
                                    },
                                    child: Icon(
                                      Icons.dangerous_outlined,
                                      size: 22.h,
                                      color: Colors.white,
                                    ),
                                  ),
                                  title: CustomText(
                                    text: "Deactivate Account".tr,
                                    color: Colors.black,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  onTap: () {
                                    CustomDialogs.deactivateConfirmation(
                                        context,false);
                                  },
                                ),

                                ListTile(
                                  leading: ShaderMask(
                                    shaderCallback: (Rect bound) {
                                      return Constants.secGradient
                                          .createShader(bound);
                                    },
                                    child: Icon(
                                      Icons.delete_outlined,
                                      size: 22.h,
                                      color: Colors.white,
                                    ),
                                  ),
                                  title: CustomText(
                                    text: "Delete Account".tr,
                                    color: Colors.black,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  onTap: () {
                                    CustomDialogs.deactivateConfirmation(
                                        context,true);
                                    print("asfa${instance<HiveHelper>().getUser()!.accessToken}");
                                  },
                                ),
                              ],
                            ),
                          )
                        : const SizedBox();
          },
        ),
      ),
    );
  }
}

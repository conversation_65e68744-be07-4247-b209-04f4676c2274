import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/sidebar/reports/presentation/controllers/campaign_reports/campaign_reports_cubit.dart';
import 'package:ads_dv/features/sidebar/reports/presentation/controllers/change_status/change_status_cubit.dart';
import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/constants.dart';
import '../../../../../utils/res/custom_widgets.dart';
import '../../../../../utils/res/router/routes.dart';
import '../../../../../widgets/appbar.dart';
import '../../../../../widgets/handle_error_widget.dart';
import '../../../../../widgets/svg_widget.dart';
import '../../../../home_layout/presentation/controllers/home_layout_cubit.dart';
import '../../../ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../../../wallet/presentation/views/send_tickets_widget.dart';
import '../widgets/report_card_widget.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String searchWord = "";

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CampaignReportsCubit()
            ..campaignReports(
                context: context,
                accountId:
                    instance<HiveHelper>().getUser()?.defaultAccountId ?? ''),
        ),
        BlocProvider(
          create: (context) => ChangeStatusCubit(),
        ),
      ],
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          height: 180.h,
          logo: AppAssets.mainLogo,
          actions: const [
            ComplaintsWidget(),
          ],
          leading: Padding(
            padding: const EdgeInsets.all(8.0),
            child: InkWell(
              onTap: () {
                Constants.scaffoldKey.currentState!.openDrawer();
              },
              child: CustomSvgWidget(
                svg: AppAssets.drawer,
                height: 4.h,
                width: 12.h,
              ),
            ),
          ),
        ),
        body: BlocBuilder<CreateAdCubit, CreateAdState>(
          builder: (context, state) {
            return BlocBuilder<CampaignReportsCubit, CampaignsReportsState>(
              builder: (reportsContext, reportsState) {
                if (reportsState is CampaignsReportsStateError) {
                  return Center(
                    child: HandleErrorWidget(
                        fun: () {
                          CampaignReportsCubit.get(context).campaignReports(
                            accountId: instance<HiveHelper>()
                                    .getUser()
                                    ?.defaultAccountId ??
                                '',
                            context: reportsContext,
                          );
                        },
                        failure: reportsState.message),
                  );
                } else if (reportsState is CampaignsReportsStateLoaded) {
                  return Padding(
                    padding: EdgeInsets.only(
                        right: 24.sp, left: 24.sp, bottom: 24.sp, top: 24.sp),
                    child: reportsState.data.result!.data!.isEmpty
                        ? const Center(
                            child: CustomText(
                              text: 'There is no reports until now',
                              alignment: AlignmentDirectional.center,
                            ),
                          )
                        : BlocListener<ChangeStatusCubit, ChangeStatusState>(
                            listener: (listenContext, listenState) {
                              if (listenState is ChangeStatusLoaded) {
                                CampaignReportsCubit.get(context)
                                    .campaignReports(
                                        context: reportsContext,
                                        accountId: instance<HiveHelper>()
                                                .getUser()
                                                ?.defaultAccountId ??
                                            '');
                              }
                            },
                            child: SingleChildScrollView(
                              clipBehavior: Clip.none,
                              child: Column(
                                children: [
                                  if (instance<HiveHelper>()
                                          .getUser()
                                          ?.defaultAccountName !=
                                      null)
                                    Column(
                                      children: [
                                        5.verticalSpace,
                                        AccountHintText(
                                          isDefaultHint: true,
                                          hint:
                                              '${"Now you are using".tr} ${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}',
                                        ),
                                        20.verticalSpace,
                                      ],
                                    )
                                  else
                                    const SizedBox(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CustomText(
                                            text: 'Campaigns',
                                            color: Constants.darkColor,
                                            fontSize: 20.sp,
                                            fontWeight: FontWeight.w700,
                                          ),
                                          5.verticalSpace,
                                          CustomText(
                                            text:
                                                '${reportsState.data.result?.data?.length} total campaigns',
                                            color: const Color(0xFF5E5E5E),
                                            fontSize: 10.sp,
                                            fontWeight: FontWeight.w400,
                                          )
                                        ],
                                      ),
                                      BlocBuilder<HomeLayoutCubit,
                                          HomeLayoutState>(
                                        builder: (homeContext, homeState) {
                                          return InkWell(
                                            onTap: () {
                                              if (instance<HiveHelper>()
                                                          .getUser()
                                                          ?.defaultAccountId ==
                                                      null ||
                                                  instance<HiveHelper>()
                                                          .getUser()
                                                          ?.defaultPageAccessToken ==
                                                      null ||
                                                  instance<HiveHelper>()
                                                          .getUser()
                                                          ?.defaultPageId ==
                                                      null) {
                                                Navigator.pushNamed(context,
                                                    Routes.metaAccounts);
                                              } else if (instance<HiveHelper>()
                                                          .getUser()
                                                          ?.accessToken ==
                                                      null ||
                                                  instance<HiveHelper>()
                                                          .getUser()
                                                          ?.accessToken ==
                                                      "") {
                                                Navigator.pushNamed(context,
                                                        Routes.accounts)
                                                    .then((value) {
                                                  HomeLayoutCubit.get(context)
                                                      .hasCampaign(
                                                          context: homeContext);
                                                });
                                                showErrorToast(
                                                    "Please connect with your facebook account");
                                              } else {
                                                Navigator.pushNamed(context,
                                                    Routes.createCampaign);
                                              }
                                            },
                                            child: Container(
                                              width: 120.h,
                                              height: 35.h,
                                              decoration: ShapeDecoration(
                                                gradient: Constants.secGradient,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(52),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x23000000),
                                                    blurRadius: 22,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: 0,
                                                  )
                                                ],
                                              ),
                                              child: FittedBox(
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceEvenly,
                                                  children: [
                                                    const Icon(
                                                      Icons.add,
                                                      color: Colors.white,
                                                      size: 25,
                                                    ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              8.0),
                                                      child: CustomText(
                                                        text: 'New Campaign',
                                                        color: Colors.white,
                                                        fontSize: 10.sp,
                                                        fontWeight:
                                                            FontWeight.w700,
                                                        alignment:
                                                            AlignmentDirectional
                                                                .center,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      )
                                    ],
                                  ),
                                  18.verticalSpace,
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Container(
                                          width: 278.w,
                                          height: 40.h,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 16.sp),
                                          decoration: ShapeDecoration(
                                            color: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(14),
                                            ),
                                            shadows: const [
                                              BoxShadow(
                                                color: Color(0x33000000),
                                                blurRadius: 123.10,
                                                offset: Offset(0, 0),
                                                spreadRadius: 0,
                                              )
                                            ],
                                          ),
                                          child: Row(
                                            children: [
                                              const CustomSvgWidget(
                                                svg: AppAssets.search,
                                                height: 18,
                                              ),
                                              Expanded(
                                                  child: CustomTextField(
                                                borderColor: Colors.transparent,
                                                hintText:
                                                    "Search for Campaigns..",
                                                onChanged: (search) {
                                                  setState(() {
                                                    searchWord = search;
                                                  });
                                                  // Call the searchDrivers method to filter the drivers
                                                  context
                                                      .read<
                                                          CampaignReportsCubit>()
                                                      .searchForCampaign(
                                                          searchWord);
                                                },
                                                hintStyle: TextStyle(
                                                    fontSize: 14.sp,
                                                    color: Constants.gray),
                                                controller: _searchController,
                                              )),
                                            ],
                                          ),
                                        ),
                                      ),
                                      // 15.horizontalSpace,
                                      // Container(
                                      //   width: 40.h,
                                      //   height: 40.h,
                                      //   decoration: ShapeDecoration(
                                      //     color: Colors.white,
                                      //     shape: RoundedRectangleBorder(
                                      //       borderRadius:
                                      //           BorderRadius.circular(14),
                                      //     ),
                                      //     shadows: const [
                                      //       BoxShadow(
                                      //         color: Color(0x33000000),
                                      //         blurRadius: 123.10,
                                      //         offset: Offset(0, 0),
                                      //         spreadRadius: 0,
                                      //       )
                                      //     ],
                                      //   ),
                                      //   child: const Padding(
                                      //     padding: EdgeInsets.all(8.0),
                                      //     child: CustomSvgWidget(
                                      //       svg: AppAssets.filter,
                                      //     ),
                                      //   ),
                                      // ),
                                    ],
                                  ),
                                  30.verticalSpace,
                                  reportsState.data.result!.data!.isEmpty
                                      ? SizedBox(
                                          height:
                                              SizeConfig.screenHeight(context) *
                                                  0.3,
                                          child: const CustomText(
                                            text: 'There is no reports yet',
                                            alignment:
                                                AlignmentDirectional.center,
                                          ),
                                        )
                                      : ListView.builder(
                                          itemBuilder: (item, index) {
                                            return MetaCampaignCard(report: reportsState.data.result!.data![index],);
                                            //   Padding(
                                            //   padding: const EdgeInsets.only(
                                            //       bottom: 16.0),
                                            //   child: Stack(
                                            //     children: [
                                            //       Container(
                                            //         padding:
                                            //             EdgeInsets.all(12.sp),
                                            //         decoration: ShapeDecoration(
                                            //           color: Colors.white,
                                            //           shape:
                                            //               RoundedRectangleBorder(
                                            //             borderRadius:
                                            //                 BorderRadius
                                            //                     .circular(25),
                                            //           ),
                                            //           shadows: const [
                                            //             BoxShadow(
                                            //               color:
                                            //                   Color(0x4C000000),
                                            //               blurRadius: 50,
                                            //               offset: Offset(0, 4),
                                            //               spreadRadius: -10,
                                            //             )
                                            //           ],
                                            //         ),
                                            //         child: Column(
                                            //           children: [
                                            //             Row(
                                            //               mainAxisAlignment:
                                            //                   MainAxisAlignment
                                            //                       .spaceBetween,
                                            //               children: [
                                            //                 Row(
                                            //                   children: [
                                            //                     Container(
                                            //                       width: 45.h,
                                            //                       height: 45.h,
                                            //                       decoration:
                                            //                           ShapeDecoration(
                                            //                         color: const Color(
                                            //                             0xFFE4E9F1),
                                            //                         shape:
                                            //                             RoundedRectangleBorder(
                                            //                           borderRadius:
                                            //                               BorderRadius.circular(
                                            //                                   10),
                                            //                         ),
                                            //                         shadows: const [
                                            //                           BoxShadow(
                                            //                             color: Color(
                                            //                                 0x4C000000),
                                            //                             blurRadius:
                                            //                                 50,
                                            //                             offset: Offset(
                                            //                                 0,
                                            //                                 4),
                                            //                             spreadRadius:
                                            //                                 -10,
                                            //                           )
                                            //                         ],
                                            //                       ),
                                            //                       child:
                                            //                           Padding(
                                            //                         padding: EdgeInsets
                                            //                             .all(6
                                            //                                 .sp),
                                            //                         child:
                                            //                             const CustomSvgWidget(
                                            //                           svg: AppAssets
                                            //                               .fbIcons,
                                            //                         ),
                                            //                       ),
                                            //                     ),
                                            //                     8.horizontalSpace,
                                            //                     Container(
                                            //                       padding: const EdgeInsets
                                            //                           .symmetric(
                                            //                           horizontal:
                                            //                               12),
                                            //                       height: 45.h,
                                            //                       decoration:
                                            //                           ShapeDecoration(
                                            //                         color: const Color(
                                            //                             0x1906398A),
                                            //                         shape:
                                            //                             RoundedRectangleBorder(
                                            //                           borderRadius:
                                            //                               BorderRadius.circular(
                                            //                                   10),
                                            //                         ),
                                            //                       ),
                                            //                       child: Row(
                                            //                         mainAxisAlignment:
                                            //                             MainAxisAlignment
                                            //                                 .center,
                                            //                         children: [
                                            //                           CustomSvgWidget(
                                            //                             svg: reportsState.data.result?.data?[index].objective ==
                                            //                                     'OUTCOME_SALES'
                                            //                                 ? 'assets/icons/sales.svg'
                                            //                                 : reportsState.data.result?.data?[index].objective == 'OUTCOME_ENGAGEMENT'
                                            //                                     ? AppAssets.engagement
                                            //                                     : 'assets/icons/awareness.svg',
                                            //                             height:
                                            //                                 20,
                                            //                             color: const Color(
                                            //                                 0xFF06398A),
                                            //                           ),
                                            //                           4.horizontalSpace,
                                            //                           CustomText(
                                            //                             text: reportsState.data.result?.data?[index].objective ==
                                            //                                     'OUTCOME_SALES'
                                            //                                 ? 'Sales'
                                            //                                 : reportsState.data.result?.data?[index].objective == 'OUTCOME_ENGAGEMENT'
                                            //                                     ? 'Engagement'
                                            //                                     : 'Awareness',
                                            //                             color: const Color(
                                            //                                 0xFF06398A),
                                            //                             fontSize:
                                            //                                 12.sp,
                                            //                             fontWeight:
                                            //                                 FontWeight.w700,
                                            //                             alignment:
                                            //                                 AlignmentDirectional.center,
                                            //                           )
                                            //                         ],
                                            //                       ),
                                            //                     )
                                            //                   ],
                                            //                 ),
                                            //                 BlocBuilder<
                                            //                     ChangeStatusCubit,
                                            //                     ChangeStatusState>(
                                            //                   builder:
                                            //                       (statusContext,
                                            //                           statusState) {
                                            //                     return InkWell(
                                            //                       onTap: () {
                                            //                         ChangeStatusCubit.get(statusContext).showPopupMenu(
                                            //                             context,
                                            //                             reportsState.data.result?.data?[index].status ??
                                            //                                 "",
                                            //                             (selectedStatus) {
                                            //                           // Update the status in the reportsState when a new status is selected
                                            //                           reportsState
                                            //                               .data
                                            //                               .result
                                            //                               ?.data?[
                                            //                                   index]
                                            //                               .status = selectedStatus;
                                            //                         },
                                            //                             ChangeStatusCubit.get(
                                            //                                 statusContext),
                                            //                             reportsState.data.result?.data![index].id.toString() ??
                                            //                                 "");
                                            //                       },
                                            //                       child:
                                            //                           Container(
                                            //                         width: 85,
                                            //                         height:
                                            //                             39.h,
                                            //                         decoration:
                                            //                             ShapeDecoration(
                                            //                           shape:
                                            //                               RoundedRectangleBorder(
                                            //                             side:
                                            //                                 BorderSide(
                                            //                               width:
                                            //                                   1,
                                            //                               color: reportsState.data.result?.data?[index].status == 'ACTIVE'
                                            //                                   ? Constants.primaryTextColor
                                            //                                   : const Color(0xFF616161),
                                            //                             ),
                                            //                             borderRadius:
                                            //                                 BorderRadius.circular(10),
                                            //                           ),
                                            //                           shadows: const [
                                            //                             BoxShadow(
                                            //                               color:
                                            //                                   Color(0x4C000000),
                                            //                               blurRadius:
                                            //                                   50,
                                            //                               offset: Offset(
                                            //                                   0,
                                            //                                   4),
                                            //                               spreadRadius:
                                            //                                   -10,
                                            //                             )
                                            //                           ],
                                            //                         ),
                                            //                         child:
                                            //                             CustomText(
                                            //                           text: reportsState.data.result?.data?[index].status ==
                                            //                                   'ACTIVE'
                                            //                               ? 'Active'
                                            //                               : 'Paused',
                                            //                           color: reportsState.data.result?.data?[index].status ==
                                            //                                   'ACTIVE'
                                            //                               ? Constants
                                            //                                   .primaryTextColor
                                            //                               : const Color(
                                            //                                   0xFF616161),
                                            //                           fontSize:
                                            //                               12,
                                            //                           fontFamily:
                                            //                               'IBM Plex Sans Devanagari',
                                            //                           fontWeight:
                                            //                               FontWeight
                                            //                                   .w700,
                                            //                           alignment:
                                            //                               AlignmentDirectional
                                            //                                   .center,
                                            //                         ),
                                            //                       ),
                                            //                     );
                                            //                   },
                                            //                 ),
                                            //               ],
                                            //             ),
                                            //             15.verticalSpace,
                                            //             CustomText(
                                            //               text: reportsState
                                            //                       .data
                                            //                       .result
                                            //                       ?.data?[index]
                                            //                       .name ??
                                            //                   "",
                                            //               color: const Color(
                                            //                   0xFF0B0F26),
                                            //               fontSize: 16.sp,
                                            //               fontWeight:
                                            //                   FontWeight.w700,
                                            //             ),
                                            //             10.verticalSpace,
                                            //             (reportsState
                                            //                         .data
                                            //                         .result
                                            //                         ?.data?[
                                            //                             index]
                                            //                         .insights
                                            //                         ?.data !=
                                            //                     null)
                                            //                 ? reportsState
                                            //                         .data
                                            //                         .result!
                                            //                         .data![
                                            //                             index]
                                            //                         .insights!
                                            //                         .data!
                                            //                         .isNotEmpty
                                            //                     ? CustomText(
                                            //                         text:
                                            //                             '${CommonUtils.convertDateString(reportsState.data.result?.data?[index].insights!.data!.first.dateStart ?? "")} - ${CommonUtils.convertDateString(reportsState.data.result?.data?[index].insights!.data!.first.dateStop ?? "")}',
                                            //                         color: const Color(
                                            //                             0xFF0B0F26),
                                            //                         fontSize: 8,
                                            //                         fontWeight:
                                            //                             FontWeight
                                            //                                 .w400,
                                            //                       )
                                            //                     : const SizedBox()
                                            //                 : CustomText(
                                            //                     text: CommonUtils.convertDateString(reportsState
                                            //                             .data
                                            //                             .result
                                            //                             ?.data?[
                                            //                                 index]
                                            //                             .createdTime ??
                                            //                         ""),
                                            //                     color: const Color(
                                            //                         0xFF0B0F26),
                                            //                     fontSize: 8,
                                            //                     fontWeight:
                                            //                         FontWeight
                                            //                             .w400,
                                            //                   ),
                                            //             20.verticalSpace,
                                            //             reportsState
                                            //                         .data
                                            //                         .result
                                            //                         ?.data?[
                                            //                             index]
                                            //                         .insights !=
                                            //                     null
                                            //                 ? Row(
                                            //                     children: [
                                            //                       Expanded(
                                            //                         child:
                                            //                             Container(
                                            //                           decoration:
                                            //                               ShapeDecoration(
                                            //                             color: Colors
                                            //                                 .white,
                                            //                             shape:
                                            //                                 RoundedRectangleBorder(
                                            //                               borderRadius:
                                            //                                   BorderRadius.circular(12),
                                            //                             ),
                                            //                             shadows: const [
                                            //                               BoxShadow(
                                            //                                 color:
                                            //                                     Color(0x33000000),
                                            //                                 blurRadius:
                                            //                                     30,
                                            //                                 offset:
                                            //                                     Offset(0, 4),
                                            //                                 spreadRadius:
                                            //                                     -5,
                                            //                               )
                                            //                             ],
                                            //                           ),
                                            //                           child:
                                            //                               Padding(
                                            //                             padding:
                                            //                                 EdgeInsets.symmetric(vertical: 4.sp),
                                            //                             child:
                                            //                                 Column(
                                            //                               mainAxisAlignment:
                                            //                                   MainAxisAlignment.spaceBetween,
                                            //                               children: [
                                            //                                 const Center(
                                            //                                   child: CustomSvgWidget(svg: AppAssets.click),
                                            //                                 ),
                                            //                                 3.verticalSpace,
                                            //                                 CustomText(
                                            //                                   text: 'Clicks',
                                            //                                   textAlign: TextAlign.center,
                                            //                                   color: Constants.primaryTextColor,
                                            //                                   fontSize: 10.sp,
                                            //                                   fontWeight: FontWeight.w400,
                                            //                                   alignment: AlignmentDirectional.center,
                                            //                                 ),
                                            //                                 3.verticalSpace,
                                            //                                 CustomText(
                                            //                                   text: reportsState.data.result?.data?[index].insights?.data?.first.clicks ?? "",
                                            //                                   textAlign: TextAlign.center,
                                            //                                   color: Constants.primaryTextColor,
                                            //                                   alignment: AlignmentDirectional.center,
                                            //                                   fontSize: 18.sp,
                                            //                                   fontWeight: FontWeight.w400,
                                            //                                 )
                                            //                               ],
                                            //                             ),
                                            //                           ),
                                            //                         ),
                                            //                       ),
                                            //                       10.horizontalSpace,
                                            //                       Expanded(
                                            //                         child:
                                            //                             Container(
                                            //                           // width: 144.71,
                                            //                           // height: 78.45,
                                            //                           decoration:
                                            //                               ShapeDecoration(
                                            //                             color: Colors
                                            //                                 .white,
                                            //                             shape:
                                            //                                 RoundedRectangleBorder(
                                            //                               borderRadius:
                                            //                                   BorderRadius.circular(12),
                                            //                             ),
                                            //                             shadows: const [
                                            //                               BoxShadow(
                                            //                                 color:
                                            //                                     Color(0x33000000),
                                            //                                 blurRadius:
                                            //                                     30,
                                            //                                 offset:
                                            //                                     Offset(0, 4),
                                            //                                 spreadRadius:
                                            //                                     -5,
                                            //                               )
                                            //                             ],
                                            //                           ),
                                            //                           child:
                                            //                               Padding(
                                            //                             padding:
                                            //                                 EdgeInsets.symmetric(vertical: 4.sp),
                                            //                             child:
                                            //                                 Column(
                                            //                               mainAxisAlignment:
                                            //                                   MainAxisAlignment.spaceBetween,
                                            //                               children: [
                                            //                                 const Center(
                                            //                                   child: CustomSvgWidget(svg: AppAssets.salesValue),
                                            //                                 ),
                                            //                                 3.verticalSpace,
                                            //                                 CustomText(
                                            //                                   text: 'Impression',
                                            //                                   textAlign: TextAlign.center,
                                            //                                   color: Constants.primaryTextColor,
                                            //                                   fontSize: 10.sp,
                                            //                                   fontWeight: FontWeight.w400,
                                            //                                   alignment: AlignmentDirectional.center,
                                            //                                 ),
                                            //                                 3.verticalSpace,
                                            //                                 CustomText(
                                            //                                   text: reportsState.data.result?.data?[index].insights?.data?.first.impressions ?? "",
                                            //                                   textAlign: TextAlign.center,
                                            //                                   color: Constants.primaryTextColor,
                                            //                                   alignment: AlignmentDirectional.center,
                                            //                                   fontSize: 18.sp,
                                            //                                   fontWeight: FontWeight.w400,
                                            //                                 )
                                            //                               ],
                                            //                             ),
                                            //                           ),
                                            //                         ),
                                            //                       ),
                                            //                       15.horizontalSpace,
                                            //                       Expanded(
                                            //                         child:
                                            //                             Container(
                                            //                           // width: 144.71,
                                            //                           // height: 78.45,
                                            //                           decoration:
                                            //                               ShapeDecoration(
                                            //                             color: Colors
                                            //                                 .white,
                                            //                             shape:
                                            //                                 RoundedRectangleBorder(
                                            //                               borderRadius:
                                            //                                   BorderRadius.circular(12),
                                            //                             ),
                                            //                             shadows: const [
                                            //                               BoxShadow(
                                            //                                 color:
                                            //                                     Color(0x33000000),
                                            //                                 blurRadius:
                                            //                                     30,
                                            //                                 offset:
                                            //                                     Offset(0, 4),
                                            //                                 spreadRadius:
                                            //                                     -5,
                                            //                               )
                                            //                             ],
                                            //                           ),
                                            //                           child:
                                            //                               Padding(
                                            //                             padding:
                                            //                                 EdgeInsets.symmetric(vertical: 4.sp),
                                            //                             child:
                                            //                                 Column(
                                            //                               mainAxisAlignment:
                                            //                                   MainAxisAlignment.spaceBetween,
                                            //                               children: [
                                            //                                 const Center(
                                            //                                   child: CustomSvgWidget(svg: AppAssets.reach),
                                            //                                 ),
                                            //                                 3.verticalSpace,
                                            //                                 CustomText(
                                            //                                   text: 'Reach',
                                            //                                   textAlign: TextAlign.center,
                                            //                                   color: Constants.primaryTextColor,
                                            //                                   fontSize: 10.sp,
                                            //                                   fontWeight: FontWeight.w400,
                                            //                                   alignment: AlignmentDirectional.center,
                                            //                                 ),
                                            //                                 3.verticalSpace,
                                            //                                 CustomText(
                                            //                                   text: reportsState.data.result?.data?[index].insights?.data?.first.reach ?? "",
                                            //                                   textAlign: TextAlign.center,
                                            //                                   color: Constants.primaryTextColor,
                                            //                                   alignment: AlignmentDirectional.center,
                                            //                                   fontSize: 18.sp,
                                            //                                   fontWeight: FontWeight.w400,
                                            //                                 )
                                            //                               ],
                                            //                             ),
                                            //                           ),
                                            //                         ),
                                            //                       ),
                                            //                       15.horizontalSpace,
                                            //                     ],
                                            //                   )
                                            //                 : const SizedBox(),
                                            //             // 20.verticalSpace,
                                            //             // LinearPercentIndicator(
                                            //             //   animation: true,
                                            //             //   lineHeight:
                                            //             //       SizeConfig.hieghtr(
                                            //             //           13, context),
                                            //             //   animationDuration: 2000,
                                            //             //   percent: 60 / 100,
                                            //             //   padding:
                                            //             //       const EdgeInsets.only(
                                            //             //           left: 3),
                                            //             //   barRadius:
                                            //             //       const Radius.circular(40),
                                            //             //   progressColor: Constants
                                            //             //       .primaryTextColor,
                                            //             //   backgroundColor: Constants
                                            //             //       .gray
                                            //             //       .withOpacity(0.3),
                                            //             // ),
                                            //             // 15.verticalSpace,
                                            //             // Row(
                                            //             //   mainAxisAlignment:
                                            //             //       MainAxisAlignment
                                            //             //           .spaceBetween,
                                            //             //   children: [
                                            //             //     Text.rich(
                                            //             //       TextSpan(
                                            //             //         children: [
                                            //             //           TextSpan(
                                            //             //             text: 'Spend ',
                                            //             //             style: TextStyle(
                                            //             //               color: const Color(
                                            //             //                   0xFF0B0F26),
                                            //             //               fontSize: 10.sp,
                                            //             //               fontFamily:
                                            //             //                   'IBM Plex Sans Devanagari',
                                            //             //               fontWeight:
                                            //             //                   FontWeight
                                            //             //                       .w400,
                                            //             //               height: 0.16,
                                            //             //               letterSpacing:
                                            //             //                   -0.25,
                                            //             //             ),
                                            //             //           ),
                                            //             //           TextSpan(
                                            //             //             text: '1.560 AED',
                                            //             //             style: TextStyle(
                                            //             //               color: const Color(
                                            //             //                   0xFF06398A),
                                            //             //               fontSize: 10.sp,
                                            //             //               fontFamily:
                                            //             //                   'IBM Plex Sans Devanagari',
                                            //             //               fontWeight:
                                            //             //                   FontWeight
                                            //             //                       .w700,
                                            //             //               height: 0.16,
                                            //             //               letterSpacing:
                                            //             //                   -0.25,
                                            //             //             ),
                                            //             //           ),
                                            //             //         ],
                                            //             //       ),
                                            //             //     ),
                                            //             //     Text.rich(
                                            //             //       TextSpan(
                                            //             //         children: [
                                            //             //           TextSpan(
                                            //             //             text: 'All Budget ',
                                            //             //             style: TextStyle(
                                            //             //               color: const Color(
                                            //             //                   0xFF0B0F26),
                                            //             //               fontSize: 10.sp,
                                            //             //               fontFamily:
                                            //             //                   'IBM Plex Sans Devanagari',
                                            //             //               fontWeight:
                                            //             //                   FontWeight
                                            //             //                       .w400,
                                            //             //               height: 0.16,
                                            //             //               letterSpacing:
                                            //             //                   -0.25,
                                            //             //             ),
                                            //             //           ),
                                            //             //           TextSpan(
                                            //             //             text: '3.000 AED',
                                            //             //             style: TextStyle(
                                            //             //               color: const Color(
                                            //             //                   0xFF06398A),
                                            //             //               fontSize: 10.sp,
                                            //             //               fontFamily:
                                            //             //                   'IBM Plex Sans Devanagari',
                                            //             //               fontWeight:
                                            //             //                   FontWeight
                                            //             //                       .w700,
                                            //             //               height: 0.16,
                                            //             //               letterSpacing:
                                            //             //                   -0.25,
                                            //             //             ),
                                            //             //           ),
                                            //             //         ],
                                            //             //       ),
                                            //             //     )
                                            //             //   ],
                                            //             // ),
                                            //             60.verticalSpace,
                                            //           ],
                                            //         ),
                                            //       ),
                                            //       Positioned(
                                            //         right: 0,
                                            //         left: 0,
                                            //         bottom: 0,
                                            //         child: InkWell(
                                            //           onTap: () {
                                            //             Navigator.pushNamed(
                                            //               context,
                                            //               Routes
                                            //                   .campaignDetails,
                                            //               arguments: {
                                            //                 'campaign':
                                            //                     reportsState
                                            //                             .data
                                            //                             .result
                                            //                             ?.data?[
                                            //                         index],
                                            //               },
                                            //             );
                                            //           },
                                            //           child: Container(
                                            //             height: 56,
                                            //             decoration:
                                            //                 const ShapeDecoration(
                                            //               gradient: Constants
                                            //                   .secGradient,
                                            //               shape:
                                            //                   RoundedRectangleBorder(
                                            //                 borderRadius:
                                            //                     BorderRadius
                                            //                         .only(
                                            //                   bottomLeft: Radius
                                            //                       .circular(25),
                                            //                   bottomRight:
                                            //                       Radius
                                            //                           .circular(
                                            //                               25),
                                            //                 ),
                                            //               ),
                                            //               shadows: [
                                            //                 BoxShadow(
                                            //                   color: Color(
                                            //                       0x4C000000),
                                            //                   blurRadius: 50,
                                            //                   offset:
                                            //                       Offset(0, 4),
                                            //                   spreadRadius: -10,
                                            //                 )
                                            //               ],
                                            //             ),
                                            //             child: Row(
                                            //               mainAxisAlignment:
                                            //                   MainAxisAlignment
                                            //                       .center,
                                            //               crossAxisAlignment:
                                            //                   CrossAxisAlignment
                                            //                       .center,
                                            //               children: [
                                            //                 CustomText(
                                            //                   text:
                                            //                       'All Details',
                                            //                   color:
                                            //                       Colors.white,
                                            //                   fontSize: 16.sp,
                                            //                   fontWeight:
                                            //                       FontWeight
                                            //                           .w400,
                                            //                   alignment:
                                            //                       AlignmentDirectional
                                            //                           .center,
                                            //                 ),
                                            //                 8.horizontalSpace,
                                            //                 const CustomSvgWidget(
                                            //                     svg: AppAssets
                                            //                         .arrowUp),
                                            //               ],
                                            //             ),
                                            //           ),
                                            //         ),
                                            //       )
                                            //     ],
                                            //   ),
                                            // );
                                          },
                                          itemCount: reportsState
                                              .data.result?.data?.length,
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                        ),
                                  30.verticalSpace,
                                  (reportsState.data.result?.next == "null" ||
                                          reportsState.data.result?.next ==
                                              null)
                                      ? const SizedBox()
                                      : Column(
                                          children: [
                                            reportsState
                                                    is CampaignsReportsStateLoading
                                                ? const LoadingWidget(
                                                    isCircle: true,
                                                  )
                                                : InkWell(
                                                    onTap: () {
                                                      CampaignReportsCubit.get(
                                                              reportsContext)
                                                          .loadMoreReports(
                                                        url: reportsState.data
                                                                .result?.next ??
                                                            "",
                                                        context: reportsContext,
                                                      );
                                                    },
                                                    child: Container(
                                                      decoration:
                                                          ShapeDecoration(
                                                        gradient: Constants
                                                            .defGradient,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(38),
                                                        ),
                                                        shadows: const [
                                                          BoxShadow(
                                                            color: Color(
                                                                0x19000000),
                                                            blurRadius: 22,
                                                            offset:
                                                                Offset(0, 4),
                                                            spreadRadius: 0,
                                                          )
                                                        ],
                                                      ),
                                                      child: Padding(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                vertical: 8.sp,
                                                                horizontal:
                                                                    14.sp),
                                                        child: Text(
                                                          'Load More'.tr,
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 12.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                          ],
                                        ),
                                  100.verticalSpace,
                                ],
                              ),
                            ),
                          ),
                  );
                } else {
                  return const Center(
                    child: LoadingWidget(
                      isCircle: true,
                    ),
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }
}

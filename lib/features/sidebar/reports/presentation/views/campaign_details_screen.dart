import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/campaign_response.dart';
import 'package:ads_dv/features/sidebar/reports/presentation/controllers/change_status/change_status_cubit.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/res/app_assets.dart';
import '../../../../../utils/res/colors.dart';
import '../../../../../utils/res/common_utils.dart';
import '../../../../../widgets/appbar.dart';
import '../../../../../widgets/handle_error_widget.dart';
import '../../../../../widgets/svg_widget.dart';
import '../../../ad_accounts/presentation/views/widgets/account_hint_text.dart';
import '../controllers/campaign_insights/campaign_insights_cubit.dart';

class CampaignDetailsScreen extends StatelessWidget {
  CampaignReports campaign;

  CampaignDetailsScreen({super.key, required this.campaign});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: "Campaign Details",
        showBackButton: true,
        hasDrawer: true,
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => CampaignInsightsCubit()
              ..getCampaignInsights(
                  context: context,
                  campaignId: campaign.id ?? "",
                  pageAccessToken: instance<HiveHelper>()
                          .getUser()
                          ?.defaultPageAccessToken ??
                      ""),
          ),
          BlocProvider(
            create: (context) => ChangeStatusCubit(),
          ),
        ],
        child: BlocBuilder<CreateAdCubit, CreateAdState>(
          builder: (context, state) {
            return BlocBuilder<CampaignInsightsCubit, CampaignInsightsState>(
              builder: (campaignContext, campaignState) {
                if (campaignState is CampaignInsightsStateError) {
                  return Center(
                    child: HandleErrorWidget(
                        fun: () {
                          CampaignInsightsCubit.get(context)
                              .getCampaignInsights(
                            campaignId: campaign.id ?? "",
                            pageAccessToken: instance<HiveHelper>()
                                    .getUser()
                                    ?.defaultPageAccessToken ??
                                "",
                            context: campaignContext,
                          );
                        },
                        failure: campaignState.message),
                  );
                } else if (campaignState is CampaignInsightsStateLoaded) {
                  return BlocListener<ChangeStatusCubit, ChangeStatusState>(
                    listener: (listenContext, listenState) {
                      if (listenState is ChangeStatusLoaded) {
                        CampaignInsightsCubit.get(context).getCampaignInsights(
                          campaignId: campaign.id ?? "",
                          pageAccessToken: instance<HiveHelper>()
                                  .getUser()
                                  ?.defaultPageAccessToken ??
                              "",
                          context: campaignContext,
                        );
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.all(24.sp),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            if (instance<HiveHelper>()
                                    .getUser()
                                    ?.defaultAccountName !=
                                null)
                              Column(
                                children: [
                                  5.verticalSpace,
                                  AccountHintText(
                                    isDefaultHint: true,
                                    hint:
                                        '${"Now you are using".tr} ${instance<HiveHelper>().getUser()?.defaultAccountName ?? ""}${"'s Ad Account".tr}',
                                  ),
                                  20.verticalSpace,
                                ],
                              )
                            else
                              const SizedBox(),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 15.verticalSpace,
                                    FittedBox(
                                      child: SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.5,
                                        child: CustomText(
                                          text: campaign.name ?? "",
                                          color: Constants.darkColor,
                                          fontSize: 16.sp,
                                          maxLines: 3,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    15.verticalSpace,
                                    CustomText(
                                      text: 'Campaign Dates:',
                                      color: const Color(0xFF8E8E8E),
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w700,
                                    ),
                                    15.verticalSpace,
                                    campaign.insights != null
                                        ? CustomText(
                                            text:
                                                '${CommonUtils.convertDateString(campaign.insights!.data!.first.dateStart ?? "")} - ${CommonUtils.convertDateString(campaign.insights!.data!.first.dateStop ?? "")}',
                                            color: const Color(0xFF8E8E8E),
                                            fontSize: 8.sp,
                                            fontWeight: FontWeight.w400,
                                          )
                                        : CustomText(
                                            text: CommonUtils.convertDateString(
                                                campaign.createdTime ?? ""),
                                            color: const Color(0xFF8E8E8E),
                                            fontSize: 8.sp,
                                            fontWeight: FontWeight.w400,
                                          ),
                                    15.verticalSpace,
                                    Row(
                                      children: [
                                        Container(
                                          width: 45.h,
                                          height: 45.h,
                                          decoration: ShapeDecoration(
                                            color: const Color(0xFFE4E9F1),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            shadows: const [
                                              BoxShadow(
                                                color: Color(0x4C000000),
                                                blurRadius: 50,
                                                offset: Offset(0, 4),
                                                spreadRadius: -10,
                                              )
                                            ],
                                          ),
                                          child: Padding(
                                            padding: EdgeInsets.all(6.sp),
                                            child: const CustomSvgWidget(
                                              svg: AppAssets.fbIcons,
                                            ),
                                          ),
                                        ),
                                        8.horizontalSpace,
                                        Container(
                                          height: 45.h,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 12.sp),
                                          decoration: ShapeDecoration(
                                            color: const Color(0x1906398A),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              CustomSvgWidget(
                                                color:
                                                    Constants.primaryTextColor,
                                                svg: campaign.objective ==
                                                        'OUTCOME_SALES'
                                                    ? 'assets/icons/sales.svg'
                                                    : campaign.objective ==
                                                            'OUTCOME_ENGAGEMENT'
                                                        ? AppAssets.engagement
                                                        : 'assets/icons/awareness.svg',
                                                height: 20,
                                              ),
                                              4.horizontalSpace,
                                              CustomText(
                                                text: campaign.objective ==
                                                        'OUTCOME_SALES'
                                                    ? 'Sales'
                                                    : campaign.objective ==
                                                            'OUTCOME_ENGAGEMENT'
                                                        ? 'Engagement'
                                                        : 'Awareness',
                                                color: const Color(0xFF06398A),
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w700,
                                                alignment:
                                                    AlignmentDirectional.center,
                                              )
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                                Padding(
                                  padding: EdgeInsets.only(top: 30.sp),
                                  child: InkWell(
                                    onTap: () {
                                      // CommonUtils.showBottomDialog(
                                      //   context,
                                      //   Column(
                                      //     mainAxisSize: MainAxisSize.min,
                                      //     children: [
                                      //       Padding(
                                      //         padding: EdgeInsets.all(24.sp),
                                      //         child: Row(
                                      //           mainAxisAlignment:
                                      //               MainAxisAlignment.spaceBetween,
                                      //           children: [
                                      //             CustomText(
                                      //               text: 'Metric Details',
                                      //               color: Constants.primaryTextColor,
                                      //               fontSize: 18.sp,
                                      //               fontWeight: FontWeight.w400,
                                      //             ),
                                      //             const Icon(
                                      //               Icons.close,
                                      //               color: Colors.grey,
                                      //             ),
                                      //           ],
                                      //         ),
                                      //       ),
                                      //       Padding(
                                      //         padding: const EdgeInsets.symmetric(
                                      //             horizontal: 20.0),
                                      //         child: StatefulBuilder(
                                      //           builder: (context, setState) => SizedBox(
                                      //             height: SizeConfig.hieghtr(35, context),
                                      //             child: ListView.builder(
                                      //               scrollDirection: Axis.horizontal,
                                      //               itemBuilder: (context, index) {
                                      //                 return Padding(
                                      //                   padding: const EdgeInsets.all(4.0),
                                      //                   child: InkWell(
                                      //                     onTap: () {
                                      //                       setState(() {
                                      //                         _selectIndex = index;
                                      //                       });
                                      //                     },
                                      //                     child: Container(
                                      //                       height: SizeConfig.hieghtr(
                                      //                           28, context),
                                      //                       width: SizeConfig.widthr(
                                      //                           85, context),
                                      //                       decoration: BoxDecoration(
                                      //                         color: index == _selectIndex
                                      //                             ? Constants.darkColor
                                      //                             : AppColors.whiteColor,
                                      //                         borderRadius:
                                      //                             BorderRadius.circular(28),
                                      //                         border: Border.all(
                                      //                             color: index ==
                                      //                                     _selectIndex
                                      //                                 ? Constants.darkColor
                                      //                                 : Constants
                                      //                                     .darkColor),
                                      //                       ),
                                      //                       child: Center(
                                      //                         child: Padding(
                                      //                           padding:
                                      //                               const EdgeInsets.all(
                                      //                                   4.0),
                                      //                           child: FittedBox(
                                      //                             fit: BoxFit.scaleDown,
                                      //                             child: CustomText(
                                      //                               text: dummy[index],
                                      //                               fontSize: 12.0,
                                      //                               fontWeight: index ==
                                      //                                       _selectIndex
                                      //                                   ? FontWeight.w600
                                      //                                   : FontWeight.w400,
                                      //                               color: index ==
                                      //                                       _selectIndex
                                      //                                   ? AppColors
                                      //                                       .whiteColor
                                      //                                   : Constants
                                      //                                       .darkColor,
                                      //                             ),
                                      //                           ),
                                      //                         ),
                                      //                       ),
                                      //                     ),
                                      //                   ),
                                      //                 );
                                      //               },
                                      //               itemCount: dummy.length,
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       20.verticalSpace,
                                      //       Container(
                                      //         width: double.infinity,
                                      //         height: 48.h,
                                      //         decoration: const BoxDecoration(
                                      //           color: Colors.white,
                                      //           boxShadow: [
                                      //             BoxShadow(
                                      //               color: Color(0x26000000),
                                      //               blurRadius: 40,
                                      //               offset: Offset(0, 4),
                                      //               spreadRadius: 0,
                                      //             )
                                      //           ],
                                      //         ),
                                      //         child: Padding(
                                      //           padding:  EdgeInsets.symmetric(
                                      //               horizontal: 20.0,vertical: 7.sp),
                                      //           child: StatefulBuilder(
                                      //             builder: (context, setState) => ListView.builder(
                                      //               scrollDirection: Axis.horizontal,
                                      //               itemBuilder: (context, index) {
                                      //                 if(index == 0){
                                      //                   return InkWell(
                                      //                     onTap: (){
                                      //                       setState(() {
                                      //                         _selectIndex1 = index;
                                      //
                                      //                       });
                                      //                     },
                                      //                     child: _selectIndex1 != index ? Center(
                                      //                       child: Padding(
                                      //                         padding:
                                      //                         const EdgeInsets.all(
                                      //                             4.0),
                                      //                         child: FittedBox(
                                      //                           fit: BoxFit.scaleDown,
                                      //                           child: CustomText(
                                      //                             text: dummy1[0],
                                      //                             fontSize: 12.0,
                                      //                             fontWeight: FontWeight.w400,
                                      //                             color:  Constants
                                      //                                 .gray,
                                      //                           ),
                                      //                         ),
                                      //                       ),
                                      //                     ):Column(
                                      //                       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      //                       children: [
                                      //                         ShaderMask(
                                      //                           shaderCallback:(Rect rect){
                                      //                             return Constants.secGradient.createShader(rect);
                                      //                           },
                                      //                           child: CustomText(
                                      //                             text: dummy1[0],
                                      //                             fontSize: 12.0,
                                      //                             fontWeight: FontWeight.w700,
                                      //                             color:  Colors.white,
                                      //                           ),
                                      //                         ),
                                      //                         Container(
                                      //                           width: 26,
                                      //                           height: 2,
                                      //                           decoration: ShapeDecoration(
                                      //                             gradient: Constants.secGradient,
                                      //                             shape: RoundedRectangleBorder(
                                      //                               borderRadius: BorderRadius.circular(50),
                                      //                             ),
                                      //                           ),
                                      //                         )
                                      //                       ],
                                      //                     ),
                                      //                   );
                                      //                 }else{
                                      //                   return Padding(
                                      //                     padding: const EdgeInsets.all(4.0),
                                      //                     child: InkWell(
                                      //                       onTap: () {
                                      //                         setState(() {
                                      //                           _selectIndex1 = index;
                                      //                         });
                                      //                       },
                                      //                       child: _selectIndex1 == index ?Container(
                                      //                         height: SizeConfig.hieghtr(
                                      //                             28, context),
                                      //                         width: SizeConfig.widthr(
                                      //                             70, context),
                                      //                         decoration: ShapeDecoration(
                                      //                           gradient: Constants.secGradient,
                                      //                           shape: RoundedRectangleBorder(
                                      //                             borderRadius: BorderRadius.circular(40),
                                      //                           ),
                                      //                         ),
                                      //
                                      //                         child: Center(
                                      //                           child: Padding(
                                      //                             padding:
                                      //                             const EdgeInsets.all(
                                      //                                 4.0),
                                      //                             child: FittedBox(
                                      //                               fit: BoxFit.scaleDown,
                                      //                               child: CustomText(
                                      //                                 text: dummy1[index],
                                      //                                 fontSize: 12.0,
                                      //                                 fontWeight:  FontWeight.w600
                                      //                                     ,
                                      //                                 color:  AppColors
                                      //                                     .whiteColor
                                      //                                     ,
                                      //                               ),
                                      //                             ),
                                      //                           ),
                                      //                         ),
                                      //                       ):Center(
                                      //                         child: Padding(
                                      //                           padding:
                                      //                           const EdgeInsets.all(
                                      //                               4.0),
                                      //                           child: FittedBox(
                                      //                             fit: BoxFit.scaleDown,
                                      //                             child: CustomText(
                                      //                               text: dummy1[index],
                                      //                               fontSize: 12.0,
                                      //                               fontWeight: FontWeight.w400,
                                      //                               color:  Constants
                                      //                                   .gray,
                                      //                             ),
                                      //                           ),
                                      //                         ),
                                      //                       ),
                                      //                     ),
                                      //                   );
                                      //                 }
                                      //
                                      //               },
                                      //               itemCount: dummy1.length,
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       20.verticalSpace,
                                      //       CustomText(
                                      //         text: 'Total',
                                      //         color: const Color(0xFFB5B5B5),
                                      //         fontSize: 14.sp,
                                      //         alignment: AlignmentDirectional.center,
                                      //         fontWeight: FontWeight.w400,
                                      //       ),
                                      //       15.verticalSpace,
                                      //       const CustomText(
                                      //         text: '146.351 AED',
                                      //         color: Constants.primaryTextColor,
                                      //         fontSize: 20,
                                      //         alignment: AlignmentDirectional.center,
                                      //         fontWeight: FontWeight.w700,
                                      //       ),
                                      //       15.verticalSpace,
                                      //       Padding(
                                      //         padding:
                                      //             EdgeInsets.symmetric(horizontal: 12.sp),
                                      //         child: SfCartesianChart(
                                      //           // Initialize category axis
                                      //           primaryXAxis: const CategoryAxis(),
                                      //           series: <LineSeries<SalesData, String>>[
                                      //             LineSeries<SalesData, String>(
                                      //               // Bind data source
                                      //               color: Constants.darkColor,
                                      //               dataSource: <SalesData>[
                                      //                 SalesData('Jan', 0),
                                      //                 SalesData('Feb', 15),
                                      //                 SalesData('Mar', 34),
                                      //                 SalesData('Apr', 32),
                                      //                 SalesData('May', 18)
                                      //               ],
                                      //               xValueMapper: (SalesData sales, _) =>
                                      //                   sales.year,
                                      //               yValueMapper: (SalesData sales, _) =>
                                      //                   sales.sales,
                                      //               markerSettings: const MarkerSettings(
                                      //                 shape: DataMarkerType.image,
                                      //                 image: AssetImage(
                                      //                   AppAssets.dot,
                                      //                 ),
                                      //                 height: 10,
                                      //                 width: 10,
                                      //                 isVisible:
                                      //                     true, // Set to true to show markers
                                      //               ),
                                      //             )
                                      //           ],
                                      //         ),
                                      //       ),
                                      //       50.verticalSpace,
                                      //     ],
                                      //   ),
                                      // );
                                    },
                                    child: Container(
                                      width: 85.h,
                                      height: 40.h,
                                      decoration: BoxDecoration(
                                          border: const GradientBoxBorder(
                                            gradient: Constants.secGradient,
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(10.sp)),
                                      child: ShaderMask(
                                        shaderCallback: (Rect bound) {
                                          return Constants.secGradient
                                              .createShader(bound);
                                        },
                                        child: CustomText(
                                          text: campaign.status == 'ACTIVE'
                                              ? 'Active'
                                              : 'Paused',
                                          color: Colors.white,
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w700,
                                          alignment:
                                              AlignmentDirectional.center,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // Positioned(
                                //   right: -20,
                                //   bottom: -135,
                                //   child: SizedBox(
                                //     height: 400.h,
                                //     width: 170.h,
                                //     child: const CustomSvgWidget(
                                //       svg: AppAssets.preview,
                                //     ),
                                //   ),
                                // ),
                              ],
                            ),
                            20.verticalSpace,
                            // Container(
                            //   width: double.infinity,
                            //   height: 107.h,
                            //   padding: EdgeInsets.symmetric(horizontal: 12.sp),
                            //   decoration: ShapeDecoration(
                            //     color: Colors.white,
                            //     shape: RoundedRectangleBorder(
                            //       side:
                            //           const BorderSide(width: 1, color: Constants.darkColor),
                            //       borderRadius: BorderRadius.circular(20),
                            //     ),
                            //     shadows: const [
                            //       BoxShadow(
                            //         color: Color(0x33000000),
                            //         blurRadius: 150.20,
                            //         offset: Offset(0, 0),
                            //         spreadRadius: 0,
                            //       )
                            //     ],
                            //   ),
                            //   child: Column(
                            //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            //     children: [
                            //       CustomText(
                            //         text: 'Campaign URL',
                            //         color: Constants.primaryTextColor,
                            //         fontSize: 15.sp,
                            //         fontWeight: FontWeight.w400,
                            //       ),
                            //       CustomTextField(
                            //         filledColor: const Color(0xFFD3D7DE),
                            //         borderColor: Colors.transparent,
                            //         style: TextStyle(
                            //             fontSize: 12.sp,
                            //             color: const Color(0xFF919CAE),
                            //             fontWeight: FontWeight.w400),
                            //         controller: _linkController,
                            //         suffixIcon: const Padding(
                            //           padding: EdgeInsets.all(15.0),
                            //           child: CustomSvgWidget(
                            //             svg: AppAssets.file,
                            //             height: 18,
                            //           ),
                            //         ),
                            //       ),
                            //     ],
                            //   ),
                            // ),
                            // 25.verticalSpace,
                            // ExpansionTileBorderItem(
                            //   expansionKey: Constants.expansionTileKey,
                            //   onExpansionChanged: (val) {},
                            //   childrenPadding:
                            //   const EdgeInsets.symmetric(horizontal: 16),
                            //   iconColor: AppColors.secondColor,
                            //   collapsedIconColor: AppColors.secondColor,
                            //   expandedAlignment: Alignment.center,
                            //   expandedCrossAxisAlignment: CrossAxisAlignment.center,
                            //   trailing: Row(
                            //     mainAxisSize: MainAxisSize.min,
                            //     children: [
                            //       Padding(
                            //         padding: const EdgeInsets.only(left: 6.0),
                            //         child: ShaderMask(
                            //           shaderCallback: (Rect bounds) {
                            //             return Constants.secGradient
                            //                 .createShader(bounds);
                            //           },
                            //           child: const Icon(
                            //             Icons.expand_more,
                            //             size: 24.0,
                            //             color: Colors.white,
                            //           ),
                            //         ),
                            //       )
                            //     ],
                            //   ),
                            //   title: const CustomText(
                            //       text: 'Performance summary',
                            //       color: AppColors.mainColor,
                            //       fontSize: 16,
                            //       fontWeight: FontWeight.w400),
                            //   decoration: BoxDecoration(
                            //       borderRadius: BorderRadius.circular(20),
                            //       border: Border.all(color: AppColors.mainColor)),
                            //   children: [
                            //     const SizedBox(height: 20),
                            //     Container(
                            //       decoration: ShapeDecoration(
                            //         color: Colors.white,
                            //         shape: RoundedRectangleBorder(
                            //           borderRadius: BorderRadius.circular(40),
                            //         ),
                            //         shadows: const [
                            //           BoxShadow(
                            //             color: Color(0x3D000000),
                            //             blurRadius: 13.93,
                            //             offset: Offset(0, 0),
                            //             spreadRadius: -3.80,
                            //           )
                            //         ],
                            //       ),
                            //       child: Column(
                            //         children: [
                            //           Container(
                            //             //height: 242.h,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(20),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x3D000000),
                            //                   blurRadius: 13.93,
                            //                   offset: Offset(0, 0),
                            //                   spreadRadius: -3.80,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Column(
                            //               mainAxisAlignment:
                            //               MainAxisAlignment.spaceBetween,
                            //               children: [
                            //                 Padding(
                            //                   padding: const EdgeInsets.all(14.0),
                            //                   child: CustomText(
                            //                     text: "Spend",
                            //                     fontWeight: FontWeight.w700,
                            //                     fontSize: 16.sp,
                            //                     alignment: AlignmentDirectional
                            //                         .centerStart,
                            //                     color: Constants.darkColor,
                            //                   ),
                            //                 ),
                            //                 Center(
                            //                   child: CircularPercentIndicator(
                            //                     radius: 80.0,
                            //                     animation: true,
                            //                     animationDuration: 1200,
                            //                     lineWidth: 13.sp,
                            //                     percent: 0.65,
                            //                     center: Column(
                            //                       mainAxisAlignment:
                            //                       MainAxisAlignment.center,
                            //                       crossAxisAlignment:
                            //                       CrossAxisAlignment.center,
                            //                       children: [
                            //                         ShaderMask(
                            //                           shaderCallback:
                            //                               (Rect bounds) {
                            //                             return Constants.defGradient
                            //                                 .createShader(bounds);
                            //                           },
                            //                           child: CustomText(
                            //                             text: "80.45",
                            //                             fontWeight: FontWeight.w700,
                            //                             fontSize: 19.sp,
                            //                             alignment:
                            //                             AlignmentDirectional
                            //                                 .center,
                            //                             color: Colors.white,
                            //                           ),
                            //                         ),
                            //                         6.verticalSpace,
                            //                         ShaderMask(
                            //                           shaderCallback:
                            //                               (Rect bounds) {
                            //                             return Constants.defGradient
                            //                                 .createShader(bounds);
                            //                           },
                            //                           child: CustomText(
                            //                             text: "Pacing",
                            //                             fontWeight: FontWeight.w300,
                            //                             fontSize: 12.sp,
                            //                             alignment:
                            //                             AlignmentDirectional
                            //                                 .center,
                            //                             color: Colors.white,
                            //                           ),
                            //                         ),
                            //                       ],
                            //                     ),
                            //                     reverse: true,
                            //                     circularStrokeCap:
                            //                     CircularStrokeCap.round,
                            //                     linearGradient:
                            //                     Constants.secGradient,
                            //                     backgroundColor:
                            //                     const Color(0xFFFB533E)
                            //                         .withOpacity(0.1),
                            //                   ),
                            //                 ),
                            //                 10.verticalSpace,
                            //                 Padding(
                            //                   padding: const EdgeInsets.all(14.0),
                            //                   child: Row(
                            //                     mainAxisAlignment:
                            //                     MainAxisAlignment.spaceBetween,
                            //                     children: [
                            //                       Column(
                            //                         children: [
                            //                           const CustomText(
                            //                             text: 'Campaign Budget',
                            //                             color: Color(0xFFACACAC),
                            //                             fontSize: 10,
                            //                             fontWeight: FontWeight.w300,
                            //                           ),
                            //                           5.verticalSpace,
                            //                           const CustomText(
                            //                             text: '12.250 AED',
                            //                             color: Color(0xFF06398A),
                            //                             fontSize: 10,
                            //                             fontWeight: FontWeight.w400,
                            //                           )
                            //                         ],
                            //                       ),
                            //                       Column(
                            //                         children: [
                            //                           const CustomText(
                            //                             text: 'Amount Spent',
                            //                             color: Color(0xFFACACAC),
                            //                             fontSize: 10,
                            //                             fontWeight: FontWeight.w300,
                            //                           ),
                            //                           5.verticalSpace,
                            //                           const CustomText(
                            //                             text: '54.55 AED',
                            //                             color: Color(0xFF06398A),
                            //                             fontSize: 10,
                            //                             fontWeight: FontWeight.w400,
                            //                           )
                            //                         ],
                            //                       ),
                            //                       Column(
                            //                         children: [
                            //                           const CustomText(
                            //                             text: 'Daily Budget',
                            //                             color: Color(0xFFACACAC),
                            //                             fontSize: 10,
                            //                             fontWeight: FontWeight.w300,
                            //                           ),
                            //                           5.verticalSpace,
                            //                           const CustomText(
                            //                             text: '120 AED',
                            //                             color: Color(0xFF06398A),
                            //                             fontSize: 10,
                            //                             fontWeight: FontWeight.w400,
                            //                           )
                            //                         ],
                            //                       ),
                            //                     ],
                            //                   ),
                            //                 ),
                            //               ],
                            //             ),
                            //           ),
                            //         ],
                            //       ),
                            //     ),
                            //     15.verticalSpace,
                            //     const CustomText(
                            //       text:
                            //       'Click on a metric below to see more information',
                            //       color: Color(0xFF06398A),
                            //       fontSize: 8,
                            //       fontWeight: FontWeight.w300,
                            //       alignment: AlignmentDirectional.center,
                            //     ),
                            //     15.verticalSpace,
                            //     Row(
                            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Expanded(
                            //           child: Container(
                            //             // width: 144.71,
                            //             // height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.click),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Clicks',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '6.120',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //         10.horizontalSpace,
                            //         Expanded(
                            //           child: Container(
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.reach),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Reach',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '435.380',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         )
                            //       ],
                            //     ),
                            //     15.verticalSpace,
                            //     Row(
                            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Expanded(
                            //           child: Container(
                            //             // width: 144.71,
                            //             // height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.views),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Views',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '2.453',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //         10.horizontalSpace,
                            //         Expanded(
                            //           child: Container(
                            //             //  width: 144.71,
                            //             //  height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.dollar),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Spend',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '\$365.30 AED',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         )
                            //       ],
                            //     ),
                            //     15.verticalSpace,
                            //     Row(
                            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Expanded(
                            //           child: Container(
                            //             // width: 144.71,
                            //             // height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.costClick),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Cost pre click',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '6.120',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //         10.horizontalSpace,
                            //         Expanded(
                            //           child: Container(
                            //             //  width: 144.71,
                            //             //  height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.crm),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'CRM',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '435.380',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         )
                            //       ],
                            //     ),
                            //     15.verticalSpace,
                            //     Row(
                            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Expanded(
                            //           child: Container(
                            //             // width: 144.71,
                            //             // height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.rate),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Click through rate',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '2.453',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //         10.horizontalSpace,
                            //         Expanded(
                            //           child: Container(
                            //             //  width: 144.71,
                            //             //  height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.cart),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Purchases',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '365.30 AED',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         )
                            //       ],
                            //     ),
                            //     15.verticalSpace,
                            //     Row(
                            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Expanded(
                            //           child: Container(
                            //             // width: 144.71,
                            //             // height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.salesValue),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Sales value',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '6.120',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //         10.horizontalSpace,
                            //         Expanded(
                            //           child: Container(
                            //             //  width: 144.71,
                            //             //  height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.roas),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'ROAS',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '435.380',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         )
                            //       ],
                            //     ),
                            //     15.verticalSpace,
                            //     Row(
                            //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Expanded(
                            //           child: Container(
                            //             // width: 144.71,
                            //             // height: 78.45,
                            //             decoration: ShapeDecoration(
                            //               color: Colors.white,
                            //               shape: RoundedRectangleBorder(
                            //                 borderRadius: BorderRadius.circular(12),
                            //               ),
                            //               shadows: const [
                            //                 BoxShadow(
                            //                   color: Color(0x33000000),
                            //                   blurRadius: 30,
                            //                   offset: Offset(0, 4),
                            //                   spreadRadius: -5,
                            //                 )
                            //               ],
                            //             ),
                            //             child: Padding(
                            //               padding:
                            //               EdgeInsets.symmetric(vertical: 8.sp),
                            //               child: Column(
                            //                 mainAxisAlignment:
                            //                 MainAxisAlignment.spaceBetween,
                            //                 children: [
                            //                   const CustomSvgWidget(
                            //                       svg: AppAssets.frequency),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: 'Frequency',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     fontSize: 10.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                   ),
                            //                   4.verticalSpace,
                            //                   CustomText(
                            //                     text: '2.453',
                            //                     textAlign: TextAlign.center,
                            //                     color: Constants.primaryTextColor,
                            //                     alignment:
                            //                     AlignmentDirectional.center,
                            //                     fontSize: 16.sp,
                            //                     fontWeight: FontWeight.w400,
                            //                   )
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //         10.horizontalSpace,
                            //         const Expanded(
                            //           child: SizedBox(),
                            //         ),
                            //       ],
                            //     ),
                            //     20.verticalSpace,
                            //     Container(
                            //       width: 89.h,
                            //       height: 27.h,
                            //       decoration: ShapeDecoration(
                            //         shape: RoundedRectangleBorder(
                            //           side: const BorderSide(
                            //               width: 1, color: Constants.darkColor),
                            //           borderRadius: BorderRadius.circular(29),
                            //         ),
                            //         shadows: const [
                            //           BoxShadow(
                            //             color: Color(0x4C000000),
                            //             blurRadius: 50,
                            //             offset: Offset(0, 4),
                            //             spreadRadius: -10,
                            //           )
                            //         ],
                            //       ),
                            //       child: Row(
                            //         mainAxisAlignment: MainAxisAlignment.center,
                            //         children: [
                            //           CustomText(
                            //             text: 'Show Less',
                            //             color: Constants.darkColor,
                            //             fontSize: 10.sp,
                            //             alignment: AlignmentDirectional.center,
                            //             fontWeight: FontWeight.w400,
                            //           ),
                            //           const Icon(
                            //             Icons.keyboard_arrow_up,
                            //             color: Constants.darkColor,
                            //             size: 18,
                            //           ),
                            //         ],
                            //       ),
                            //     ),
                            //     20.verticalSpace,
                            //     Container(
                            //       width: double.infinity,
                            //       height: 41.h,
                            //       decoration: ShapeDecoration(
                            //         color: const Color(0xFF0B0F26),
                            //         shape: RoundedRectangleBorder(
                            //           borderRadius: BorderRadius.circular(20),
                            //         ),
                            //         shadows: const [
                            //           BoxShadow(
                            //             color: Color(0x4C000000),
                            //             blurRadius: 50,
                            //             offset: Offset(0, 4),
                            //             spreadRadius: -10,
                            //           )
                            //         ],
                            //       ),
                            //       child: Row(
                            //         mainAxisAlignment: MainAxisAlignment.center,
                            //         children: [
                            //           CustomSvgWidget(
                            //             svg: AppAssets.export,
                            //             height: 15.h,
                            //           ),
                            //           8.horizontalSpace,
                            //           CustomText(
                            //             text: 'Export Reports',
                            //             color: Colors.white,
                            //             fontSize: 14.sp,
                            //             alignment: AlignmentDirectional.center,
                            //             fontWeight: FontWeight.w600,
                            //           )
                            //         ],
                            //       ),
                            //     ),
                            //     10.verticalSpace,
                            //   ],
                            // ),
                            campaign.insights != null
                                ? Column(
                                    children: [
                                      const CustomText(
                                        text: "Insights",
                                        color: Constants.primaryTextColor,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      20.verticalSpace,
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Container(
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x33000000),
                                                    blurRadius: 30,
                                                    offset: Offset(0, 4),
                                                    spreadRadius: -5,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 4.sp),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    const Center(
                                                      child: CustomSvgWidget(
                                                          svg: AppAssets.click),
                                                    ),
                                                    3.verticalSpace,
                                                    CustomText(
                                                      text: 'Clicks',
                                                      textAlign:
                                                          TextAlign.center,
                                                      color: Constants
                                                          .primaryTextColor,
                                                      fontSize: 10.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    ),
                                                    3.verticalSpace,
                                                    CustomText(
                                                      text: campaign
                                                              .insights
                                                              ?.data
                                                              ?.first
                                                              .clicks ??
                                                          "",
                                                      textAlign:
                                                          TextAlign.center,
                                                      color: Constants
                                                          .primaryTextColor,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                      fontSize: 18.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          10.horizontalSpace,
                                          Expanded(
                                            child: Container(
                                              // width: 144.71,
                                              // height: 78.45,
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x33000000),
                                                    blurRadius: 30,
                                                    offset: Offset(0, 4),
                                                    spreadRadius: -5,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 4.sp),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    const Center(
                                                      child: CustomSvgWidget(
                                                          svg: AppAssets
                                                              .salesValue),
                                                    ),
                                                    3.verticalSpace,
                                                    CustomText(
                                                      text: 'Impression',
                                                      textAlign:
                                                          TextAlign.center,
                                                      color: Constants
                                                          .primaryTextColor,
                                                      fontSize: 10.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    ),
                                                    3.verticalSpace,
                                                    CustomText(
                                                      text: campaign
                                                              .insights
                                                              ?.data
                                                              ?.first
                                                              .impressions ??
                                                          "",
                                                      textAlign:
                                                          TextAlign.center,
                                                      color: Constants
                                                          .primaryTextColor,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                      fontSize: 18.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          15.horizontalSpace,
                                          Expanded(
                                            child: Container(
                                              // width: 144.71,
                                              // height: 78.45,
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x33000000),
                                                    blurRadius: 30,
                                                    offset: Offset(0, 4),
                                                    spreadRadius: -5,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 4.sp),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    const Center(
                                                      child: CustomSvgWidget(
                                                          svg: AppAssets.reach),
                                                    ),
                                                    3.verticalSpace,
                                                    CustomText(
                                                      text: 'Reach',
                                                      textAlign:
                                                          TextAlign.center,
                                                      color: Constants
                                                          .primaryTextColor,
                                                      fontSize: 10.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                    ),
                                                    3.verticalSpace,
                                                    CustomText(
                                                      text: campaign
                                                              .insights
                                                              ?.data
                                                              ?.first
                                                              .reach ??
                                                          "",
                                                      textAlign:
                                                          TextAlign.center,
                                                      color: Constants
                                                          .primaryTextColor,
                                                      alignment:
                                                          AlignmentDirectional
                                                              .center,
                                                      fontSize: 18.sp,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          15.horizontalSpace,
                                        ],
                                      ),
                                    ],
                                  )
                                : const SizedBox(),
                            25.verticalSpace,
                            ExpansionTileItem(
                              //  expansionKey: Constants.expansionTileKey,
                              onExpansionChanged: (val) {},
                              childrenPadding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              iconColor: AppColors.secondColor,
                              collapsedIconColor: AppColors.secondColor,
                              expandedAlignment: Alignment.center,
                              expandedCrossAxisAlignment:
                                  CrossAxisAlignment.center,
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 6.0),
                                    child: ShaderMask(
                                      shaderCallback: (Rect bounds) {
                                        return Constants.secGradient
                                            .createShader(bounds);
                                      },
                                      child: const Icon(
                                        Icons.expand_more,
                                        size: 24.0,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              title: const CustomText(
                                  text: 'Ads',
                                  color: AppColors.mainColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  border:
                                      Border.all(color: AppColors.mainColor)),
                              children: [
                                20.verticalSpace,
                                (campaignState.data.ads != null &&
                                        campaignState.data.ads!.isNotEmpty)
                                    ? ListView.builder(
                                        itemCount:
                                            campaignState.data.ads!.length,
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (item, index) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 8.0),
                                            child: Container(
                                              width: double.infinity,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 16.sp,
                                                  vertical: 8.sp),
                                              //  height: 60.h,
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                                shadows: const [
                                                  BoxShadow(
                                                    color: Color(0x3D000000),
                                                    blurRadius: 13.93,
                                                    offset: Offset(0, 0),
                                                    spreadRadius: -3.80,
                                                  )
                                                ],
                                              ),
                                              child: Column(
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      // CustomSvgWidget(
                                                      //   svg: AppAssets.demo,
                                                      //   color: Constants.darkColor,
                                                      //   height: 22.h,
                                                      // ),
                                                      // 20.horizontalSpace,
                                                      Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceEvenly,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          CustomText(
                                                            text: campaignState
                                                                    .data
                                                                    .ads?[index]
                                                                    .name ??
                                                                "",
                                                            color: Constants
                                                                .darkColor,
                                                            fontSize: 12.sp,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                          5.verticalSpace,
                                                          campaignState
                                                                      .data
                                                                      .ads?[
                                                                          index]
                                                                      .insights
                                                                      ?.data !=
                                                                  null
                                                              ? CustomText(
                                                                  text:
                                                                      '${CommonUtils.convertDateString(campaignState.data.ads?[index].insights?.data?.first.dateStart ?? "")} - ${CommonUtils.convertDateString(campaignState.data.ads?[index].insights?.data?.first.dateStop ?? "")}',
                                                                  color: const Color(
                                                                      0xFF0B0F26),
                                                                  fontSize: 8,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                )
                                                              : CustomText(
                                                                  text: 'Effective Status: ${campaignState.data.ads![index].effectiveStatus!}' ==
                                                                          'CAMPAIGN_PAUSED'
                                                                      ? 'Paused'
                                                                      : 'Active',
                                                                  color: const Color(
                                                                      0xFF0B0F26),
                                                                  fontSize: 8,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                ),
                                                        ],
                                                      ),
                                                      BlocBuilder<
                                                          ChangeStatusCubit,
                                                          ChangeStatusState>(
                                                        builder: (statusContext,
                                                            statusState) {
                                                          return InkWell(
                                                            onTap: () {
                                                              ChangeStatusCubit.get(statusContext).showPopupMenu(
                                                                  context,
                                                                  campaignState
                                                                          .data
                                                                          .ads?[
                                                                              index]
                                                                          .status ??
                                                                      "",
                                                                  (selectedStatus) {
                                                                // Update the status in the reportsState when a new status is selected
                                                                campaignState
                                                                        .data
                                                                        .ads?[index]
                                                                        .status =
                                                                    selectedStatus;
                                                              },
                                                                  ChangeStatusCubit
                                                                      .get(
                                                                          statusContext),
                                                                  campaignState
                                                                          .data
                                                                          .ads?[
                                                                              index]
                                                                          .id
                                                                          .toString() ??
                                                                      "");
                                                            },
                                                            child: Container(
                                                              width: 70,
                                                              height: 30.h,
                                                              decoration:
                                                                  ShapeDecoration(
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  side:
                                                                      BorderSide(
                                                                    width: 1,
                                                                    color: campaignState.data.ads?[index].status ==
                                                                            'ACTIVE'
                                                                        ? Constants
                                                                            .primaryTextColor
                                                                        : const Color(
                                                                            0xFF616161),
                                                                  ),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              10),
                                                                ),
                                                                shadows: const [
                                                                  BoxShadow(
                                                                    color: Color(
                                                                        0x4C000000),
                                                                    blurRadius:
                                                                        50,
                                                                    offset:
                                                                        Offset(
                                                                            0,
                                                                            4),
                                                                    spreadRadius:
                                                                        -10,
                                                                  )
                                                                ],
                                                              ),
                                                              child: CustomText(
                                                                text: campaignState
                                                                            .data
                                                                            .ads?[index]
                                                                            .status ==
                                                                        'ACTIVE'
                                                                    ? 'Active'
                                                                    : 'Paused',
                                                                color: campaignState
                                                                            .data
                                                                            .ads?[
                                                                                index]
                                                                            .status ==
                                                                        'ACTIVE'
                                                                    ? Constants
                                                                        .primaryTextColor
                                                                    : const Color(
                                                                        0xFF616161),
                                                                fontSize: 12,
                                                                fontFamily:
                                                                    'IBM Plex Sans Devanagari',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                alignment:
                                                                    AlignmentDirectional
                                                                        .center,
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                  15.verticalSpace,
                                                  campaignState.data.ads?[index]
                                                              .insights?.data !=
                                                          null
                                                      ? Row(
                                                          children: [
                                                            Expanded(
                                                              child: Container(
                                                                decoration:
                                                                    ShapeDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            12),
                                                                  ),
                                                                  shadows: const [
                                                                    BoxShadow(
                                                                      color: Color(
                                                                          0x33000000),
                                                                      blurRadius:
                                                                          30,
                                                                      offset:
                                                                          Offset(
                                                                              0,
                                                                              4),
                                                                      spreadRadius:
                                                                          -5,
                                                                    )
                                                                  ],
                                                                ),
                                                                child: Padding(
                                                                  padding: EdgeInsets
                                                                      .symmetric(
                                                                          vertical:
                                                                              4.sp),
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceBetween,
                                                                    children: [
                                                                      const Center(
                                                                        child: CustomSvgWidget(
                                                                            svg:
                                                                                AppAssets.click),
                                                                      ),
                                                                      3.verticalSpace,
                                                                      CustomText(
                                                                        text:
                                                                            'Clicks',
                                                                        textAlign:
                                                                            TextAlign.center,
                                                                        color: Constants
                                                                            .primaryTextColor,
                                                                        fontSize:
                                                                            10.sp,
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                        alignment:
                                                                            AlignmentDirectional.center,
                                                                      ),
                                                                      3.verticalSpace,
                                                                      CustomText(
                                                                        text: campaignState.data.ads?[index].insights?.data?.first.inlineClicks ??
                                                                            "0",
                                                                        textAlign:
                                                                            TextAlign.center,
                                                                        color: Constants
                                                                            .primaryTextColor,
                                                                        alignment:
                                                                            AlignmentDirectional.center,
                                                                        fontSize:
                                                                            18.sp,
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            10.horizontalSpace,
                                                            Expanded(
                                                              child: Container(
                                                                // width: 144.71,
                                                                // height: 78.45,
                                                                decoration:
                                                                    ShapeDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            12),
                                                                  ),
                                                                  shadows: const [
                                                                    BoxShadow(
                                                                      color: Color(
                                                                          0x33000000),
                                                                      blurRadius:
                                                                          30,
                                                                      offset:
                                                                          Offset(
                                                                              0,
                                                                              4),
                                                                      spreadRadius:
                                                                          -5,
                                                                    )
                                                                  ],
                                                                ),
                                                                child: Padding(
                                                                  padding: EdgeInsets
                                                                      .symmetric(
                                                                          vertical:
                                                                              4.sp),
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceBetween,
                                                                    children: [
                                                                      const Center(
                                                                        child: CustomSvgWidget(
                                                                            svg:
                                                                                AppAssets.salesValue),
                                                                      ),
                                                                      3.verticalSpace,
                                                                      CustomText(
                                                                        text:
                                                                            'Impression',
                                                                        textAlign:
                                                                            TextAlign.center,
                                                                        color: Constants
                                                                            .primaryTextColor,
                                                                        fontSize:
                                                                            10.sp,
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                        alignment:
                                                                            AlignmentDirectional.center,
                                                                      ),
                                                                      3.verticalSpace,
                                                                      CustomText(
                                                                        text: campaignState.data.ads?[index].insights?.data?.first.impressions ??
                                                                            "",
                                                                        textAlign:
                                                                            TextAlign.center,
                                                                        color: Constants
                                                                            .primaryTextColor,
                                                                        alignment:
                                                                            AlignmentDirectional.center,
                                                                        fontSize:
                                                                            18.sp,
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            15.horizontalSpace,
                                                            Expanded(
                                                              child: Container(
                                                                // width: 144.71,
                                                                // height: 78.45,
                                                                decoration:
                                                                    ShapeDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            12),
                                                                  ),
                                                                  shadows: const [
                                                                    BoxShadow(
                                                                      color: Color(
                                                                          0x33000000),
                                                                      blurRadius:
                                                                          30,
                                                                      offset:
                                                                          Offset(
                                                                              0,
                                                                              4),
                                                                      spreadRadius:
                                                                          -5,
                                                                    )
                                                                  ],
                                                                ),
                                                                child: Padding(
                                                                  padding: EdgeInsets
                                                                      .symmetric(
                                                                          vertical:
                                                                              4.sp),
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceBetween,
                                                                    children: [
                                                                      const Center(
                                                                        child: CustomSvgWidget(
                                                                            svg:
                                                                                AppAssets.dollar),
                                                                      ),
                                                                      3.verticalSpace,
                                                                      CustomText(
                                                                        text:
                                                                            'Spend',
                                                                        textAlign:
                                                                            TextAlign.center,
                                                                        color: Constants
                                                                            .primaryTextColor,
                                                                        fontSize:
                                                                            10.sp,
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                        alignment:
                                                                            AlignmentDirectional.center,
                                                                      ),
                                                                      3.verticalSpace,
                                                                      CustomText(
                                                                        text: campaignState.data.ads?[index].insights?.data?.first.spend ??
                                                                            "",
                                                                        textAlign:
                                                                            TextAlign.center,
                                                                        color: Constants
                                                                            .primaryTextColor,
                                                                        alignment:
                                                                            AlignmentDirectional.center,
                                                                        fontSize:
                                                                            18.sp,
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            15.horizontalSpace,
                                                          ],
                                                        )
                                                      : const SizedBox(),
                                                  10.verticalSpace,
                                                ],
                                              ),
                                            ),
                                          );
                                        })
                                    : const Padding(
                                        padding: EdgeInsets.only(bottom: 12.0),
                                        child: CustomText(
                                          text: "There is no ads create yet!.",
                                          alignment:
                                              AlignmentDirectional.center,
                                        ),
                                      ),
                                8.verticalSpace,
                                // Container(
                                //   width: double.infinity,
                                //   padding: EdgeInsets.symmetric(
                                //       horizontal: 16.sp, vertical: 8.sp),
                                //   height: 60.h,
                                //   decoration: ShapeDecoration(
                                //     color: Colors.white,
                                //     shape: RoundedRectangleBorder(
                                //       borderRadius: BorderRadius.circular(20),
                                //     ),
                                //     shadows: const [
                                //       BoxShadow(
                                //         color: Color(0x3D000000),
                                //         blurRadius: 13.93,
                                //         offset: Offset(0, 0),
                                //         spreadRadius: -3.80,
                                //       )
                                //     ],
                                //   ),
                                //   child: Row(
                                //     mainAxisAlignment:
                                //     MainAxisAlignment.spaceBetween,
                                //     children: [
                                //       Row(
                                //         children: [
                                //           CustomSvgWidget(
                                //             svg: AppAssets.interests,
                                //             color: Constants.darkColor,
                                //             height: 22.h,
                                //           ),
                                //           20.horizontalSpace,
                                //           CustomText(
                                //             text: 'Interests',
                                //             color: Constants.darkColor,
                                //             alignment: AlignmentDirectional.center,
                                //             fontSize: 12.sp,
                                //             fontWeight: FontWeight.w600,
                                //           ),
                                //         ],
                                //       ),
                                //       ShaderMask(
                                //           shaderCallback: (Rect rect) {
                                //             return Constants.secGradient
                                //                 .createShader(rect);
                                //           },
                                //           child: CustomSvgWidget(
                                //             svg: AppAssets.ed,
                                //             color: Colors.white,
                                //             height: 12.h,
                                //           )),
                                //     ],
                                //   ),
                                // ),
                                // 8.verticalSpace,
                                // Container(
                                //   width: double.infinity,
                                //   padding: EdgeInsets.symmetric(
                                //       horizontal: 16.sp, vertical: 8.sp),
                                //   height: 60.h,
                                //   decoration: ShapeDecoration(
                                //     color: Colors.white,
                                //     shape: RoundedRectangleBorder(
                                //       borderRadius: BorderRadius.circular(20),
                                //     ),
                                //     shadows: const [
                                //       BoxShadow(
                                //         color: Color(0x3D000000),
                                //         blurRadius: 13.93,
                                //         offset: Offset(0, 0),
                                //         spreadRadius: -3.80,
                                //       )
                                //     ],
                                //   ),
                                //   child: Row(
                                //     mainAxisAlignment:
                                //     MainAxisAlignment.spaceBetween,
                                //     children: [
                                //       Row(
                                //         children: [
                                //           CustomSvgWidget(
                                //             svg: AppAssets.location,
                                //             color: Constants.darkColor,
                                //             height: 22.h,
                                //           ),
                                //           20.horizontalSpace,
                                //           CustomText(
                                //             text: 'Location',
                                //             color: Constants.darkColor,
                                //             alignment: AlignmentDirectional.center,
                                //             fontSize: 12.sp,
                                //             fontWeight: FontWeight.w600,
                                //           ),
                                //         ],
                                //       ),
                                //       ShaderMask(
                                //           shaderCallback: (Rect rect) {
                                //             return Constants.secGradient
                                //                 .createShader(rect);
                                //           },
                                //           child: CustomSvgWidget(
                                //             svg: AppAssets.ed,
                                //             color: Colors.white,
                                //             height: 12.h,
                                //           )),
                                //     ],
                                //   ),
                                // ),
                                // 8.verticalSpace,
                                // Container(
                                //   width: double.infinity,
                                //   padding: EdgeInsets.symmetric(
                                //       horizontal: 16.sp, vertical: 8.sp),
                                //   height: 60.h,
                                //   decoration: ShapeDecoration(
                                //     color: Colors.white,
                                //     shape: RoundedRectangleBorder(
                                //       borderRadius: BorderRadius.circular(20),
                                //     ),
                                //     shadows: const [
                                //       BoxShadow(
                                //         color: Color(0x3D000000),
                                //         blurRadius: 13.93,
                                //         offset: Offset(0, 0),
                                //         spreadRadius: -3.80,
                                //       )
                                //     ],
                                //   ),
                                //   child: Row(
                                //     mainAxisAlignment:
                                //     MainAxisAlignment.spaceBetween,
                                //     children: [
                                //       Row(
                                //         children: [
                                //           CustomSvgWidget(
                                //             svg: AppAssets.device,
                                //             color: Constants.darkColor,
                                //             height: 22.h,
                                //           ),
                                //           20.horizontalSpace,
                                //           CustomText(
                                //             text: 'Devices',
                                //             color: Constants.darkColor,
                                //             fontSize: 12.sp,
                                //             alignment: AlignmentDirectional.center,
                                //             fontWeight: FontWeight.w600,
                                //           ),
                                //         ],
                                //       ),
                                //       ShaderMask(
                                //           shaderCallback: (Rect rect) {
                                //             return Constants.secGradient
                                //                 .createShader(rect);
                                //           },
                                //           child: CustomSvgWidget(
                                //             svg: AppAssets.ed,
                                //             color: Colors.white,
                                //             height: 12.h,
                                //           )),
                                //     ],
                                //   ),
                                // ),
                                // 20.verticalSpace,
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                } else {
                  return const Center(
                    child: LoadingWidget(
                      isCircle: true,
                    ),
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }
}

final _formKey = GlobalKey<FormState>();
final TextEditingController _linkController =
    TextEditingController(text: 'https//example.com');
int? _selectIndex;
int? _selectIndex1;

List<String> dummy = [
  'Views',
  'Clicks',
  'Spend',
  'CRM',
  'Cost per click',
  'Clicks',
  'Sales value'
];
List<String> dummy1 = [
  'All',
  'Today',
  'Yesterday',
  'Last Week',
  'Last Month',
  'Custom',
];

class SalesData {
  SalesData(this.year, this.sales);

  final String year;
  final double sales;
}

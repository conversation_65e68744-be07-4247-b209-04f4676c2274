import 'package:flutter/material.dart';

import '../../data/models/campaign_response.dart';

class MetaCampaignCard extends StatefulWidget {
  final CampaignReports? report;
  const MetaCampaignCard({super.key,this.report});

  @override
  State<MetaCampaignCard> createState() => _MetaCampaignCardState();
}

class _MetaCampaignCardState extends State<MetaCampaignCard> {

  @override
  void initState() {
    print('reporsthere ${widget.report}');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 8)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.facebook, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(widget.report?.objective ?? "",
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                ],
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text("Paused"),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Title & Date
          Text(widget.report?.name ?? "",
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
           Text(widget.report?.createdTime ?? "",
              style: const TextStyle(color: Colors.grey)),

          const SizedBox(height: 16),

          // Stats Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _statCard("Clicks", widget.report?.insights?.data?[0].clicks ?? "", Icons.mouse),
              _statCard("Views", widget.report?.insights?.data?[0].impressions ?? "", Icons.remove_red_eye),
              _statCard("Reach", widget.report?.insights?.data?[0].reach ?? "", Icons.bolt),
            ],
          ),

          const SizedBox(height: 16),

          // Progress bar
          LinearProgressIndicator(
            value: 1560 / 3000,
            color: Colors.blue,
            backgroundColor: Colors.grey[300],
          ),
          const SizedBox(height: 8),

          // Spend and Budget
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("Spend ${widget.report?.insights ??""} ${widget.report?.insights?.data?[0].spend}", style: const TextStyle(color: Colors.blue)),
              const Text("All Budget 3.000 AED",
                  style: TextStyle(color: Colors.grey)),
            ],
          ),

          const SizedBox(height: 16),

          // Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              gradient:
                  const LinearGradient(colors: [Colors.orange, Colors.pink]),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text("All Details",
                  style: TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _statCard(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.redAccent),
        const SizedBox(height: 4),
        Text(value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
        Text(label, style: const TextStyle(color: Colors.grey)),
      ],
    );
  }
}

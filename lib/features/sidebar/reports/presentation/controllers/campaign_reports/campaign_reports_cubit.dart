import 'package:ads_dv/features/sidebar/reports/data/models/campaign_response.dart';
import 'package:ads_dv/features/sidebar/reports/data/repos/reports_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';

part 'campaign_reports_state.dart';

class CampaignReportsCubit extends Cubit<CampaignsReportsState> {
  CampaignReportsCubit() : super(CampaignsReportsInitial());

  static CampaignReportsCubit get(context) => BlocProvider.of(context);

  CampaignResponse? campaigns;

  campaignReports(
      {required BuildContext context, required String accountId}) async {
    emit(CampaignsReportsStateLoading());
    instance<ReportsRepo>()
        .getCampaignsReports(accountId: accountId)
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(CampaignsReportsStateError(l));
      }, (r) {
        campaigns = r;
        emit(CampaignsReportsStateLoaded(campaigns!));
      });
    });
  }

  loadMoreReports({
    required BuildContext context,
    required String url,
    // required String pageAccessToken,
  }) async {
    emit(CampaignsReportsStateLoading());
    instance<ReportsRepo>().loadMoreReports(url: url).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(CampaignsReportsStateError(l));
      }, (r) {
        campaigns?.result?.data
            ?.addAll(r.result?.data?.map((e) => e).toList() ?? []);
        // instaPosts = r.result?.data ?? [];
        // setSelectedUrl(r.next);

        emit(CampaignsReportsStateLoaded(r));
      });
    });
  }

  searchForCampaign(String searchQuery) {
    final currentState = state;
    if (currentState is CampaignsReportsStateLoaded) {
      final filteredData = currentState.filterByName(searchQuery);
      if (searchQuery.isEmpty) {
        emit(currentState.copyWith(data: campaigns));
      } else {
        // emit(currentState.copyWith(data: filteredData));
      }
    }
  }
}

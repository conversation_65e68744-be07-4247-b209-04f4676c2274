part of 'campaign_reports_cubit.dart';

@immutable
abstract class CampaignsReportsState {
  const CampaignsReportsState();

  List<Object?> get props => [];
}

class CampaignsReportsInitial extends CampaignsReportsState {}

class CampaignsReportsStateLoading extends CampaignsReportsState {}

class CampaignsReportsStateLoaded extends CampaignsReportsState {
  final CampaignResponse data;

  const CampaignsReportsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  List<CampaignReports> filterByName(String searchQuery) {
    if (searchQuery.isEmpty) {
      return data.result?.data ?? [];
    } else {
      List<CampaignReports> temp = data.result?.data ?? [];
      return temp
          .where((searchClass) =>
              (searchClass.name
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false) ||
              (searchClass.name
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false))
          .toList();
    }
  }

  CampaignsReportsStateLoaded copyWith({
    CampaignResponse? data,
  }) {
    return CampaignsReportsStateLoaded(
      data ?? this.data,
    );
  }
}

class CampaignsReportsStateError extends CampaignsReportsState {
  final Failure message;

  const CampaignsReportsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

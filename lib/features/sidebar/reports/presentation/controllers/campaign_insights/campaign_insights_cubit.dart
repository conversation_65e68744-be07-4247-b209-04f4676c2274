import 'package:ads_dv/features/sidebar/reports/data/models/campaign_details.dart';
import 'package:ads_dv/utils/network/failure_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../data/repos/reports_repo.dart';

part 'campaign_insights_state.dart';

class CampaignInsightsCubit extends Cubit<CampaignInsightsState> {
  CampaignInsightsCubit() : super(CampaignInsightsInitial());

  static CampaignInsightsCubit get(context) => BlocProvider.of(context);

  getCampaignInsights({
    required BuildContext context,
    required String campaignId,
    required String pageAccessToken

  }) async {
    emit(CampaignInsightsStateLoading());
    instance<ReportsRepo>().getCampaignInsights(campaignId: campaignId,pageAccessToken: pageAccessToken).then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(CampaignInsightsStateError(l));
      }, (r) {
        emit(CampaignInsightsStateLoaded(r));
      });
    });
  }
}

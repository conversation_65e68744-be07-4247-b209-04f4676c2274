part of 'campaign_insights_cubit.dart';

@immutable
abstract class CampaignInsightsState {}

class CampaignInsightsInitial extends CampaignInsightsState {}

class CampaignInsightsStateLoading extends CampaignInsightsState {}

class CampaignInsightsStateLoaded extends CampaignInsightsState {
  final CampaignDetailsResponse data;

  CampaignInsightsStateLoaded(this.data);

  @override
  List<Object?> get props => [data];

  CampaignInsightsStateLoaded copyWith({
    CampaignDetailsResponse? data,
  }) {
    return CampaignInsightsStateLoaded(
      data ?? this.data,
    );
  }
}

class CampaignInsightsStateError extends CampaignInsightsState {
  final Failure message;

  CampaignInsightsStateError(this.message);

  @override
  List<Object?> get props => [message];
}

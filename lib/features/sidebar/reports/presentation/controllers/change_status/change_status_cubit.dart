import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../utils/di/injection.dart';
import '../../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../../utils/network/errors/failures.dart';
import '../../../../../../utils/network/failure_helper.dart';
import '../../../../../../utils/res/custom_widgets.dart';
import '../../../data/models/status.dart';
import '../../../data/repos/reports_repo.dart';

part 'change_status_state.dart';

class ChangeStatusCubit extends Cubit<ChangeStatusState> {
  ChangeStatusCubit() : super(ChangeStatusInitial());

  static ChangeStatusCubit get(context) => BlocProvider.of(context);

  final List<Status> statuses = [
    Status(name: 'Active', value: 'ACTIVE'),
    Status(name: 'Paused', value: 'PAUSED'),
    Status(name: 'Deleted', value: 'DELETED'),
    Status(name: 'Archived', value: 'ARCHIVED'),
  ];
  changeStatus(
      {required String objectId,
        required String status,
        required String pageAccessToken,

        required BuildContext context}) async {
    emit(ChangeStatusLoading());
    instance<ReportsRepo>()
        .changeStatus(
        objectId: objectId,
        status: status,
        pageAccessToken: pageAccessToken
    )
        .then((value) {
      value.fold((l) {
        FailureHelper.instance.handleFailures(l, context);
        emit(ChangeStatusError(l));
      }, (r) async {

        showSuccessToast("Status updated successfully");

        emit(ChangeStatusLoaded());
      });
    });
  }

  void showPopupMenu(
      BuildContext context,
      String currentStatus,
      Function(String) onStatusSelected,
      ChangeStatusCubit statusCubit,
      String objectId) {


    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(100, 450.h, 0, 0),
      items: statuses.map((status) {
        return PopupMenuItem<String>(
          value: status.value,
          child: Text(
            status.name,
            textAlign: TextAlign.center,
          ),
        );
      }).toList(),
    ).then((selectedValue) {
      if (selectedValue != null) {
        onStatusSelected(selectedValue);
        ChangeStatusCubit.get(context).changeStatus(
            objectId: objectId,
            status: selectedValue,
            pageAccessToken:
            instance<HiveHelper>().getUser()?.defaultPageAccessToken ?? "",
            context: context);
      }
    });
  }
}

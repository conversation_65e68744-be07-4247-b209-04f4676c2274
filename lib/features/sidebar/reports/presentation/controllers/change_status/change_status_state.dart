part of 'change_status_cubit.dart';

@immutable
abstract class ChangeStatusState {
  const ChangeStatusState();
  List<Object?> get props => [];
}

class ChangeStatusInitial extends ChangeStatusState {}

class ChangeStatusLoading extends ChangeStatusState {}

class ChangeStatusLoaded extends ChangeStatusState {
}

class ChangeStatusError extends ChangeStatusState {
  final Failure message;

  const ChangeStatusError(this.message);

  @override
  List<Object?> get props => [message];
}

final class UpdateStates extends ChangeStatusState {}
class Insights {
  List<Data>? data;

  Insights({this.data});

  Insights.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class Data {
  String? dateStart;
  String? dateStop;
  String? clicks;
  String? inlineClicks;
  String? spend;

  String? impressions;
  String? reach;

  Data(
      {this.dateStart,
        this.dateStop,
        this.clicks,
        this.inlineClicks,
        this.spend,
        this.impressions,
        this.reach});

  Data.fromJson(Map<String, dynamic> json) {
    dateStart = json['date_start'];
    dateStop = json['date_stop'];
    clicks = json['clicks'];
    inlineClicks = json['inline_link_clicks'];
    spend = json['spend'];

    impressions = json['impressions'];
    reach = json['reach'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['date_start'] = dateStart;
    data['date_stop'] = dateStop;
    data['clicks'] = clicks;
    data['inline_link_clicks'] = inlineClicks;
    data['spend'] = spend;

    data['impressions'] = impressions;
    data['reach'] = reach;
    return data;
  }
}

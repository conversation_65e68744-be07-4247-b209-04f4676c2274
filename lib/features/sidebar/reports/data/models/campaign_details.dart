import 'package:ads_dv/features/sidebar/reports/data/models/ads.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/insights.dart';

class CampaignDetailsResponse {
  bool? success;
  String? message;
  List<Insights>? insights;
  List<Ads>? ads;

  CampaignDetailsResponse({this.success, this.message, this.insights, this.ads});

  CampaignDetailsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['insights'] != null) {
      insights = <Insights>[];
      json['insights'].forEach((v) {
        insights!.add(Insights.fromJson(v));
      });
    }
    if (json['ads'] != null) {
      ads = <Ads>[];
      json['ads'].forEach((v) {
        ads!.add(Ads.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (insights != null) {
      data['insights'] = insights!.map((v) => v.toJson()).toList();
    }
    if (ads != null) {
      data['ads'] = ads!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
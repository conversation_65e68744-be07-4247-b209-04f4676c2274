import 'package:ads_dv/features/sidebar/reports/data/models/insights.dart';

class CampaignResponse {
  bool? success;
  String? message;
  Result? result;

  CampaignResponse({this.success, this.message, this.result});

  CampaignResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    result =
        json['result'] != null ? Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.toJson();
    }
    return data;
  }
}

class Result {
  List<CampaignReports>? data;
  String? next;
  String? previous;

  Result({this.data, this.next, this.previous});

  Result.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <CampaignReports>[];
      json['data'].forEach((v) {
        data!.add(CampaignReports.fromJson(v));
      });
    }
    next = json['next'];
    previous = json['previous'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['next'] = next;
    data['previous'] = previous;
    return data;
  }
}

class CampaignReports {
  String? name;
  String? objective;
  String? status;
  String? createdTime;
  String? id;
  Insights? insights;

  CampaignReports(
      {this.name,
      this.objective,
      this.status,
      this.createdTime,
      this.id,
      this.insights});

  CampaignReports.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    objective = json['objective'];
    status = json['status'];
    createdTime = json['created_time'];
    id = json['id'];
    insights =
        json['insights'] != null ? Insights.fromJson(json['insights']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['objective'] = objective;
    data['status'] = status;
    data['created_time'] = createdTime;
    data['id'] = id;
    if (insights != null) {
      data['insights'] = insights!.toJson();
    }
    return data;
  }
}

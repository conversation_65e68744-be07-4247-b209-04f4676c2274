import 'package:ads_dv/features/sidebar/reports/data/models/insights.dart';

class Ads {
  String? name;
  String? effectiveStatus;
  String? status;
  Insights? insights;
  String? id;

  Ads({this.name, this.effectiveStatus, this.status, this.insights, this.id});

  Ads.from<PERSON>son(Map<String, dynamic> json) {
    name = json['name'];
    effectiveStatus = json['effective_status'];
    status = json['status'];
    insights = json['insights'] != null
        ? Insights.fromJson(json['insights'])
        : null;
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['effective_status'] = effectiveStatus;
    data['status'] = status;
    if (insights != null) {
      data['insights'] = insights!.toJson();
    }
    data['id'] = id;
    return data;
  }
}

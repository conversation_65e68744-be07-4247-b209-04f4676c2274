class CampaignInsights {
  String? impressions;
  String? inlineLinkClicks;
  String? spend;
  String? dateStart;
  String? dateStop;

  CampaignInsights(
      {this.impressions,
        this.inlineLinkClicks,
        this.spend,
        this.dateStart,
        this.dateStop});

  CampaignInsights.fromJson(Map<String, dynamic> json) {
    impressions = json['impressions'];
    inlineLinkClicks = json['inline_link_clicks'];
    spend = json['spend'];
    dateStart = json['date_start'];
    dateStop = json['date_stop'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['impressions'] = impressions;
    data['inline_link_clicks'] = inlineLinkClicks;
    data['spend'] = spend;
    data['date_start'] = dateStart;
    data['date_stop'] = dateStop;
    return data;
  }
}
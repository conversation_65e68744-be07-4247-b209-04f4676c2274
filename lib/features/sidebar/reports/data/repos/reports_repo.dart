import 'package:ads_dv/features/sidebar/reports/data/data_sources/reports_data_source.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/campaign_details.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/campaign_response.dart';
import 'package:ads_dv/utils/network/connection/network_info.dart';
import 'package:dartz/dartz.dart';

import '../../../../../utils/network/errors/failures.dart';
import '../../../../../utils/network/failure_helper.dart';

class ReportsRepo {
  NetworkInfo networkInfo;
  ReportsDataSource reportsDataSource;

  ReportsRepo({required this.networkInfo, required this.reportsDataSource});

  Future<Either<Failure, CampaignResponse>> getCampaignsReports(
      {required String accountId}) {
    return FailureHelper.instance(
      method: () async {
        return await reportsDataSource.getCampaignsReports(
            accountId: accountId);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, CampaignResponse>> loadMoreReports({
    String? url,
    // required String pageAccessToken,
  }) {
    return FailureHelper.instance(
      method: () async {
        return await reportsDataSource.loadMoreReportss(url: url);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, CampaignDetailsResponse>> getCampaignInsights(
      {required String campaignId, required String pageAccessToken}) {
    return FailureHelper.instance(
      method: () async {
        return await reportsDataSource.getCampaignInsights(
            campaignId: campaignId, pageAccessToken: pageAccessToken);
      },
      networkInfo: networkInfo,
    );
  }

  Future<Either<Failure, bool>> changeStatus(
      {required String status,
      required String objectId,
      required String pageAccessToken}) {
    return FailureHelper.instance(
      method: () async {
        return await reportsDataSource.changeStatus(
            objectId: objectId,
            pageAccessToken: pageAccessToken,
            status: status);
      },
      networkInfo: networkInfo,
    );
  }
}

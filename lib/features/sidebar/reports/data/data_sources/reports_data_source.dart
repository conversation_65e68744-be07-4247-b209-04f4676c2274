import 'package:ads_dv/features/sidebar/reports/data/models/campaign_details.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/campaign_response.dart';
import 'package:dio/dio.dart';

import '../../../../../utils/di/injection.dart';
import '../../../../../utils/hive_helper/hive_helper.dart';
import '../../../../../utils/network/dio/enum.dart';
import '../../../../../utils/network/dio/network_call.dart';
import '../../../../../utils/network/urls/end_points.dart';

class ReportsDataSource {
  Future<CampaignResponse> getCampaignsReports(
      {required String accountId}) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.getAllCampaigns,
        queryParameters: {
          "ad_account_id": accountId,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      dynamic data = response;
      var cam = CampaignResponse.fromJson(data);
      return cam;
    } catch (error) {
      rethrow;
    }
  }

  Future<CampaignResponse> loadMoreReportss({
    String? url,
    // required String pageAccessToken,
  }) async {
    try {
      Map<String, dynamic> response = await instance<NetworkCall>().request(
        EndPoints.instaPostsPagination,
        queryParameters: {
          if (url != null) "link": url,
          // "page_access_token": pageAccessToken,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return CampaignResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<CampaignDetailsResponse> getCampaignInsights(
      {required String campaignId, required String pageAccessToken}) async {
    try {
      var response = await instance<NetworkCall>().request(
        EndPoints.getCampaignsInsights,
        queryParameters: {
          'page_access_token': pageAccessToken,
          'campaign_id': campaignId,
        },
        options: Options(
          method: Method.get.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return CampaignDetailsResponse.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> changeStatus(
      {required String objectId,
      required String status,
      required String pageAccessToken}) async {
    try {
      var response = await instance<NetworkCall>().request(
        EndPoints.changeStatus,
        params: {
          'page_access_token': pageAccessToken,
          'object_id': objectId,
          'status': status,
        },
        options: Options(
          method: Method.post.name,
          headers: {
            "Authorization": 'Bearer ${instance<HiveHelper>().getToken()}'
          },
        ),
      );
      return true;
    } catch (error) {
      rethrow;
    }
  }
}

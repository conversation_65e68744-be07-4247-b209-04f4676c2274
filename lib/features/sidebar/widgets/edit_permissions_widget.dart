import 'package:ads_dv/features/sidebar/management_area/presentation/controllers/delete_access/delete_access_cubit.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../../utils/res/app_assets.dart';
import '../../../../../../../utils/res/constants.dart';
import '../../../../../../../widgets/custom_text.dart';
import '../../../utils/res/router/routes.dart';
import '../ad_accounts/data/models/access.dart';
import '../management_area/presentation/controllers/get_access_info/get_access_info_cubit.dart';

class EditPermissionWidget extends StatelessWidget {
  String? accountName;
  String permission;
  AccessedUser accessedUser;
  GetAccessInfoCubit getAccessInfoCubit;
  EditPermissionWidget({super.key,
     this.accountName,
    required this.permission,
    required this.getAccessInfoCubit,
    required this.accessedUser});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DeleteAccessCubit(),
      child: BlocListener<DeleteAccessCubit, DeleteAccessState>(
        listener: (context, state) {
          if(state is DeleteAccessLoaded){
            getAccessInfoCubit.getAccessInfo(context: context);
          }
        },
        child: Column(
          children: [
            Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 50.22,
                      decoration: const ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10.0),
                            topRight: Radius.circular(10.0),
                          ),
                        ),
                        shadows: [
                          BoxShadow(
                            color: Color(0x3F000000),
                            blurRadius: 40,
                            offset: Offset(0, 0),
                            spreadRadius: -10,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.sp, vertical: 4.sp),
                        child: CustomText(
                          text: accountName ??"",
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                        ),
                      ),
                    )
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(top: 26.sp),
                  child: Container(
                    // height: 80.73.h,
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x3F000000),
                          blurRadius: 40,
                          offset: Offset(0, 0),
                          spreadRadius: -10,
                        )
                      ],
                    ),
                    child: Padding(
                      padding:
                      EdgeInsets.symmetric(horizontal: 8.sp, vertical: 22.sp),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: permission,
                            color: Constants.gray,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w500,
                          ),
                          RSizedBox.vertical(12.h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              InkWell(
                                onTap: () {
                                  Navigator.of(context)
                                      .pushNamed(Routes.editAccess, arguments: {
                                    'accessedUser': accessedUser,
                                  }).then((value) {
                                    if (value == true) {
                                      GetAccessInfoCubit.get(context)
                                          .getAccessInfo(context: context);
                                    }
                                  });
                                },
                                child: Container(
                                  width: 115.h,

                                  //  height: 44.66,
                                  //width: 120.w,
                                  decoration: ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    shadows: const [
                                      BoxShadow(
                                        color: Color(0x3F000000),
                                        blurRadius: 40,
                                        offset: Offset(0, 0),
                                        spreadRadius: -10,
                                      )
                                    ],
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 14.sp, horizontal: 4.sp),
                                    child: FittedBox(
                                      child: Row(
                                        crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.all(3.sp),
                                            child: CustomSvgWidget(
                                                svg: AppAssets.update,
                                                height: 12.h),
                                          ),
                                          CustomText(
                                            text: "Edit Permission".tr,
                                            color: Colors.black,
                                            alignment:
                                            AlignmentDirectional.center,
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              BlocBuilder<DeleteAccessCubit, DeleteAccessState>(
                                builder: (context, state) {
                                  return state is DeleteAccessLoading
                                      ? const LoadingWidget(
                                    isCircle: true,
                                  )
                                      : InkWell(
                                    onTap: () {
                                      DeleteAccessCubit.get(context)
                                          .deleteAccess(
                                          accessId: accessedUser.id,
                                          context: context);
                                    },
                                    child: Container(
                                      // height: 44.66,
                                      // width: 120.w,
                                      width: 115.h,

                                      decoration: ShapeDecoration(
                                        color: Constants.lightRedColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                          BorderRadius.circular(10),
                                        ),
                                        shadows: const [
                                          BoxShadow(
                                            color: Color(0x3F000000),
                                            blurRadius: 40,
                                            offset: Offset(0, 0),
                                            spreadRadius: -10,
                                          )
                                        ],
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 14.sp,
                                            horizontal: 4.sp),
                                        child: Row(
                                          crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.all(3.sp),
                                              child: CustomSvgWidget(
                                                svg: AppAssets.delete,
                                                height: 12.h,
                                              ),
                                            ),
                                            CustomText(
                                              text: "Delete".tr,
                                              color: Constants.darRedColor,
                                              alignment:
                                              AlignmentDirectional.center,
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

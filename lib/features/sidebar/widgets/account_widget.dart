import 'package:ads_dv/features/auth/presentation/login/controllers/login/login_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/router/routes.dart';

class AccountWidget extends StatefulWidget {
  String icon;
  String title;
  bool isConnected;
  AccountWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.isConnected,
  });

  @override
  State<AccountWidget> createState() => _AccountWidgetState();
}

class _AccountWidgetState extends State<AccountWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: Container(
        height: 70.h,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x19000000),
              blurRadius: 22,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 22.sp, horizontal: 16.sp),
                  child: CachedImageWidget(assetsImage: widget.icon),
                ),
                CustomText(
                  text: widget.title,
                  alignment: AlignmentDirectional.center,
                  color: Constants.primaryTextColor,
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w700,
                )
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: widget.isConnected
                  ? Row(
                      children: [
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(context, Routes.metaAccounts);
                          },
                          child: Container(
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: const BorderSide(
                                    width: 0.50,
                                    color: Constants.primaryTextColor),
                                borderRadius: BorderRadius.circular(26),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x19000000),
                                  blurRadius: 22,
                                  offset: Offset(0, 4),
                                  spreadRadius: 0,
                                )
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                'View Account'.tr,
                                style: TextStyle(
                                  color: Constants.primaryTextColor,
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 12.w,
                        ),
                        Container(
                          decoration: ShapeDecoration(
                            color: Constants.redColor.withOpacity(0.1),
                            shape: RoundedRectangleBorder(
                              side: const BorderSide(
                                  width: 0.50, color: Colors.transparent),
                              borderRadius: BorderRadius.circular(26),
                            ),
                            shadows: const [
                              BoxShadow(
                                color: Color(0x19000000),
                                blurRadius: 22,
                                offset: Offset(0, 4),
                                spreadRadius: 0,
                              )
                            ],
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              'Disconnect'.tr,
                              style: TextStyle(
                                color: Constants.redColor,
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        )
                      ],
                    )
                  : Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      InkWell(
                          onTap: () {
                      
                          },
                          child: Container(
                            decoration: ShapeDecoration(
                              gradient: Constants.defGradient,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(38),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x19000000),
                                  blurRadius: 22,
                                  offset: Offset(0, 4),
                                  spreadRadius: 0,
                                )
                              ],
                            ),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 8.sp, horizontal: 14.sp),
                              child: Text(
                                'Connect'.tr,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                      10.horizontalSpace,
                      CustomText(text: "Soon".tr,alignment: AlignmentDirectional.center,fontSize: 12.sp,),

                    ],
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

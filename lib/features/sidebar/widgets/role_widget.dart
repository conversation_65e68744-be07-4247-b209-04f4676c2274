import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../widgets/custom_text.dart';

class RoleWidget extends StatelessWidget {
  String role;
  String description;
  bool isSelected;
  RoleWidget(
      {super.key,
      required this.role,
      required this.description,
      required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: isSelected
          ? ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                side: const BorderSide(width: 1, color: Color(0xFFFF006F)),
                borderRadius: BorderRadius.circular(16),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x3F000000),
                  blurRadius: 40,
                  offset: Offset(0, 0),
                  spreadRadius: -10,
                )
              ],
            )
          : ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x3F000000),
                  blurRadius: 40,
                  offset: Offset(0, 0),
                  spreadRadius: -10,
                )
              ],
            ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 22.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: role,
                  color: Colors.black,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
                RSizedBox.vertical(6.h),
                CustomText(
                  text: description,
                  color: Constants.gray,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                ),
              ],
            ),
            isSelected
                ? SizedBox(
                    width: 22.h,
                    height: 22.h,
                    child: ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return Constants.secGradient.createShader(bounds);
                        },
                        child: const Icon(
                          Icons.check_circle,
                          color: Colors.white,
                        )),
                  )
                : Container(
                    width: 22.h,
                    height: 22.h,
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: const BorderSide(
                            width: 1, color: Color(0xFF131534)),
                        borderRadius: BorderRadius.circular(18),
                      ),
                    ),
                  )
          ],
        ),
      ),
    );
  }
}

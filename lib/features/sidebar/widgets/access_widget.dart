import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../utils/res/constants.dart';
import '../../../../../../widgets/custom_text.dart';
import '../../../utils/res/router/routes.dart';
import '../ad_accounts/data/models/ad_account.dart';
import '../ad_accounts/data/models/page.dart';

class AccessWidget extends StatelessWidget {
  String? accountName;
  String permission;
  List<AdAccount> userAccount;
  List<Pages> metaPages;

  AccessWidget(
      {super.key,
      this.accountName,
      required this.permission,
      required this.metaPages,
      required this.userAccount});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, state) {
        return Column(
          children: [
            Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 50.22,
                      decoration: const ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10.0),
                            topRight: Radius.circular(10.0),
                          ),
                        ),
                        shadows: [
                          BoxShadow(
                            color: Color(0x3F000000),
                            blurRadius: 40,
                            offset: Offset(0, 0),
                            spreadRadius: -10,
                          )
                        ],
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.sp, vertical: 4.sp),
                        child: CustomText(
                          text: accountName ?? "",
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                        ),
                      ),
                    )
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(top: 26.sp),
                  child: Container(
                    // height: 80.73.h,
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x3F000000),
                          blurRadius: 40,
                          offset: Offset(0, 0),
                          spreadRadius: -10,
                        )
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.sp, vertical: 22.sp),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: "access level requested".tr,
                            color: Colors.black,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                          RSizedBox.vertical(6.h),
                          CustomText(
                            text: permission,
                            color: Constants.gray,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              ],
            ),
            RSizedBox.vertical(14.h),
            (CreateAdCubit.get(context).adAccount != null &&
                    CreateAdCubit.get(context).accessedMetaPages != null)
                ? InkWell(
                    onTap: () {
                      CreateAdCubit.get(context).removeAccount() ;
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: const BorderSide(
                              width: 0.50, color: Colors.black),
                          borderRadius: BorderRadius.circular(26),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        child: CustomText(
                          alignment: AlignmentDirectional.center,
                          text: 'Close'.tr,
                          color: Colors.black,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  )
                : InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, Routes.accessedPages,
                          arguments: {
                            'userAccount': userAccount,
                            'metaPages': metaPages,
                          });
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: const BorderSide(
                              width: 0.50, color: Colors.black),
                          borderRadius: BorderRadius.circular(26),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        child: CustomText(
                          alignment: AlignmentDirectional.center,
                          text: 'Access Now'.tr,
                          color: Colors.black,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
          ],
        );
      },
    );
  }
}

// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAjMzDDwb1xz49hjAMOqFgBHOMQRuUtg8Y',
    appId: '1:923196999770:web:f3093ffaee87758011b063',
    messagingSenderId: '923196999770',
    projectId: 'dv-ads-manger',
    authDomain: 'dv-ads-manger.firebaseapp.com',
    storageBucket: 'dv-ads-manger.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCsMEwMdMBcW3tADL1F_KIck-mvA5CfjVo',
    appId: '1:923196999770:android:262e71ab1cdb547511b063',
    messagingSenderId: '923196999770',
    projectId: 'dv-ads-manger',
    storageBucket: 'dv-ads-manger.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC9M4VYiPGgYQDmWf5NVDwGDYiwT12nZ40',
    appId: '1:923196999770:ios:6e798933ca77aea311b063',
    messagingSenderId: '923196999770',
    projectId: 'dv-ads-manger',
    storageBucket: 'dv-ads-manger.appspot.com',
    androidClientId: '923196999770-4gbiados7mu9sa3p4k8e8kqdi023b57a.apps.googleusercontent.com',
    iosClientId: '923196999770-o5ajf93iq235fgj0eluk6gk8r66fo0m5.apps.googleusercontent.com',
    iosBundleId: 'com.dv.ads.manger',
  );
}

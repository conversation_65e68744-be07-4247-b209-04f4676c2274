import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../utils/res/app_assets.dart';
import '../utils/res/media_query_config.dart';


class CachedImageWidget extends StatelessWidget {
  final assetsImage;
  final String? svg;
  final ColorFilter? colorFilter;

  const CachedImageWidget({
    Key? key,
    this.image,
    this.width,
    this.height,
    this.svg,
    this.colorFilter,
    this.fit = BoxFit.fill,
    this.assetsImage = AppAssets.logo
  }) : super(key: key);

  final String? image;
  final BoxFit? fit;
  final double? width;
  final double?  height;
  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: image??'',
      fit: fit,
      width: width!=null? SizeConfig.widthr(width!, context):width,
      height:height!=null? SizeConfig.hieghtr(height!, context):height,
      placeholder: (context, url) =>  Image.asset(assetsImage),
      errorWidget: (context, url, error) =>svg != null ? SvgPicture.asset(
        svg!,
        colorFilter: colorFilter,
        width: SizeConfig.widthr(24, context),
        height: SizeConfig.hieghtr(24, context),
      ): Image.asset(assetsImage,fit: BoxFit.fill,width: width,height: height,),
    );
  }
}
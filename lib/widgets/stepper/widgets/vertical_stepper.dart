
import 'package:flutter/material.dart';

import '../dto/stepper_data.dart';
import 'common/dot_provider.dart';
import 'dashed.dart';

class VerticalStepperItem extends StatelessWidget {
  /// Stepper Item to show vertical stepper
  const VerticalStepperItem(
      {Key? key,
      required this.item,
      required this.index,
      required this.totalLength,
      required this.gap,
      required this.activeIndex,
      required this.isInverted,
      required this.activeBarColor,
      required this.inActiveBarColor,
      required this.barWidth,
      required this.iconHeight,
      required this.iconWidth})
      : super(key: key);

  /// Stepper item of type [StepperData] to inflate stepper with data
  final StepperData item;

  /// Index at which the item is present
  final int index;

  /// Total length of the list provided
  final int totalLength;

  /// Active index which needs to be highlighted and before that
  final int activeIndex;

  /// Gap between the items in the stepper
  final double gap;

  /// Inverts the stepper with text that is being used
  final bool isInverted;

  /// Bar color for active step
  final Color activeBarColor;

  /// Bar color for inactive step
  final Color inActiveBarColor;

  /// Bar width/thickness
  final double barWidth;

  /// Height of [StepperData.iconWidget]
  final double iconHeight;

  /// Width of [StepperData.iconWidget]
  final double iconWidth;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: isInverted ? getInvertedChildren() : getChildren(),
    );
  }

  List<Widget> getChildren() {
    return [
      Column(
        children: [

          Dash(
              direction: Axis.vertical,
              length: gap,
              dashLength: 5,
              dashColor: index == 0
                  ? Colors.transparent
                  : (index <= activeIndex ? activeBarColor : inActiveBarColor),),

          // Container(
          //   color: index == 0
          //       ? Colors.transparent
          //       : (index <= activeIndex ? activeBarColor : inActiveBarColor),
          //   width: barWidth,
          //   height: gap,
          // ),


          DotProvider(
            activeIndex: activeIndex,
            index: index,
            item: item,
            totalLength: totalLength,
            iconHeight: iconHeight,
            iconWidth: iconWidth,
          ),


          Dash(
              direction: Axis.vertical,
              length: gap,
              dashLength: 5,
              dashColor: index == totalLength - 1
                  ? Colors.transparent
                  : (index < activeIndex ? activeBarColor : inActiveBarColor),),

          // Container(
          //   color: index == totalLength - 1
          //       ? Colors.transparent
          //       : (index < activeIndex ? activeBarColor : inActiveBarColor),
          //   width: barWidth,
          //   height: gap,
          // ),

        ],
      ),
      const SizedBox(width: 8),
      Expanded(
        child: Column(
          crossAxisAlignment:
              isInverted ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            if (item.title != null) ...[
              item.title!
              // Text(
              //   item.title!.text,
              //   textAlign: TextAlign.start,
              //   style: item.title!.textStyle ??
              //       const TextStyle(
              //         fontSize: 14,
              //         color: Colors.black,
              //         fontWeight: FontWeight.w600,
              //       ),
              // ),
            ],
            // if (item.subtitle != null) ...[
            //   const SizedBox(height: 8),
            //   Text(
            //     item.subtitle!.text,
            //     textAlign: TextAlign.start,
            //     style: item.subtitle!.textStyle ??
            //         const TextStyle(
            //           fontSize: 12,
            //           color: Colors.grey,
            //           fontWeight: FontWeight.w500,
            //         ),
            //   ),
            // ],
          ],
        ),
      ),
    ];
  }

  List<Widget> getInvertedChildren() {
    return getChildren().reversed.toList();
  }
}

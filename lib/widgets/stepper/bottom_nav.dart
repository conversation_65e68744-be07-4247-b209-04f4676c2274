import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

import '../../features/snapChat_campaign/presentation/controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapchat_campaign/snap_chat_campaign_cubit.dart';
import '../../features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import '../../features/tiktok_campigns/presentation/controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import '../../utils/di/injection.dart';
import '../../utils/hive_helper/hive_helper.dart';
import '../../utils/res/app_assets.dart';
import '../../utils/res/constants.dart';
import '../../utils/res/router/routes.dart';
import '../custom_text.dart';

class CustomBottomNavBar extends StatelessWidget {
  final bool isReview;
  final bool isTiktok;
  final bool isSnapChat;

  const CustomBottomNavBar({
    super.key,
    required this.isReview,
    this.isTiktok = false,
    this.isSnapChat = false,
  });

  bool isFormValid(CreateAdCubit cubit) {
    return cubit.isCampaignCreated == true &&
        cubit.isAdSetCreated == true &&
        cubit.isAdCreativeCreated == true &&
        cubit.isAdReviewCreated == true;
  }

  bool isTiktokFormValid(TiktokAdCubit cubit) {
    return cubit.isTiktokCampaign == true &&
        cubit.isTiktokAdGroup == true &&
        cubit.isTiktokAd == true;
  }

  bool isSnapChatFormValid(CreateSnapChatAdCubit cubit) {
    return cubit.isSnapChatAdComplete == true &&
        cubit.isSnapChatAdSetComplete == true &&
        cubit.isSnapChatCampaignComplete == true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAdCubit, CreateAdState>(
      builder: (context, mstate) {
        return BlocBuilder<TiktokAdCubit, TiktokAdState>(
          builder: (context, tiktokState) {
            return BlocBuilder<CreateSnapChatAdCubit, CreateSnapChatAdState>(
              builder: (context, snapchatState) {
                final createAdCubit = CreateAdCubit.get(context);
                final tiktokAdCubit = TiktokAdCubit.get(context);
                final snapChatAdCubit = CreateSnapChatAdCubit.get(context);

                return InkWell(
                  onTap: isTiktok
                      ? isTiktokFormValid(tiktokAdCubit)
                          ? () => _handleTiktokTap(context, tiktokAdCubit)
                          : null
                      : isSnapChat
                          ? isSnapChatFormValid(snapChatAdCubit)
                              ? () =>
                                  _handleSnapChatTap(context, snapChatAdCubit)
                              : null
                          : isFormValid(createAdCubit)
                              ? () =>
                                  _handleRegularAdTap(context, createAdCubit)
                              : null,
                  child: Container(
                    height: 80.h,
                    decoration: _buildDecoration(context),
                    child: _buildContent(context),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  ShapeDecoration _buildDecoration(BuildContext context) {
    final isValid = isTiktok
        ? isTiktokFormValid(TiktokAdCubit.get(context))
        : isSnapChat
            ? isSnapChatFormValid(CreateSnapChatAdCubit.get(context))
            : isFormValid(CreateAdCubit.get(context));

    return ShapeDecoration(
      gradient: isValid
          ? const LinearGradient(
              begin: Alignment(-0.88, -0.48),
              end: Alignment(0.88, 0.48),
              stops: [0.025, 1.0],
              colors: [Color(0xFF296AEB), Color(0xFF0B0F26)],
            )
          : const LinearGradient(
              begin: Alignment(-0.88, -0.48),
              end: Alignment(0.88, 0.48),
              stops: [0.025, 1.0],
              colors: [Colors.grey, Colors.grey],
            ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return isReview
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                alignment: AlignmentDirectional.center,
                text: "Review",
                fontSize: 24.sp,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.center,
                color: Colors.white,
              ),
            ],
          )
        : Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomText(
                    text: "Publish",
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w700,
                    textAlign: TextAlign.center,
                    color: Colors.white,
                  ),
                ],
              ),
              Lottie.asset(AppAssets.rocket, width: 60.h, height: 60.h),
            ],
          );
  }

  void _handleRegularAdTap(BuildContext context, CreateAdCubit cubit) {
    if (isReview) {
      // Navigator.pop(context);
      Navigator.pushNamed(context, Routes.reviewCampaign);
    } else {
      cubit.createAD(
        context: Constants.navigatorKey.currentContext!,
        imagesFiles: cubit.adImages,
        videosFiles: cubit.adVideo,
      );
    }
  }

  void _handleTiktokTap(BuildContext context, TiktokAdCubit cubit) {
    if (isReview) {
      Navigator.pop(context);
      Navigator.pushNamed(context, Routes.tiktokCampaignReview);
    } else {
      cubit.createAD(
        context: Constants.navigatorKey.currentContext!,
        imagesFiles: [],
        videosFiles: [],
        advertiserId: instance.get<HiveHelper>().getAdvertiserId() ?? "",
        campaignName: cubit.tiktokAdModel.campaignName ?? "",
        objectiveType: cubit.tiktokAdModel.objectiveType ?? "",
        optimizationGoal: cubit.tiktokAdModel.optimizationGoal ?? "",
        adGroupName: cubit.tiktokAdModel.adGroupName ?? "",
        location: cubit.tiktokAdModel.location ?? [],
        age: cubit.tiktokAdModel.age ?? [],
        gender: cubit.tiktokAdModel.gender!,
        languages: TiktokAdGroupCubit.get(context).selectedLanguages ?? [],
        dailyBudget: cubit.tiktokAdModel.dailyBudget ?? "",
        startDate: cubit.tiktokAdModel.startDate ?? "",
        endDate: cubit.tiktokAdModel.endDate ?? "",
        selectedTiktokInterests:
            cubit.tiktokAdModel.selectedTiktokInterests ?? [],
        adName: cubit.tiktokAdModel.adName ?? "",
        adText: cubit.tiktokAdModel.adText ?? "",
        websiteUrl: cubit.tiktokAdModel.websiteUrl ?? "",
        callToAction: cubit.tiktokAdModel.callToAction ?? '',
        adVideo: cubit.tiktokAdModel.adVideo!,
        placementType: cubit.tiktokAdModel.placementType ?? "",
        selectedTiktokPositions: cubit.tiktokAdModel.palcementsPositions ?? [],
        identityId: cubit.tiktokAdModel.identity ?? "",
        dailyBudgetMode: cubit.tiktokAdModel.dailyBudgetMode ?? "",
        existingCampaign: cubit.tiktokAdModel.existingCampaign,
      );
    }
  }

  void _handleSnapChatTap(
      BuildContext context, CreateSnapChatAdCubit cubit) async {
    if (isReview) {
      // Navigator.pop(context);
      await Navigator.pushNamed(context, Routes.snapChatReviewScreen);
    } else {
      // Implement SnapChat ad creation logic
      await cubit.createAD(
          context: Constants.navigatorKey.currentContext ?? context,
          imagesFiles: CreateSnapChatAdCubit.get(context).adImages,
          videosFiles: CreateSnapChatAdCubit.get(context).adVideo,
          snapChatAdModel: CreateSnapChatAdCubit.get(context).snapChatAdModel);
      SnapChatAdSetCubit.get(context).clearAllAdGroupData();
      SnapChatCampaignCubit.get(context).clear();
      // cubit.createSnapChatAd(
      //   context: context,
      //   // Add required SnapChat parameters here
      //   // Example:
      //   // campaignName: cubit.snapChatModel.campaignName,
      //   // adText: cubit.snapChatModel.adText,
      //   // etc...
      // );
    }
  }
}

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class Options extends Equatable {
  final dynamic id;
  final String value;
  final Widget? child;
  final String? pageAccessToken;
  final String? accountId;
  final String? pageId;

  const Options(
      {required this.id,
      required this.value,
      this.pageAccessToken,
      this.pageId,
      this.child,
      this.accountId});

  factory Options.fromJson(Map<String, dynamic> json) => Options(
        id: json['id'],
        value: json['name'],
        pageAccessToken: json['access_token'],
        accountId: json['id'],
        pageId: json['id'],
        child: json['child'],
      );
  Map<String, dynamic> toJson() => {
        'id': id,
        'access_token': pageAccessToken,
        'id': accountId,
        'id': pageId,
        'name': value,
        'child': child,
      };

  @override
  List<Object?> get props =>
      [id, value, child, pageAccessToken, accountId, pageId];

  @override
  String toString() =>
      '$id: $value $child $pageAccessToken $accountId $pageId ';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Options &&
          runtimeType == other.runtimeType &&
          id == other.id; // Only compare the 'id' field for equality.

  @override
  int get hashCode => id.hashCode;
}

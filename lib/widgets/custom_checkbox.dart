import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomCheckbox extends StatefulWidget {
  const CustomCheckbox({
    super.key,
    this.onChanged,
    this.intialValue,
    // this.subtitle,
    // this.contentPadding,
    this.controlAffinity = ListTileControlAffinity.platform,
    this.label,
    this.child,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  }) : assert(
  label != null || child != null,
  );

  final void Function(bool? value)? onChanged;
  final String? label;
  final Widget? child;
  // final String? subtitle;
  final bool? intialValue;
  final ListTileControlAffinity controlAffinity;
  final CrossAxisAlignment crossAxisAlignment;
  // final EdgeInsetsGeometry? contentPadding;

  @override
  State<CustomCheckbox> createState() => CustomCheckboxState();
}

class CustomCheckboxState extends State<CustomCheckbox> {
  bool? isSeleted;

  @override
  void initState() {
    super.initState();

    isSeleted = widget.intialValue ?? false;
  }

  void changeValue(bool? value) {
    setState(() => isSeleted = value);
    if (widget.onChanged != null) widget.onChanged!(value);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: widget.crossAxisAlignment,
      children: [
        if (widget.controlAffinity == ListTileControlAffinity.platform ||
            widget.controlAffinity == ListTileControlAffinity.leading)
          Checkbox(

            activeColor: Constants.primaryTextColor,
            value: isSeleted,
            visualDensity: VisualDensity.compact,
            onChanged: changeValue,
          ),
        if (widget.child != null || widget.label != null) //Expanded(child: widget.child!),
          Expanded(
            child: widget.child ??
                CustomText(text: widget.label!, fontWeight: FontWeight.w400, fontSize: 15.sp),
          ),
        if (widget.controlAffinity == ListTileControlAffinity.trailing)
          Checkbox(
            activeColor: Constants.primaryTextColor,

            value: isSeleted,
            visualDensity: VisualDensity.compact,
            onChanged: changeValue,
          ),
      ],
    );

    // return CheckboxListTile(
    //   value: isSeleted,
    //   controlAffinity: widget.controlAffinity,
    //   contentPadding: widget.contentPadding,
    //   title: widget.child ??
    //       (widget.label == null
    //           ? null
    //           : CustomText(
    //               text: widget.label!,
    //               fontWeight: FontWeight.w400,
    //               fontSize: 15.sp,
    //             )),
    //   subtitle: widget.subtitle == null
    //       ? null
    //       : CustomText(
    //           text: widget.subtitle!,
    //           fontWeight: FontWeight.w400,
    //           color: Constants.darkGray,
    //           fontSize: 12.sp,
    //         ),
    //   onChanged: (value) {
    //     setState(() => isSeleted = value);
    //     if (widget.onChanged != null) widget.onChanged!(value);
    //   },
    // );
  }
}

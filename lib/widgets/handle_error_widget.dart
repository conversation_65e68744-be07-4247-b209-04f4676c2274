import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../utils/network/errors/failures.dart';
import '../utils/res/colors.dart';



class HandleErrorWidget extends StatelessWidget {
  final  failure;
  final text;

  const HandleErrorWidget({Key? key,required this.fun,required  this.failure,this.text}) : super(key: key);

  final VoidCallback  fun;

  @override
  Widget build(BuildContext context) {
    return failure is ConnectionFailure ? Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.wifi,
            size: 20,
          ),
          const SizedBox(
            height: 20,
          ),
          Text(
            'no connection'.tr,
            style: const TextStyle(
                fontSize: 16, color: AppColors.mainColor),
          ),
          const SizedBox(
            height: 20,
          ),
          MaterialButton(
            color: AppColors.mainColor,
            onPressed: fun,
            child: Text(
              'try again'.tr,
              style: const TextStyle(fontSize: 16, color: Colors.white),
            ),
          )
        ],
      ),
    ):Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error,
            size: 20,
          ),
          const SizedBox(
            height: 20,
          ),
          Text(
            text??'error'.tr,
            style: const TextStyle(
                fontSize: 16, color: AppColors.mainColor),
          ),
          const SizedBox(
            height: 20,
          ),
          MaterialButton(
            color: AppColors.mainColor,
            onPressed: fun,
            child: Text(
              'try again'.tr,
              style: const TextStyle(fontSize: 16, color: Colors.white),
            ),
          )
        ],
      ),
    );
  }
}
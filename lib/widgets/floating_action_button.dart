import 'package:flutter/material.dart';

class CustomFloatingButton extends StatelessWidget {
  const CustomFloatingButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: 60,
          height: 60,
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: const ShapeDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.56, -0.83),
                      end: Alignment(-0.56, 0.83),
                      colors: [Color(0xFF3A8CFF), Color(0xFF053B88)],
                    ),
                    shape: OvalBorder(
                      side: BorderSide(
                        width: 0.50,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: Color(0xFFD7D7D7),
                      ),
                    ),
                    shadows: [
                      BoxShadow(
                        color: Color(0x338E9BAE),
                        blurRadius: 10,
                        offset: Offset(4, 4),
                        spreadRadius: 0,
                      )
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 9.02,
                top: 9.02,
                child: Container(
                  width: 42.12,
                  height: 42.12,
                  decoration: const ShapeDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.57, -0.82),
                      end: Alignment(-0.57, 0.82),
                      colors: [Color(0xFFF5F5F9), Color(0xFFDADFE7)],
                    ),
                    shape: OvalBorder(
                      side: BorderSide(width: 0.13, color: Colors.white),
                    ),
                    shadows: [
                      BoxShadow(
                        color: Color(0x338E9BAE),
                        blurRadius: 14.26,
                        offset: Offset(0, 7.13),
                        spreadRadius: 0,
                      )
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 12.10,
                top: 12.10,
                child: Container(
                  width: 35.80,
                  height: 35.80,
                  decoration: const ShapeDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.56, -0.83),
                      end: Alignment(-0.56, 0.83),
                      colors: [Color(0xFFDBE0E7), Color(0xFFF7FAFF)],
                    ),
                    shape: OvalBorder(
                      side: BorderSide(
                        width: 0.39,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: Color(0xFFD7D7D7),
                      ),
                    ),
                    shadows: [
                      BoxShadow(
                        color: Color(0x338E9BAE),
                        blurRadius: 7.86,
                        offset: Offset(3.14, 3.14),
                        spreadRadius: 0,
                      )
                    ],
                  ),
                ),
              ),
              const Positioned(
                left: 22.14,
                top: 22.14,
                child: SizedBox(
                  width: 15.71,
                  height: 15.71,

                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
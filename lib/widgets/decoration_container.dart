import 'package:flutter/material.dart';

class DecoratedContainer extends StatelessWidget {
  final Widget widget;
  final Color color;
  final BoxShadow boxShadow;
  final double radius;
  final double? height;
  final double? width;

  const DecoratedContainer({super.key,required this.widget,required this.color,required this.boxShadow,required this.radius, this.width, this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: ShapeDecoration(
        color: color,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius),
        ),
        shadows:  [
          boxShadow
        ],
      ),
      child: widget,
    );
  }
}

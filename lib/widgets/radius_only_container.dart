import 'package:flutter/material.dart';

class RadiusOnlyContainer extends StatelessWidget {
  final Widget widget;
  final double height;
  final RoundedRectangleBorder rectangleBorder;
  final Color color;
  const RadiusOnlyContainer({super.key,required this.rectangleBorder,required this.widget,required this.height,required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: height,
      decoration:
       ShapeDecoration(
        color: color,
        shape:
         rectangleBorder,

      ),
      child: widget,
    );
  }
}

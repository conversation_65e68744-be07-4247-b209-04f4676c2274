import 'package:flutter/material.dart';

class ShaderMaskWidget extends StatelessWidget {
  final Gradient gradient;
  final Widget widget;
  const ShaderMaskWidget({super.key,required this.widget,required this.gradient});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (Rect bounds) {
        return gradient.createShader(bounds);
      },
      child: widget,
    );
  }
}

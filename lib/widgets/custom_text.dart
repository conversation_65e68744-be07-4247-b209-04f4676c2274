import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../utils/res/colors.dart';
import '../utils/res/media_query_config.dart';

class CustomText extends StatelessWidget {
  final String text;
  final Color color;
  final TextAlign? textAlign;
  final String? fontFamily;
  final EdgeInsetsGeometry? textMargin;
  final TextOverflow? textOverflow;
  final double fontSize;
  final double? wordSpacing;
  final TextDecoration? textDecoration;
  final AlignmentDirectional? alignment;
  final BorderRadiusGeometry? borderRadius;

  final FontWeight fontWeight;
  final EdgeInsetsGeometry? textPadding;
  final Color? bgColor;

  final int? maxLines;

  const CustomText(
      {super.key,
      this.maxLines = 1,
      this.fontFamily = "Cairo",
      this.textAlign,
      this.textOverflow = TextOverflow.visible,
      this.textDecoration,
      required this.text,
      this.textMargin,
      this.borderRadius,
      this.wordSpacing,
      this.color = AppColors.textColor,
      this.fontSize = 16,
      this.alignment = AlignmentDirectional.topStart,
      this.textPadding,
      this.bgColor,
      this.fontWeight = FontWeight.w400});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: alignment,
      padding: textPadding,
      margin: textMargin,
      decoration: BoxDecoration(color: bgColor, borderRadius: borderRadius),
      child: AutoSizeText(
        overflow: maxLines! > 3 ? TextOverflow.ellipsis : TextOverflow.visible,
        text,
        style: TextStyle(
          color: color,
          fontSize: SizeConfig.textscale(context) * fontSize,
          fontWeight: fontWeight,
          fontFamily: fontFamily,
          height: 1.20,
          wordSpacing: wordSpacing,
          overflow: textOverflow,
          decoration: textDecoration,
        ),
        maxLines: maxLines,
        textAlign: textAlign,
      ),
    );
  }
}

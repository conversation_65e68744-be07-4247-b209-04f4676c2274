import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/res/constants.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? radius;
  final EdgeInsetsGeometry? contentPadding;
  final Color? color;
  final Color? textColor;
  final Color? hoveColor;
  final FontWeight? textWeight;
  final double? textSize;
  final double? elevation;
  final double? gapLeadingText;
  final Widget? child;
  final double? height;
  final double? letterSpacing;
  final BorderSide? side;
  final double textHeight;

  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.radius,
    this.contentPadding,
    this.color,
    this.textColor,
    this.hoveColor,
    this.textSize,
    this.textWeight,
    this.elevation,
    this.letterSpacing,
    this.textHeight = 1,
    this.height,
    this.side,
  })  : child = null,
        gapLeadingText = null,
        super(key: key);

  const CustomButton.icon({
    Key? key,
    required this.text,
    required this.onPressed,
    required this.child,
    this.gapLeadingText = 10,
    this.radius,
    this.contentPadding,
    this.color,
    this.textColor,
    this.hoveColor,
    this.textSize,
    this.textWeight,
    this.elevation,
    this.letterSpacing,
    this.height,
    this.textHeight = 1,
    this.side,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height ?? 48.h,
      child: TextButton(
        onPressed: onPressed,
        clipBehavior: Clip.antiAlias,
        style: TextButton.styleFrom(
          side: side,
          foregroundColor: hoveColor ?? Colors.white,
          backgroundColor: onPressed != null ? (color ?? Constants.mainColor) : Constants.lightGray,
          padding: contentPadding ?? const EdgeInsets.all(8).r,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius ?? 50.r),
          ),
          elevation: elevation,
          fixedSize: Size(double.infinity, height ?? 48.h),
        ),
        child: child == null
            ? CustomText(
          text: text,
          color: onPressed == null ? Constants.lightGray : textColor ?? Colors.white,
          alignment: AlignmentDirectional.center,
          maxLines: 1,
          fontWeight: textWeight ?? FontWeight.bold,
          fontSize: textSize ?? 16.sp,
        )
            : Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            child!,
            SizedBox(width: gapLeadingText),
            CustomText(
              text: text,
              color: textColor ?? Colors.white,
              alignment: AlignmentDirectional.center,
              maxLines: 1,
              fontWeight: textWeight ?? FontWeight.bold,
              fontSize: textSize ?? 16.sp,
            )
          ],
        ),
      ),
    );
  }
}

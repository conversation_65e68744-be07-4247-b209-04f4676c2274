// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import '../../../../../../utils/res/media_query_config.dart';
// import '../utils/res/app_assets.dart';
// import 'custom_text.dart';
//
//
//
// class CheckItemWidget extends StatefulWidget {
//   final String title;
//    bool isChecked;
//
//    CheckItemWidget({super.key,required String this.title,required bool this.isChecked,});
//
//   @override
//   State<CheckItemWidget> createState() => _CheckItemWidgetState();
// }
//
// class _CheckItemWidgetState extends State<CheckItemWidget> {
//
//
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//
//       // mainAxisAlignment:
//       // MainAxisAlignment.spaceBetween,
//       children: [
//         widget.isChecked
//             ? SvgPicture.asset(AppAssets.checkFillIcon,height: SizeConfig.hieghtr(16, context),width: SizeConfig.widthr(16, context))
//             : SvgPicture.asset(AppAssets.checkIcon,height: SizeConfig.hieghtr(16, context),width: SizeConfig.widthr(16, context)),
//         SizedBox(width: SizeConfig.widthr(8, context),),
//         TextCustom(text: widget.title,fontSize: 14,fontWeight: FontWeight.w400),
//       ],
//     );
//   }
// }

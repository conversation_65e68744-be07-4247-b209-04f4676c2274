import 'package:flutter/material.dart';

import '../utils/res/app_assets.dart';
import '../utils/res/colors.dart';

class LoadingWidget extends StatelessWidget {
  final bool isCircle;

  const LoadingWidget({
    this.isCircle = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if(isCircle) {
      return const Center(child: CircularProgressIndicator(color: AppColors.mainColor),);
    }
    return  Center(child:
    Image.asset(AppAssets.logo)
    );

  }
}
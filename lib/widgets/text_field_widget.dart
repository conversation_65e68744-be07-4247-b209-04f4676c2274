import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/res/colors.dart';

// ignore: must_be_immutable
class CustomTextField extends StatefulWidget {
  String? lable;
  Widget? icon;

  Widget? suffixIcon;
  Color? filledColor;
  bool? isMobile;
  bool? centerText;
  TextEditingController? controller;
  List<TextInputFormatter>? inputFormatters;
  bool hasPassword;
  bool? isEmail;
  bool? radiusOnly;

  bool? passwordIdentical;
  bool? isPhoneCode;
  bool? isFinal;
  bool? isEditable;
  bool? isNotes;
  bool? hasBorder;
  Function? onFieldSubmitted;
  Function(String)? onChanged;
  Function? onSaved;
  bool? readOnly;
  TextStyle? style;
  FormFieldValidator? validator;
  String? hintText;
  String? initialValue;
  TextStyle? hintStyle;
  int? maxLine;
  int? fontSize;
  TextInputType? textInputType;
  double radius;
  Color? borderColor;
  int? maxLength;
  bool? borderRaduisOnly;
  FocusNode? node;

  String? errorText;

  CustomTextField(
      {super.key,
      this.errorText,
      this.radius = 10,
      this.fontSize,
      this.radiusOnly = false,
      this.icon,
      this.borderColor,
      this.lable,
      this.filledColor,
      this.hasBorder = true,
      this.isEditable = true,
      this.isNotes = false,
      this.centerText = false,
      this.isFinal = false,
      this.isPhoneCode = false,
      this.isMobile = false,
      this.isEmail = false,
      this.passwordIdentical,
      this.hasPassword = false,
      this.controller,
      this.suffixIcon,
      this.onFieldSubmitted,
      this.onChanged,
      this.onSaved,
      this.readOnly,
      this.style,
      this.validator,
      this.hintText,
      this.initialValue,
      this.hintStyle,
      this.maxLine,
      this.inputFormatters,
      this.textInputType,
      this.maxLength,
      this.borderRaduisOnly,
      this.node});

  @override
  CustomTextFieldState createState() => CustomTextFieldState();
}

class CustomTextFieldState extends State<CustomTextField> {
  bool showPassword = true;

  List<String> codes = ['+20'];

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      maxLength: widget.maxLength,
      validator: widget.validator,
      focusNode: widget.node,
      // textDirection: TextDirection.rtl,
      key: widget.key,
      initialValue: widget.initialValue,
      readOnly: widget.readOnly ?? false,
      style: widget.style,
      controller: widget.controller,
      keyboardType: widget.textInputType,
      enabled: widget.isEditable,
      textInputAction: TextInputAction.next,
      inputFormatters: widget.inputFormatters,
      onChanged: widget.onChanged,
      obscureText: (widget.hasPassword) ? showPassword : widget.hasPassword,
      maxLines: widget.maxLine ?? ((widget.isNotes!) ? 3 : 1),
      // textAlign: (widget.centerText!) ? TextAlign.center : TextAlign.right,
      cursorColor: AppColors.mainColor,
      decoration: InputDecoration(
        errorText: widget.errorText,
        counterText: '',
        hintText: widget.hintText,
        hintStyle: widget.hintStyle ?? const TextStyle(color: AppColors.hint),
        contentPadding: const EdgeInsets.all(10),
        enabledBorder: (!widget.hasBorder!)
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: (widget.radiusOnly == true)
                    ? BorderRadius.only(
                        bottomLeft: Radius.circular(widget.radius),
                        topLeft: Radius.circular(widget.radius))
                    : BorderRadius.circular(widget.radius),
                borderSide: BorderSide(
                    width: .5,
                    color: widget.borderColor != null
                        ? widget.borderColor!
                        : AppColors.borderColor)),
        disabledBorder: (!widget.hasBorder!)
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: (widget.radiusOnly == true)
                    ? BorderRadius.only(
                        bottomLeft: Radius.circular(widget.radius),
                        topLeft: Radius.circular(widget.radius))
                    : BorderRadius.circular(widget.radius),
                borderSide: BorderSide(
                    width: .5,
                    color: widget.borderColor != null
                        ? widget.borderColor!
                        : AppColors.borderColor)),
        border: (!widget.hasBorder!)
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: (widget.radiusOnly == true)
                    ? BorderRadius.only(
                        bottomLeft: Radius.circular(widget.radius),
                        topLeft: Radius.circular(widget.radius))
                    : BorderRadius.circular(widget.radius),
                borderSide: BorderSide(
                    width: .5,
                    color: widget.borderColor != null
                        ? widget.borderColor!
                        : AppColors.borderColor)),
        focusedBorder: (!widget.hasBorder!)
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: (widget.radiusOnly == true)
                    ? BorderRadius.only(
                        bottomLeft: Radius.circular(widget.radius),
                        topLeft: Radius.circular(widget.radius))
                    : BorderRadius.circular(widget.radius),
                borderSide: BorderSide(
                    width: .5,
                    color: widget.borderColor != null
                        ? widget.borderColor!
                        : AppColors.borderColor)),
        // labelText: widget.lable,
        // labelStyle:  TextStyle(color: AppColors.mainColor),
        prefixIcon: widget.icon,
        fillColor: (widget.filledColor == null)
            ? Colors.transparent
            : widget.filledColor,
        filled: true,
        suffixIcon: (widget.hasPassword)
            ? InkWell(
                splashColor: Colors.transparent,
                onTap: () {
                  showPassword = !showPassword;
                  setState(() {});
                },
                child: (!showPassword)
                    ? const Icon(
                        Icons.visibility,
                        color: AppColors.mainColor,
                      )
                    : const Icon(
                        Icons.visibility_off,
                        color: Colors.grey,
                      ),
              )
            : (!widget.isPhoneCode!)
                ? widget.suffixIcon
                : Directionality(
                    textDirection: TextDirection.ltr,
                    child: Container(),
                  ),
      ),
    );
  }
}

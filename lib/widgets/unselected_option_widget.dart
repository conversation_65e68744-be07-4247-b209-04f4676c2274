import 'package:flutter/material.dart';

import '../utils/res/constants.dart';
import 'custom_text.dart';

class UnselectedOptionWidget extends StatelessWidget {
  String name;
   UnselectedOptionWidget({super.key,required this.name});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment:
          MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: CustomText(
                text: name,
                color: Constants.darkColor,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
            Container(
              height: 22,
              width: 22,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Constants.darkColor,
                  width: 1.5,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        const Divider(color: Constants.gray),
      ],
    );
  }
}

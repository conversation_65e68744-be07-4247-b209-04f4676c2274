import 'package:flutter/material.dart';

import '../utils/res/constants.dart';

class SlidingSwitch extends StatefulWidget {
  final double height;
  final ValueChanged<bool> onChanged;
  final double width;
  final bool value;
  final String textOff;
  final String textOn;
  final IconData? iconOff;
  final IconData? iconOn;
  final double contentSize;
  final Duration animationDuration;
  final Color colorOn;
  final Color colorOff;
  final Color background;
  final Color buttonColor;
  final Color inactiveColor;
  final Function onTap;
  final Function onDoubleTap;
  final Function onSwipe;

  const SlidingSwitch({super.key, 
    required this.value,
    required this.onChanged,
    this.height = 55,
    this.width = 250,
    this.animationDuration = const Duration(milliseconds: 400),
    required this.onTap,
    required this.onDoubleTap,
    required this.onSwipe,
    this.textOff = "Off",
    this.textOn = "On",
    this.iconOff,
    this.iconOn,
    this.contentSize = 17,
    this.colorOn = const Color(0xffdc6c73),
    this.colorOff = const Color(0xff6682c0),
    this.background = const Color(0xffe4e5eb),
    this.buttonColor = const Color(0xfff7f5f7),
    this.inactiveColor = const Color(0xff636f7b),
  });
  @override
  _SlidingSwitch createState() => _SlidingSwitch();
}

class _SlidingSwitch extends State<SlidingSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> animation;
  double value = 0.0;

  late bool turnState;

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
        vsync: this,
        lowerBound: 0.0,
        upperBound: 1.0,
        duration: widget.animationDuration);
    animation =
        CurvedAnimation(parent: animationController, curve: Curves.easeInOut);
    animationController.addListener(() {
      setState(() {
        value = animation.value;
      });
    });
    turnState = widget.value;
    _determine();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onDoubleTap: _handleDoubleTap,
      onTap: _handleTap,
      onPanEnd: _handleSwipe,
      child: Container(
        height: widget.height,
        width: widget.width,
        decoration: BoxDecoration(
          color: widget.background,
          borderRadius: BorderRadius.circular(50),
        ),
        padding: const EdgeInsets.all(2),
        child: Stack(
          children: <Widget>[
            _buildBackground(),
            _buildSlidingButton(),
            _buildTextAndIcons(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        gradient: Constants.secGradient,
        borderRadius: const BorderRadius.all(Radius.circular(50.0)),
        color: widget.buttonColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            offset: const Offset(0, 10),
            blurRadius: 20.0,
          ),
        ],
      ),
    );
  }

  Widget _buildSlidingButton() {
    return AnimatedPositioned(
      left: value * (widget.width - 28),
      duration: widget.animationDuration,
      child: Container(
        height: widget.height - 4,
        width: widget.width * 0.5 - 4,
        decoration: BoxDecoration(
          color: widget.buttonColor,
          borderRadius: BorderRadius.circular(50),
        ),
      ),
    );
  }

  Widget _buildTextAndIcons() {
    return Row(
      children: [
        _buildLeftContent(),
        _buildRightContent(),
      ],
    );
  }

  Widget _buildLeftContent() {
    return Expanded(
      child: Center(
        child: widget.iconOff == null
            ? Text(widget.textOff, style: _textStyle(turnState))
            : Icon(widget.iconOff, size: widget.contentSize, color: _getIconColor(turnState)),
      ),
    );
  }

  Widget _buildRightContent() {
    return Expanded(
      child: Center(
        child: widget.iconOn == null
            ? Text(widget.textOn, style: _textStyle(turnState))
            : Icon(widget.iconOn, size: widget.contentSize, color: _getIconColor(turnState)),
      ),
    );
  }

  TextStyle _textStyle(bool isOn) {
    return TextStyle(
      color: isOn ? widget.colorOn : widget.inactiveColor,
      fontSize: widget.contentSize,
      fontWeight: FontWeight.w600,
    );
  }

  Color _getIconColor(bool isOn) {
    return isOn ? widget.colorOn : widget.inactiveColor;
  }

  void _handleTap() {
    _action();
    widget.onTap();
  }

  void _handleDoubleTap() {
    _action();
    widget.onDoubleTap();
  }

  void _handleSwipe(DragEndDetails details) {
    _action();
    widget.onSwipe();
  }

  _action() {
    _determine(changeState: true);
  }

  _determine({bool changeState = false}) {
    setState(() {
      if (changeState) turnState = !turnState;
      (turnState)
          ? animationController.forward()
          : animationController.reverse();
      if (changeState) widget.onChanged(turnState);
    });
  }
}
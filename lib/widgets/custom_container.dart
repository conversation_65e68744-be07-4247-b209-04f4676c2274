import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomContainer extends StatelessWidget {
  final double height;
  final double width;
  final Gradient gradient;
  final double radius;
  final String text;
  final Color textColor;
  final FontWeight fontWeight;
  final double fontSize;
  const CustomContainer({
    super.key,
    required this.height,
    required this.width,
    required this.gradient,
    required this.radius,
    required this.text,
    required this.fontWeight,
    required this.fontSize,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: ShapeDecoration(
        gradient: gradient,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x4CFFFFFF),
            blurRadius: 53.50,
            offset: Offset(0, 4),
            spreadRadius: -7,
          )
        ],
      ),
      child: CustomText(
        text: text,
        color: textColor,
        fontWeight: fontWeight,
        fontSize: fontSize,
        alignment: AlignmentDirectional.center,
      ),
    );
  }
}

import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomSvgWidget extends StatelessWidget {
  final String svg;
  final double height;
  final double width;
  final Color? color;
  final bool? hasGradient;
   const CustomSvgWidget(
      {super.key, required this.svg, this.height = 24, this.width = 24,this.color,this.hasGradient = false});

  @override
  Widget build(BuildContext context) {
    return hasGradient == true ? ShaderMask(
      shaderCallback: (Rect bound){
        return Constants.secGradient.createShader(bound);
      },
      child: SvgPicture.asset(
        svg,
        height: height,
        width: width,

        colorFilter: color != null ?ColorFilter.mode(

            color!,
            BlendMode.srcIn):null,
      ),
    ):SvgPicture.asset(
      svg,
      height: height,
      width: width,

      colorFilter: color != null ?ColorFilter.mode(

          color!,
          BlendMode.srcIn):null,
    );
  }
}

import 'dart:io';

import 'package:ads_dv/utils/res/media_query_config.dart';
import 'package:flutter/material.dart';

class UploadedImageWidget extends StatefulWidget {
  final String text;
  final double height;

  final IconData? icon;

  final VoidCallback? onPressed;

  File image;
  bool isDestination = false;

  UploadedImageWidget(
      {super.key,
      required this.text,
      this.icon,
      required this.image,
      required this.onPressed,
      required this.height,
      this.isDestination = false});

  @override
  State<UploadedImageWidget> createState() => _UploadedImageWidgetState();
}

class _UploadedImageWidgetState extends State<UploadedImageWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
            height: widget.height,
            width: SizeConfig.screenWidth(context) * 0.9,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
            ),
            child: Image.file(widget.image, fit: BoxFit.fill),
          ),
        ),
        // Positioned(
        //   bottom: 0,
        //   left: 0,
        //   child: Align(
        //     alignment: Alignment.bottomLeft,
        //     child: Padding(
        //       padding: EdgeInsets.all(16.sp),
        //       child: Row(
        //         crossAxisAlignment: CrossAxisAlignment.center,
        //         children: [
        //           Icon(
        //             widget.icon,
        //             color:
        //                 widget.image.path.isEmpty ? Colors.black : Colors.white,
        //           ),
        //           const SizedBox(width: 8),
        //           CustomText(
        //             text: widget.text,
        //             fontSize: 14.sp,
        //             fontWeight: FontWeight.w400,
        //             color:
        //                 widget.image.path.isEmpty ? Colors.black : Colors.white,
        //           ),
        //         ],
        //       ),
        //     ),
        //   ),
        // ),
        if (widget.isDestination)
          Positioned(
            top: 0,
            right: 0,
            child: Align(
              alignment: Alignment.topRight,
              child: IconButton(
                onPressed: widget.onPressed,
                icon: const Icon(
                  Icons.cancel_outlined,
                ),
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}

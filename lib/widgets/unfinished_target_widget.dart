import 'package:ads_dv/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../utils/res/constants.dart';

class UnFinishedTargetWidget extends StatelessWidget {
  String name;
  String icon;
  Widget? processPercentage;
  UnFinishedTargetWidget({super.key, required this.name, required this.icon,this.processPercentage});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32.92),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x33000000),
            blurRadius: 13.93,
            offset: Offset(0, 0),
            spreadRadius: -3.80,
          )
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SvgPicture.asset(icon,
                  color: Constants.primaryTextColor, height: 24),
              SizedBox(width: 10.w),
              CustomText(
                text: name,
                fontSize: 14.sp,
                color: Constants.primaryTextColor,
                fontWeight: FontWeight.w400,
                alignment: AlignmentDirectional.centerStart,
              ),
            ],
          ),
          Row(
            children: [
              processPercentage ?? const SizedBox(),
              const Icon(
                Icons.arrow_forward_ios,
                color: Constants.primaryTextColor,
              ),
            ],
          )
        ],
      ),
    );
  }
}

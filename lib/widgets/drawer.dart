import 'package:ads_dv/features/auth/presentation/login/controllers/login/login_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_reach_estimate/reach_estimate_cubit.dart';
import 'package:ads_dv/features/home_layout/presentation/controllers/home_layout_cubit.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../utils/res/app_assets.dart';
import '../utils/res/constants.dart';
import '../utils/res/router/routes.dart';

class HomeDrawer extends StatelessWidget {
  BuildContext mainContext;

  HomeDrawer({super.key, required this.mainContext});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        color: Colors.white,
        child: ListView(
          children: <Widget>[
            Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 30.sp),
                  child: CachedImageWidget(
                    assetsImage: AppAssets.drawerLogo,
                    height: 86.h,
                  ),
                ),
                const Divider(color: Constants.gray),
              ],
            ),
            Column(
              children: [
                20.verticalSpace,
                ListTile(
                  leading: CustomSvgWidget(
                    svg: AppAssets.payment,
                    height: 20.h,
                    width: 20.h,
                  ),
                  title: CustomText(
                    text: "packages".tr,
                    color: Colors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                  onTap: () async {
                    Navigator.pop(context);
                    await Navigator.pushNamed(context, Routes.payment);
                  },
                ),
                20.verticalSpace,
                BlocBuilder<HomeLayoutCubit, HomeLayoutState>(
                  builder: (context, state) {
                    return ListTile(
                      leading: SvgPicture.asset(
                        AppAssets.accounts,
                        height: 14.h,
                        width: 14.h,
                      ),
                      title: CustomText(
                        text: "Accounts".tr,
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.pushNamed(context, Routes.accounts)
                            .then((value) {
                          HomeLayoutCubit.get(context)
                              .hasCampaign(context: context);
                        });
                      },
                    );
                  },
                ),
                20.verticalSpace,
                ListTile(
                  leading: CustomSvgWidget(
                    svg: AppAssets.management,
                    height: 24.h,
                    width: 24.h,
                  ),
                  title: CustomText(
                    text: "Management Area".tr,
                    color: Colors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                  onTap: () async {
                    Navigator.pop(context);
                    await Navigator.pushNamed(context, Routes.teamManagements);
                  },
                ),
                20.verticalSpace,
                ListTile(
                  leading: CustomSvgWidget(
                    svg: AppAssets.payment,
                    height: 20.h,
                    width: 20.h,
                  ),
                  title: CustomText(
                    text: "tickets".tr,
                    color: Colors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                  onTap: () async {
                    Navigator.pop(context);
                    await Navigator.pushNamed(context, Routes.ticketsHistory);
                  },
                ),
                20.verticalSpace,
                ListTile(
                  leading: CustomSvgWidget(
                    svg: AppAssets.settings,
                    height: 20.h,
                    width: 20.h,
                  ),
                  title: CustomText(
                    text: "Settings".tr,
                    color: Colors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                  onTap: () async {
                    Navigator.pop(context);
                    await Navigator.pushNamed(context, Routes.setting);
                  },
                ),
                40.verticalSpace,
              ],
            ),
            BlocBuilder<CreateAdCubit, CreateAdState>(
              builder: (context, state) {
                return Column(
                  children: [
                    const Divider(color: Constants.gray),
                    20.verticalSpace,
                    ListTile(
                      leading: CustomSvgWidget(
                        svg: AppAssets.terms,
                        height: 20.h,
                        width: 20.h,
                      ),
                      title: CustomText(
                        text: "Terms and conditions".tr,
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.pushNamed(
                            context, Routes.termsConditions);
                      },
                    ),
                    20.verticalSpace,
                    ListTile(
                      leading: CustomSvgWidget(
                        svg: AppAssets.privacy,
                        height: 20.h,
                        width: 20.h,
                      ),
                      title: CustomText(
                        text: "Privacy & Policy".tr,
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.pushNamed(
                            context, Routes.privacyPolicy);
                      },
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 30.sp),
                      child: ListTile(
                        leading: CustomSvgWidget(
                          svg: AppAssets.logout,
                          height: 20.h,
                          width: 20.h,
                        ),
                        title: ShaderMask(
                          shaderCallback: (Rect bounds) {
                            return Constants.secGradient.createShader(bounds);
                          },
                          child: CustomText(
                            text: "Logout".tr,
                            color: Constants.redColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        onTap: () async {
                          //
                          // await instance<HiveHelper>().deleteUserModel();
                          CreateAdCubit.get(context).clearCampaignData(context);
                          ReachEstimateCubit.get(context).clearReachEstimate();
                          await instance.get<HiveHelper>().clearAllCache();
                          // await instance.get<HiveHelper>().deleteAdAccount();
                          // await instance.get<HiveHelper>().deleteMetaPages();
                          // await instance.get<HiveHelper>().deleteUserModel();
                          // await instance
                          //     .get<HiveHelper>()
                          //     .deleteTiktokPageName();
                          // await instance.get<HiveHelper>().deleteAdvertiserId();
                          await instance<LoginCubit>().logout();
                          // Navigator.pushNamedAndRemoveUntil(context, Routes.login, (route) => false);
                          // Navigator.pop(context);
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

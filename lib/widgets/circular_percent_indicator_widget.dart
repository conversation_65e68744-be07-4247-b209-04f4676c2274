import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_campaign/create_campaign_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';

class CircularIndicatorWidget extends StatelessWidget {
  CreateCampaignCubit? cubit;
  CreateAdCubit? adCubit;
  bool isAdSet;
  bool isDemographic;
  bool isTargeting;
  bool isLocation;

  bool isAdCreative;
  CircularIndicatorWidget(
      {super.key,
      this.cubit,
      this.adCubit,
      required this.isLocation,
      required this.isTargeting,
      required this.isDemographic,
      required this.isAdSet,
      required this.isAdCreative});

  @override
  Widget build(BuildContext context) {
    return cubit != null
        ? BlocBuilder<CreateAdCubit, CreateAdState>(
            builder: (context, state) {
              return CircularPercentIndicator(
                circularStrokeCap: CircularStrokeCap.round,
                radius: 12.0,
                lineWidth: 5.5,
                percent: CreateAdCubit.get(context).campaignProcessPercentage,
                linearGradient: Constants.secGradient,

                //  progressColor: Constants.mainColor,
                backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                reverse: true,
              );
            },
          )
        : (isAdCreative)
            ? BlocBuilder<CreateAdCubit, CreateAdState>(
                builder: (context, state) {
                  return CircularPercentIndicator(
                    circularStrokeCap: CircularStrokeCap.round,
                    radius: 12.0,
                    lineWidth: 5.5,
                    percent: CreateAdCubit.get(context).adCreativePercentage,
                    linearGradient: Constants.secGradient,

                    //  progressColor: Constants.mainColor,
                    backgroundColor: const Color(0xFFFB533E).withOpacity(0.1),
                    reverse: true,
                  );
                },
              )
            : (isAdSet)
                ? BlocBuilder<CreateAdCubit, CreateAdState>(
                    builder: (context, state) {
                      return CircularPercentIndicator(
                        circularStrokeCap: CircularStrokeCap.round,
                        radius: 12.0,
                        lineWidth: 5.5,
                        percent: CreateAdCubit.get(context).adSetPercentage,
                        linearGradient: Constants.secGradient,

                        //  progressColor: Constants.mainColor,
                        backgroundColor:
                            const Color(0xFFFB533E).withOpacity(0.1),
                        reverse: true,
                      );
                    },
                  )
                : (isDemographic)
                    ? BlocBuilder<CreateAdCubit, CreateAdState>(
                        builder: (context, state) {
                          return CircularPercentIndicator(
                            circularStrokeCap: CircularStrokeCap.round,
                            radius: 12.0,
                            lineWidth: 5.5,
                            percent: CreateAdCubit.get(context).demoPercentage,
                            linearGradient: Constants.secGradient,

                            //  progressColor: Constants.mainColor,
                            backgroundColor:
                                const Color(0xFFFB533E).withOpacity(0.1),
                            reverse: true,
                          );
                        },
                      )
                    : (isLocation)
                        ? BlocBuilder<CreateAdCubit, CreateAdState>(
                            builder: (context, state) {
                              return CircularPercentIndicator(
                                circularStrokeCap: CircularStrokeCap.round,
                                radius: 12.0,
                                lineWidth: 5.5,
                                percent: CreateAdCubit.get(context)
                                    .locationPercentage,
                                linearGradient: Constants.secGradient,

                                //  progressColor: Constants.mainColor,
                                backgroundColor:
                                    const Color(0xFFFB533E).withOpacity(0.1),
                                reverse: true,
                              );
                            },
                          )
                        : (isTargeting)
                            ? BlocBuilder<CreateAdCubit, CreateAdState>(
                                builder: (context, state) {
                                  return CircularPercentIndicator(
                                    circularStrokeCap: CircularStrokeCap.round,
                                    radius: 12.0,
                                    lineWidth: 5.5,
                                    percent:
                                    CreateAdCubit.get(context).interestsPercentage,
                                    linearGradient: Constants.secGradient,

                                    //  progressColor: Constants.mainColor,
                                    backgroundColor: const Color(0xFFFB533E)
                                        .withOpacity(0.1),
                                    reverse: true,
                                  );
                                },
                              )
                            : BlocBuilder<CreateAdCubit, CreateAdState>(
                                builder: (context, state) {
                                  return CircularPercentIndicator(
                                    circularStrokeCap: CircularStrokeCap.round,
                                    radius: 12.0,
                                    lineWidth: 5.5,
                                    percent:
                                        CreateAdCubit.get(context).adSetPercentage,
                                    linearGradient: Constants.secGradient,

                                    //  progressColor: Constants.mainColor,
                                    backgroundColor: const Color(0xFFFB533E)
                                        .withOpacity(0.1),
                                    reverse: true,
                                  );
                                },
                              );
  }
}

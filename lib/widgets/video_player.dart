

import 'dart:io';

import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';

import '../utils/res/constants.dart';

class CustomVideoPlayer extends StatefulWidget {
  const CustomVideoPlayer.file({super.key, required this.file})
      : videoUrl = null;
  const CustomVideoPlayer.network({super.key, required this.videoUrl})
      : file = null;

  final File? file;
  final String? videoUrl;

  @override
  State<CustomVideoPlayer> createState() => CustomVideoPlayerState();
}

class CustomVideoPlayerState extends State<CustomVideoPlayer> {
  late VideoPlayerController _controller;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    intialize();
  }

  intialize() async {
    _controller = widget.file != null
        ? VideoPlayerController.file(widget.file!)
        : VideoPlayerController.network(widget.videoUrl!);
    await _controller.initialize();
    _chewieController = ChewieController(
      videoPlayerController: _controller,
      looping: false,
      autoPlay: false,
      materialProgressColors: ChewieProgressColors(
        playedColor: Constants.mainColor,
        bufferedColor: Colors.grey.shade400,
        handleColor: Constants.mainColor,
      ),
      // aspectRatio: 16 / 9,
      aspectRatio: 1,
      showOptions: false,
      allowPlaybackSpeedChanging: false,
      showControlsOnInitialize: false,
    );

    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
    _chewieController?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_chewieController == null) return const LoadingWidget(isCircle: true,);
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.sp),
      child: Container(
        height: 258.h,
      
        color: Colors.black,
        child: Chewie(controller: _chewieController!),
      ),
    );
  }
}

import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/res/app_assets.dart';

class FinishedTargetWidget extends StatelessWidget {
  String name;
  String icon;
   FinishedTargetWidget({super.key,required this.name,required this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 46.h,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(37),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x33000000),
            blurRadius: 20,
            offset: Offset(0, 0),
            spreadRadius: -4,
          )
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
                padding: EdgeInsets.zero,
                decoration: const ShapeDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF1C4294), Color(0xFF0B0F26)],
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(100),
                      bottomLeft: Radius.circular(100),
                    ),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 12.0, vertical: 12.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                           CustomSvgWidget(svg: icon),
                          SizedBox(width: 10.w),
                          CustomText(
                            text: name,
                            fontSize: 14.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                            alignment: AlignmentDirectional.centerStart,
                          ),
                        ],
                      ),
                      const CustomSvgWidget(svg: AppAssets.edit)
                    ],
                  ),
                )),
          ),
          Container(
            width: 55.w,
            height: 46.h,
            padding: EdgeInsets.zero,
            decoration: const ShapeDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFFFF006F),
                  Color(0xFFF6BA00),
                ],
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(100),
                  bottomRight: Radius.circular(100),
                ),
              ),
            ),
            child: const Icon(
              Icons.check_circle_outline,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

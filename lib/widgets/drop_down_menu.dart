import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/options.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:control_style/control_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DropDownSelectionMenu extends StatefulWidget {
  //typedef ValueChanged<T> = void Function(T value);
  final ValueChanged<Options?> onChanged;
  final List<Options> items;
  final String? placeHolder;
  final bool reset;
  final dynamic initialValu;
  final String? Function(dynamic)? validator;
  final Widget? prefixIcon;
  final String? label;
  final bool showIsReqired;
  final double borderRadius;
  final double childLeadingSpace;
  final double border;

  const DropDownSelectionMenu({
    Key? key,
    required this.onChanged,
    required this.items,
    this.placeHolder,
    this.initialValu,
    this.validator,
    this.prefixIcon,
    this.reset = false,
    this.label,
    this.borderRadius = 8.0,
    this.showIsReqired = false,
    this.childLeadingSpace = 4.0,
    this.border = 1.0,
  }) : super(key: key);

  @override
  State<DropDownSelectionMenu> createState() => _DropDownSelectionMenuState();
}

class _DropDownSelectionMenuState extends State<DropDownSelectionMenu> {
  Options? _selected;
  @override
  void initState() {
    if (widget.placeHolder == null && widget.initialValu == null) {
      _selected = widget.items.first;
    }

    if (widget.initialValu != null) _selected = widget.initialValu;

    if (widget.reset) _selected = null;

    super.initState();
  }

  @override
  void didUpdateWidget(covariant DropDownSelectionMenu oldWidget) {
    if (widget.initialValu != null) _selected = widget.initialValu;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.label != null)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: widget.label!,
                textMargin: EdgeInsetsDirectional.only(bottom: 14.h, start: 0.w, end: 4.w),
                fontWeight: FontWeight.w600,
                color: Colors.black,
                alignment: AlignmentDirectional.centerStart,
                fontSize: 14.sp,
              ),
              if (widget.showIsReqired) CustomText(text: '*', fontSize: 14.sp, color: Colors.red),
            ],
          ),
        Container(

          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            shadows: const [
              BoxShadow(
                color: Color(0x3F000000),
                blurRadius: 40,
                offset: Offset(0, 0),
                spreadRadius: -10,
              )
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: DropdownButtonFormField<Options>(
              value: _selected,
              iconEnabledColor: Constants.mainColor,
              decoration: InputDecoration(
                prefixIcon: widget.prefixIcon,
                hintText: widget.placeHolder,
                floatingLabelBehavior: FloatingLabelBehavior.never,
                contentPadding: EdgeInsets.symmetric(vertical: 13.h, horizontal: 12.w),
                enabledBorder: DecoratedInputBorder(
                  child: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius).r,
                    borderSide: BorderSide(width: widget.border.w, color: Colors.transparent),
                  ),
                ),
                border: DecoratedInputBorder(
                  child: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius).r,
                    borderSide: BorderSide(width: widget.border.w, color: Colors.transparent),
                  ),
                ),
                focusedBorder: DecoratedInputBorder(
                  child: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius).r,
                    borderSide: BorderSide(width: widget.border.w, color: Colors.transparent),
                  ),
                ),
              ),
              isExpanded: true,
              icon:CustomSvgWidget(svg: AppAssets.arrow,height: 9.h,width: 18.w,),
              borderRadius: BorderRadius.circular(widget.borderRadius).r,
              style: TextStyle(height: 1.3, fontSize: 16.sp, color: Constants.darkColor),
              onChanged: (value) {
                setState(() => _selected = value);
                widget.onChanged(value);
                if (widget.reset) {
                  WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
                    setState(() => _selected = null);
                  });
                }
              },
              items: widget.items
                  .map(
                    (e) => DropdownMenuItem<Options>(

                  value: e,
                  child: Row(
                    children: [
                      if (e.child != null) ...[e.child!, (widget.childLeadingSpace).wSpace],

                      CustomText(
                        text: e.value,
                        color: e.accountId == null ? Colors.grey.shade600 : Constants.darkColor,
                        alignment: AlignmentDirectional.centerStart,
                      ).expanded,
                    ],
                  ),
                ),
              )
                  .toList(),
              validator: widget.validator,
            ),
          ),
        ),
      ],
    );
  }
}
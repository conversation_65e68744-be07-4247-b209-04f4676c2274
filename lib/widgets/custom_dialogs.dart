import 'package:ads_dv/features/auth/presentation/login/controllers/deactivate_account/deactivate_account_cubit.dart';
import 'package:ads_dv/utils/ext.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/custom_button.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomDialogs {
  static Future<bool> exitConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.sp),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    15.verticalSpace,
                    CustomText(
                      text: 'Confirm Exit',
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                    ),
                    CustomText(
                      text: 'Do you want to close the Application?',
                      fontSize: 16.sp,
                      alignment: null,
                      color: Constants.darkerGray,
                    ),
                    30.verticalSpace,
                    Row(
                      children: [
                        CustomButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          text: "No",
                          height: 34.h,
                          radius: 4,
                        ).expanded,
                        CustomButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          text: "Yes",
                          height: 34.h,
                          radius: 4,
                          color: Colors.transparent,
                          textColor: Constants.mainColor,
                          hoveColor: Constants.mainColor,
                        ),
                      ],
                    ),
                    15.verticalSpace,
                  ],
                ),
              ),
            );
          },
        ) ??
        false;
  }

  static Future<bool> deactivateConfirmation(
      BuildContext context, bool isDelete) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.sp),
                child: BlocProvider(
                  create: (context) => DeactivateAccountCubit(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      15.verticalSpace,
                      CustomText(
                        text:
                            isDelete ? "Delete Account" : 'Deactivate Account',
                        fontWeight: FontWeight.w600,
                        fontSize: 18.sp,
                      ),
                      15.verticalSpace,
                      CustomText(
                        text: isDelete
                            ? 'When the account is deleted,all your data will be deleted and you cannot log into it again.'
                            : 'When the account is deactivated, you cannot log into it again.',
                        fontSize: 16.sp,
                        alignment: null,
                        maxLines: 3,
                        color: Constants.darkerGray,
                      ),
                      30.verticalSpace,
                      Row(
                        children: [
                          CustomButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            text: "No",
                            height: 34.h,
                            radius: 4,
                          ).expanded,
                          BlocBuilder<DeactivateAccountCubit,
                              DeactivateAccountState>(
                            builder: (context, state) {
                              if (state is DeactivateAccountLoading) {
                                return const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: LoadingWidget(
                                    isCircle: true,
                                  ),
                                );
                              } else {
                                return CustomButton(
                                  onPressed: () {
                                    DeactivateAccountCubit.get(context)
                                        .deactivateAccount(
                                            context: context,
                                            isDelete: isDelete);
                                  },
                                  text: "Yes",
                                  height: 34.h,
                                  radius: 4,
                                  color: Colors.transparent,
                                  textColor: Constants.mainColor,
                                  hoveColor: Constants.mainColor,
                                );
                              }
                            },
                          ),
                        ],
                      ),
                      15.verticalSpace,
                    ],
                  ),
                ),
              ),
            );
          },
        ) ??
        false;
  }
}

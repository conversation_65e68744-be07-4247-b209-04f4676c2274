import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DVLogo extends StatelessWidget {
  const DVLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Column(
        key: const ValueKey("Logo DV"),
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomSvgWidget(
            svg:
               AppAssets.logoWhite,
            height: 50.h,
            width: 80.w,
            color: Colors.white,
          ),
          SizedBox(height: 8.h),
          CustomText(
            text: 'DV Ads Manger',
            color: Colors.white,
            fontSize: 18.sp,
            alignment: AlignmentDirectional.center,
            textAlign: TextAlign.center,
            fontWeight: FontWeight.bold,
          ),
        ],
      ).animate(
          onComplete: (controller) => controller.forward(from: 0),
          effects: [
            ShimmerEffect(
              duration: 2.seconds,
              curve: Curves.easeOut,
              size: 0.5.w,
              color: const Color(0xffc0c0c0),
            ),
          ]),
    );
  }
}

import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:control_style/control_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/res/constants.dart';
import 'custom_text.dart';


class CustomTextFormField extends StatefulWidget {
  final String hintText;
  final String? initialValue;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final Function(String)? onFieldSubmitted;
  final Function(String)? onChanged;
  final TextInputType keyboardType;
  final TextCapitalization? textCapitalization;
  final TextInputAction? textInputAction;
  final bool? enableSuggestions;
  final bool? autocorrect;
  final bool? obscureText;
  final int? maxLength;
  final int? minLines;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final double? hintFontSize;
  final double? textFontSize;
  final bool isReqired;
  final bool? enabled;
  final bool showIsReqiredFlag;
  final bool? isReadOnly;
  final int? maxLines;
  final double? borderRadius;
  final List<TextInputFormatter>? inputFormatters;
  final String? label;
  final String svgURL;
  final TextEditingController? controller;
  final TextAlign textAlign;
  final VoidCallback? onTap;
  final bool showCounter;

  const CustomTextFormField({
    Key? key,
    this.hintText = '',
    this.initialValue,
    this.onSaved,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.enableSuggestions,
    this.textCapitalization,
    this.autocorrect,
    this.obscureText,
    this.maxLength,
    this.suffixIcon,
    this.prefixIcon,
    this.hintFontSize,
    this.textFontSize,
    this.textInputAction,
    this.onFieldSubmitted,
    this.onChanged,
    this.enabled = true,
    this.isReqired = false,
    this.isReadOnly,
    this.maxLines = 1,
    this.minLines = 1,
    this.inputFormatters,
    this.controller,
    this.label,
    this.onTap,
    this.showIsReqiredFlag = false,
    this.borderRadius,
    this.textAlign = TextAlign.start,
    this.showCounter = true,
  })  : svgURL = '',
        super(key: key);

  const CustomTextFormField.withPrefixSVG({
    Key? key,
    this.hintText = '',
    this.initialValue,
    this.onSaved,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.enableSuggestions,
    this.textCapitalization,
    this.autocorrect,
    this.obscureText,
    this.maxLength,
    this.suffixIcon,
    this.hintFontSize,
    this.textFontSize,
    this.textInputAction,
    this.onFieldSubmitted,
    this.onChanged,
    this.enabled = true,
    this.isReqired = false,
    this.isReadOnly,
    this.maxLines = 1,
    this.controller,
    this.inputFormatters,
    this.label,
    this.onTap,
    this.showIsReqiredFlag = false,
    required this.svgURL,
    this.borderRadius,
    this.textAlign = TextAlign.start,
    this.showCounter = true,
  })  : prefixIcon = null,
        minLines = 1,
        super(key: key);

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  var svgColor = Constants.darkGray;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.label != null)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: widget.label!,
                textMargin: EdgeInsetsDirectional.only(bottom: 14.h, start: 0.w, end: 4.w),
                fontSize: 14.sp,
                color: Constants.darkGray,
              ),
              if (widget.showIsReqiredFlag)
                CustomText(text: '*', fontSize: 14.sp, color: Colors.red),
            ],
          ),
        Container(
       //   height: 50,
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32.92),
            ),

          ),
          child: Focus(
            onFocusChange: (value) => setState(
                  () => svgColor = value ? Constants.mainColor : Constants.darkGray,
            ),
            child: TextFormField(

              onTapOutside: (event) => FocusScope.of(context).unfocus(),
              controller: widget.controller,
              key: widget.key,
              readOnly: widget.isReadOnly ?? false,
              enabled: widget.enabled,
              initialValue: widget.initialValue,
              autocorrect: widget.autocorrect ?? true,
              textCapitalization: widget.textCapitalization ?? TextCapitalization.none,
              enableSuggestions: widget.enableSuggestions ?? true,
              keyboardType: widget.keyboardType,
              obscureText: widget.obscureText ?? false,
              cursorColor: Constants.mainColor,
              textInputAction: widget.textInputAction,
              maxLength: widget.maxLength,
              maxLines: widget.maxLines,
              minLines: widget.minLines,
              inputFormatters: widget.inputFormatters,
              textAlign: widget.textAlign,
              decoration: InputDecoration(
                counterText: (widget.maxLength != null && !widget.showCounter) ? '' : null,
                enabledBorder: DecoratedInputBorder(
                  child: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius ?? 40.r),
                    borderSide: BorderSide(width: 1.w, color: Constants.gray),
                  ),
                ),
                border: DecoratedInputBorder(
                  child: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius ?? 40.r),
                    borderSide: BorderSide(width: 1.w, color: Constants.gray),
                  ),
                ),
                focusedBorder: DecoratedInputBorder(
                  child: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius ?? 40.r),
                    borderSide: BorderSide(width: 1.w, color:Constants.gray),
                  ),
                  // shadow: Constants.blackAndWhiteShadow,
                ),
                suffixIcon: widget.suffixIcon,
                prefixIcon: widget.svgURL.isNotEmpty
                    ? buildPrefixSVG(widget.svgURL, svgColor)
                    : widget.prefixIcon,
                hintStyle: TextStyle(
                  height: 1.2,
                  fontSize:  14.sp,
                  color: Constants.textColor,
                ),
                hintText: widget.hintText,

                fillColor: Colors.white,
                filled: true,
              ),
              style: TextStyle(
                height: 1.2,
                fontSize: widget.textFontSize ?? 14.sp,
                color: widget.enabled! ? Colors.black : Colors.grey,
              ),
              validator: widget.validator,
              onSaved: widget.onSaved,
              onFieldSubmitted: widget.onFieldSubmitted,
              onChanged: widget.onChanged,
              onTap: widget.onTap,
            ),
          ),
        )
      ],
    );
  }

  Padding buildPrefixSVG(String url, color) {
    return Padding(
      padding: EdgeInsetsDirectional.only(end: 20.w, start: 20.w),
      child: CustomSvgWidget(

        svg: url,
        height: 20.h,
        width: 20.h,
        color: color,
      ),
    );
  }
}

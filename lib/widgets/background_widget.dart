import 'package:flutter/material.dart';

import '../utils/res/colors.dart';
import '../utils/res/media_query_config.dart';


class BackgroundWidget extends StatelessWidget {
  final double height;

  const BackgroundWidget({super.key, required this.height});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        height: SizeConfig.hieghtr(height, context),
        decoration:  const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.00, -0.03),
            end: Alignment(1, 0.03),
            colors: [AppColors.mainColor, AppColors.secondColor],
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowColor,
              blurRadius: 15,
              offset: Offset(0, 4),
              spreadRadius: 1,
            )
          ],
        ),
      ),
    );
  }
}

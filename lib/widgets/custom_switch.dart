

import 'package:ads_dv/utils/res/colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/res/constants.dart';

class CustomSwitch extends StatelessWidget {
  const CustomSwitch({
    super.key,
    required this.value,
    required this.onChanged,
  });

  final bool value;
  final void Function(bool newValue) onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Constants.primaryTextColor,width: 1)
      ),
      child: Sized<PERSON><PERSON>(
        height: 24.h,
        width: 40.w,
        child: FittedBox(
          child: CupertinoSwitch(
            thumbColor:value ? AppColors.white:  Constants.primaryTextColor,
            activeColor: value ?Constants.primaryTextColor :AppColors.white,
            trackColor: value ? Constants.primaryTextColor:   AppColors.white ,
            value: value,
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}

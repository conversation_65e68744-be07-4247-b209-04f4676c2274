import 'package:ads_dv/utils/res/constants.dart';
import 'package:flutter/material.dart';

import '../utils/res/colors.dart';
import '../utils/res/media_query_config.dart';
import 'custom_text.dart';

class ButtonWidget extends StatelessWidget {
  final Function()? onTap;
  final String text;
  IconData? iconData;
  final bool isEnabled;
  final Color mainColor;
  final Color secondColor;
  final double width;
  final double highet;
  final Decoration? decoration;
  final textColor;
  final double padding;
  final double fontSize;

  ButtonWidget({
    this.iconData,
    this.onTap,
    required this.text,
    this.isEnabled = true,
    this.width = 296,
    this.highet = 44,
    this.textColor = Colors.white,
    this.decoration,
    this.padding = 14.0,
    this.fontSize = 14.0,
    this.mainColor = AppColors.mainColor,
    this.secondColor = AppColors.secondColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return isEnabled
        ? InkWell(
            onTap: onTap,
            child: Container(
              width: SizeConfig.widthr(width, context),
              height: SizeConfig.hieghtr(highet, context),
              decoration: decoration ??
                  ShapeDecoration(
                    gradient: Constants.defGradient,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(52)),
                  ),
              child: Padding(
                padding: EdgeInsets.all(padding),
                child: iconData != null
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            iconData,
                            color: Colors.white,
                          ),
                          FittedBox(
                              child: Center(
                                  child: CustomText(
                            text: text,
                            color: textColor,
                            fontSize: fontSize,
                            alignment: AlignmentDirectional.center,
                          ))),
                        ],
                      )
                    : FittedBox(
                        child: Center(
                            child: CustomText(
                        text: text,
                        color: textColor,
                        fontSize: fontSize,
                      ))),
              ),
            ),
          )
        : Container(
            width: SizeConfig.widthr(width, context),
            height: SizeConfig.hieghtr(highet, context),
            decoration: ShapeDecoration(
              color: AppColors.greyButton,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(52)),
            ),
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: FittedBox(
                  child: Center(
                      child: CustomText(
                text: text,
                color: AppColors.greyText,
                fontSize: fontSize,
              ))),
            ),
          );
  }
}

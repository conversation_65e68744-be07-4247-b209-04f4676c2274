import 'package:flutter/material.dart';

import '../utils/res/constants.dart';
import 'custom_text.dart';

class SelectedOptionWidget extends StatelessWidget {
  String name;
  SelectedOptionWidget({super.key, required this.name});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0, left: 8),
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.zero,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x33000000),
              blurRadius: 20,
              offset: Offset(0, 0),
              spreadRadius: -6,
            )
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Center(
                  child: ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return Constants.secGradient.createShader(bounds);
                    },
                    child: CustomText(
                      text: name,
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
              ShaderMask(
                shaderCallback: (Rect bounds) {
                  return Constants.secGradient.createShader(bounds);
                },
                child: const Icon(
                  Icons.check_circle,
                  size: 22,
                  color: Colors.white,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

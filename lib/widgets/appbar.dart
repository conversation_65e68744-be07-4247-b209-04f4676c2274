import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/widgets/cached__image.dart';
import 'package:ads_dv/widgets/custom_text.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../features/sidebar/wallet/presentation/views/send_tickets_widget.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? child;
  final List<Widget>? actions;
  final bool showBackButton;
  final bool hasDrawer;
  final Color? bgColor;
  final Widget? leading;
  final String? logo;
  final double? height;

  const CustomAppBar({
    Key? key,
    this.title,
    this.actions,
    this.child,
    this.logo,
    this.showBackButton = false,
    this.height,
    this.hasDrawer = false,
    this.bgColor,
    this.leading = const SizedBox.shrink(),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          width: double.infinity,
          height: height?.h ?? 154.h,
          decoration: const ShapeDecoration(
            gradient: LinearGradient(
              begin: Alignment(-0.88, -0.48),
              end: Alignment(0.88, 0.48),
              colors: [Color(0xFF296AEB), Color(0xFF0B0F26)],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(25),
                bottomRight: Radius.circular(25),
              ),
            ),
          ),
          child: AppBar(
            centerTitle: true,
            leading: hasDrawer ? _buildDrawer(context) : leading,
            title: logo != null
                ? CachedImageWidget(
                    assetsImage: AppAssets.mainLogo,
                    height: 60.h,
                  )
                : Padding(
                    padding: EdgeInsets.only(top: 8.sp),
                    child: CustomText(
                      text: title!,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w700,
                      alignment: AlignmentDirectional.center,
                      textAlign: TextAlign.center,
                      color: Colors.white,
                      // overflow: TextOverflow.ellipsis,
                      maxLines: 3,
                    ),
                  ),
            actions: actions ??
                [
                  const ComplaintsWidget(),
                ],
            toolbarHeight: 100.h,
            //  leadingWidth: hasDrawer ? 56.w : 0,
            elevation: 0,
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return SizedBox(
      height: 50.h,
      width: 50.w,
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(50.r),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: CustomSvgWidget(
            svg: AppAssets.back,
            width: 50.w,
            height: 50.h,
            //    color: Colors.white,
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size(0, 154.h);
}

import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/utils/res/colors.dart';
import 'package:ads_dv/utils/res/custom_widgets.dart';
import 'package:flutter/material.dart';

class CustomRadioButton extends StatelessWidget {
  CreateAdCubit createAdCubit;
  String title;
  int value;
  int? selectedValue;

  CustomRadioButton(
      {super.key,
      required this.value,
      this.selectedValue,
      required this.title,
      required this.createAdCubit});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Radio<int>(
          value: value,
          groupValue: createAdCubit.selectedPost,
          onChanged: (int? newValue) {
            if (CreateAdCubit.get(context).optimization?.actualName ==
                    "POST_ENGAGEMENT" &&
                title == "New Post") {
              showErrorToast('you can`t make new post');
              return;
            }
            createAdCubit.changePostValue(newValue);
            print("asdfaf${createAdCubit.selectedPost}");
          },
          activeColor: AppColors.mainColor,
        ),
        Text(
          title,
          style: const TextStyle(color: AppColors.mainColor),
        ),
      ],
    );
  }
}

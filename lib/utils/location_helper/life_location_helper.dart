// import 'dart:async';
//
// // import 'package:cloud_firestore/cloud_firestore.dart';
// // import 'package:flutter/material.dart';
// // import 'package:geocoding/geocoding.dart';
// // import 'package:geolocator/geolocator.dart';
// import 'package:location/location.dart';
//
// // life location
//
// class LocationTrackignHelper{
//
//   static StreamSubscription<LocationData>? streamOrderDriver;
//   static StreamSubscription<LocationData>? streamDriver;
//
//   static  updateBackGroundLocations({required Function(LocationData locationData) onLocationChange,bool isBackground = false}) async {
//
//     Location _location = Location();
//
//     bool serviceEnabled = await _location.serviceEnabled();
//     if (!serviceEnabled) {
//       serviceEnabled = await _location.requestService();
//       if (!serviceEnabled) {
//         print('Location services are disabled.');
//         return;
//       }
//     }
//
//     PermissionStatus permissionStatus = await _location.hasPermission();
//     if (permissionStatus == PermissionStatus.denied) {
//       permissionStatus = await _location.requestPermission();
//       if (permissionStatus != PermissionStatus.granted) {
//         print('Location permissions are denied.');
//         return;
//       }
//     }
//
//     if(isBackground) {
//       _location.enableBackgroundMode(enable: true);
//     }
//
//     streamDriver = _location.onLocationChanged.listen(onLocationChange
//             // (LocationData locationData) async{
//
//       // print('Background Location Update:');
//       // print('Latitude: ${locationData.latitude}');
//       // print('Longitude: ${locationData.longitude}');
//       //
//       // Log.e('msg');
//       // final currentPosition = GeoFlutterFire().point(
//       //   latitude: locationData.latitude??0,
//       //   longitude: locationData.longitude??0,
//       // );
//       //
//       // await Firebase.initializeApp(
//       //   options: DefaultFirebaseOptions.currentPlatform,
//       // );
//       //
//       // print('update location here ');
//       // print(currentPosition.data);
//       // // Store the location data in Firestore
//       // await FirebaseFirestore.instance.collection('locations').doc(loadNumber).update({"driverPosition": currentPosition.data,"time":FieldValue.serverTimestamp()});
//     // }
//     );
//
//   }
//
//
//
//
//   static  updateOrderBackGroundLocations({required Function(LocationData locationData) onLocationChange,bool isBackground = false}) async {
//
//     Location _location = Location();
//
//     bool serviceEnabled = await _location.serviceEnabled();
//     if (!serviceEnabled) {
//       serviceEnabled = await _location.requestService();
//       if (!serviceEnabled) {
//         print('Location services are disabled.');
//         return;
//       }
//     }
//
//     PermissionStatus permissionStatus = await _location.hasPermission();
//     if (permissionStatus == PermissionStatus.denied) {
//       permissionStatus = await _location.requestPermission();
//       if (permissionStatus != PermissionStatus.granted) {
//         print('Location permissions are denied.');
//         return;
//       }
//     }
//
//     if(isBackground) {
//       _location.enableBackgroundMode(enable: true);
//     }
//
//     streamOrderDriver = _location.onLocationChanged.listen(onLocationChange
//       // (LocationData locationData) async{
//
//       // print('Background Location Update:');
//       // print('Latitude: ${locationData.latitude}');
//       // print('Longitude: ${locationData.longitude}');
//       //
//       // Log.e('msg');
//       // final currentPosition = GeoFlutterFire().point(
//       //   latitude: locationData.latitude??0,
//       //   longitude: locationData.longitude??0,
//       // );
//       //
//       // await Firebase.initializeApp(
//       //   options: DefaultFirebaseOptions.currentPlatform,
//       // );
//       //
//       // print('update location here ');
//       // print(currentPosition.data);
//       // // Store the location data in Firestore
//       // await FirebaseFirestore.instance.collection('locations').doc(loadNumber).update({"driverPosition": currentPosition.data,"time":FieldValue.serverTimestamp()});
//       // }
//     );
//
//   }
//
//   static disposeOrderBackGroundLocations(){
//     streamOrderDriver?.pause();
//     streamOrderDriver?.cancel();
//   }
//
//   static disposeBackGroundLocations(){
//     streamDriver?.pause();
//     streamDriver?.cancel();
//   }
//
// }
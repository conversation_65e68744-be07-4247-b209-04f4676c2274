import 'dart:async';

import 'package:ads_dv/widgets/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:location/location.dart' as location;
import '../../../../../utils/di/injection.dart';
import '../hive_helper/hive_helper.dart';
import '../res/custom_widgets.dart';

class LocationHelper {
  static const int _maxPermissionRequests = 2;
  static int _permissionRequestCount = 0;

  /// Handles location permission requests with safety limits
  static Future<bool> _handleLocationPermission(BuildContext context) async {
    try {
      // Check location service status
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        final location.Location locationInstance = location.Location();
        serviceEnabled = await locationInstance.requestService();
        if (!serviceEnabled) return false;
      }

      // Check and request permissions
      LocationPermission permission = await Geolocator.checkPermission();

      while (permission == LocationPermission.denied &&
          _permissionRequestCount < _maxPermissionRequests) {
        _permissionRequestCount++;
        permission = await Geolocator.requestPermission();
        await Future.delayed(const Duration(milliseconds: 500));
      }

      if (permission == LocationPermission.deniedForever) {
        // showErrorToast(
        //     "Location permissions are permanently denied. Please enable them in settings.");
        Future.delayed(const Duration(milliseconds: 1000), () async {
          showDialog(
              context: context,
              builder: (BuildContext context) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3.0),
                  child: Center(
                    child: SingleChildScrollView(
                      child: Card(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0)),
                        child: Padding(
                          padding: const EdgeInsets.all(30.0),
                          child: Column(
                            children: [
                              const Text(
                                "Location permissions are permanently denied. Please enable them in settings.",
                                style: TextStyle(fontSize: 20.0),
                              ),
                              20.verticalSpace,
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Flexible(
                                    child: ButtonWidget(
                                      // width: MediaQuery.of(context).size.width *
                                      //     0.15,
                                      text: 'back',
                                      onTap: () async {
                                        Navigator.pop(context);
                                      },
                                      // fontSize: 0.2,
                                    ),
                                  ),
                                  10.horizontalSpace,
                                  Flexible(
                                    child: ButtonWidget(
                                      // width: MediaQuery.of(context).size.width *
                                      //     0.15,
                                      text: 'Go to settings',
                                      onTap: () async {
                                        await Geolocator.openAppSettings();
                                      },
                                      // fontSize: 0.2,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              });
        });
        return false;
      }

      return permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
    } catch (e) {
      _showError(context, "Error checking location permissions: $e");
      return false;
    }
  }

  // final String? city;
  // final String? countryCode


  /// Fetches current location address with proper error handling
  static Future<void> getAddressFromCurrentLocation(
      BuildContext context) async {
    try {
      final hasPermission = await _handleLocationPermission(context);
      if (!hasPermission) return;

      final Position position = await Geolocator.getCurrentPosition(
        // desiredAccuracy: LocationAccuracy.best,
        // timeLimit: const Duration(seconds: 15),
      );

      print('homeLayoutewretgf $position');

      final List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      final String? city = _extractCityFromPlacemarks(placemarks);
      if (city != null) {
        instance.get<HiveHelper>().setMyCity(city: city);
        // city;
      }
      final String? countryCode = _extractCountryCodeFromPlacemarks(placemarks);
      if (countryCode != null) {
        print('countryCodeasdewrwerasd $countryCode');
        instance
            .get<HiveHelper>()
            .setMyCountryCode(countryCode: countryCode.toLowerCase());
        // countryCode;
      }
      // _showError(context, "Could not determine city from location");
    } on LocationServiceDisabledException {
      _showError(context, "Location services are disabled");
      return;
    } on TimeoutException {
      _showError(context, "Location request timed out");
      return;
    } catch (e) {
      _showError(context, "Failed to get location: ${e.toString()}");
      return;
    }
  }

  /// Extracts city name from placemarks with fallbacks
  static String? _extractCityFromPlacemarks(List<Placemark> placemarks) {
    for (final placemark in placemarks) {
      final String? city = placemark.locality ??
          placemark.subAdministrativeArea ??
          placemark.administrativeArea;
      if (city != null && city.isNotEmpty) return city;
    }
    return null;
  }

  static String? _extractCountryCodeFromPlacemarks(List<Placemark> placemarks) {
    for (final placemark in placemarks) {
      print('olacemarkCCode ${placemark.isoCountryCode}');
      final String? countryCode = placemark.isoCountryCode;
      if (countryCode != null && countryCode.isNotEmpty) return countryCode;
    }
    return null;
  }

  static void _showError(BuildContext context, String message) {
    if (context.mounted) {
      showErrorToast(message);
      print("Location Error: $message");
    }
  }

  static void _showPersistentError(BuildContext context, String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 5),
      ));
    }
  }
}

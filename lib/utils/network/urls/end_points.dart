class EndPoints {
  static const getAllCampaigns = 'get_campaigns';
  static const getCampaignsStatus = 'get-reach-estimate';
  static const getChannelName = 'chat/create';
  static const sendMessage = 'message/send';
  static const getBotMessages = 'get/chatboot';
  static const getAllTiktokCampaigns = 'tiktok/campaign/get';

  static const getCampaignsInsights = 'get_campaign_insights';
  static const changeStatus = 'change_ad_status';

  static const getAdSets = 'get_adsets';
  static const getAdSetsPagination = 'get_adsets_pagination';

  static const getObjectives = 'objective';
  static const getCurrentObjective = 'get/objective';

  static const getTiktokObjectives = 'tiktok/objective';
  static const getTiktokOptimizations = 'tiktok/optimizations';

  static const getTiktokIdentities = 'tiktok/identity';

  static const getTiktokRealIdentities = 'tiktok/identity/users';

  static const createTiktokIdentity = 'tiktok/identity/create';

  static getOptimizations(int objectiveId) => 'opt/$objectiveId';

  // static getTiktokOptimizations(int objectiveId) =>
  //     'tiktok/optimizations/$objectiveId';

  static getBillingEvent(int optimizationId) => 'billing/$optimizationId';

  static const searchWithTextUrl = 'keyword_search';

  static const searchInTiktokInterests = 'tiktok/get/Interests';
  static const searchInTiktokLocations = 'tiktok/location/get';
  static const searchInTiktokLanguages = 'tiktok/languages/get';

  static const searchWithClassUrl = 'class_search';
  static const login = 'login';
  static const checkEmailValidation = 'check_data';
  static String logoutUrl = 'logout';
  static const updateToken = 'update_user_details';
  static const storeFcmToken = 'store_fcm_token';
  static const facebookDisconnect = 'disconnect';

  static const register = 'sign_up';
  static const socialRegister = 'social-register';
  static const resetPassword = 'password/reset';
  static const fullCycle = 'fullcycle';
  static const tikTokFullCycle = 'tiktok/fullcical';
  static const tikTokDisconnect = 'tiktok/disconnect';

  static const forgetPassword = 'password/forgot-password';

  static const validateOtp = 'verifyOtp';
  static const getAdAccounts = 'ad_account';
  static const getTikTokAdAccounts = 'tiktok/ad/account';
  static const storeTikTokAdAccount = 'tiktok/store_defult_account';
  static const getDefaultAccounts = 'accounts';
  static const getDefaultTiktokAccounts = 'tiktok/accounts';
  static const checkDefaultAccounts = 'check_default_accounts';
  static const checkAccessToken = 'check_access_token';

  static const getSnapChatAdAccounts = 'snapchat/getAdAccounts';
  static const storeSnapChatAdAccount = 'snapchat/store-defult-account';
  static const getSnapChatDefaultAccounts = 'snapchat/accounts';
  static const getSnapChatObjectives = 'snapchat/getObjective';
  static const getSnapChatOptimizations = 'snapchat/getoptimizations';
  static const getAllSnapChatCampaigns = 'snapchat/getCampaigns';
  static const getAllSnapChatInterests = 'snapchat/get-interests';
  static const getAllSnapChatProfilesId = 'snapchat/get-Profiles';
  static const snapChatFullCycle = 'snapchat/fullcycle';
  static const snapChatDisconnect = 'snapchat/disconnect';
  static const snapChatGetPhoneNumbers = 'snapchat/phone-numbers';

  static const getAllNotification = 'notifications/all';
  static const markAllAsRead = 'notifications/read';
  static const getNotificationsStatus = 'getStatus';
  static const changeNotificationsStatus = 'changeStatus';

  static const getFbPages = 'get_accounts';
  static const addAccountsPages = 'ad_account_pages';
  static const pagination = 'account_pagination';

  static const getPosts = 'get_all_posts';
  static const getInstaPosts = 'get_all_instagram_posts';
  static const postsPagination = 'get_all_posts_pagination';
  static const instaPostsPagination = 'get-Intsgram-posts-pagination';
  static const campaignsPagination = 'get_campaigns_pagination';
  static const getLeadsForms = 'get-instant-forms';
  static const leadsFormsPagination = 'get_all_posts_pagination';
  static const updateAccount = 'defaultAccount/1';
  static const storeAccount = 'defaultAccount';

  static const wallet = 'wallet';
  static const renewPackage = 'renewPackage';
  static const sendTicket = 'tickets';

  static const complaint = 'complaint';

  static const getPackages = 'packages';
  static const subscribePackage = 'payment';

  static const addAccess = 'access';

  static deleteAccess(int accessId) => 'access/$accessId';

  static const hasCampaign = 'has_campaign';
  static const deactivateAccount = 'deactivate';
  static const deleteAccount = 'delete';
}

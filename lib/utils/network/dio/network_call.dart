import 'dart:convert';
import 'dart:io';

import 'package:ads_dv/utils/di/injection.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../features/auth/presentation/login/controllers/login/login_cubit.dart';
import '../../hive_helper/hive_helper.dart';
import '../../res/constants.dart';
import '../../res/router/routes.dart';
import '../errors/exceptions.dart';
import 'error_status.dart';
import '../log/log.dart';
import 'dio.dart';
import 'enum.dart';

class NetworkCall {
  DioHelper dioHelper;

  NetworkCall({required this.dioHelper});

  Future<Response> _request<T>(String url,
      {dynamic data,
      Map<String, dynamic>? queryParameters,
      CancelToken? cancelToken,
      required Options options,
      Map<String, dynamic>? headers,
      void Function(int, int)? onSendProgress}) async {
    // if (headers != null) {

    options.headers?.addAll(dioHelper.dio.options.headers);
    // dioHelper.dio.options.headers = headers;
    // }
    return await dioHelper.dio.request(url,
        onSendProgress: onSendProgress,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken);
  }

  Future<Map<String, dynamic>> request(
    String url, {
    dynamic params,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    required Options options,
  }) async {
    print('inLocalStorage ${instance.get<HiveHelper>().getUser()?.toJson()}');
    return await _handleException(_request(
      url,
      data: params,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    ));
  }

  Stream<Map<String, dynamic>> requestStream<Response>(
    Method method,
    String url, {
    Function(int code, String msg)? onError,
    Map<String, dynamic>? params,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    required Options options,
    Map<String, dynamic>? headers,
  }) {
    return Stream.fromFuture(_handleException(_request(url,
        data: params,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        headers: headers)));
  }

  Future<Map<String, dynamic>> _handleException(
      Future<Response<dynamic>> function) async {
    try {
      final response = await function;
      late final Map<String, dynamic> result;
      if (response.data is String) {
        result = jsonDecode(response.data);
      } else {
        result = response.data;
      }
      // Log.i("sssssss ${result}");
      Log.i(result.toString());
      if (response.statusCode == ErrorStatus.unAuthorized) {
        print('here1');
        await instance<HiveHelper>().setToken("");
        await Navigator.of(Constants.navigatorKey.currentContext!)
            .pushNamedAndRemoveUntil(
          Routes.login,
          (Route<dynamic> route) => false,
        );
        throw UnAuthorizedException();
      } else if (response.statusCode == ErrorStatus.forbidden) {
        throw UnVerifiedException(message: result["message"]);
      }
      // if (!result.containsKey('status')) {
      //   return result;
      // }

      if (result['status'] == ErrorStatus.success ||
          result['status'] == ErrorStatus.success1 ||
          result['status'] == 'true' ||
          result['status'] == true ||
          result['success'] == true ||
          result['success'] == 'true') {
        return result;
      } else if (result['error'] == "Unauthenticated.") {
        throw UnAuthorizedException();
      }

      if (result['message'] == "This token is not valid" ||
          result['message'] == "Unauthorised") {
        print('here2');
        await instance.get<LoginCubit>().logout();
        throw UnAuthorizedException();
      } else if (result['status'] == ErrorStatus.forbidden) {
        throw UnVerifiedException(message: result['message']);
      } else {
        late final String errorMessage;
        if (result['message'] is List) {
          errorMessage = result['message'].first;
        } else {
          errorMessage = result['message'];
        }
        throw ServerException(message: errorMessage);
      }
    } on DioException catch (dioError) {
      if (dioError.error is SocketException) {
        throw const SocketException('no connect');
      }

      final responseBody = dioError.response!.data;
      final response = dioError.response!;
      Log.e(response.requestOptions.uri.toString());
      Log.i(response.toString());
      Log.i(StackTrace.current.toString());

      if (response.statusCode == ErrorStatus.unAuthorized) {
        print('here1');
        await instance.get<LoginCubit>().logout();
        throw UnAuthorizedException();
      } else if (response.statusCode == ErrorStatus.forbidden) {
        throw UnVerifiedException(message: responseBody['message']);
      } else {
        late final String errorMessage;
        if (responseBody['message'] is List) {
          errorMessage = responseBody['message'].first;
        } else {
          errorMessage = responseBody['message'];
        }

        throw ServerException(message: errorMessage);
      }
    } catch (error) {
      rethrow;
    }
  }
}

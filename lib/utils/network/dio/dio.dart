import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';

import '../../di/injection.dart';
import '../../hive_helper/hive_helper.dart';
import '../dio_inter/interceptors.dart';
import '../urls/services_urls.dart';

class DioHelper {
  late Dio _dio;

  DioHelper() {
    createDio();
    _listenToLangChanges();
  }

  Dio get dio => _dio;

  // Listen to language changes and update the Dio headers
  void _listenToLangChanges() {
    // Listen to the language changes in HiveHelper's ValueNotifier
    instance<HiveHelper>().languageNotifier.addListener(() {
      // Refresh Dio when the language changes
      _refreshDioHeaders();
    });
  }

  // Refresh Dio headers based on the current language
  void _refreshDioHeaders() {
    _dio.options.headers['lang'] = instance<HiveHelper>().getLang();
    print("Headers updated with new lang: ${_dio.options.headers['lang']}");
  }

  void createDio() {
    _dio = Dio(createNewBaseOptions());
    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(LoggingInterceptor());
    (_dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
        (HttpClient client) {
      client.badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
      return client;
    };
    print('Initial lang: ${instance<HiveHelper>().getLang()}');
  }

  static BaseOptions createNewBaseOptions(
      {baseUrl = ServicesURLs.developmentEnvironment,
      }) {
    return BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 300),
      receiveTimeout: const Duration(seconds: 13000),
      responseType: ResponseType.plain,
      headers: {
        "Accept": "application/json",
        'Content-Type': 'application/json',
        'lang': instance.get<HiveHelper>().getLang()
      },
      validateStatus: (status) {
        return true;
      },
    );
  }
}

import 'package:ads_dv/features/auth/presentation/login/controllers/login/login_cubit.dart';
import 'package:ads_dv/features/chat/repos/chat_repository.dart';
import 'package:ads_dv/features/create_campaigns/data/data_sources/create_campagin_data_source.dart';
import 'package:ads_dv/features/create_campaigns/data/repos/create_campaign_repo.dart';
import 'package:ads_dv/features/home_layout/data/data_source/home_layout_data_source.dart';
import 'package:ads_dv/features/home_layout/data/repos/home_layout_repo.dart';
import 'package:ads_dv/features/notifications/data/data_sources/notification_data_source.dart';
import 'package:ads_dv/features/notifications/data/repos/notification_repo.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/data_source/snapChat_data_source.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/data_source/tikTok_data_source.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/repos/snapChat_repo.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/repos/tikTok_repo.dart';
import 'package:ads_dv/features/sidebar/management_area/data/data_source/management_area_data_source.dart';
import 'package:ads_dv/features/sidebar/management_area/data/repos/management_area_repo.dart';
import 'package:ads_dv/features/sidebar/payment/data/data_sources/payment_data_source.dart';
import 'package:ads_dv/features/sidebar/payment/data/repos/payment_repo.dart';
import 'package:ads_dv/features/sidebar/profile/data/data_sources/edit_profile_data_source.dart';
import 'package:ads_dv/features/sidebar/profile/data/repos/edit_profile_repo.dart';
import 'package:ads_dv/features/sidebar/reports/data/data_sources/reports_data_source.dart';
import 'package:ads_dv/features/sidebar/reports/data/repos/reports_repo.dart';
import 'package:ads_dv/features/sidebar/settings/data/data_source/complains_data_source.dart';
import 'package:ads_dv/features/sidebar/settings/data/repos/complains_repo.dart';
import 'package:ads_dv/features/sidebar/wallet/data/data_sources/wallet_data_source.dart';
import 'package:ads_dv/features/sidebar/wallet/data/repos/wallet_repo.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

import '../../features/auth/data/data_source/auth_data_source.dart';
import '../../features/auth/data/repos/auth_repo.dart';
import '../../features/auth/presentation/login/controllers/tiktok_login/tiktok_login_cubit.dart';
import '../../features/chat/data/chat_data_source.dart';
import '../../features/chat/presentation/controllers/chat_cubit.dart';
import '../../features/maps/presentation/controllers/select_map_cubit.dart';
import '../../features/sidebar/ad_accounts/data/data_source/meta_data_source.dart';
import '../../features/sidebar/ad_accounts/data/repos/meta_repo.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/get_add_accounts/get_add_accounts_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/get_snapChat_add_accounts/get_snap_chat_add_accounts_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/store_snapChat_ad_accounts/store_snap_chat_ad_account_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/store_tiktok_ad_account/store_tiktok_ad_account_cubit.dart';
import '../../features/sidebar/wallet/data/data_sources/tickets_data_source.dart';
import '../../features/sidebar/wallet/data/repos/ticket_repo.dart';
import '../../features/sidebar/wallet/presentation/controllers/ticket/ticket_cubit.dart';
import '../../features/snapChat_campaign/data/data_source/craete_snapChat_campaign_data_source.dart';
import '../../features/snapChat_campaign/data/repos/snapChat_create_campaign_repo.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapChat_objectives/get_snap_chat_objectives_cubit.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapChat_optimizations/get_snap_chat_optimizations_cubit.dart';
import '../../features/tiktok_campigns/data/data_source/create_tiktok_campaign_data_source.dart';
import '../../features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import '../../features/tiktok_campigns/repos/create_tiktok_campaign_repo.dart';
import '../hive_helper/hive_helper.dart';
import '../network/connection/network_info.dart';
import '../network/dio/dio.dart';
import '../network/dio/network_call.dart';

final instance = GetIt.instance;

Future<void> initAppInjection() async {
  instance.registerLazySingleton<HiveHelper>(() => HiveHelper());

  instance.registerLazySingleton<LoginCubit>(() => LoginCubit());
  instance.registerLazySingleton<ChatCubit>(() => ChatCubit());
  instance.registerLazySingleton<SelectMapCubit>(() => SelectMapCubit());
  instance.registerLazySingleton<GetSnapChatOptimizationsCubit>(
      () => GetSnapChatOptimizationsCubit());
  instance.registerLazySingleton<GetSnapChatObjectivesCubit>(
      () => GetSnapChatObjectivesCubit());
  instance.registerLazySingleton<StoreSnapChatAdAccountCubit>(
      () => StoreSnapChatAdAccountCubit());
  instance.registerLazySingleton<StoreTiktokAdAccountCubit>(
      () => StoreTiktokAdAccountCubit());
  instance.registerLazySingleton<TiktokLoginCubit>(() => TiktokLoginCubit());
  instance.registerLazySingleton<GetSnapChatAddAccountsCubit>(
      () => GetSnapChatAddAccountsCubit());
  instance
      .registerLazySingleton<TiktokCampaignCubit>(() => TiktokCampaignCubit());
  instance
      .registerLazySingleton<TiktokAdGroupCubit>(() => TiktokAdGroupCubit());
  instance
      .registerLazySingleton<GetAdAccountsCubit>(() => GetAdAccountsCubit());

  instance.registerLazySingleton<TicketCubit>(() => TicketCubit());

  instance.registerLazySingleton<DioHelper>(() => DioHelper());
  instance.registerLazySingleton<NetworkCall>(
      () => NetworkCall(dioHelper: instance<DioHelper>()));

  instance.registerLazySingleton<InternetConnectionChecker>(
      () => InternetConnectionChecker());
  instance.registerLazySingleton<NetworkInfoImp>(() =>
      NetworkInfoImp(connectionChecker: instance<InternetConnectionChecker>()));

  instance.registerLazySingleton<CreateCampaignDataSource>(
      () => CreateCampaignDataSource());
  instance.registerLazySingleton<CreateSnapChatCampaignDataSource>(
      () => CreateSnapChatCampaignDataSource());
  instance.registerLazySingleton<CreateSnapChatCampaignRepo>(() =>
      CreateSnapChatCampaignRepo(
          createSnapChatCampaignDataSource:
              instance<CreateSnapChatCampaignDataSource>(),
          networkInfo: instance<NetworkInfoImp>()));
  instance.registerLazySingleton<CreateCampaignRepo>(() => CreateCampaignRepo(
      createCampaignDataSource: instance<CreateCampaignDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<SnapChatRepo>(() => SnapChatRepo(
      snapChatDataSource: instance<SnapChatDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<CreateTikTokCampaignDataSource>(
      () => CreateTikTokCampaignDataSource());
  instance
      .registerLazySingleton<SnapChatDataSource>(() => SnapChatDataSource());
  instance.registerLazySingleton<CreateTikTokCampaignRepo>(() =>
      CreateTikTokCampaignRepo(
          createTikTokCampaignDataSource:
              instance<CreateTikTokCampaignDataSource>(),
          networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<ChatRepo>(() => ChatRepo(
      chatDataSource: instance<ChatDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<TikTokRepo>(() => TikTokRepo(
      tikTokDataSource: instance<TikTokDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<TicketRepo>(() => TicketRepo(
      ticketDataSource: instance<TicketDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<ChatDataSource>(() => ChatDataSource());
  instance.registerLazySingleton<TikTokDataSource>(() => TikTokDataSource());

  instance.registerLazySingleton<TicketDataSource>(() => TicketDataSource());

  instance.registerLazySingleton<AuthDataSource>(() => AuthDataSource());
  instance.registerLazySingleton<AuthRepo>(() => AuthRepo(
      authDataSource: instance<AuthDataSource>(),
      fbAuth: FacebookAuth.instance,
      auth: FirebaseAuth.instance,
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<MetaDataSource>(() => MetaDataSource());
  instance.registerLazySingleton<MetaRepo>(() => MetaRepo(
      metaDataSource: instance<MetaDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<PaymentDataSource>(() => PaymentDataSource());
  instance.registerLazySingleton<PaymentRepo>(() => PaymentRepo(
      paymentDataSource: instance<PaymentDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<ManagementAreaDataSource>(
      () => ManagementAreaDataSource());
  instance.registerLazySingleton<ManagementAreaRepo>(() => ManagementAreaRepo(
      managementAreaDataSource: instance<ManagementAreaDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<HomeLayoutDataSource>(
      () => HomeLayoutDataSource());
  instance.registerLazySingleton<HomeLayoutRepo>(() => HomeLayoutRepo(
      homeLayoutDataSource: instance<HomeLayoutDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<EditProfileDataSource>(
      () => EditProfileDataSource());
  instance.registerLazySingleton<EditProfileRepo>(() => EditProfileRepo(
      editProfileDataSource: instance<EditProfileDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<NotificationDataSource>(
      () => NotificationDataSource());
  instance.registerLazySingleton<NotificationRepo>(() => NotificationRepo(
      notificationDataSource: instance<NotificationDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<WalletDataSource>(() => WalletDataSource());
  instance.registerLazySingleton<WalletRepo>(() => WalletRepo(
      walletDataSource: instance<WalletDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance
      .registerLazySingleton<ComplainsDataSource>(() => ComplainsDataSource());
  instance.registerLazySingleton<ComplaintsRepo>(() => ComplaintsRepo(
      complainsDataSource: instance<ComplainsDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));

  instance.registerLazySingleton<ReportsDataSource>(() => ReportsDataSource());
  instance.registerLazySingleton<ReportsRepo>(() => ReportsRepo(
      reportsDataSource: instance<ReportsDataSource>(),
      networkInfo: instance<NetworkInfoImp>()));
}

import 'package:ads_dv/features/sidebar/ad_accounts/data/models/ad_account.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/meta_page.dart';
import 'package:ads_dv/utils/res/router/routes.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../features/auth/data/models/user.dart';

class HiveHelper extends ValueNotifier<String> {
  final String _name = 'noon111';

  final List<String> _boxNames = [
    // HiveKeys.darkModeKey.name,
    // HiveKeys.langKey.name,
    HiveKeys.token.name,
    HiveKeys.userId.name,
    HiveKeys.user.name,
    HiveKeys.userLocation.name,
    // HiveKeys.isFirst.name,
    HiveKeys.fcmToken.name,
    HiveKeys.adAccount.name,
    HiveKeys.metaPages.name,
    HiveKeys.advertiserId.name,
    HiveKeys.snapAdAccountId.name,
    HiveKeys.tiktokPageName.name,
  ];

  // ValueNotifier to listen to language changes
  ValueNotifier<String> languageNotifier = ValueNotifier<String>('en');

  HiveHelper() : super('en'); // Default language is English

  Future initHiveBoxes() async {
    await Hive.initFlutter();
    await Future.forEach(HiveKeys.values, (HiveKeys element) async {
      await Hive.openBox(element.name + _name);
    });

    // Listen for language changes from Hive
    String savedLang = getLang();
    languageNotifier.value = savedLang;
  }

  Future<void> clearAllCache() async {
    for (var boxName in _boxNames) {
      var box = await Hive.openBox(boxName + _name); // Ensure the box is opened
      await box.clear(); // Clear all data in the box
    }
    print("All cache cleared successfully.");
  }

  bool getModeState() {
    return Hive.box(HiveKeys.darkModeKey.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.darkModeKey.name + _name)
            .get(HiveKeys.darkModeKey.name + _name)
        : false;
  }

  void setModeState({required bool isDark}) {
    Hive.box(HiveKeys.darkModeKey.name + _name)
        .put(HiveKeys.darkModeKey.name + _name, isDark);
  }

  // Get language from Hive
  String getLang() {
    return Hive.box(HiveKeys.langKey.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.langKey.name + _name)
            .get(HiveKeys.langKey.name + _name)
        : 'en';
  }

  // Set language in Hive and notify listeners
  void setLang({required String langCode}) {
    Hive.box(HiveKeys.langKey.name + _name)
        .put(HiveKeys.langKey.name + _name, langCode);
    // Update the ValueNotifier whenever the language is set
    languageNotifier.value = langCode;
  }

  void setMyCity({required String city}) {
    Hive.box(HiveKeys.cityKey.name + _name)
        .put(HiveKeys.cityKey.name + _name, city);
    // Update the ValueNotifier whenever the language is set
    // languageNotifier.value = langCode;
  }

  String getMyCity() {
    return Hive.box(HiveKeys.cityKey.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.cityKey.name + _name)
            .get(HiveKeys.cityKey.name + _name)
        : '';
  }

  void setMyCountryCode({required String countryCode}) {
    print('hereismacountrycode $countryCode');
    Hive.box(HiveKeys.countryCodeKey.name + _name)
        .put(HiveKeys.countryCodeKey.name + _name, countryCode);
    // Update the ValueNotifier whenever the language is set
    // languageNotifier.value = langCode;
  }

  String getMyCountryCode() {
    return Hive.box(HiveKeys.countryCodeKey.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.countryCodeKey.name + _name)
            .get(HiveKeys.countryCodeKey.name + _name)
        : '';
  }

  Future<void> setToken(String token) async {
    await Hive.box(HiveKeys.token.name + _name)
        .put(HiveKeys.token.name + _name, token);
  }

  String? getToken() {
    return Hive.box(HiveKeys.token.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.token.name + _name).get(HiveKeys.token.name + _name)
        : null;
  }

  Future<void> setFcmToken(String fcmToken) async {
    await Hive.box(HiveKeys.fcmToken.name + _name)
        .put(HiveKeys.fcmToken.name + _name, fcmToken);
  }

  String? getFcmToken() {
    return Hive.box(HiveKeys.fcmToken.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.fcmToken.name + _name)
            .get(HiveKeys.fcmToken.name + _name)
        : null;
  }

  Future<void> setUserId(String userId) async {
    await Hive.box(HiveKeys.userId.name + _name)
        .put(HiveKeys.userId.name + _name, userId);
  }

  String? getUserId() {
    return Hive.box(HiveKeys.userId.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.userId.name + _name)
            .get(HiveKeys.userId.name + _name)
        : null;
  }

  Future<void> setAdvertiserId(String advertiserId) async {
    await Hive.box(HiveKeys.advertiserId.name + _name)
        .put(HiveKeys.advertiserId.name + _name, advertiserId);
  }

  String? getAdvertiserId() {
    return Hive.box(HiveKeys.advertiserId.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.advertiserId.name + _name)
            .get(HiveKeys.advertiserId.name + _name)
        : null;
  }

  Future<void> setSnapAdAccountId(String snapAdAccountId) async {
    await Hive.box(HiveKeys.snapAdAccountId.name + _name)
        .put(HiveKeys.snapAdAccountId.name + _name, snapAdAccountId);
  }

  String? getSnapAdAccountId() {
    return Hive.box(HiveKeys.snapAdAccountId.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.snapAdAccountId.name + _name)
            .get(HiveKeys.snapAdAccountId.name + _name)
        : null;
  }

  Future<void> setTiktokPageName(String tiktokPageName) async {
    await Hive.box(HiveKeys.tiktokPageName.name + _name)
        .put(HiveKeys.tiktokPageName.name + _name, tiktokPageName);
  }

  String? getTiktokPageName() {
    return Hive.box(HiveKeys.tiktokPageName.name + _name).isNotEmpty
        ? Hive.box(HiveKeys.tiktokPageName.name + _name)
            .get(HiveKeys.tiktokPageName.name + _name)
        : null;
  }

  Future<void> setUserModel(UserData? userModel) async {
    await Hive.box(HiveKeys.user.name + _name)
        .put(HiveKeys.user.name + _name, userModelToJson(userModel!));
  }

  Future<void> deleteUserModel() async {
    var box = await Hive.openBox(HiveKeys.user.name + _name);
    await box.delete(HiveKeys.user.name + _name);
  }

  UserData? getUser() {
    return Hive.box(HiveKeys.user.name + _name).isNotEmpty
        ? userModelFromJson(Hive.box(HiveKeys.user.name + _name)
            .get(HiveKeys.user.name + _name))
        : null;
  }

  Future<void> setAdAccount(AdAccount? adAccount) async {
    await Hive.box(HiveKeys.adAccount.name + _name)
        .put(HiveKeys.adAccount.name + _name, adAccountToJson(adAccount!));
    print('issetAdAccountWork ${getAdAccount()}');
  }

  AdAccount? getAdAccount() {
    return Hive.box(HiveKeys.adAccount.name + _name).isNotEmpty
        ? adAccountFromJson(Hive.box(HiveKeys.adAccount.name + _name)
            .get(HiveKeys.adAccount.name + _name))
        : null;
  }

  Future<void> setMetaPages(MetaPages? metaPages) async {
    await Hive.box(HiveKeys.metaPages.name + _name)
        .put(HiveKeys.metaPages.name + _name, pagesModelToJson(metaPages!));
  }

  MetaPages? getMetaPages() {
    return Hive.box(HiveKeys.metaPages.name + _name).isNotEmpty
        ? pagesFromJson(Hive.box(HiveKeys.metaPages.name + _name)
            .get(HiveKeys.metaPages.name + _name))
        : null;
  }

  Future<void> deleteAdAccount() async {
    var box = await Hive.openBox(
        HiveKeys.adAccount.name + _name); // Ensure correct box name
    await box.delete(HiveKeys.adAccount.name + _name); // Delete AdAccount key
  }

  Future<void> deleteMetaPages() async {
    var box = await Hive.openBox(
        HiveKeys.metaPages.name + _name); // Ensure correct box name
    await box.delete(HiveKeys.metaPages.name + _name); // Delete MetaPages key
  }

  Future<void> deleteTiktokPageName() async {
    var box = await Hive.openBox(
        HiveKeys.tiktokPageName.name + _name); // Ensure correct box name
    await box
        .delete(HiveKeys.tiktokPageName.name + _name); // Delete AdAccount key
  }

  Future<void> deleteAdvertiserId() async {
    var box = await Hive.openBox(
        HiveKeys.advertiserId.name + _name); // Ensure correct box name
    await box
        .delete(HiveKeys.advertiserId.name + _name); // Delete AdAccount key
  }

  Future<void> deleteSnapAdAccountId() async {
    var box = await Hive.openBox(
        HiveKeys.advertiserId.name + _name); // Ensure correct box name
    await box
        .delete(HiveKeys.advertiserId.name + _name); // Delete AdAccount key
  }

  Future<void> setIsFirst(bool isFirst) async {
    await Hive.box(HiveKeys.isFirst.name + _name)
        .put(HiveKeys.isFirst.name + _name, isFirst);
  }

  bool getIsFirst() {
    final box = Hive.box(HiveKeys.isFirst.name + _name);
    return box.containsKey(HiveKeys.isFirst.name + _name)
        ? box.get(HiveKeys.isFirst.name + _name) ?? true
        : true;
  }

  Future<void> logout(BuildContext context) async {
    await Hive.box(HiveKeys.user.name + _name).clear();
    await Hive.box(HiveKeys.token.name + _name).clear();
    Navigator.pushNamedAndRemoveUntil(context, Routes.login, (route) => false);
  }
}

enum HiveKeys {
  darkModeKey,
  langKey,
  cityKey,
  countryCodeKey,
  token,
  userId,
  user,
  userLocation,
  isFirst,
  fcmToken,
  adAccount,
  metaPages,
  advertiserId,
  snapAdAccountId,
  tiktokPageName,
}

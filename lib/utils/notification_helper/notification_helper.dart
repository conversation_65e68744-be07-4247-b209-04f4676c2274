
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

import '../log/log.dart';
import 'local_notification_helper.dart';

class NotificationsHelper {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;

  static Future initNotification() async {
    await LocalNotificationHelper.initLocalNotification();
    await _registerNotification();
  }

  static Future<String?> getToken() async{

    return FirebaseMessaging.instance.getToken().then((value) {
      print("FCM Token: $value");
      return null;

    });

  }

  static Future _registerNotification() async {
    debugPrint('-------------registerNotification----------');
    await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    _firebaseMessaging.getInitialMessage().then((RemoteMessage? message) {
      if (message == null) return;
      //LocalNotificationHelper.onSelectHandler(jsonEncode(message.data));
      LocalNotificationHelper.showNotification(message);

      Log.i(message.toString());
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      LocalNotificationHelper.showNotification(message);
      Log.i(message.toString());
    });

    FirebaseMessaging.instance.subscribeToTopic('dvApp');

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
    //  LocalNotificationHelper.onSelectHandler(jsonEncode(message.data));
      LocalNotificationHelper.showNotification(message);

    });

    _firebaseMessaging.onTokenRefresh.listen((fcmToken) {
      // logout or set new token in data base
    }).onError((err) {});
  }
}
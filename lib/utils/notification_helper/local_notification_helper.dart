import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../main.dart';

class LocalNotificationHelper {
  static const _iconNotification = '@mipmap/ic_launcher_notification';

  static final FlutterLocalNotificationsPlugin
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
    'default', // id
    'High Importance Notifications', // title
    description:
        'This channel is used for important notifications.', // description
    importance: Importance.max,
  );

  static const _androidPlatformChannelSpecifics = AndroidNotificationDetails(
      'default', 'High Importance Notifications',
      channelDescription: 'This channel is used for important notifications',
      importance: Importance.max,
      icon: _iconNotification,
      playSound: true,
      showProgress: true,
      priority: Priority.high,
      ticker: '');

  static const _iOSChannelSpecifics = DarwinNotificationDetails();

  static const platformChannelSpecifics = NotificationDetails(
      android: _androidPlatformChannelSpecifics, iOS: _iOSChannelSpecifics);

  static AndroidInitializationSettings initializationSettingsAndroid =
      const AndroidInitializationSettings(_iconNotification);
  static DarwinInitializationSettings initializationSettingsIOS =
      const DarwinInitializationSettings(
    requestSoundPermission: false,
    requestBadgePermission: false,
    requestAlertPermission: false,
    onDidReceiveLocalNotification: _onDidReceiveLocalNotification,
  );
  static InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );

  /// SHOW LOCAL NOTIFICATION WHILE IS APP IS IN FOREGROUND BECAUSE FIREBASE DOESN'T SUPPORT NOTIFICATIONS IN FOREGROUND
  static Future initLocalNotification() async {
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestPermission();
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(_channel);

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
    );
  }

  static Future<void> _onDidReceiveLocalNotification(
      int id, String? title, String? body, String? payload) {
    return showDialog(
      context: navigatorKey.currentState!.context,
      builder: (BuildContext context) => Directionality(
        textDirection: TextDirection.rtl,
        child: CupertinoAlertDialog(
          title: Text(title!),
          content: Text(body!),
          actions: [
            CupertinoDialogAction(
              isDefaultAction: true,
              child: const Text('Ok'),
              onPressed: () async {
                Navigator.of(context).pop();
                await onSelectHandler(payload);
              },
            )
          ],
        ),
      ),
    );
  }

  static Future<void> _showNotification(
      String title, String body, Map payload, String imageUrl) async {
    final Uint8List imageBytes = await _getByteArrayFromUrl(imageUrl);

    final BigPictureStyleInformation bigPictureStyleInformation =
        BigPictureStyleInformation(
      ByteArrayAndroidBitmap(imageBytes),
      largeIcon: ByteArrayAndroidBitmap(imageBytes),
      contentTitle: title,
      htmlFormatContentTitle: true,
      summaryText: body,
      htmlFormatSummaryText: true,
    );

    final AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails('default', 'High Importance Notifications',
            channelDescription:
                'This channel is used for important notifications',
            importance: Importance.max,
            icon: _iconNotification,
            playSound: true,
            showProgress: true,
            styleInformation: bigPictureStyleInformation,
            priority: Priority.high,
            ticker: '');

    final NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidNotificationDetails);

    await _flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
      payload: jsonEncode(payload),
    );
  }

  static Future<Uint8List> _getByteArrayFromUrl(String imageUrl) async {
    final response = await Dio().get(
      imageUrl,
      options: Options(
        responseType: ResponseType.bytes,
      ),
    );
    return response.data;
  }

  static Future showNotification(RemoteMessage message) async {
    if (message.notification != null) {
      String title = message.notification?.title ?? '';
      String body = message.notification?.body ?? '';

      _showNotification(
        title,
        body,
        message.data,
        message.data['image'],
      );

      log("message.data.toString()");
      log(message.data.toString());

      print("Test3${message.data}");
    }
  }

  static Future onSelectHandler(String? payload) async {
    final Map decodedPayload = jsonDecode(payload ?? '');

    log(decodedPayload.toString());

    print("decodedPayload" + decodedPayload['image']);
  }
}

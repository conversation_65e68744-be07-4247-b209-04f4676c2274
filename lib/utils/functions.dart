import 'dart:async';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  void run(VoidCallback action) {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  void dismiss() => _timer?.cancel();
}

class Functions {
  static final _randomGenrator = math.Random();

  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.dv.ads.manger';
  static const String appStoreUrl = 'https://apps.apple.com/app/id6450996107';

  static Future<bool> launchURL(BuildContext context) async {
    try {
      print('hello store ');
      if (Theme.of(context).platform == TargetPlatform.iOS) {
        // iOS: Open App Store
        return await launchUrl(Uri.parse(appStoreUrl));
      } else {
        // Android: Open Google Play Store
        // print('hello store ');
        return await launchUrl(Uri.parse(playStoreUrl));
      }
    } catch (e) {
      print("Could not launch the URL: $e");
      return false;
    }
  }


  static Future<void> hideStatusBar(bool systemOverlaysAreVisible) async {
    if (!systemOverlaysAreVisible) {
      await Future.delayed(const Duration(seconds: 1));
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [SystemUiOverlay.top],
      );
    }
  }

  static int randomInt(int max) {
    return _randomGenrator.nextInt(max);
  }

  // static Future<bool> makeCall(String? phone) async {
  //   return await launchUrlString('tel:$phone');
  // }

  // static Future<bool> makeSMS(String? phone) async {
  //   return await launchUrlString('sms:$phone');
  // }

  static Future<bool> makeEmail(String? email) async {
    return await launchUrlString('mailto:$email');
  }

  static Future<bool> openLinkInBrowser(String? url) async {
    return await launchUrlString("$url", mode: LaunchMode.externalApplication);
  }



  static int calculateAge(DateTime birthDate) {
    DateTime currentDate = DateTime.now();
    int age = currentDate.year - birthDate.year;
    int month1 = currentDate.month;
    int month2 = birthDate.month;
    if (month2 > month1) {
      age--;
    } else if (month1 == month2) {
      int day1 = currentDate.day;
      int day2 = birthDate.day;
      if (day2 > day1) {
        age--;
      }
    }
    return age;
  }
}

import 'package:ads_dv/features/auth/presentation/login/controllers/login/login_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_all_campaigns/get_all_campaigns_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_objectives/get_objectives_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/get_posts/get_posts_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/search/search_cubit.dart';
import 'package:ads_dv/features/onboarding/presentation/controllers/on_boarding_cubit.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/controllers/create_snapChat_ad_cubit/create_snap_chat_ad_cubit.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/controllers/tiktok_ad_group/tiktok_ad_group_cubit.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../features/auth/presentation/login/controllers/facebook_disconnect/facebook_disconnect_cubit.dart';
import '../../features/campaigns_home/presentation/controllers/campaign_home_cubit.dart';
import '../../features/chat/presentation/controllers/chat_cubit.dart';
import '../../features/create_campaigns/presentation/controllers/get_billing_events/get_billing_events_cubit.dart';
import '../../features/create_campaigns/presentation/controllers/get_forms/get_forms_cubit.dart';
import '../../features/create_campaigns/presentation/controllers/get_optimizations/get_optimizations_cubit.dart';
import '../../features/create_campaigns/presentation/controllers/get_reach_estimate/reach_estimate_cubit.dart';
import '../../features/maps/presentation/controllers/select_map_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/get_add_accounts/get_add_accounts_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/get_fb_pages/get_fb_pages_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/get_snapChat_add_accounts/get_snap_chat_add_accounts_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/store_add_account/store_ad_account_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/store_snapChat_ad_accounts/store_snap_chat_ad_account_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/store_tiktok_ad_account/store_tiktok_ad_account_cubit.dart';
import '../../features/sidebar/ad_accounts/presentation/controllers/tiktok_accounts/tiktok_accounts_cubit.dart';
import '../../features/sidebar/payment/presentation/controllers/payment_controller/payment_cubit.dart';
import '../../features/sidebar/wallet/presentation/controllers/get_wallet_details/get_wallet_details_cubit.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapChat_adSet/snap_chat_ad_set_cubit.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapChat_objectives/get_snap_chat_objectives_cubit.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapChat_optimizations/get_snap_chat_optimizations_cubit.dart';
import '../../features/snapChat_campaign/presentation/controllers/snapchat_campaign/snap_chat_campaign_cubit.dart';
import '../../features/tiktok_campigns/presentation/controllers/get_objectives/get_tiktok_objectives_cubit.dart';
import '../../features/tiktok_campigns/presentation/controllers/get_tiktok_optimizations/get_tiktok_optimizations_cubit.dart';
import '../../features/tiktok_campigns/presentation/controllers/tiktok_ad/tiktok_ad_cubit.dart';
import '../../features/tiktok_campigns/presentation/controllers/tiktok_campain/tiktok_campaign_cubit.dart';
import '../di/injection.dart';
import '../hive_helper/hive_helper.dart';
import '../res/lang/lang_cubit.dart';
import '../res/router/routes.dart';
import '../res/router/routes_generator.dart';
import '../res/themes/theme_cubit/theme_cubit.dart';
import '../trans/app_translations.dart';

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
late WebViewController controller;

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Retrieve Firebase token and store it in Hive
    _firebaseMessaging.getToken().then((String? token) {
      print("FCM Token: $token");
      instance<HiveHelper>().setFcmToken(token ?? "");
    });

    // instance.get<TiktokLoginCubit>().tiktokGetCode(context: context);
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ThemeCubit>(
          create: (context) => ThemeCubit(),
        ),
        BlocProvider<StoreTiktokAdAccountCubit>(
          create: (context) => StoreTiktokAdAccountCubit(),
        ),
        BlocProvider<CreateSnapChatAdCubit>(
          create: (context) => CreateSnapChatAdCubit(),
        ),
        BlocProvider<SnapChatAdSetCubit>(
          create: (context) => SnapChatAdSetCubit(),
        ),
        BlocProvider<SelectMapCubit>(
          create: (context) => SelectMapCubit(),
        ),
        BlocProvider<GetSnapChatObjectivesCubit>(
          create: (context) => GetSnapChatObjectivesCubit(),
        ),
        BlocProvider<SnapChatCampaignCubit>(
          create: (context) => SnapChatCampaignCubit(),
        ),
        BlocProvider<GetSnapChatAddAccountsCubit>(
          create: (context) => GetSnapChatAddAccountsCubit(),
        ),
        BlocProvider<GetSnapChatOptimizationsCubit>(
          create: (context) => GetSnapChatOptimizationsCubit(),
        ),
        BlocProvider<ChatCubit>(
          create: (context) => ChatCubit(),
        ),
        BlocProvider<LangCubit>(
          create: (context) => LangCubit(),
        ),
        BlocProvider<CreateAdCubit>(
          create: (context) => CreateAdCubit(),
        ),
        BlocProvider<LoginCubit>(
          create: (context) => LoginCubit(),
        ),
        BlocProvider<FacebookDisconnectCubit>(
          create: (context) => FacebookDisconnectCubit(),
        ),
        BlocProvider<TiktokAdCubit>(
          create: (context) => TiktokAdCubit(),
        ),
        BlocProvider<TiktokAdGroupCubit>(
          create: (context) => TiktokAdGroupCubit(),
        ),
        BlocProvider<TiktokAccountsCubit>(
          create: (context) => TiktokAccountsCubit(),
        ),
        BlocProvider<StoreSnapChatAdAccountCubit>(
          create: (context) => StoreSnapChatAdAccountCubit(),
        ),
        BlocProvider<PaymentCubit>(
          create: (context) => PaymentCubit(),
        ),
        BlocProvider<ReachEstimateCubit>(
          create: (context) => ReachEstimateCubit(),
        ),
        BlocProvider<GetBillingEventsCubit>(
          create: (context) => GetBillingEventsCubit(),
        ),
        BlocProvider(
          create: (context) => GetAdAccountsCubit(),
        ),
        BlocProvider<GetOptimizationsCubit>(
          create: (context) => GetOptimizationsCubit(),
        ),
        BlocProvider(
          create: (context) => GetFbPagesCubit(),
        ),
        BlocProvider(
          create: (context) => StoreAdAccountCubit(),
        ),
        BlocProvider(
          create: (context) => CampaignHomeCubit(),
        ),
        BlocProvider(
          create: (context) => TiktokCampaignCubit(),
        ),
        BlocProvider(
          create: (context) => GetTiktokOptimizationsCubit(),
        ),
        BlocProvider(
          create: (context) => GetTiktokObjectivesCubit(),
        ),
        BlocProvider(
          create: (context) =>
              GetWalletDetailsCubit()..getWalletDetails(context: context),
        ),
        BlocProvider<OnBoardingCubit>(
          create: (context) => OnBoardingCubit()..initial(),
        ),
        BlocProvider<SearchCubit>(
          create: (context) => SearchCubit()
            ..searchForClass(
                context: context,
                type: "adTargetingCategory",
                searchClass: "user_os"),
        ),
        BlocProvider<GetCampaignsCubit>(
          create: (context) => GetCampaignsCubit()
            ..getCampaigns(
                context: context,
                accountId: instance<HiveHelper>().getUser()?.defaultAccountId ??
                    CreateAdCubit.get(context).adAccount?.id ??
                    ""),
        ),
        BlocProvider<GetPostsCubit>(
          create: (context) => GetPostsCubit()
            ..getFbPosts(
              context: context,
              pageAccessToken:
                  instance<HiveHelper>().getUser()?.defaultPageAccessToken ??
                      CreateAdCubit.get(context).metaPages?.accessToken ??
                      "",
              pageId: instance<HiveHelper>().getUser()?.defaultPageId ??
                  CreateAdCubit.get(context).metaPages?.id ??
                  "",
            ),
        ),
        BlocProvider<GetFormsCubit>(
          create: (context) => GetFormsCubit()
            ..getLeadForms(
              context: context,
              pageAccessToken:
                  instance<HiveHelper>().getUser()?.defaultPageAccessToken ??
                      CreateAdCubit.get(context).metaPages?.accessToken ??
                      "",
              pageId: instance<HiveHelper>().getUser()?.defaultPageId ??
                  CreateAdCubit.get(context).metaPages?.id ??
                  "",
            ),
        ),
        BlocProvider<GetObjectivesCubit>(
          create: (context) => GetObjectivesCubit(),
        ),
      ],
      child: ValueListenableBuilder<String>(
        valueListenable: instance<HiveHelper>().languageNotifier,
        // Listen to HiveHelper language changes
        builder: (context, langCode, _) {
          return BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, stateTheme) {
              return ScreenUtilInit(
                designSize: const Size(392, 834),
                minTextAdapt: true,
                splitScreenMode: true,
                child: OKToast(
                  child: GetMaterialApp(
                    navigatorKey: Constants.navigatorKey,
                    debugShowCheckedModeBanner: false,
                    initialRoute: Routes.splash,
                    onGenerateRoute: RoutesGenerator.getRoute,
                    translations: AppTranslations(),
                    locale: Locale(langCode),
                    // Use language from HiveHelper
                    themeMode: _getThemeMode(stateTheme),
                    theme: _getThemeData(stateTheme),
                    localizationsDelegates: const [
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate,
                      DefaultCupertinoLocalizations.delegate
                    ],
                    fallbackLocale: const Locale("ar"),
                    supportedLocales: const [Locale("ar"), Locale("en")],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  // Helper method to get the locale from the LangState
  Locale _getLocale(LangState langState) {
    if (langState is LangStateInitial) {
      return langState.lang;
    } else if (langState is ChangeLangState) {
      return langState.lang;
    }
    return const Locale(
        'ar'); // Default to Arabic if no language state is found
  }

  // Helper method to get the theme mode from ThemeState
  ThemeMode _getThemeMode(ThemeState stateTheme) {
    if (stateTheme is ThemeStateInitial) {
      return stateTheme.themeMode;
    } else if (stateTheme is ToggleThemeState) {
      return stateTheme.themeMode;
    }
    return ThemeMode.light; // Default theme mode is light
  }

  // Helper method to get the theme data from ThemeState
  ThemeData _getThemeData(ThemeState stateTheme) {
    if (stateTheme is ThemeStateInitial) {
      return stateTheme.themeData;
    } else if (stateTheme is ToggleThemeState) {
      return stateTheme.themeData;
    }
    return ThemeData.dark(); // Default theme data is dark
  }
}

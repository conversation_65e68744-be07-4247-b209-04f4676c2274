
enum EducationStatuses {
  HIGH_SCHOOL(1),
  UNDERGRAD(2),
  ALUM(3),
  HIGH_SCHOOL_GRAD(4),
  SOME_COLLEGE(5),
  ASSOCIATE_DEGREE(6),
  IN_GRAD_SCHOOL(7),
  SOME_GRAD_SCHOOL(8),
  MASTER_DEGREE(9),
  PROFESSIONAL_DEGREE(10),
  DOCTORATE_DEGREE(11),
  UNSPECIFIED(12),
  SOME_HIGH_SCHOOL(13);

  final int id;

  const EducationStatuses(this.id);

  String get displayName {
    return toString().split('.').last.replaceAll('_', ' ').toLowerCase();
  }
}
// const defaultStartLatLng = LatLng(25.230065, 55.3275053);

enum DestinationType {
  WEBSITE,
  APP,
  MESSENGER,
  INSTAGRAM_DIRECT,
}

enum PublisherPlatforms { facebook, instagram, messenger }

enum DistanceUnit { kilometer, mile }

extension DistanceUnitExtension on DistanceUnit {
  String toJSON() {
    switch (this) {
      case DistanceUnit.kilometer:
        return 'kilometer';
      case DistanceUnit.mile:
        return 'mile';
      default:
        return '';
    }
  }

  static DistanceUnit fromJSON(String value) {
    switch (value) {
      case 'kilometer':
        return DistanceUnit.kilometer;
      case 'mile':
        return DistanceUnit.mile;
      default:
        throw Exception('Invalid DistanceUnit value: $value');
    }
  }
}

extension PublisherPlatformsExtension on PublisherPlatforms {
  String toJSON() {
    switch (this) {
      case PublisherPlatforms.facebook:
        return 'facebook';
      case PublisherPlatforms.instagram:
        return 'instagram';
      default:
        return '';
    }
  }

  static PublisherPlatforms fromJSON(String value) {
    switch (value) {
      case 'facebook':
        return PublisherPlatforms.facebook;
      case 'instagram':
        return PublisherPlatforms.instagram;
      default:
        throw Exception('Invalid PublisherPlatforms value: $value');
    }
  }
}

extension DestinationTypeExtension on DestinationType {
  static DestinationType fromJSON(String value) => switch (value) {
        'WEBSITE' => DestinationType.WEBSITE,
        'APP' => DestinationType.APP,
        'MESSENGER' => DestinationType.MESSENGER,
        'INSTAGRAM_DIRECT' => DestinationType.INSTAGRAM_DIRECT,
        _ => throw ArgumentError('Invalid DestinationType value: $value')
      };
}

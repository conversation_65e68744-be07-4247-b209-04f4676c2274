import 'package:ads_dv/features/auth/presentation/login/views/login_screen.dart';
import 'package:ads_dv/features/auth/presentation/otp/views/otp_screen.dart';
import 'package:ads_dv/features/auth/presentation/register/views/register_screen.dart';
import 'package:ads_dv/features/auth/presentation/register/views/verify_email.dart';
import 'package:ads_dv/features/auth/presentation/reset_password/views/reset_password_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/create_campaign_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/review_campaign_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/demographic_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/education_majors.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/education_status_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/life_event_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/schools_universities_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/user_device_screen.dart';
import 'package:ads_dv/features/create_campaigns/presentation/views/widgets/create_adset/new_adset/user_os.dart';
import 'package:ads_dv/features/home_layout/presentation/views/home_layout.dart';
import 'package:ads_dv/features/maps/presentation/views/select_map_view.dart';
import 'package:ads_dv/features/notifications/presentation/views/notification_screen.dart';
import 'package:ads_dv/features/onboarding/presentation/views/onboarding_screen.dart';
import 'package:ads_dv/features/onboarding/presentation/views/start_page.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/access.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/data/models/ad_account.dart';
import 'package:ads_dv/features/sidebar/ad_accounts/presentation/views/screens/snapChat_accounts_screen.dart';
import 'package:ads_dv/features/sidebar/management_area/presentation/screens/edit_acces_screen.dart';
import 'package:ads_dv/features/sidebar/payment/presentation/views/payment_screen.dart';
import 'package:ads_dv/features/sidebar/profile/presentation/views/profile_screen.dart';
import 'package:ads_dv/features/sidebar/reports/data/models/campaign_response.dart';
import 'package:ads_dv/features/sidebar/reports/presentation/views/campaign_details_screen.dart';
import 'package:ads_dv/features/snapChat_campaign/presentation/views/snapChat_adSet_widget/snapChat_demographic_screen.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/tiktok_review_campaign_screen.dart';
import 'package:ads_dv/features/tiktok_campigns/presentation/views/widgets/tiktok_ad_group/tiktok_demographic_screen.dart';
import 'package:ads_dv/utils/res/router/routes.dart';
import 'package:flutter/material.dart';

import '../../../features/auth/presentation/forget_password/views/forget_password_screen.dart';
import '../../../features/chat/presentation/views/chatScreen.dart';
import '../../../features/create_campaigns/presentation/views/widgets/create_adset/new_adset/detailed_targeting_screen.dart';
import '../../../features/sidebar/ad_accounts/data/models/page.dart';
import '../../../features/sidebar/ad_accounts/presentation/views/screens/accounts_screen.dart';
import '../../../features/sidebar/ad_accounts/presentation/views/screens/meta_accounts_screen.dart';
import '../../../features/sidebar/ad_accounts/presentation/views/screens/snapChat_connect_webView.dart';
import '../../../features/sidebar/ad_accounts/presentation/views/screens/tikTok_connect_webView.dart';
import '../../../features/sidebar/ad_accounts/presentation/views/screens/tiktok_acounts_screen.dart';
import '../../../features/sidebar/management_area/presentation/screens/accessed_accounts_screen.dart';
import '../../../features/sidebar/management_area/presentation/screens/invite_access_screen.dart';
import '../../../features/sidebar/management_area/presentation/screens/team_management_screen.dart';
import '../../../features/sidebar/privacy_policy/privacy_policy_screen.dart';
import '../../../features/sidebar/settings/presentation/views/settings_screen.dart';
import '../../../features/sidebar/terms_conditions/terms_conditions_screen.dart';
import '../../../features/sidebar/wallet/presentation/views/tickets_history_screen.dart';
import '../../../features/sidebar/widgets/fullscreen_widget.dart';
import '../../../features/snapChat_campaign/presentation/views/snapChat_adSet_widget/snapChat_audience_interests_screen.dart';
import '../../../features/snapChat_campaign/presentation/views/snapChat_review/snapChat_review_screen.dart';
import '../../../features/snapChat_campaign/snapChat_campaign.dart';
import '../../../features/splash/presentation/views/splash_screen.dart';
import '../../../features/tiktok_campigns/presentation/views/tiktok_campaign.dart';
import '../../../features/tiktok_campigns/presentation/views/widgets/tiktok_ad_group/tiktok_adLocation_screen.dart';
import '../../../features/tiktok_campigns/presentation/views/widgets/tiktok_ad_group/tiktok_audience_interests_screen.dart';

class RoutesGenerator {
  static Route<dynamic> getRoute(RouteSettings routeSettings) {
    switch (routeSettings.name) {
      case Routes.splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case Routes.onboard:
        return MaterialPageRoute(builder: (_) => const OnBoardingScreen());
      case Routes.home:
        return MaterialPageRoute(builder: (_) => const HomeLayoutScreen());
      case Routes.chat:
        return MaterialPageRoute(builder: (_) => const ChatScreen());
      case Routes.createCampaign:
        return MaterialPageRoute(builder: (_) => const CreateCampaignScreen());
      case Routes.demo:
        return MaterialPageRoute(
            builder: (_) => DemographicScreen(
                  createAdCubit: routeSettings.arguments as CreateAdCubit,
                ));
      case Routes.tiktokLocations:
        return MaterialPageRoute(builder: (_) => const SelectLocationScreen());
      case Routes.tiktokDemographics:
        return MaterialPageRoute(
            builder: (_) => const TiktokDemographicScreen());
      case Routes.details:
        return MaterialPageRoute(builder: (_) => const DetailedTargeting());
      case Routes.tiktokAudienceInterests:
        return MaterialPageRoute(
            builder: (_) => const TiktokAudienceInterestsScreen());
      case Routes.snapChatCampaign:
        return MaterialPageRoute(
            builder: (_) => const CreateSnapChatCampaignScreen());
      case Routes.snapChatAccountsScreen:
        return MaterialPageRoute(
            builder: (_) => const SnapChatAccountsScreen());
      case Routes.snapChatDemographicScreen:
        return MaterialPageRoute(builder: (_) => const SnapChatDemographicScreen());
      case Routes.snapChatReviewScreen:
        return MaterialPageRoute(
            builder: (_) => const SnapChatReviewCampaignScreen());
      case Routes.snapChatInterestsScreen:
        return MaterialPageRoute(
            builder: (_) => const SnapChatAudienceInterestsScreen());
      case Routes.tiktokCampaignReview:
        return MaterialPageRoute(
            builder: (_) => const TiktokReviewCampaignScreen());
      case Routes.schools:
        return MaterialPageRoute(
            builder: (_) => const SchoolsUniversitiesScreen());
      case Routes.education:
        return MaterialPageRoute(builder: (_) => const EducationMajorsScreen());
      case Routes.educationStatus:
        return MaterialPageRoute(builder: (_) => const EducationStatusScreen());
      case Routes.lifeEvent:
        return MaterialPageRoute(builder: (_) => const LifeEventsScreen());
      case Routes.userDevice:
        return MaterialPageRoute(builder: (_) => const UserDeviceScreen());
      case Routes.userOs:
        return MaterialPageRoute(builder: (_) => const UserOsScreen());
      case Routes.forgetPassword:
        return MaterialPageRoute(builder: (_) => const ForgetPasswordScreen());
      case Routes.accounts:
        return MaterialPageRoute(builder: (_) => const AccountsScreen());
      case Routes.metaAccounts:
        return MaterialPageRoute(builder: (_) => const MetaAccountsScreen());
      case Routes.ticketsHistory:
        return MaterialPageRoute(builder: (_) => const TicketsHistoryScreen());
      case Routes.teamManagements:
        return MaterialPageRoute(builder: (_) => const TeamManagementScreen());
      case Routes.invite:
        return MaterialPageRoute(builder: (_) => const InviteAccessScreen());
      case Routes.accessedPages:
        return MaterialPageRoute(
            builder: (_) => AccessedAccountsScreen(
                userAccount: (routeSettings.arguments
                    as Map<String, dynamic>)['userAccount'] as List<AdAccount>,
                metaPages: (routeSettings.arguments
                    as Map<String, dynamic>)['metaPages'] as List<Pages>));
      case Routes.otp:
        return MaterialPageRoute(
            builder: (_) => OtpScreen(
                  email: routeSettings.arguments as String,
                ));
      case Routes.resetPassword:
        return MaterialPageRoute(
            builder: (_) => ResetPasswordScreen(
                  email: (routeSettings.arguments
                      as Map<String, dynamic>)['email'] as String,
                  otp: (routeSettings.arguments as Map<String, dynamic>)['otp']
                      as String,
                ));
      case Routes.register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());
      case Routes.startPage:
        return MaterialPageRoute(builder: (_) => const StartPage());
      case Routes.login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      case Routes.verifyEmail:
        return MaterialPageRoute(
          builder: (_) => VerifyEmailScreen(
            name: (routeSettings.arguments as Map<String, dynamic>)['name']
                as String,
            email: (routeSettings.arguments as Map<String, dynamic>)['email']
                as String,
            phone: (routeSettings.arguments as Map<String, dynamic>)['phone']
                as String,
            password: (routeSettings.arguments
                as Map<String, dynamic>)['password'] as String,
          ),
        );
      case Routes.map:
        return MaterialPageRoute(
          builder: (_) => SelectMapView(
            createAdCubit: (routeSettings.arguments
                as Map<String, dynamic>)['cubit'] as CreateAdCubit,
            isFromSnapChat: (routeSettings.arguments
                as Map<String, dynamic>)['isFromSnapChat'] as bool,
          ),
        );
      case Routes.setting:
        return MaterialPageRoute(builder: (_) => const SettingScreen());
      case Routes.privacyPolicy:
        return MaterialPageRoute(builder: (_) => const PrivacyPolicyScreen());
      case Routes.termsConditions:
        return MaterialPageRoute(builder: (_) => const TermsConditionsScreen());
      case Routes.payment:
        return MaterialPageRoute(builder: (_) => const PaymentScreen());
      case Routes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case Routes.campaignDetails:
        return MaterialPageRoute(
            builder: (_) => CampaignDetailsScreen(
                campaign: (routeSettings.arguments
                    as Map<String, dynamic>)['campaign'] as CampaignReports));
      case Routes.notifications:
        return MaterialPageRoute(builder: (_) => const NotificationsScreen());
      case Routes.reviewCampaign:
        return MaterialPageRoute(builder: (_) => const ReviewCampaignScreen());
      case Routes.image:
        return MaterialPageRoute(
            builder: (_) => FullScreenPhotoViewer(
                  imageUrl: (routeSettings.arguments
                      as Map<String, dynamic>)['imageUrl'] as String,
                ));
      case Routes.editAccess:
        return MaterialPageRoute(
            builder: (_) => EditAccessScreen(
                accessedUser: (routeSettings.arguments
                    as Map<String, dynamic>)['accessedUser'] as AccessedUser));
      case Routes.tiktokAccounts:
        return MaterialPageRoute(builder: (_) => const TiktokAccountsScreen());
      case Routes.tiktokCampaign:
        return MaterialPageRoute(
            builder: (_) => const CreateTiktokCampaignScreen());
      case Routes.tiktokConnectionWebView:
        return MaterialPageRoute(
            builder: (_) => const TiktokConnectionWebViewScreen());
      case Routes.snapChatConnectionWebView:
        return MaterialPageRoute(
            builder: (_) => const SnapChatConnectionWebViewScreen());
      default:
        return unDefinedRoute();
    }
  }

  static Route unDefinedRoute() {
    return MaterialPageRoute(builder: (_) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('no route'),
        ),
      );
    });
  }
}

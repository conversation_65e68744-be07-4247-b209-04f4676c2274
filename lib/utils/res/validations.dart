import 'package:flutter/cupertino.dart';

mixin AppValidator {
  static String? validPassword(dynamic value) {
    if (value!.isNotEmpty) {
      if (value.length < 6) {
        return 'Please enter strong password';
      }
    } else {
      return "Please fill this field";
    }

    return null;
  }

  String? nameAddress(dynamic value) {
    if (value!.isNotEmpty) {
      if (value.length <= 5) {
        return 'برجاء كتابة الاسم كامل';
      }
    } else {
      return 'برجاء املاء الحقل';
    }

    return null;
  }

  String? detailsAddress(dynamic value) {
    if (value!.isNotEmpty) {
      if (value.length <= 10) {
        return 'برجاء كتابة التفاصيل كاملة كامل';
      }
    } else {
      return 'برجاء املاء الحقل';
    }

    return null;
  }

  validatePassword(String value) {
    if (value.isEmpty) {
      return 'من فضلك ادخل الرقم السري';
    } else if (value.length < 6) {
      return 'برجاء يكون اكثر من 6 ارقام او حروف';
    }
    return null;
  }

  static validateConfirmPassword(String? value, String passwordController) {
    if (value!.isEmpty) {
      return "Please fill this field";
    } else {
      if (value != passwordController) {
        return 'password does not matching';
      } else {
        return null;
      }
    }
  }

  static validateEmail(String? value) {
    if (value!.isEmpty) {
      return "Please fill this field";
    }
    if (!RegExp(r"^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$")
            .hasMatch(value) ||
        value.contains(' ')) {
      return 'Please enter valid email address';
    }
    return null;
  }

  validateUserName(value) {
    if (value.isEmpty) {
      return 'قم بادخال اسم المستخدم';
    }
    if (!RegExp(r"([a-zA-Z]{3,30}\s*)+").hasMatch(value)) {
      return 'من فضلك ادخل اسم مستخدم صحيح';
    }
    return null;
  }

  static validateIdentity(String? value, BuildContext context) {
    if (value!.isEmpty) {
      return 'Please fill this field';
    }
    return null;
  }

  static validateUrl(String? value, BuildContext context) {
    if (value!.startsWith('https://') == false) {
      return 'it should start with https://';
    }
    return null;
  }

  String? validateRegistrationNumber(value) {
    RegExp regex =
        RegExp(r'(^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$)');
    print(regex.hasMatch(value));
    print(value[0] == '0');
    print(value.length != 9);
    if (value.length != 9 || value[0] == '0') {
      return 'قم بادخال رقم هاتف صحيح';
    }
    return null;
  }

  static String? validateCodeNumber(value) {
    if (value.length != 4) {
      return 'قم بادخال رقم هاتف صحيح';
    }
    return null;
  }
}

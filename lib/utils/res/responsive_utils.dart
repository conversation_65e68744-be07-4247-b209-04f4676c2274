import 'package:flutter/cupertino.dart';

import 'media_query_config.dart';

class ResponsiveUtils {
  static heightSizedBox(double height, BuildContext context, {child}) {
    return SizedBox(
      height: SizeConfig.hieghtr(height, context),
    );
  }

  static widthSizedBox(double width, BuildContext context) {
    return SizedBox(
      width: SizeConfig.widthr(width, context),
    );
  }

  static sizedBox(
      {required double width,
      required double height,
      required BuildContext context,
      Widget? child}) {
    return SizedBox(
      height: SizeConfig.hieghtr(height, context),
      width: SizeConfig.widthr(width, context),
      child: child,
    );
  }

  static padding(
      {double width = 0, double height = 0, required BuildContext context}) {
    return EdgeInsets.symmetric(
        horizontal: SizeConfig.widthr(width, context),
        vertical: SizeConfig.hieghtr(height, context));
  }

  static paddingOnly(
      {double top = 0,
      double left = 0,
      double right = 0,
      double bottom = 0,
      required BuildContext context}) {
    return EdgeInsets.only(
        top: SizeConfig.hieghtr(top, context),
        bottom: SizeConfig.hieghtr(bottom, context),
        left: SizeConfig.widthr(left, context),
        right: SizeConfig.widthr(right, context));
  }
}

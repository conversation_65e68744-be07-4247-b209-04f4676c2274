import 'dart:ui';

import 'package:ads_dv/utils/res/app_assets.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:ads_dv/widgets/svg_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

import '../../widgets/bouncing_widgets.dart';

showErrorToast(dynamic message) => showToast(
  " $message ",
  position: ToastPosition.bottom,
  dismissOtherToast: true,
  duration: const Duration(seconds: 3),
  textPadding:  const EdgeInsets.all(10),
  radius: 15,
  backgroundColor: Colors.red,
);

showSuccessToast(dynamic message) => showToast(
  " $message ",
  position: ToastPosition.bottom,
  dismissOtherToast: true,
  duration: const Duration(seconds: 3),
  textPadding: const EdgeInsets.all(10),
  radius: 15,
  textStyle: const TextStyle(color: Colors.white),
  backgroundColor: Constants.greenColor,
);


Widget buildPasswordVisibilityBtn({required bool isVisible, required VoidCallback onPressed}) =>
    Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.h),
      child: Material(
        color: Colors.transparent,
        child: IconButton(
          padding: const EdgeInsets.all(10),
          splashRadius: 25,
          onPressed: onPressed,
          icon: CustomSvgWidget(
            svg: AppAssets.eye,
            height: 20.h,
            width: 20.h,
            color: isVisible ? Constants.mainColor : Constants.darkGray,
          ),
        ),
      ),
    );

Widget buildBackBtn({bool reverseColors = false, VoidCallback? onClick}) {
  return BounceIt(
    onPressed: onClick ?? Get.back,
    child: SizedBox(
      width: 32.h,
      height: 32.h,
      child: Material(
        color: reverseColors ? Constants.mainColor : Colors.white,
        borderRadius: BorderRadius.circular(50.r),
        clipBehavior: Clip.antiAlias,
        child: CustomSvgWidget(
          svg: "assets/svgs/back.svg",
          color: reverseColors ? Colors.white : Constants.mainColor,
          height: 24.h,
          width: 24.h,
        ).paddingAll(6.h),
      ),
    ),
  );
}
customBottomSheet({
  required List<Widget> children,
  EdgeInsetsGeometry? padding,
  double? height,
  GlobalKey<FormState>? formKey,
  VoidCallback? onFinished,
  bool isDismissible = true,
  bool enableDrag = true,
}) async {
  await showDialog(
    context: Get.overlayContext!,
    barrierDismissible: isDismissible,
    builder: (BuildContext context) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: AlertDialog(
          backgroundColor: Colors.transparent,
          contentPadding: padding ?? EdgeInsets.only(top: 30.h, bottom: 65.h, right: 20, left: 20),
          content: Container(
            padding: EdgeInsets.only(top: 30.h, bottom: 30.h, right: 20, left: 20),
            decoration: ShapeDecoration(
              color: Colors.white.withOpacity(0.5),
              shape: RoundedRectangleBorder(
                side: const BorderSide(width: 1, color: Colors.white),
                borderRadius: BorderRadius.circular(19),
              ),
            ),
          //  height: 337.h,
            width: 300.w,
            child: Form(
              key: formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: children,
              ),
            ),
          ),
        ),
      );
    },
  );
  if (onFinished != null) onFinished();
}
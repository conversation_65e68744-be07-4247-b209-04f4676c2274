part of 'lang_cubit.dart';

@immutable
abstract class LangState extends Equatable {
  const LangState();
}

class LangStateInitial extends LangState {
  final Locale lang;
  const LangStateInitial(this.lang);

  @override
  List<Object?> get props => [lang];
}

class ChangeLangState extends LangState {
  final Locale lang;
  const ChangeLangState(this.lang);

  @override
  List<Object?> get props => [lang];
}

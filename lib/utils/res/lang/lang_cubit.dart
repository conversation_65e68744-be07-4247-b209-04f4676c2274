import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../../features/create_campaigns/presentation/controllers/create_ad/create_ad_cubit.dart';
import '../../di/injection.dart';
import '../../hive_helper/hive_helper.dart';

part 'lang_state.dart';

class LangCubit extends Cubit<LangState> {
  LangCubit() : super(LangStateInitial(Locale(instance<HiveHelper>().getLang())));

  static LangCubit get(context) => BlocProvider.of(context);

  // Method to change the language
  changeLang(String langCode, {required BuildContext context}) async {
    instance<HiveHelper>().setLang(langCode: langCode);

    await Get.updateLocale(Locale(langCode));

    print('Language changed to: ${instance<HiveHelper>().getLang()}');

    emit(LangStateInitial(Locale(langCode)));
    CreateAdCubit.get(context).clearCampaignData(context);
    // await Navigator.pushNamedAndRemoveUntil(context, Routes.splash, (route) => false);
  }
}
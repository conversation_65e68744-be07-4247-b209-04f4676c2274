import 'package:dartz/dartz.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../network/errors/failures.dart';

typedef EitherFunction<T> = Either<Failure, T>;
typedef FutureEither<T> = Future<Either<Failure, T>>;

enum TransactionType {
  deposit(color: Constants.mainColor, svgUrl: 'assets/svgs/arrow_down.svg'),
  withdraw(color: Constants.redColor, svgUrl: 'assets/svgs/arrow_up.svg');

  const TransactionType({required this.svgUrl, required this.color});

  final Color color;
  final String svgUrl;
}

enum SocialPlatform {
  // Facebook,
  // Instagram,
  Meta,
  Google,
  Snapchat,
  TikTok;

  List<String> get adsType {
    switch (this) {
      // case SocialPlatform.Facebook:
      //   return ["Feed", "Story", "Reels"];
      // case SocialPlatform.Instagram:
      //   return ["Feed", "Story", "Reels"];
      case SocialPlatform.Meta:
        return ["Feed", "Story", "Reels"];
      case SocialPlatform.Google:
        return ["Search", "Smart", "Video"];

      case SocialPlatform.Snapchat:
        return ["Snap", "Collection", "Explore"];

      case SocialPlatform.TikTok:
        return ["Snap", "Collection", "Explore"];
    }
  }

  String get svgIcon {
    switch (this) {
      case SocialPlatform.Meta:
        return 'assets/svgs/meta.svg';
      // return 'assets/svgs/facebook_icon.svg';

      // case SocialPlatform.Instagram:
      //   return 'assets/svgs/instgram.svg';
      case SocialPlatform.Google:
        return 'assets/svgs/google_icon.svg';

      case SocialPlatform.Snapchat:
        return 'assets/svgs/snapchat.svg';

      case SocialPlatform.TikTok:
        return 'assets/svgs/tiktok.svg';
    }
  }

  Color get color {
    switch (this) {
      case SocialPlatform.Meta:
        return Constants.mainColor;
      // return 'assets/svgs/facebook_icon.svg';

      // case SocialPlatform.Instagram:
      //   return 'assets/svgs/instgram.svg';
      //   return const Color(0xffC3327E);
      case SocialPlatform.Google:
        return Constants.greenColor;

      case SocialPlatform.Snapchat:
        return Constants.darkColor;

      case SocialPlatform.TikTok:
        return const Color(0xffFF004F);
    }
  }
}

extension SocialNavigator on SocialPlatform {
  openAddCampignScreen() {
    // Get.toNamed(
    //   Routes.ADD_CAMPAIN,
    //   arguments: this,
    // );
  }

// openCreateCampigan() {
//   switch (this) {
//     case SocialPlatform.Meta:
//       Get.toNamed(Routes.ADD_FACEBOOK_CAMPAIN);
//       break;
//     case SocialPlatform.Google:
//       Get.toNamed(Routes.ADD_GOOGLE_CAMPIN);
//
//       break;
//     case SocialPlatform.Snapchat:
//       Get.toNamed(Routes.ADD_SNAPCHAT_CAMPAIN);
//
//       break;
//     case SocialPlatform.TikTok:
//       Get.toNamed(Routes.ADD_TIKTOK_CAMPAIN);
//
//       break;
//   }
// }
}

class Constants {
  // static const publishableKey = 'pk_test_51PIU2H051aoyftT0ZwUa8p3hhbcnZKkkCucWgqOfIFVSOHeMK2sbKYnRadSxqBC3Fo7u3D9D9vzZeZRccjytgTck00EFLPyRwa';
  // static const secretKey = 'sk_test_51PIU2H051aoyftT0N67ylzsl4oJ41jyhq8Akszt2RmqBL9pEbef9ay4XMoF4sl3fe0NBZX11aAa8iYUWnHxBCXMG00ld8LhcWd';

  static const publishableKey =
      // 'pk_test_51P915uP4sHKp61mYCOYnxFzet79BHvRUqHyzpNfwvblTHQ9RhwBEymdcUQh2QSgAD5fQsmi7ZK4hTJuDNqGaTAnk00HMzas8q1';

      "pk_live_51P915uP4sHKp61mYBp5KOexcqujxRFbycuBucHiCWUpaYa3R3EFHxUWH9zR7AeXXzjOpXW8ENbH2lFkHCMt4L1QB00TE1MHMhP";

  // 'pk_test_51P915uP4sHKp61mYCOYnxFzet79BHvRUqHyzpNfwvblTHQ9RhwBEymdcUQh2QSgAD5fQsmi7ZK4hTJuDNqGaTAnk00HMzas8q1';
  static const secretKey =
      // "sk_test_51P915uP4sHKp61mYmQXrOLVMX7G8DtkfKcUWvM0TscBV1ize0j4F4ZKwc1ATnb6uWn2GNB2WGFcU4W6sCx9Xjuon00Yt34ncqc";
      "***********************************************************************************************************";

  // 'sk_test_51P915uP4sHKp61mYmQXrOLVMX7G8DtkfKcUWvM0TscBV1ize0j4F4ZKwc1ATnb6uWn2GNB2WGFcU4W6sCx9Xjuon00Yt34ncqc';
  static String clientKey = 'aw2nxlvd2wg2dafu';
  static String redirectUri =
      'https://www.dvadsmanager.devdigitalvibes.com/en/';
  static String scope = 'user.info.basic';
  static String tikTokAppId = '7296701670581749765';

  static GlobalKey<ExpansionTileCustomState> expansionTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> accountsTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokAdGroupTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> pagesTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> adSetExpansionTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokAdTileKey =
      GlobalKey<ExpansionTileCustomState>();

  static GlobalKey<ExpansionTileCustomState> callToActionKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> adCreativeExpansionTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> adExpansionTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokAccountsTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokPagesTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  static GlobalKey<ExpansionTileCustomState> optimizationKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> savedAudienceKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> phoneNumbersKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> snapChatOptimizationKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokOptimizationKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> tiktokExistingCampaignKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> billingKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> customIdentityTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> snapChatCampaignTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> snapChatAdSetTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> snapChatAdTileKey =
      GlobalKey<ExpansionTileCustomState>();
  static GlobalKey<ExpansionTileCustomState> snapChatReviewTileKey =
      GlobalKey<ExpansionTileCustomState>();

  // static GlobalKey<ExpansionTileCustomState> customIdentityTileKey =
  // GlobalKey<ExpansionTileCustomState>();
  static const String mainFont = 'Karma';
  static const String arabicFont = 'Cairo';

  static const mainColor = Color(0xff0977FF);
  static const darkColor = Color(0xff0B0F26);

  static const secondColor = Color(0xff0977FF);
  static const textColor = Color(0xFFB8B8B8);
  static const primaryTextColor = Color(0xFF06398A);

  //static const darkColor = Color(0xff0B0B0B);

  static const greenColor = Color(0xff34A853);
  static const greenTextColor = Color(0xff2BCA28);
  static const whiteGreyColor = Color(0xffFAFAFA);
  static const shadowColor = Color(0x00000040);

  static const redColor = Color(0xffFD2D54);
  static const darRedColor = Color(0xff7E1212);
  static const lightRedColor = Color(0xffFFD5D5);

  static const lighterGray = Color(0xffF5F5F5);
  static const lightGray = Color(0xffE1E1E1);
  static const gray = Color(0xffA7A7A7);
  static const darkGray = Color(0xff8F8E94);
  static const darkerGray = Color(0xff565656);
  static const backgroundCardsGray = Color(0xffF4F3F3);

  // static const tttttt = Color(0xFFF6BA00);

  static const defGradient = LinearGradient(
    begin: Alignment(-0.88, -0.48),
    end: Alignment(0.88, 0.48),
    colors: [Color(0xFF296AEB), Color(0xFF0B0F26)],
  );

  static const secGradient = LinearGradient(
    colors: [Color(0xFFF6BA00), Color(0xFFFF006F)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  static const unableGradient = LinearGradient(
    colors: [gray, darkGray],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  static const mainAndWhiteShadow = [
    BoxShadow(
      color: mainColor,
      blurRadius: 10,
      spreadRadius: -5,
      offset: Offset(5, 5),
    ),
    BoxShadow(
      color: Colors.white,
      blurRadius: 25,
      offset: Offset(-5, -5),
    ),
  ];
  static const blackAndWhiteShadow = [
    BoxShadow(
      color: Colors.black45,
      blurRadius: 10,
      spreadRadius: -5,
      offset: Offset(5, 5),
    ),
    BoxShadow(
      color: Colors.white,
      blurRadius: 25,
      offset: Offset(-5, -5),
    ),
  ];
  static final mainShadow = [
    BoxShadow(
      color: mainColor,
      blurRadius: 12.r,
      spreadRadius: -5.w,
      offset: const Offset(0, 4),
    ),
  ];
  static final selectedShadow = [
    BoxShadow(
      color: mainColor,
      blurRadius: 12.r,
      spreadRadius: -5.w,
      offset: const Offset(0, 0),
    ),
  ];
  static final unSelectedShadow = [
    const BoxShadow(
      color: Color(0x3D000000),
      blurRadius: 13.93,
      offset: Offset(0, 0),
      spreadRadius: -3.80,
    )
  ];

  static const dateFormat = 'dd-MMM-yyyy';
}

class ApiConstants {
  static const imagesPath =
      'https://adsmanager.devdigitalvibes.com/public/assets';
  static const svgsPath = 'http://adsmanager.devdigitalvibes.com.ae/';

  //User
  static const getUserUrl = 'get_user_details';

  static const updateUserUrl = 'update_user_details';

  static String registerUrl = 'sign_up';

  static String loginUrl = 'login';

  static String updateFcmUrl = 'store_fcm_token';

  /// Meta
  static String createPostUrl = 'create_post';

  static String createCampaignUrl = 'create_campaign';

  static String createAdSetUrl = 'create_adset';

  static String forgetPassword = 'password/forgot-password';

  static String validateOtp = 'verifyOtp';

  static String sendEmail = 'send-email-verification';

  static String sendEmailVerify = 'email-verification';

  static String resetPassword = 'password/reset';

  static String createAdImageUrl = 'create_adimage';

  static String createAdVideoUrl = 'create_advideo';

  static String createAdUrl = 'create_ad';

  static String createAdCreativeUrl = 'create_adcreative';

  static String getAccountsUrl = 'get_accounts';

  static String getIGAccountUrl = 'get_insta_accounts';

  static String getAdAccountUrl = 'ad_account';

  static String searchWithTextUrl = 'keyword_search';

  static String searchWithClassUrl = 'class_search';

  static String getAllPostsUrl = 'get_all_posts';

  static String getAllCampaignsUrl = 'get_campaigns';

  static String getAllObjectives = 'objective';

  static String getAllAdsetsUrl = 'get_adsets';

  static String getAllCampaignsInsights = 'get_all_campaigns_insights';

  static String connectWithSocialInsights = 'connect_with_social';

  static String getLiveAdsURL = 'live_ads';

  static String getNonLiveAdsURL = 'live_ads';

  static String changeAdStatusUrl = 'change_ad_status';
}

class ProjectConstants {
  // static final languages = [
  //   const Option(id: 1, value: 'en'),
  //   const Option(id: 2, value: 'ar'),
  // ];

  // static const List<Map<String, dynamic>> social = [
  //   {"title": "facebook", "icon": ""},
  //   {"title": "instgram", "icon": "assets/svgs/instgram.svg"},
  //   {"title": "google", "icon": "assets/svgs/google_icon.svg"},
  //   {"title": "snapchat", "icon": "assets/svgs/snapchat.svg"},
  //   {"title": "tiktok", "icon": "assets/svgs/tiktok.svg"},
  // ];
  static const List<String> leads = [
    "Impressions",
    "Clicks",
    "Conversions",
    "Conversion value",
    "Cost",
    "CTR",
    "CPA",
    "ROAS",
  ];
  static const List<String> btnExamples = [
    "Download",
    "Shop Now",
    "Sign Up",
    "Contact Us",
    "Apply now",
    "Book Now",
    "Apply now",
    "Play game",
    "Watch now",
    "Read more",
    "View now",
    "Get quote",
    "Order now",
    "Install now",
    "get showtimes",
    "interested",
    "subscribe",
    "get tickets",
    "pre-order now",
    "Visit store",
    "Brand lift study",
    "view profile",
  ];
}

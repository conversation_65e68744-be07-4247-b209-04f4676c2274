import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../colors.dart';

abstract class MyThemes{

  static TextTheme get _textThemeLight {
    return ThemeData.light().textTheme.apply(fontFamily: 'Cairo');
  }

  static TextTheme get _textThemeDark{
    return ThemeData.dark().textTheme.apply(fontFamily: 'Cairo');
  }

  static ThemeData lightTheme = ThemeData.light().copyWith(

    brightness: Brightness.light,
    scaffoldBackgroundColor: AppColors.lightColor,
    drawerTheme: const DrawerThemeData(
      backgroundColor: AppColors.lightColor,
    ),
    appBarTheme: AppBarTheme(
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.light,

        // statusBarColor: AppColors.mainColor
      ),
      backgroundColor: AppColors.lightColor,
      elevation: 0,
      iconTheme: const IconThemeData(
        color: AppColors.blackColor,
      ),
      titleTextStyle: const TextStyle(fontSize: 23,color: AppColors.blackColor,fontFamily: "KARMA"),
      toolbarTextStyle: _textThemeLight.titleMedium,
    ),
    textTheme: _textThemeLight,

    primaryColorLight: AppColors.lightColor,
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.mainColor),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.darkColor,
      ),
    ), checkboxTheme: CheckboxThemeData(
 fillColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return AppColors.mainColor; }
 return null;
 }),
 ), radioTheme: RadioThemeData(
 fillColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return AppColors.mainColor; }
 return null;
 }),
 ), switchTheme: SwitchThemeData(
 thumbColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return AppColors.mainColor; }
 return null;
 }),
 trackColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return AppColors.mainColor; }
 return null;
 }),
 ),

  );

  static ThemeData darkTheme = ThemeData.dark().copyWith(
    brightness: Brightness.dark,
    drawerTheme: const DrawerThemeData(
      backgroundColor: AppColors.darkColor,
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.mainColor),
    appBarTheme: AppBarTheme(
      toolbarTextStyle: _textThemeDark.titleMedium,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        // statusBarColor: AppColors.mainColor
      ),
      backgroundColor: AppColors.darkColor,
      elevation: 0,
      iconTheme: const IconThemeData(
        color: Colors.black,
      ),
      titleTextStyle: const TextStyle(fontSize: 23),
    ),
    textTheme: _textThemeDark,

    scaffoldBackgroundColor: AppColors.darkColor,
    primaryColorDark: AppColors.darkColor,
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: AppColors.darkColor,
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: Colors.white,
      ),
    ),
    // toggleableActiveColor: AppColors.mainColor,
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.all(AppColors.mainColor),
      trackColor: MaterialStateProperty.all(
        AppColors.mainColor.withOpacity(0.6),
      ),
    ),

  );
}
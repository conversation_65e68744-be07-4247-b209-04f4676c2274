part of 'theme_cubit.dart';

@immutable
abstract class ThemeState extends Equatable {
 const ThemeState();
 @override
 List<Object?> get props => [];
}

class ThemeStateInitial extends ThemeState {
  final ThemeData themeData;
  final ThemeMode themeMode;

  const ThemeStateInitial(this.themeData, this.themeMode);
}

class ToggleThemeState extends ThemeState {
  final ThemeData themeData;
  final ThemeMode themeMode;
  const ToggleThemeState(this.themeData, this.themeMode);

  @override
  List<Object?> get props => [themeData];

  ToggleThemeState copyWith({
    required ThemeData themeData,
    required ThemeMode themeMode
  }) {
    return ToggleThemeState(
        themeData,themeMode
    );
  }

}


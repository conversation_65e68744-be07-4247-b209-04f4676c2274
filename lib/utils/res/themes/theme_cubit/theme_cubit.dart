import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../di/injection.dart';
import '../../../hive_helper/hive_helper.dart';
import '../themes.dart';

part 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit()
      : super(ThemeStateInitial(
            instance<HiveHelper>().getModeState()
                ? MyThemes.darkTheme
                : MyThemes.lightTheme,
            instance<HiveHelper>().getModeState()
                ? ThemeMode.dark
                : ThemeMode.light));

  static ThemeCubit get(context) => BlocProvider.of(context);

  toggle(bool isDarkMode) {

    print('toggle');
    if (isDarkMode) {
      instance<HiveHelper>().setModeState(isDark: !isDarkMode);
      emit(ToggleThemeState(MyThemes.lightTheme, ThemeMode.light));
      // isDark = false;
    } else {
      instance<HiveHelper>().setModeState(isDark: !isDarkMode);
      emit(ToggleThemeState(MyThemes.darkTheme, ThemeMode.dark));
      // isDark = true;
    }
  }
}

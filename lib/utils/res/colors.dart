import 'package:flutter/material.dart';

abstract class AppColors{

  static const  mainColor = Color(0xFF06398A);

  static const  phoneColor = Color(0xFFD1DCF6);

  static const  iconColor = Color(0xFFB6B6B6);


  static const  secondColor =  Color(0xFF006BFF);

  static const  mainColorLight =  Color(0xFFB6B6B6);


  static const  tealColor =  Color(0xFF339073);

  static const  tealLightColor =  Color(0xFF71C1AE);

  static const  iconBottomColor =  Color(0xFF9B9B9B);

  static const  shadowColor =  Color(0x660B2E63);


  static const  lightColor = Color(0xFFF2F1F1);
  static const  darkColor = secondColor;

  static const textHintColor = Color(0xff9298A9);

  static const borderColor = mainColor;

  static const showNumbersColors = Color(0xE5FFFFFF);

  static const selectToggleColors = Color(0xFFB6B6B6);

  static const whiteColor = Color(0xFFEFF1EE);

  static const white = Colors.white;


  static const blackColor = Color(0xff001B29);
  static const grayColor = Color(0xff878787);
  static const grayLightColor = Color(0xffDCDCDC);
  static const navInColor = Color(0xFF8891A5);


  static const successColor = Color(0xff76C37C);

  static const warningColor = Color(0xffCE8C4B);
  static const errorColor = Color(0xffC74438);
  static const colorPlaceHolder = Color(0xffC4C4C4);

  static const green = Color(0xFF28AE0F);

  static const photoCover = Color(0xffCCCCCC);

  static const yellow= Color(0xffFCCC23);

  static const textColor = Color(0xFF6F7984);

  static const hint = Color(0xFFA2A7B4);

  static const greyText = Color(0xFFA3A7B5);
  static const greyButton = Color(0xFFE4E5EE);

  static const textQuestion = Color(0xFF6F7983);

  static const mainLight = Color(0xFFDAEDFF);

  static const cardColor = Color(0x33000000);

  static const sloganColor = Color(0xFF6E6E6E);

  static var disabledToggleColors = const Color(0xFFD3D3D3);

  static var salaryColors = const Color(0xFF5B5B5B);

  static var enableChipsColor = const Color(0xFFB6B6B6);


  static var disapleChipsColor = const Color(0xFFE6E6E6);

  static const myMessageColor = Color(0xFF5B5B5B);
  static const anotherMessageColor = Color(0xFFE7E7E7);




  static Color getColorFromHex(String? hexColor) {
    if (hexColor == null) {
      return Colors.transparent;
    }
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "0xFF$hexColor";
    }
    return Color(int.parse(hexColor));
  }


}



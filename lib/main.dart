import 'dart:convert';
import 'dart:ui';

import 'package:ads_dv/firebase_options.dart';
import 'package:ads_dv/utils/functions.dart';
import 'package:ads_dv/utils/res/constants.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:ads_dv/utils/app/my_app.dart';
import 'package:ads_dv/utils/di/injection.dart';
import 'package:ads_dv/utils/hive_helper/hive_helper.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:showcaseview/showcaseview.dart';

import 'utils/notification_helper/local_notification_helper.dart';
import 'utils/notification_helper/notification_helper.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message,
    {BuildContext? context}) async {
  await NotificationsHelper.initNotification();
  LocalNotificationHelper.onSelectHandler(jsonEncode(message.data));
}

void main() async {
  Stripe.publishableKey = Constants.publishableKey;
  // Stripe.merchantIdentifier = 'merchant.com.dv.ads.manager';
  WidgetsFlutterBinding.ensureInitialized();
  await initAppInjection();
  await Hive.initFlutter();
  await instance<HiveHelper>().initHiveBoxes();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await FirebaseMessaging.instance.setAutoInitEnabled(true);

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  await NotificationsHelper.initNotification();

  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);

    return true;
  };

  await ScreenUtil.ensureScreenSize();
  // await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  //
  // await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  // await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
  );
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
  );
  SystemChrome.setSystemUIChangeCallback(
    (systemOverlaysAreVisible) async =>
        await Functions.hideStatusBar(systemOverlaysAreVisible),
  );

  runApp(
    ShowCaseWidget(
      onFinish: () => print('Showcase finished'),
      builder: (context)=> const MyApp(),
    ),
  );
}

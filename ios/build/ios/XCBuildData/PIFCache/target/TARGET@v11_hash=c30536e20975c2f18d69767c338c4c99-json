{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981fcf18166e4a35b0f2376b7989da2ca3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e985917b713e1b1d91e2700eff6f735654b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d738c8afb1f6ceb1298048fba602637", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/DKImagePickerController/DKImagePickerController.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9815b52bb56d17a8417da24637abd78fd9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d738c8afb1f6ceb1298048fba602637", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/DKImagePickerController/DKImagePickerController.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98e7117c1dd1484123945587c4fb9ea135", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d86b255a40a8aae807d76c18ec38dfb2", "guid": "bfdfe7dc352907fc980b868725387e98984b62ec23747da40e05177aea0ff704"}], "guid": "bfdfe7dc352907fc980b868725387e98978f6854e6e9cdecfd6db0afa66dff75", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98814a2d0101e5e0684e4c42881b58da20", "guid": "bfdfe7dc352907fc980b868725387e981dde49a295caaa6dbf0fab1e8555fb97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792420dff1c429cd0d1cb8fb16065033", "guid": "bfdfe7dc352907fc980b868725387e98694a5b2166254ecd3402cc2e9885cec3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850222978151c5a955f8186aba767504b", "guid": "bfdfe7dc352907fc980b868725387e98758104f6e8c02f7727ba9d518830462c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629519bafe5db782d68e097ccef169a0", "guid": "bfdfe7dc352907fc980b868725387e9809eb93fd04e990e6619bb8173c72bacc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d80eed9783643f3acad8c453ec817ab7", "guid": "bfdfe7dc352907fc980b868725387e98ef64408ecd3517b887fadbdb15e2dce9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98146ec503b000b11e9520a6b29c510cea", "guid": "bfdfe7dc352907fc980b868725387e98600fcbc6e7fe4562840f7b4b9143694d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8cdf7e707fdfe83bc8e637fcd8e2fb1", "guid": "bfdfe7dc352907fc980b868725387e98602c4f557ab929bc69323140933c8df0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd856184914decfa749b061ed49ecbb3", "guid": "bfdfe7dc352907fc980b868725387e986d4bd28e138b1c168263c2d4ed382d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a12b2e106637f18bb943dd84178102", "guid": "bfdfe7dc352907fc980b868725387e98ec1ee2c33550357d12b2f2f0183e14b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd77ae80f986990870c45311cf0dca6f", "guid": "bfdfe7dc352907fc980b868725387e9803429d1cb4dbe6d39353520bc32a7afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98385b640e602aec54be7fdfcac49da34f", "guid": "bfdfe7dc352907fc980b868725387e98b133e1309b2d7ad3bb93345add7afef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894808d3f6cc7a542d74987b6432363da", "guid": "bfdfe7dc352907fc980b868725387e986f2fe3a36e4f175b00233ba8764f5987"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980493bc0589255a167800c49a75aca47a", "guid": "bfdfe7dc352907fc980b868725387e98ffb3d4bd324b2e2df9012d56f2c5c06d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0862839448bec4563af89d7c0a67441", "guid": "bfdfe7dc352907fc980b868725387e986817d0f921e7ca49605dc4556d028688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859bf82c68cc5c2a5b6beb0c1a1765bda", "guid": "bfdfe7dc352907fc980b868725387e98a5280f9063c330d412f64db174cd2f7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f720c6aeaa2ee32e15c4539dcec9768", "guid": "bfdfe7dc352907fc980b868725387e98662f6e8633c5cde213c994159ba47cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a9d30d7996b629576744734a320e84", "guid": "bfdfe7dc352907fc980b868725387e9835074fd43507f96333372e96eb8853b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854639089c5d310c4ac7544efb183897d", "guid": "bfdfe7dc352907fc980b868725387e98b1d4f5900d6454349b08e67f53c2412d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d63a02737ea80ea2b83a44a61c9d985", "guid": "bfdfe7dc352907fc980b868725387e9893a035eca33a7e30056bc61abc695641"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f859a42c92e4ec1b90843745cc6a741b", "guid": "bfdfe7dc352907fc980b868725387e989c84a65e6abacc7ce0f5efd9570145fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98484cede19d0ccf576e5e7574e42638a4", "guid": "bfdfe7dc352907fc980b868725387e9849f7c258a3334f7c39206df1f82b78f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827482e0985927f1a1455f51639d8b205", "guid": "bfdfe7dc352907fc980b868725387e980c436646b83f955d410ac9119f52b407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bbd12f3745b81d2d8bfb2886a4b53d0", "guid": "bfdfe7dc352907fc980b868725387e98eee136b9a7d8506deed30cd846e25f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e578b07c26d4449a0e45784783a315", "guid": "bfdfe7dc352907fc980b868725387e9828116237d423b657c144d7a0a4a543d0"}], "guid": "bfdfe7dc352907fc980b868725387e985cd989f4ded4c48508e3848de0f78f3d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98236f78d800435006cb31cdff592b7909", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98d47b33a7f83b5f89870b79dbb49ed2dd", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/DKImagePickerController/DKImagePickerController.modulemap", "${PODS_ROOT}/Headers/Public/DKImagePickerController/DKImagePickerController-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "EF0469EAA8B18AA2D5F865FA469C1FEC", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/DKImagePickerController-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/DKImagePickerController/DKImagePickerController.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/DKImagePickerController/DKImagePickerController-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "libDKImagePickerController.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
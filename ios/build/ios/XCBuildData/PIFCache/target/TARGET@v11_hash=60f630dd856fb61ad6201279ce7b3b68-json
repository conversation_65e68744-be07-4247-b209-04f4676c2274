{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ecb25e01d54ed1485cf508dd4327d43d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98562b118f2eaf5fc3bf1f2141f7ab1695", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982aeed68cbaabf61234a65fb7cec047ac", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/DKPhotoGallery/DKPhotoGallery.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9804e4e3591d9665dff5bc213034fd53ec", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982aeed68cbaabf61234a65fb7cec047ac", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/DKPhotoGallery/DKPhotoGallery.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e987590c7a38a1effa32849b7ade86dee52", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f905bdd5f5c67a7126b3fa45bd5cb64b", "guid": "bfdfe7dc352907fc980b868725387e987931d4ca726d15143c01f88c166c0821"}], "guid": "bfdfe7dc352907fc980b868725387e9845f2cbc2b6777adc08f6bf0a9473f5dc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981db5e7b8c03ecaddfeef180e940a39f3", "guid": "bfdfe7dc352907fc980b868725387e98c1c2006f779ad6bf05eefa721a56885d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a594ce9cfab86c630a29f3c06360b92", "guid": "bfdfe7dc352907fc980b868725387e98bf1ca2e44becac6467c9e74309c864a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826fc2a1970bc55e03d642e1e1bce2b60", "guid": "bfdfe7dc352907fc980b868725387e98f0552a42df8c99adef582a4e28641cab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dc12ce9d6ed1cc1a9834a7f48e88fd4", "guid": "bfdfe7dc352907fc980b868725387e98b8a31af48a9a2089e5069caf01e9acf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814374b2be8d7d8f39d4dbdafa466b2cd", "guid": "bfdfe7dc352907fc980b868725387e984ac1945d2ce4cfb1ec0a559277a1150b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cea38ee99f79a87e12383ab3a5c3b78", "guid": "bfdfe7dc352907fc980b868725387e98de5c2ae08edf5f1ef04e160072c18ebf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c74140e0db1ded67940a482b167d7c0", "guid": "bfdfe7dc352907fc980b868725387e98893828a7ca7d24a92ae8f765adc7d687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33f5eba63d941a39479560d0d7c10f5", "guid": "bfdfe7dc352907fc980b868725387e98327927f6f909c6b5c7579607e572c69c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983990034d5ec5f16bdb6fbe077b85a6c1", "guid": "bfdfe7dc352907fc980b868725387e98e302f82c97f4f426d5f6c0331d3077e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba7451ce2d044fb299ca5615b0daf5e", "guid": "bfdfe7dc352907fc980b868725387e98145ee868c306068262522f07efb15fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e1852bd39041e00b5df4c35a037870", "guid": "bfdfe7dc352907fc980b868725387e98bc6282915c7368696e9cb3fdf3e28937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a5420f1eda5e217f4a467d2868a7b17", "guid": "bfdfe7dc352907fc980b868725387e984cd7423a08250b67d5d159700abd52d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ef5963cc4614d49334a9b84edc26a2", "guid": "bfdfe7dc352907fc980b868725387e98f165998528fe1d71696ccc124208e6e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826dbd1e99b3b2e0cddf3d9f4b9ab3aa3", "guid": "bfdfe7dc352907fc980b868725387e98147499d43a98f58532bd6e6b7e8e25c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981822b32d262ad838ca81e91870dc51fe", "guid": "bfdfe7dc352907fc980b868725387e988e8c4a8cd83ed48690a4456243b62a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4b47d3176310ae2d1ceeb13e081d85", "guid": "bfdfe7dc352907fc980b868725387e98a55ed885ad1cb0ed53615fc4569985d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15592ca3b5b42c2381a7ec7e6e91bb3", "guid": "bfdfe7dc352907fc980b868725387e9822ba03f8d75f00d561ed6f9905ab8406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831dcb049610ddb63f5aeec8ce7c331b9", "guid": "bfdfe7dc352907fc980b868725387e9836f35cb9ef92771d1fea6949234bd9ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5de62cb2f4748faff7a6cad4d32a8a", "guid": "bfdfe7dc352907fc980b868725387e98b762ecb7b733cf1744f8a88325d884a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f01de31f4c6a29a4d6bf7ab1251df92", "guid": "bfdfe7dc352907fc980b868725387e98130656dcd4d2e75b0123763d0d34afc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497f30f84cab829337215b6af255c733", "guid": "bfdfe7dc352907fc980b868725387e9883e7e1c8cce24cd546583c7b70a35124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbe1be7e5f4902e0dd522179c680147", "guid": "bfdfe7dc352907fc980b868725387e98f0a026816ba5ddbdf4d36ef92c8f9ac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807fd5f82694a381c418d6e0160a9aaac", "guid": "bfdfe7dc352907fc980b868725387e98cc230fbbacc72ce980abfbd6de979553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b317d8bff11dea70d87bc0a55ae113", "guid": "bfdfe7dc352907fc980b868725387e9809bede7cccf7467e6d712c2faf6947c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9eed560a2e3a1230d5c826de44e00e4", "guid": "bfdfe7dc352907fc980b868725387e980a334c3b9fed2ad930994b7b5fa76e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354b8a283bc19a83f7429fbab146120d", "guid": "bfdfe7dc352907fc980b868725387e989320273f8c1d84e01b6eb72825b7f742"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983524ed05be086a5de54ef41050d92423", "guid": "bfdfe7dc352907fc980b868725387e98a67e6eb460964ac52279cd0a68fa8a9a"}], "guid": "bfdfe7dc352907fc980b868725387e981c8106631971995f4c014501284eaec0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9857b32f2c3b9417d06641a8a22bb2e4d1", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e982f2f40fc319de6c701fd7e12c87822eb", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/DKPhotoGallery/DKPhotoGallery.modulemap", "${PODS_ROOT}/Headers/Public/DKPhotoGallery/DKPhotoGallery-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "83714E5D935DCA6DFCA9177ABEB27FEB", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/DKPhotoGallery-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/DKPhotoGallery/DKPhotoGallery.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/DKPhotoGallery/DKPhotoGallery-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "libDKPhotoGallery.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
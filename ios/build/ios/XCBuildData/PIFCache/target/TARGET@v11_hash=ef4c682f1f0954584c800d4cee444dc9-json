{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f71ac57f01f7a39ccc1fd7c549a94a6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "MODULEMAP_FILE": "Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfigInterop", "PRODUCT_NAME": "FirebaseRemoteConfigInterop", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98c7365b3fab7b04967cefbf74b2201f93", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988287219edb280e7e5193c9836f44ecd6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "MODULEMAP_FILE": "Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfigInterop", "PRODUCT_NAME": "FirebaseRemoteConfigInterop", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98b6b71d612c79abc375cf0781c11cdf1a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988287219edb280e7e5193c9836f44ecd6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "MODULEMAP_FILE": "Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfigInterop", "PRODUCT_NAME": "FirebaseRemoteConfigInterop", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9839f3eac3feb5f73288707e3a8dcc3eea", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9816bce3dd791aba9a3c1dd8ea5173da64", "guid": "bfdfe7dc352907fc980b868725387e989670a7e2d85bfdfe22b4e7d9e932f740"}], "guid": "bfdfe7dc352907fc980b868725387e98aade8806f4b80a665a4de87bc51abd24", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98238d3f4666268de159808510a17befe3", "guid": "bfdfe7dc352907fc980b868725387e98f31c0911b7b0f97edbbbd738729b58e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a33c63aaf666d1608c61db3b519e8ded", "guid": "bfdfe7dc352907fc980b868725387e9879734397f52dec66beacbacf0e556f15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a6868448f2a1a05fd5c39e6aa569e2", "guid": "bfdfe7dc352907fc980b868725387e9838d11caf7daae828ad9048e7bbd8a0e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a185a37c268db52be7d4b70079b2d8", "guid": "bfdfe7dc352907fc980b868725387e98aaa5cf6d4eb54ce3aae630e3ea232e7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbad5fd3ff8eb6e3cba187ed1399127", "guid": "bfdfe7dc352907fc980b868725387e982e2367e11e6f1317a255efc3c6d468ac"}], "guid": "bfdfe7dc352907fc980b868725387e98b2ee8b5b902eb5dadbe982faf86aa524", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9855729d340804e1c968571d0c199d1656", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98d64f3fd87efa33e717e6d9f69146e83e", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.modulemap", "${PODS_ROOT}/Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "A62EA815E98A7E12A22ED2F4FB4A78A6", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfigInterop-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9832dc279ba06c877df1b7d9b999af5314", "name": "libFirebaseRemoteConfigInterop.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9801ee79288516aebb4f0e86e62799d7c3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Headers/Private/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e9829cf8a7c6a1a0ab2631a9d1a13ed597c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c457846ef6bc73d705cc9c9e11d331c4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Headers/Private/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98f0045d4c586cf87c8b4b5dc01052d81f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c457846ef6bc73d705cc9c9e11d331c4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Headers/Private/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98e39da803fe13b835dfaf8972a840fc1a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9813aef87fe3bac12760e7a45d51bb9753", "guid": "bfdfe7dc352907fc980b868725387e9886a4bd23e44b2c3510918ea946efa536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d251aefa3a6cacde629e8772a5b52a", "guid": "bfdfe7dc352907fc980b868725387e98bab7a6d023fcdbf67c3ed9fad196f09b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e27731d8870e9d548ce1bf39cb19c0f0", "guid": "bfdfe7dc352907fc980b868725387e98fa5c64052868205d566c91a6fe9e4996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e1da732cd1f370d6be2e031ec5673a0", "guid": "bfdfe7dc352907fc980b868725387e9820f5b048ea4ade9d9323edaa97ae64c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ff1298515e55a4502934e76a910520", "guid": "bfdfe7dc352907fc980b868725387e98aee13f44dbc98fa4094b3272ccbe9139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98870cbe4579ce9992e60ec2f2c0a15dfa", "guid": "bfdfe7dc352907fc980b868725387e9835add00610dbcbd79970222aa88314e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bfaa5b7db9cf1ec13590503190ff598", "guid": "bfdfe7dc352907fc980b868725387e98d2ff81b28333a35324fd83bf676c5fb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566b32e6b897a686df006834f4b2ca1c", "guid": "bfdfe7dc352907fc980b868725387e98722f06e24804c2dc0442a3149dec8766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e37b4cdf033822959545e0c7e285898d", "guid": "bfdfe7dc352907fc980b868725387e9861dc2a598a2e5b5a0e1c5dc2bb6e13f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f80be7c64063e9df8332627df3c3f7e3", "guid": "bfdfe7dc352907fc980b868725387e98f8645575e7b647acf6d3b2c9cd97f28b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa265851e57d6b18a9ef5adf3f7d2fe2", "guid": "bfdfe7dc352907fc980b868725387e98cd3bcea46196158f322f4946a6a59c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98029733f539f436e20d6f81b89e0f4fbf", "guid": "bfdfe7dc352907fc980b868725387e9838fb86692811ca1ade8407f16aaf12b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ace7b586917013521c31ea498320389", "guid": "bfdfe7dc352907fc980b868725387e988a41d50062953374a085416b5c201c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809aed0d6662ca3c28783f0955f87e2dd", "guid": "bfdfe7dc352907fc980b868725387e9824ed52c96f53801a86fce7e6e262ca2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dd3299facb65242b18cea53463bfcf9", "guid": "bfdfe7dc352907fc980b868725387e98f6f6f3d2757bde72ade643a2f26b6f78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfac7a72cb861d300bec7ae775a580a4", "guid": "bfdfe7dc352907fc980b868725387e98f57c8cf7288e81d27a255e316b6fc084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618fda43a01f7e45b244c4ff33976029", "guid": "bfdfe7dc352907fc980b868725387e982436c8245ed537d90288c67d2d780eef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890eb6e97982ac582ecfeb0e5160c7b30", "guid": "bfdfe7dc352907fc980b868725387e98a2e5194980bd88e9d0c5c9f63b65856f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a423e846f3232d88ac18420ac9caf6f", "guid": "bfdfe7dc352907fc980b868725387e98258a0c4cbccb77369bf19ad98c14c38c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c2ff50c092f0c84a4bd1799c8af784", "guid": "bfdfe7dc352907fc980b868725387e984bab256814b4ed97bdfa93dfd9ee8a45"}], "guid": "bfdfe7dc352907fc980b868725387e9861a90cdb2685598810424ff58feff824", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986adf64b7c9bd14e8a459ace0036a07d8", "guid": "bfdfe7dc352907fc980b868725387e98d4e4f0e785f07042786e465bcb8a9b0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd8d4ee9adff22c2bee3c1ac89b11649", "guid": "bfdfe7dc352907fc980b868725387e9848a1d68b8a0d87a4f8f61f1ba93e157d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b550d8b03fb59780d3fce4df2a965fa5", "guid": "bfdfe7dc352907fc980b868725387e9837010acdad6b201f6b654de54e55c409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa6a7dc314f7ae3515d4539c4a3e9d8", "guid": "bfdfe7dc352907fc980b868725387e9800f7ef5985e2ee6a01a407e28714a037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855758655bd7c38670bf9c6e3f8453704", "guid": "bfdfe7dc352907fc980b868725387e98a1ded7a659f406c486e215313360933b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980264821944f0193e33072280ae5f9d35", "guid": "bfdfe7dc352907fc980b868725387e98ad563bb05b71d97a97b3f83cb8ea9ce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883901d4ea3deff2e734f0a3966485d6b", "guid": "bfdfe7dc352907fc980b868725387e9800bf85b83f318dad6ab8b33566a1b907"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854447fb7752115ca50cda31d4e37f3ab", "guid": "bfdfe7dc352907fc980b868725387e9846d3e765b7638ea4e07d67cd2a6a7292"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a1f19aa15e8c8e4353d82f482cbba2", "guid": "bfdfe7dc352907fc980b868725387e986a0a64d1ab1fa3359df0331807819b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c58889b3c2e6ad35c10ab270366b7768", "guid": "bfdfe7dc352907fc980b868725387e98494e7de904a79bf313f7dc3b93e54f06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f153a24ed41ae69635f246dd8bf937d", "guid": "bfdfe7dc352907fc980b868725387e98d2890dd9c2b2182876e1d5ed1da8f8b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70fd36e4140de016ab7948381f72474", "guid": "bfdfe7dc352907fc980b868725387e98c83b9311c8dee8ccb1b1d896bdbdecd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ec5e60e58a99109091809c984393c4", "guid": "bfdfe7dc352907fc980b868725387e982cba80fffd639f17cccf04406906a537"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710325a2fd2db03ff8a3b3a25bed6e57", "guid": "bfdfe7dc352907fc980b868725387e98625732b7bb3ab3514332851df668800c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a0281997e0b9a48cb03d56c6e3088f5", "guid": "bfdfe7dc352907fc980b868725387e98c97838cdf802167bf4c02e3030bfbb89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98530035cac4cc270cdbcf3fc32ea65b06", "guid": "bfdfe7dc352907fc980b868725387e98e7371b5525069649a1979d13d111cf5a"}], "guid": "bfdfe7dc352907fc980b868725387e98c468dbaabee62059132f00ed9d62e0dd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981e582af61cce7040fcf52e21c75814c4", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "libgoogle_maps_flutter_ios.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98683f9eda8f016c3c24043f7fbdc121af", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/wakelock_plus/wakelock_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/wakelock_plus/wakelock_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "wakelock_plus", "PRODUCT_NAME": "wakelock_plus", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e985e886232232a2f0e3c6241e51521ff0a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883c7fe22cb16180e2ee92dd635065c63", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/wakelock_plus/wakelock_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/wakelock_plus/wakelock_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "wakelock_plus", "PRODUCT_NAME": "wakelock_plus", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e987939e78645049706eff2fcbfd38e6d0a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883c7fe22cb16180e2ee92dd635065c63", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/wakelock_plus/wakelock_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/wakelock_plus/wakelock_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "wakelock_plus", "PRODUCT_NAME": "wakelock_plus", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98296921918d6ba03c343629b28657dd2f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98833af46b65c6f9302831fb6b1f68c49f", "guid": "bfdfe7dc352907fc980b868725387e9824e30cb204c6b552a20aca6276f911df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919bf65ab313471cefc559742e1dd9a1", "guid": "bfdfe7dc352907fc980b868725387e98c90bb75219ba1d70ca93a9b6f541419e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803ff89be892ce0e5599cb823d4c0e986", "guid": "bfdfe7dc352907fc980b868725387e9869d9b1a0b39d8560845a26bd2189f8c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e541e242124a0574bd12c28e7cd2448", "guid": "bfdfe7dc352907fc980b868725387e989a64b99fe7cc5b63fbbb224e3e0bc5ec"}], "guid": "bfdfe7dc352907fc980b868725387e98df1e2cb89be7ab9f39777efbb4dac716", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c23286a94b5c03fd6acee75a21e433d8", "guid": "bfdfe7dc352907fc980b868725387e985ed62738341df999b3548ac3a4bcf652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eebc9ff0fec6a29e859588353f3a28b6", "guid": "bfdfe7dc352907fc980b868725387e986592c34b24bdbf888445daf4d8f3a5ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f1d9cf3c2d54b93511c6d4ecc229a20", "guid": "bfdfe7dc352907fc980b868725387e98de1bf56f06453e2f0676f8125dcdf3ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e021240a3b49ae9fb88fa78832925bad", "guid": "bfdfe7dc352907fc980b868725387e988fc040a1cb5e0cd8f75dbcab0579d33a"}], "guid": "bfdfe7dc352907fc980b868725387e9807ce726551ba940e7c44fe34e18d6ab0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986d9c72372325939ba5660cf9b858ed4a", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98783e90de49ca9314c826dcf3e387700d", "name": "wakelock_plus-thermal"}], "guid": "bfdfe7dc352907fc980b868725387e985ee86805101bc8fd279e03690a1048af", "name": "wakelock_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98076c34494e4629b9e213e9d6e2a4736f", "name": "libwakelock_plus.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e6eb2758cba289fa304be8a3c8886495", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Private/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98ae7f7257db357617239026688bdd46ac", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981aee31d02248914cc384da235c653eb0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Private/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e980f40f66a27dd58532351081c7417d38a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981aee31d02248914cc384da235c653eb0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Private/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98e58767f3c5b3042a0ade537f95db8f28", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982fee5aac8415182514546d890f1b8424", "guid": "bfdfe7dc352907fc980b868725387e986ee0eba8179e68701b70cad0597eff32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d414d6907cdd63fee7043b4543a07212", "guid": "bfdfe7dc352907fc980b868725387e982984977afd764424e93dcd688d11ddcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c65632be2ee983304a641556dc83942f", "guid": "bfdfe7dc352907fc980b868725387e983ccf4dc2fc77554cfa867e7f29284ade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98030536c5da888a6a835d080a0d66332d", "guid": "bfdfe7dc352907fc980b868725387e987e6862b417e8a95ab23103e84a287e22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f246a4d1bd81608324c68cafef2ca0", "guid": "bfdfe7dc352907fc980b868725387e98c7cfa6c2598815887bd326966cbfc14b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8139b7f049ceb133aee8743d8c6cfcf", "guid": "bfdfe7dc352907fc980b868725387e98491165b5dc0e488b242660e1eabf117e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fe3505d96e3358f786655beed95d9c2", "guid": "bfdfe7dc352907fc980b868725387e986a257c15c8dd798a2d89e64e691210a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb6fd01d9336fa15f9b99feade2aae4a", "guid": "bfdfe7dc352907fc980b868725387e98ac145e3f2c2737dc13db7e1dcdc0eb92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acee726e5fe3888f79cfc99ec2180320", "guid": "bfdfe7dc352907fc980b868725387e986a47b673bb1fee29373f966e8c7cb67b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e304fa8929b4d87da9a10b248a31d999", "guid": "bfdfe7dc352907fc980b868725387e9852df8b820d63c2ffc701af234d908fec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd5633558f6bad1031c18e103719df8", "guid": "bfdfe7dc352907fc980b868725387e98d409b2b6128c72a8b8e1f2c182b3f854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f86e153f7043c7e678edb576aad6bfc", "guid": "bfdfe7dc352907fc980b868725387e98fecc659c78c7fcf422d64ccccb75df22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea816c34947189d401ae67f750ecd41", "guid": "bfdfe7dc352907fc980b868725387e98430576c6c46b1fb5abb23fd1956b5e3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc7e0a6fcce1292dbc5b4e6a1303feb", "guid": "bfdfe7dc352907fc980b868725387e9827fcd46c59017553e5893b276d8e7d57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2dafcdabafb05a46cf91f0eebc4700b", "guid": "bfdfe7dc352907fc980b868725387e9871b8c1d1225590b482927d380eab3c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986247c752b5cf3bebb3ece50cc45b9ca4", "guid": "bfdfe7dc352907fc980b868725387e98aef1f17ff31bfe18d42ec14484ecab68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9413f85a0e71e330318f7ab0825322", "guid": "bfdfe7dc352907fc980b868725387e98b0b312e04d10629fb81b7618e68e42a4"}], "guid": "bfdfe7dc352907fc980b868725387e984ed9aed418a173ebf92052fa74105736", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d7edec0b281abcc6b03233d83675fc83", "guid": "bfdfe7dc352907fc980b868725387e980254a7c23444b395a650c2fc549b1ca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2aba3a9d7e2d7f5b7407ac0aaa1665b", "guid": "bfdfe7dc352907fc980b868725387e985930f43390d989a435abca1119ec301e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cef22f9689809dce880853ecccac8d1", "guid": "bfdfe7dc352907fc980b868725387e984939f4ba1fbeba9010860ad3a72310d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806e42f82f1cd0066c861ba6ff42c9c40", "guid": "bfdfe7dc352907fc980b868725387e98544f7171cf7f8e8563113c4e00827de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895483c3197d80fb776029a6f93f84b99", "guid": "bfdfe7dc352907fc980b868725387e98110781f448bef0b9cdf4a480fbf2b176"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc106cc041ef9bea527b02ab7f6e2b0c", "guid": "bfdfe7dc352907fc980b868725387e981b98465d7fd7a6378fc6cedd8a109619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854e2dfa2ecd4cdec5add5410ed492376", "guid": "bfdfe7dc352907fc980b868725387e9867891aeeeb5afabc875f7bff76001ff8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e500c062fc16341cc05d212cb0d80eb", "guid": "bfdfe7dc352907fc980b868725387e9811d239c1b7e66c68c336d1e66c8bcb38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c07388aa54420599e367347b4cc4e0f", "guid": "bfdfe7dc352907fc980b868725387e986be923242d987106f7c3f9e1be3d60e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0966c4ef299932e68492659ce651e5", "guid": "bfdfe7dc352907fc980b868725387e981c56efc7df0281471fee95abed6a9318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd588c1789819c710ea542d7bee6dcdc", "guid": "bfdfe7dc352907fc980b868725387e9811fde69489aee28b0b0ecb9f9a53e2d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98377a774a3f0d8668220263a3cf465637", "guid": "bfdfe7dc352907fc980b868725387e9873ccb81fc346a2954caf82c90c3dcdbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987486fb502be3f1f634dbd8bb23963d7f", "guid": "bfdfe7dc352907fc980b868725387e98d578aea8ac49e34f57ad00b54b9f1723"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98005ef0d5179125ac0cade527cf62ea20", "guid": "bfdfe7dc352907fc980b868725387e98564d41cd33fcdc07a33fe2b12eec7943"}], "guid": "bfdfe7dc352907fc980b868725387e98f049b14f25e9f69cbcba5121a70bb0cd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b10c033554ae041ff9cd598b9d8a48a2", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "libgeolocator_apple.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a027a8a1ee4915912231ebdc9a69ec87", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e986fcb13ed4585b83d444becfbbbf27810", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb385acd4d397eafefec7af9a897c99c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98214de2d2b7d351b813ae567319a24f8e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb385acd4d397eafefec7af9a897c99c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98db128dc47e94983c4922e3d61f1d9d26", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98df6d3a1f22126286c57da1ecbd845fad", "guid": "bfdfe7dc352907fc980b868725387e98b7dfbf5b19a64b59ae8482cbde89a30f"}], "guid": "bfdfe7dc352907fc980b868725387e98ee244ad707f564987d679fa194fa49fc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985b75a2c7347a6f6cd8f2d5cc4f9b744c", "guid": "bfdfe7dc352907fc980b868725387e98024028404c123c05d47c4f4350ae675d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e65ba3472d120d2746ab8164a221bf5f", "guid": "bfdfe7dc352907fc980b868725387e98d294c5e1a9960246756bc4d9c38e5ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f799b619c730c530ef4b6018fe331f0", "guid": "bfdfe7dc352907fc980b868725387e98084a65362b6952905fc8986b22749bf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d27055c2f85b60d0257e894114185845", "guid": "bfdfe7dc352907fc980b868725387e9800577b49369b4602ea734cc435322814"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988614d091489081696ed8b801755f4395", "guid": "bfdfe7dc352907fc980b868725387e9892dba2d9df4889e3320b7a01ac754834"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb37fdda2d8a4c16b94b46828aa7e291", "guid": "bfdfe7dc352907fc980b868725387e98119f0965c8f4246afd65d3c0262b717c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce2861375bb3bda490a87e93a9defb3", "guid": "bfdfe7dc352907fc980b868725387e989f76963b2ab708fa8eec3d29b2f9cd4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f300a09cb1c963f100197889d3fa58b", "guid": "bfdfe7dc352907fc980b868725387e98d4d2f559e4076c0554f21829e72f4e9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e52e37d452fbe90e3396e06e9b10599", "guid": "bfdfe7dc352907fc980b868725387e988ad37c58dd8b54f5c3809a124a2f4773"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98290428cc37e244f0db1396c002a9c906", "guid": "bfdfe7dc352907fc980b868725387e981fe198f97601024ac5b06189f8d31732"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98664c975f8d206bdbe668fed658e3284b", "guid": "bfdfe7dc352907fc980b868725387e98728f7152cccfd69d2271df000d25e23e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98473fa563c7da1a05404c77bbf548b418", "guid": "bfdfe7dc352907fc980b868725387e980ec8bfeec4c0accfefa3b1bc2c5ed8bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae935ffefd7e9e7398e7890518d0c15", "guid": "bfdfe7dc352907fc980b868725387e984acd4eac6a3c5506d17ea56c3ababdd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7769e8b2c0358905a41ec5dbcffcc02", "guid": "bfdfe7dc352907fc980b868725387e98d1a43018db5023ff2a7ef177c73d142a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d94147b13500f70e2242346d52420a40", "guid": "bfdfe7dc352907fc980b868725387e98c73c1a586dd461078787f50bb906c477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889482ebfd198866eccfcaef433d863aa", "guid": "bfdfe7dc352907fc980b868725387e9824591a1b147a27fc9b26ce3088e726c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6cd3970b94d7c4f15415fa26c6673c", "guid": "bfdfe7dc352907fc980b868725387e98168f0908ca22933cf50a169a457d03db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f79742bbfd4b6df3f32dca4a450240e", "guid": "bfdfe7dc352907fc980b868725387e98b5e8858056b7a39d644b2b83a2bec2b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e373ea5891e3073867d774688303ace1", "guid": "bfdfe7dc352907fc980b868725387e98e7000fa2330dfb0c4f187befaafed0b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaac01a0c935e44f8f468bef0a77a85e", "guid": "bfdfe7dc352907fc980b868725387e98bf7b8ec07c2ab20b7ee0d896faf65079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98915fdb77c7e2cf16a70c02e30e99798e", "guid": "bfdfe7dc352907fc980b868725387e98fc59ed0e25ada854959b28a1f0e1552b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0a709646b06a46995c97ee2a65716e", "guid": "bfdfe7dc352907fc980b868725387e981374b0e9c3b230f31b32fab957b512aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98562974e3715505d86ea1a7f4697c7acc", "guid": "bfdfe7dc352907fc980b868725387e98a99dfbbc5837408cd82e79048a6053b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988789c80031c8826704c0bc42046a131e", "guid": "bfdfe7dc352907fc980b868725387e98bc04bebf62af12548df402a6c4ec3a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e78e28cb3168ba2c1fad19d9b88f1e6f", "guid": "bfdfe7dc352907fc980b868725387e98141a937e7b2693d6ed03105450a3e0e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdef6a209ba227e49bd6935436b1fea6", "guid": "bfdfe7dc352907fc980b868725387e984332dd7b41d54aac3e65a0d1472d587a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3432b92f1d55b1a9da7c40c14680824", "guid": "bfdfe7dc352907fc980b868725387e988feefaeb7afa845df3004e36d2deaff6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857c217033944e58696c8167398adbe11", "guid": "bfdfe7dc352907fc980b868725387e9893f320f7c9a46720944277277c628bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b2fb101c49e561f9ee0b02fbce58f2", "guid": "bfdfe7dc352907fc980b868725387e98dafeb3c77370d78eeec049aeb12cad7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a62c7cdb804d4bd87b697e4a8a6b96f", "guid": "bfdfe7dc352907fc980b868725387e98d10f85c088ced2a44706d7c8c0607837"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a9b7ca95a869a6811a69cb8c1e4764", "guid": "bfdfe7dc352907fc980b868725387e980c73908bf5d7755cc4c0864d943fbb80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9c7162d8dc8b6a882324de37c1c93c1", "guid": "bfdfe7dc352907fc980b868725387e9831d7bed6bb7607bea76500b8cb656360"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8c2417cd8023017102de432f141914", "guid": "bfdfe7dc352907fc980b868725387e984b1994e5a6cbb2a73af75c4917a3a39a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d2cc5d626bbca5b9037cfa1da101679", "guid": "bfdfe7dc352907fc980b868725387e98cf7fd5cfaa80e22ad0395411260d8745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a69bdbaf9b50162bac165e2ed86746a", "guid": "bfdfe7dc352907fc980b868725387e981b8ae5147a74f0261ccc7d3f36e5be01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b3c188fb3306ca8f2b156002b5f708", "guid": "bfdfe7dc352907fc980b868725387e989394c82781f6a80e577140655c43127e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98498b278e613ffc5190835be04af43504", "guid": "bfdfe7dc352907fc980b868725387e98415bb038db43ee8ed6d70cabbc3b1e87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a1237153fe0d28607c737aec10c7565", "guid": "bfdfe7dc352907fc980b868725387e98e8a20e418347bef30c066bb87ae0c5c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9eaec872a838351a572fd32dc13dd06", "guid": "bfdfe7dc352907fc980b868725387e983658cf2ec62a1bafc84fd49b28aa7730"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f70a5b42b0265f47e00941f4d2aa471", "guid": "bfdfe7dc352907fc980b868725387e9818cd84ee8705b9b00c7ecda9e353a0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c6e6248c155567225cda45b7150dcc4", "guid": "bfdfe7dc352907fc980b868725387e989909a6d46e35da2d70db059ed0716812"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989616d79ac54f98a9ab946778790a3e4a", "guid": "bfdfe7dc352907fc980b868725387e98c6b4110d2cfba02a06f6c85bd9182dfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eab89b93819e3e13703502cdaef3b214", "guid": "bfdfe7dc352907fc980b868725387e98d9fd75a7a19aca71cea2402691015c63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98708dacd343a6795a0dbf4fb8c73d3f12", "guid": "bfdfe7dc352907fc980b868725387e98facbb3cb08add5e9e8763120c868a183"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98916fec4aa624d1ec22fc2af775f84488", "guid": "bfdfe7dc352907fc980b868725387e98eaf9f6f7a7be79e79e61d982f97ab504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7fd702c9c58c4df6f5385555a3c6df4", "guid": "bfdfe7dc352907fc980b868725387e9806090bcde5831184840d1ccd36baa6ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee5ca2e62fb7e8588a7e99379cc4b0af", "guid": "bfdfe7dc352907fc980b868725387e98237afb6603458b73be70c434e0d5a5bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ccbc142511a3c3a070d344e9eb6aee8", "guid": "bfdfe7dc352907fc980b868725387e98f539a0fbcb9a39b048fa289badeae9fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f876ad21ec54d2eb6467e063e5ed4a84", "guid": "bfdfe7dc352907fc980b868725387e9850ae691eb1bc4a40a7016ae56367ccab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f31ac876a2db0bcd539db5154a82faaf", "guid": "bfdfe7dc352907fc980b868725387e98b584e6fd6d51644701275d7851080c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfc7d99ee6cdf1cfefa864e75432162f", "guid": "bfdfe7dc352907fc980b868725387e986042ab4042f0b7fe6f7903ed41c8e6cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e31f730b11ffca196b6023534c0034ff", "guid": "bfdfe7dc352907fc980b868725387e98db5bd0c393be6a46178e8544ec856c30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982056ba6aaedaccef640ecff7497ba033", "guid": "bfdfe7dc352907fc980b868725387e98bdc674f4c41b91161c3f82a96448ef56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def9d37e4f1e94da1eae13a4f1dbe4b8", "guid": "bfdfe7dc352907fc980b868725387e9806e1d16a27eb296b3467f365f0fb6b56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0946e4a8ea796e0dfbf9609f1515bc6", "guid": "bfdfe7dc352907fc980b868725387e98ddcf41747b82b7d97851cd4097329666"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d80fa5409f2e1cabd8113783dd001ae", "guid": "bfdfe7dc352907fc980b868725387e989d37fbe0b836d5c8dba45beb2be8af2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e21e3c9aa57614f27c5e50f2a66d3d25", "guid": "bfdfe7dc352907fc980b868725387e984e40baafa365e8195dbf53d291fd076e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed3d820d94e3bd736c94538ba304c36d", "guid": "bfdfe7dc352907fc980b868725387e98a9be9b82ae0a4e812459a78564cec353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c9200733256ee04d1f453b1f56bbe9", "guid": "bfdfe7dc352907fc980b868725387e9801fabe82c71cd0dc88600864a7949afb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a36cb766f12f08922f1f235fe6a2368", "guid": "bfdfe7dc352907fc980b868725387e98d97c42eb99209d26e94a602ae414a434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841b67fce3f2afe8010bcd7e258bfda8c", "guid": "bfdfe7dc352907fc980b868725387e989131b7876aed55b36f59789861986584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6dc39bcbd0dd806e0e74e5f4aca79e", "guid": "bfdfe7dc352907fc980b868725387e986498a50bb136cbc09682a6abafc48a1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8707c7d9ff2b4924f64851a0202529e", "guid": "bfdfe7dc352907fc980b868725387e983ad1518ae9505681d085282dbd897a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e88c57977ac8a64cba6d3980b2d2d7ab", "guid": "bfdfe7dc352907fc980b868725387e98b441c5c100335b9bab8b7ad8fd496774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fd5cb0c35e5567683d2175784a693a4", "guid": "bfdfe7dc352907fc980b868725387e987f915e7cda66326fb997f358ac4c9172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827931e46363f2db33d0794e695bbdd56", "guid": "bfdfe7dc352907fc980b868725387e98ec8bb0dd4adcfc75d210cfcf4909c672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cc1463daaf514fbba512687f2f695d3", "guid": "bfdfe7dc352907fc980b868725387e985c165a1364ad92e615efb46c8ba93365"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98641e289a632e42111304204d4c78dd0c", "guid": "bfdfe7dc352907fc980b868725387e98d1d32caf5a03f815ad159e48d05be911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342a2871e19dab175923682417440e63", "guid": "bfdfe7dc352907fc980b868725387e9893cd0421d5c4ecc7aa329af589db36ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc1cae114e29d45d15b25a81c0f73bf", "guid": "bfdfe7dc352907fc980b868725387e981b4b944617fceb4ae78f51acc6774f03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989660e3e5735568330c56742e882ac5d7", "guid": "bfdfe7dc352907fc980b868725387e984cd131ddc5ff0aafbd53208c9373af5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986644683b0d3b4a33dbacc5e1e24e5c58", "guid": "bfdfe7dc352907fc980b868725387e98ccee215836ad2b361f09fbabcb53a2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099140f7b2c77e2cbd421c9d472c83b5", "guid": "bfdfe7dc352907fc980b868725387e98bd1ddf7b1bc9b98204fdeaf13dbcfd2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dee129d9b51ba7d24dddf1e53460f1ad", "guid": "bfdfe7dc352907fc980b868725387e98c3824439ee5448e859ead34cf8adec5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9697a94f41281d3dd9f1d9eee1e0933", "guid": "bfdfe7dc352907fc980b868725387e98e124f37ce4cdb83aaaad40bedb8af861"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e2dd4cf43e8e848a19d37dbf07575f", "guid": "bfdfe7dc352907fc980b868725387e98bbbb4895c52055030cd134e9648a30ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3e34ef623f8ee47858be087fcf88db2", "guid": "bfdfe7dc352907fc980b868725387e98fc4f5f21fc446fb2c7a6e92887ae3835"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e298f2b711fcffb410f9e905c11436de", "guid": "bfdfe7dc352907fc980b868725387e9876cd99f96d76225e0cade4a526ae6ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a5841441963ecad4afe084dde1f0ea4", "guid": "bfdfe7dc352907fc980b868725387e9886b816357a25b71c489155e85afa91a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc5ec3b058e5b4f922c343a5723a156", "guid": "bfdfe7dc352907fc980b868725387e98d1cc23544416ece7f109b6cdac16ff4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5cd7c01c2ec25968a11eb4f043b616", "guid": "bfdfe7dc352907fc980b868725387e986b9296bdadf0886d35f88f42b5eb8018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98372b9cbd678ccdf63b640aaba4d9aa44", "guid": "bfdfe7dc352907fc980b868725387e98e9488bd8ab0e7d66a371dfa254db11d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882f1a20dd30e0a01c19c80d7b82c6020", "guid": "bfdfe7dc352907fc980b868725387e98b581213a408c36700c402b31edb35873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8a8ad8302923482542f25b1321776a3", "guid": "bfdfe7dc352907fc980b868725387e98a9969c20935023bd0c79172b99b83220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5a753748bba1e4c03fa2502813b468", "guid": "bfdfe7dc352907fc980b868725387e983648290918031ae7065a79c0df599455"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42bd63530a39040872e2d952f684d5e", "guid": "bfdfe7dc352907fc980b868725387e98c7432de26218d7062a4b7da9ff647a86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba12dc5ec5ee122ff62047edb2227dd", "guid": "bfdfe7dc352907fc980b868725387e986223c589eb07692225c3f645877855b6"}], "guid": "bfdfe7dc352907fc980b868725387e988c18d4917033b4f0858bfc123bdb441c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ab8182f2ca3a97d3ebfaf018644b9f24", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98137248990f7dd25c52dc8e51fce0679e", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/rive_common/rive_common.modulemap", "${PODS_ROOT}/Headers/Public/rive_common/rive_common-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "FEFFC1FC5FD4045259D65513C025F2C1", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/rive_common-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/rive_common/rive_common.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/rive_common/rive_common-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "librive_common.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
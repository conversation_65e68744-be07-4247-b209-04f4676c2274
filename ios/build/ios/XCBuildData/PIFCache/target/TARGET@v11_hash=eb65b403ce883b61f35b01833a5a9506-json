{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98055db8619aaced5b30bf1140c9f525e0", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/PromisesSwift", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "IBSC_MODULE": "Promises", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/ResourceBundle-Promises_Privacy-PromisesSwift-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "Promises_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9812444dd7cbcc53ba56d25c3f243e23c4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e46fde7f4a3bf82d9eae6408e9a7795", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/PromisesSwift", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "IBSC_MODULE": "Promises", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/ResourceBundle-Promises_Privacy-PromisesSwift-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "Promises_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9837abc67a601256a2ab84a265345821dc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e46fde7f4a3bf82d9eae6408e9a7795", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/PromisesSwift", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "IBSC_MODULE": "Promises", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/ResourceBundle-Promises_Privacy-PromisesSwift-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "Promises_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fdc972db29664d9e75a0bff6d35ebda4", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c45ac377755ae358e175bda788ebf516", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e96abe29671d7408c603a7ed0399e65a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988208738e172709a96acb6169650ade78", "guid": "bfdfe7dc352907fc980b868725387e983faef2db86529d6f3d1709b403032d53"}], "guid": "bfdfe7dc352907fc980b868725387e98fd82d0d84623cc4dabc7145f6ea997e2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98aeb750aa70b27aef91e7058e461fb73c", "name": "Promises_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
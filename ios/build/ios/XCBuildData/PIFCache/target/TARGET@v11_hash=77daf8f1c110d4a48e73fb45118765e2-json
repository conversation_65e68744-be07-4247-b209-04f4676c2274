{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845398a3744243abea6c4cb21aceda20c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98404f25bb253e2e1c2e6d95d0eec914f8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5fdf936b897c530e04eafed8fdf94d5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e989c28b0a3d44d7006f4f023c69596430c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5fdf936b897c530e04eafed8fdf94d5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98b3b7b1c0593182334ddaa559fb619032", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c6407fbec558078df636031d33d6d9b", "guid": "bfdfe7dc352907fc980b868725387e98fa98301ab066e7a377002989e55898ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f907e64a6d6f9f623f22ac6a0d861701", "guid": "bfdfe7dc352907fc980b868725387e9803785904ba7cfec2d0a75ffd5da12876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c1b61db82bfdc171278ac1c831956fb", "guid": "bfdfe7dc352907fc980b868725387e984d8425f554b3d620b79cf90ba89b0722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f5bdc2821f295cb950315222f52e26", "guid": "bfdfe7dc352907fc980b868725387e98176a5c030067149d45aaea1b50f59005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c2b258d242bc5a814d4ca1d1d3730a", "guid": "bfdfe7dc352907fc980b868725387e981f80ef0f30f07cfd509107023066804f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51c000d780eff730e6bdea941083a8d", "guid": "bfdfe7dc352907fc980b868725387e98ce3d09f7ec1f6a516a7822900965a1bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ee1bbf6dbf1b98df0f3ee6ce15e527f", "guid": "bfdfe7dc352907fc980b868725387e98004b6b6fc8c25a47f1d4e3dace33a2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e370e7b24be10ee4001d0d699ee798e0", "guid": "bfdfe7dc352907fc980b868725387e989da91c19356349df689ea39c42d08662"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b12df757f356fef02d820391b3fe4581", "guid": "bfdfe7dc352907fc980b868725387e982ff14955c4f60a617e045824be716341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f994cb99173f52ac1429bd2f19e39779", "guid": "bfdfe7dc352907fc980b868725387e98ca48647a395abfca76a3252098d4462d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f834161f13ea2951e83b3972680b10b4", "guid": "bfdfe7dc352907fc980b868725387e98af5041e48877f56bdd3b319252b8e1f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d830ced64ca39e583cbeb27b3b9a36", "guid": "bfdfe7dc352907fc980b868725387e98cc419eca88ca35d2bb659738ffdf54ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832c92acad50c1fe17c39884894f679d9", "guid": "bfdfe7dc352907fc980b868725387e98f1df1fa0647569eef7c856d3c92bc540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb3f1b1216ea09558571a655e21db314", "guid": "bfdfe7dc352907fc980b868725387e98e574aa6c200d3aa7020c9ce327585bcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a5ad57f22916483ea196fd5d566e5f", "guid": "bfdfe7dc352907fc980b868725387e98f124ff66d096d320d80b97a492b8529c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860957c16e4ace6d3e03253d61878965b", "guid": "bfdfe7dc352907fc980b868725387e9888cae5e15c9e881244211c7fcb1a28fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a1dfbd1526889dfba0a5954e2b38bd", "guid": "bfdfe7dc352907fc980b868725387e9893f0f028a78b9f4ab98acf7a02f79fa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d86221d085938c83e1dd250cc0f171", "guid": "bfdfe7dc352907fc980b868725387e98a171a9e97ec248f349a359c2d931cd9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984558920b2493bc5ece5db8582e869604", "guid": "bfdfe7dc352907fc980b868725387e98a35cea1faa51a27b53a807a057213346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bdc6132df25ea232de821efc2467f8a", "guid": "bfdfe7dc352907fc980b868725387e98a3978de6c57090e70fde8e3c26d9411d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6e8808622dcb9e12be6e527aaebb8c6", "guid": "bfdfe7dc352907fc980b868725387e98e84b580fa6d70be3681aca4cc4c5d854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44c375d3e23a0032de57ac8ea2e3352", "guid": "bfdfe7dc352907fc980b868725387e9876eac74c2f61bf3b22a0bf221eae8005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a08343ab19dbdb1791f38c163ad7f2d6", "guid": "bfdfe7dc352907fc980b868725387e98c87b0ab653d8aaea09c871b076e33d39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ef26d225d7b17df8f1e4067ae6eb0a", "guid": "bfdfe7dc352907fc980b868725387e985c9e5d23f2733b16f32dd7e5eb1c8a6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b9aecbb740008fabb5e219a0561da7f", "guid": "bfdfe7dc352907fc980b868725387e98522b1eb32b27495f3fec2d9da15f6050"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f531cbbb561904ab01c5bd4c3fddb1c", "guid": "bfdfe7dc352907fc980b868725387e98f67b3283e52e0fa7b7c110b3818d1641"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cf71b4371621de371218a022900a945", "guid": "bfdfe7dc352907fc980b868725387e98dde4b33e60d8b2114e22a47ebeb56154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9d2d232ce2405ebe323821bf707c56", "guid": "bfdfe7dc352907fc980b868725387e98afce76f64a0d31bbcc89ad49736f8b9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bb4ea381d98602f4519c9d51e2203e6", "guid": "bfdfe7dc352907fc980b868725387e98f632e2a0fcc96f8f01efb8d4d6b586e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fde7887edccf541de59320af041949f", "guid": "bfdfe7dc352907fc980b868725387e9833fa4b081ffcce6f55c8c29dfd8b211b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daa06a6bdfb12201462e45ea7b3c487c", "guid": "bfdfe7dc352907fc980b868725387e98a34a49de6b5040eac9b0a805a6a0a7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872753ed47eb9066aaf9344a27195b7e3", "guid": "bfdfe7dc352907fc980b868725387e98ab047e67d065fbd72b5f255f59cee290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f025834487bdfd38c5362f47bb56da23", "guid": "bfdfe7dc352907fc980b868725387e9860ee89bc0d80fbe5a3fcdd5bd73f7bb1"}], "guid": "bfdfe7dc352907fc980b868725387e989a29b1b29022f946f3b7dd100badde4e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e3ccd58f2310c1f13e598235c1f170fe", "guid": "bfdfe7dc352907fc980b868725387e983000d000c3e6c28e29e43bd511549873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98292a78c33e6bf2f8a39a12f427a89826", "guid": "bfdfe7dc352907fc980b868725387e9815395814ea4e47a7001927bcf59890d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822467a5216516f778ad634cb0c1e2838", "guid": "bfdfe7dc352907fc980b868725387e98a65722ca235957ab07b1177f97aa2e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ced05668bd1de2416b5eef0d285a66c", "guid": "bfdfe7dc352907fc980b868725387e988411376c2c18c26df1d2d86857315903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3510b961ec14e5389e3991bb962c722", "guid": "bfdfe7dc352907fc980b868725387e9836d0936d1ff992788f7740118fc69f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de54830f68239a92c2b5ab6ca1f1dc22", "guid": "bfdfe7dc352907fc980b868725387e985adf1e8fee3d8735226076113231ae8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c7b90094fda43dee9ac7cc29ec542d3", "guid": "bfdfe7dc352907fc980b868725387e98008620b714de55113f375eeb2679b0bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d33139718574ff14c44a376688ab4864", "guid": "bfdfe7dc352907fc980b868725387e98bebedfcfbf2d3cdd3c01e99f61e10e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e380cc2d17721bbf237a66622bc87d8f", "guid": "bfdfe7dc352907fc980b868725387e98d188e5a3afd317e4ca646d5867330911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f4b558b076e399f40c0a1fea010fcae", "guid": "bfdfe7dc352907fc980b868725387e9861eb9dcd2bd3ef7a8c4823d569fb8e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d215077bf9aa9165ce9a8f3ec946e38f", "guid": "bfdfe7dc352907fc980b868725387e9860e3f67819dbb71610a845294231bf11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c7bb49a60731a4b680623141ff134e4", "guid": "bfdfe7dc352907fc980b868725387e9803265801598ae006cdb33159cb521a28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edde8e5a1ff986640374ae9111a7b03e", "guid": "bfdfe7dc352907fc980b868725387e987b8eee7ba90b42d688aef81acec7b5db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980461bf33cf24bc369ef324f3ea7c2c88", "guid": "bfdfe7dc352907fc980b868725387e986646b4cd8d58a9dc88b12145d04b0d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6d33cc6c0f37dc6c3156913d5edd94", "guid": "bfdfe7dc352907fc980b868725387e9844c73fc3080943859d03bd053760899e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985457919d524e93e5cd5f8a21a012d841", "guid": "bfdfe7dc352907fc980b868725387e984f9ad20a91f0bf67b6a924d5aa00a233"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c5390e00d6434d1ae3eb4a4a2a8122e", "guid": "bfdfe7dc352907fc980b868725387e98c1b070f7ebc90fc28d5c9f619de21ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7315942e3549cb2f80516529b31e6fb", "guid": "bfdfe7dc352907fc980b868725387e98c93c73f63acd6d181446d7ba192e82d8"}], "guid": "bfdfe7dc352907fc980b868725387e985f11813132cdd139fd9f16d23e7d18dc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980ea371aeaf6dff0f783748224e9ca350", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "libFirebaseInstallations.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981fcf18166e4a35b0f2376b7989da2ca3", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989431291bb409a74528bfd9cbde141371", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d738c8afb1f6ceb1298048fba602637", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9865ec056b6679cdd68d239d7d8db108f8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d738c8afb1f6ceb1298048fba602637", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9894cbc5bffac9c6c70c39dc2baaac0a30", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a62ae73f29b541133ca65ae4d912f7fb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988a259d43b63ec12a113b5759f4e07bcf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982dfd7afe982a77ee806743700c800dc6", "guid": "bfdfe7dc352907fc980b868725387e983e917f659e1a1023f430446c663bccd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f47772260f85dca01d2ba5a9fc353970", "guid": "bfdfe7dc352907fc980b868725387e9850307e062997dedbe3a7fc7ddd7890f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806362190558e4c93d8d0f42fe1431cc9", "guid": "bfdfe7dc352907fc980b868725387e984f34b77d5a6ad765c6d5f42dca072ccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980715af725b03750fd84ee1dc5f6ffd37", "guid": "bfdfe7dc352907fc980b868725387e9822fdd81133e7e5ba665398b05026f6c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d389fca570316f250100521804fd7aed", "guid": "bfdfe7dc352907fc980b868725387e98f2a747645ba33866e228e72c752f97da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd60ffaaf7359fc2ef2fd2357245b85", "guid": "bfdfe7dc352907fc980b868725387e983ba91e898d1e005d5abdbc4443b15a67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e4beec105be8173b71925ee8fee7c46", "guid": "bfdfe7dc352907fc980b868725387e98b4c6a60b65f1e0a672bf00f9cd2552a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e4713ead9d7e519a4319a4e0be03814", "guid": "bfdfe7dc352907fc980b868725387e98abe5cf6f7321acb69af9cf88fdf6c5b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873271eedcf903032bd7bbd8fef92f13e", "guid": "bfdfe7dc352907fc980b868725387e98bd679c6771e089773c236dea2f897c58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c821654a2714b8a62144c457778a17a", "guid": "bfdfe7dc352907fc980b868725387e98e2635ea7f24de7b8be851ea110690fa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa1b14f3e138be466ec4836ef75f4fc", "guid": "bfdfe7dc352907fc980b868725387e98c5afbedea27a9e00ffc9b92072a0c399"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3b48ae98ee8e8662f268775491b89d9", "guid": "bfdfe7dc352907fc980b868725387e983dbd3b44654561997316424967bf60e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5266187cd73a262eed32106e6789028", "guid": "bfdfe7dc352907fc980b868725387e98efa5316c36728546da74588f05ca1944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a540b6599741057f3f8261789434f49", "guid": "bfdfe7dc352907fc980b868725387e985590c022767e7b50c5ac95411053e646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66e1ff666dac1a0d88b39a1a37325a4", "guid": "bfdfe7dc352907fc980b868725387e985e6a8adac48306010d46d65da8c82b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a96beb19fa089169738514507510aa0e", "guid": "bfdfe7dc352907fc980b868725387e9804fa37b2a17621485ca63d08aace783e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba135adc1ac208ee880569d7b3fea09d", "guid": "bfdfe7dc352907fc980b868725387e9895ec96fea2338c5b52a8ed0199a4ec2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988be1b25b7dc7b741077e15f178c1833d", "guid": "bfdfe7dc352907fc980b868725387e988edeea206f60d132bb5963a399f86735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5caa6f4962f413494bb7a23a10d7736", "guid": "bfdfe7dc352907fc980b868725387e987f5580e926e892216bdf7489ad9d54e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868d95c21ff2e96fc68d1ec9fba091cc5", "guid": "bfdfe7dc352907fc980b868725387e98f46339c74b1376b0120b223873107057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834610ca947a34095a2122f2d43c22027", "guid": "bfdfe7dc352907fc980b868725387e986f0407df329e18af59ef91e3afc94a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989724532d85937ff7ab0221d881262c6d", "guid": "bfdfe7dc352907fc980b868725387e98e557a095e228617755001a566eb93781"}], "guid": "bfdfe7dc352907fc980b868725387e983f77bafdd2bac1aef6a5f8899cb5a9ce", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
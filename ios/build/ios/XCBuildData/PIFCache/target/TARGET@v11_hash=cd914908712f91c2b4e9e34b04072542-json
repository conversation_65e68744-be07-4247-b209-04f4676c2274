{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fdfe402e62f65fa565a91da2013b8b87", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/FBLPromises/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "PromisesObjC", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e982c0ee42e58e92a63fe45b8ecfc356fba", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9840a25c5c1305bad67d7c775120a4cc66", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/FBLPromises/PromisesObjC.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "PromisesObjC", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9888d17193fdeb10e31ee763b132681698", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9840a25c5c1305bad67d7c775120a4cc66", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/FBLPromises/PromisesObjC.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "PromisesObjC", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98df40583c8df33654aea6bca2fe1a97ac", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a6ad7502699ba3af7d2d06c8901092e7", "guid": "bfdfe7dc352907fc980b868725387e985b8acdb09f7b89fd7bc03d1993447d69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485be15e0d0453f9c706ef3877381684", "guid": "bfdfe7dc352907fc980b868725387e981e7af139d989201fa1da81af746bed53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f84f70aae31b344aa19e34f3a3a6535c", "guid": "bfdfe7dc352907fc980b868725387e98c85e0995fe379bed0b6a12059afe3a52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d8f4f61cb046c6b9fac98d029b20ab", "guid": "bfdfe7dc352907fc980b868725387e98ee78ccb09327417876a67140232b1252"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3cbf4e5698e92093df2cb7086578357", "guid": "bfdfe7dc352907fc980b868725387e986d84dc5f93cc62ccba75b199351e530d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e73263a9994db35cb713595a35c01dd1", "guid": "bfdfe7dc352907fc980b868725387e9859418b7ac343f0746af6911c017089fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989237ebc79ad4568170e3d87a9023c44b", "guid": "bfdfe7dc352907fc980b868725387e9841450a9a8ff03753f87c552fadfb44e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847b775a6ef6bea6c158aefe3e50cb4d2", "guid": "bfdfe7dc352907fc980b868725387e98c115dad8c14bbc388928c154647ed3a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb7276df7a180fee7d995538bae1a37b", "guid": "bfdfe7dc352907fc980b868725387e98c01d56f975bbb590d21ff57543921a47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f1549c6ec63c70c2c89593b7728279", "guid": "bfdfe7dc352907fc980b868725387e9828715443b483075e71801ce241e8dd1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a683542208aaa38e849c35924d4d4da", "guid": "bfdfe7dc352907fc980b868725387e98f4d5906e6dabc4dae44f64debaec7348"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988109ab9d3bcc558052740a6374650f4e", "guid": "bfdfe7dc352907fc980b868725387e9899b893c4857572f950e20d2617058e59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809310489acb30263e2f8c96ea2fa9c79", "guid": "bfdfe7dc352907fc980b868725387e98ae984e9fc0f381505daff63206a56bc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b8ce5ff49bbd39b13a0c43d5fd38609", "guid": "bfdfe7dc352907fc980b868725387e98935057b62682e4dbd55f155c4310df66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b097e93bf4dcf6a87ec109e61460c4e", "guid": "bfdfe7dc352907fc980b868725387e9811eb313eea2f2e18bd8a08e985ea868d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984faf78485441a820431e4a5ebe872d6a", "guid": "bfdfe7dc352907fc980b868725387e98c304e5c899aa1de273d6f93551a9a4fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f779c8ed6b2fa1edc8d85ee51d6d746", "guid": "bfdfe7dc352907fc980b868725387e988dce63888ee66f75d4690d5bc49d1dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829bd8e8501333b96c71e38b663adcae1", "guid": "bfdfe7dc352907fc980b868725387e985d25f4de8a017c5ac2d2cfb1a093014f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db224ede26c2c673459fadb82b68c5af", "guid": "bfdfe7dc352907fc980b868725387e9866ddc6c1183f8e82f09691334dedf55d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0633870cd187c6e1efc3435014643eb", "guid": "bfdfe7dc352907fc980b868725387e98862bdd1eac50de7234938eab5b80bb32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7dce9d72186b6ab293b802a26b3cb8a", "guid": "bfdfe7dc352907fc980b868725387e9839f4443a49516f008c8fb8209d282c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab5b52f61e8157b302bfd0482a1431c6", "guid": "bfdfe7dc352907fc980b868725387e985b88fa010d0a8025d814d6c766264c21"}], "guid": "bfdfe7dc352907fc980b868725387e98dd7976c553953b045505646351eb00b1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9877bedb235c6e3cec91ec86e4559cac26", "guid": "bfdfe7dc352907fc980b868725387e98e01df17412e1ba5f66bacba82b22bee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b722a565d8bdd3f4004c45073b445be2", "guid": "bfdfe7dc352907fc980b868725387e98e0521b6f92f6ddc08a277f198950dc0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0b500cc1abf2a065c89648c5ca06f0", "guid": "bfdfe7dc352907fc980b868725387e98ade07fa5f0089cd9ac30cca5932847d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e73cf9f3c31f20b2f9c1e9797cc8cd43", "guid": "bfdfe7dc352907fc980b868725387e98f1472fe3cb59448b78295825b4cfde9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb577d6bb04f3bfad87ab533b260f9d1", "guid": "bfdfe7dc352907fc980b868725387e980ce80cf243ebabba8979c60f4f30cdc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc2179865b28401161d082cc523214a4", "guid": "bfdfe7dc352907fc980b868725387e987fb825136649f8ad095ec6329c96bc6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a2d7e72964390b648baaae62a78f8f", "guid": "bfdfe7dc352907fc980b868725387e9830b94ee7cbfbb541a6e09643cb0efa84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980459d09643d116ce59f703383758b65f", "guid": "bfdfe7dc352907fc980b868725387e980fd33592ce5b0d95422557e6a8f30794"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98184e0db697a83f4d3626ce04fedea27a", "guid": "bfdfe7dc352907fc980b868725387e98fe84af49c2d3d4febc02348d743db7ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca0e5b40e5dc08426a0b408aeab7f40", "guid": "bfdfe7dc352907fc980b868725387e989e44c990ca6043dca8da28caad339d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609869f2c1f03935fcb60bcfb350f414", "guid": "bfdfe7dc352907fc980b868725387e98e48706852932577fd40ae5a02769bf96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b775ed8061358e4a024b2d8e54a668b3", "guid": "bfdfe7dc352907fc980b868725387e9819d9561e24e2f875ca1c41436e844244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98646475150a78bcaedb459a198685aebb", "guid": "bfdfe7dc352907fc980b868725387e9889b690988d98b822dc5aa20dacef380b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d33503a795669fe17c1ec54ce3b0060", "guid": "bfdfe7dc352907fc980b868725387e98298734e9eedb64fa7705acbd9f0ffb46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df837c2a57130a1c7ae881d0181094f9", "guid": "bfdfe7dc352907fc980b868725387e98552467b8c210e6bdb46a6a91dee1c6a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5d1178f282dd52917605d3eef48f4d", "guid": "bfdfe7dc352907fc980b868725387e984d614a6e85d08bd6bf50093341251646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830f01b15e10ca7a4057463b38093da46", "guid": "bfdfe7dc352907fc980b868725387e98cb9977f9bd040ee70777f26b1f24b4e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab187f957ddb07ab1d844ac68e506ba0", "guid": "bfdfe7dc352907fc980b868725387e9856e08b791bc3ad1051b1ceb81e7e30d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1842a6998eaf682269bfd36276c92fb", "guid": "bfdfe7dc352907fc980b868725387e98e04874178ffa9c59f8ddca3ea9c9b2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b649afe512e187a515aaa1fcd141630a", "guid": "bfdfe7dc352907fc980b868725387e98d0e2a8f0b4c329d74f8f979a1b6a09dc"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ba64855895205dd1a396b11ccf0f79", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b86afa315d1454f6af2913d60a1f2ede", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "libPromisesObjC.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
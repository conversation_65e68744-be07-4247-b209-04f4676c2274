{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804ff356a09cdd9e5c3327777b7cddfcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98fca5e3c4e005126846da8fbbe5cef8f0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989303ad64d91640624810b7ac70981691", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9898c4dfb2903e337f77af3ad865e6ac0a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989303ad64d91640624810b7ac70981691", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98dcf3408314a41bdb6efe23899cc61a58", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e5b3a3794bf6e9ece58d77234f6018e", "guid": "bfdfe7dc352907fc980b868725387e9847d017783531a30d8a3d1499dc76716d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896abb2f7d27e64f9d898a64eed51bb02", "guid": "bfdfe7dc352907fc980b868725387e983a3672f214afa06828a40d2658c26b82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a6b2d05190e9c88b68a2ca1d7d4b68c", "guid": "bfdfe7dc352907fc980b868725387e98add99308abb2c5fb4ac87ddaa58af239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da47cf6b901c9ddacce6211bd24d12b7", "guid": "bfdfe7dc352907fc980b868725387e985331202c3da0a4ce28132dbad5adb769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98358de48a43cfbe552581dc213c3fddd5", "guid": "bfdfe7dc352907fc980b868725387e98399849e2fe4f776c9046b4995a298235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868731136dadc25d0b694577af0a9a1b2", "guid": "bfdfe7dc352907fc980b868725387e98fcd033cffeefee0445b5cf851e6f40c2"}], "guid": "bfdfe7dc352907fc980b868725387e9885c75f20c7b86e55aaad5bcda42ee0dd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f10c8b686e58c1d640fdb97f0becbfd", "guid": "bfdfe7dc352907fc980b868725387e984f751ebcff4a7ad52e784dc11fd1c995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e936f25bfbc0ccf77fe8b1db3028c4d", "guid": "bfdfe7dc352907fc980b868725387e98da1c2aee0aab6d1dbdbfa9fb367fb1f8"}], "guid": "bfdfe7dc352907fc980b868725387e98ddc6b3ddb17d12a88ef15d74737aeb30", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98841959224b7fe6a8cfc485b404b11668", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982cdb0c7c817307e018cfb4299b646a42", "name": "libFirebaseAppCheckInterop.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
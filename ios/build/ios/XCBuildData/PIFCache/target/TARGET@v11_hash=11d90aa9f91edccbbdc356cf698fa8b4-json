{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e50762d8fd40f66d55cd25afe76253ed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Private/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98d17be352523b72e6e59e558411312a03", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa221a6589e579683ade716d1fb0346b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Private/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98ace656401f05e818d96df2ced60b7947", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa221a6589e579683ade716d1fb0346b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Private/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98aa10d4df2197fd3fa8928daa2b0e9b1c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7b7174b5a18680379b68cff2f3ada67", "guid": "bfdfe7dc352907fc980b868725387e986f57951691d2dd1a5fe47231d8e0abec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edbc678fa65e135c90ce1c07990b519e", "guid": "bfdfe7dc352907fc980b868725387e98ba89d4ffa8f2c945cae77b5cbd395995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efb9e40b5e1be29bc54a262ec0d0b2f0", "guid": "bfdfe7dc352907fc980b868725387e9851ce58e0a4fd9b73fefb0a07f78a1fd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986946209b3dd65e18e839cec4565cdf61", "guid": "bfdfe7dc352907fc980b868725387e9895d4cb2cfc266bcfa66cc1487647cc47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0a86f8f8373955b0f2e99f19d540b1", "guid": "bfdfe7dc352907fc980b868725387e98d2698442a1df3949b4b2839abdd05c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff182a103a9533f8fdce278f8fbc9728", "guid": "bfdfe7dc352907fc980b868725387e981e1a6b45087a9e47b9d944479cb7bed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a079bd57f715422fb2d36684d9047b1", "guid": "bfdfe7dc352907fc980b868725387e98ba679d1a544c43e87b0481f3b216d368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dde09fbac548c439a07ec10eb00a33e", "guid": "bfdfe7dc352907fc980b868725387e9872154b80ea7c57b8c41fd41ef0d2dac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984502ee9592c429c1a9f17aada3028179", "guid": "bfdfe7dc352907fc980b868725387e9860704ccde11d2f6f4dbfddec377f0e99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982877ddaf3c32ba72ce435ba13fd6e322", "guid": "bfdfe7dc352907fc980b868725387e985dec3f5fb4e10afe611f245184f65d4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac12fe3c6dd18202fbd7e370aed34424", "guid": "bfdfe7dc352907fc980b868725387e98ac782fe6df436e3672ccf0c583b1e945"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983643c20a5ac8d55aa0db8d971b92101e", "guid": "bfdfe7dc352907fc980b868725387e98a5b7380c5bfdf2da3adcd6aff8b273eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abe65687750f4c4024a7a7da944c2411", "guid": "bfdfe7dc352907fc980b868725387e98b99e9a2bcd01093a80ba705a4f418cdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791f5ff4d9b7065b236cb52d0f381f17", "guid": "bfdfe7dc352907fc980b868725387e986007e287b2c43b28bd6dcbbea68f6a6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98862edd06f7f2912c2b4cfb715da86475", "guid": "bfdfe7dc352907fc980b868725387e98b3d67e184421f38abbb15498b6c7fd8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d538e3616051fa971d464e89f094e02b", "guid": "bfdfe7dc352907fc980b868725387e98c1245e974be1bf72b04c9a65d0c87999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a4be837de791eb46e47a4d543579df2", "guid": "bfdfe7dc352907fc980b868725387e98bf73a79a2eebaa8e6087085546611b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2450ac440d5737aefe16d9cb4941aa3", "guid": "bfdfe7dc352907fc980b868725387e98ec74f5b5abe59a668df614e2962ca02c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c9c9826820d4482245645f54fa18293", "guid": "bfdfe7dc352907fc980b868725387e981b4c9eab7e46734f45e807578ee4b083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e45a34d4997236ab40f3f630811df9", "guid": "bfdfe7dc352907fc980b868725387e98a03055f6a3ee8fc960d4905f1eefeeb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98097faaf12aee95de98c86e3b43a468c8", "guid": "bfdfe7dc352907fc980b868725387e98b0698adbbbac80b14726f942f35404b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb64a20e47b53659d72ea439ea8f8d11", "guid": "bfdfe7dc352907fc980b868725387e98e486ed54c30899e879e6cda2f1302d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd1bb6e2673345ea653b9008258ad14", "guid": "bfdfe7dc352907fc980b868725387e98badefef494dd24f1b6b2930bd4601ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc7f31be5cb5fcd6e7d09ebaaf775b5", "guid": "bfdfe7dc352907fc980b868725387e98172495ca0e1b0f4a1f6c894a66d9efaa"}], "guid": "bfdfe7dc352907fc980b868725387e98394f0d1a1bb8def16b6733d00d9f498b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982070d654dbbb417b30c6e73f9be8c079", "guid": "bfdfe7dc352907fc980b868725387e98aba77ec164690091cb97b002fce9516d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823fc93ea0251e85ec30aef1eb43e0eb1", "guid": "bfdfe7dc352907fc980b868725387e98e14d3554a81c9959b18804a01ea49635"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868ec48866eceb72680c8c87569860135", "guid": "bfdfe7dc352907fc980b868725387e989280940723a08fe869d18a3acf60f0eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98679cfdc02340b7f33f5be7f4ccf89b05", "guid": "bfdfe7dc352907fc980b868725387e982fcbbe9a7ec09b4b1cd4dcb8ffb12651"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89ceb08f81e5d06d8a64d0339a7d4b8", "guid": "bfdfe7dc352907fc980b868725387e98f32d4f22722cb949f85bdbd6e1070e7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880fd2f587609b7bfcff92ca8d953b788", "guid": "bfdfe7dc352907fc980b868725387e98317b838aa8b02c51af7c25b41207c738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a7a6dae689c40a1a2471458a7e2630", "guid": "bfdfe7dc352907fc980b868725387e98941e6551418989e8f864a212bd0e293b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983057641969d802858df8f50db3329e42", "guid": "bfdfe7dc352907fc980b868725387e98e2e048e4e14f86686d82d2dcdd621912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881c181419157d69769998463df2a45a8", "guid": "bfdfe7dc352907fc980b868725387e98bcd52347913618ecd9a0c53499c27253"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8cd885cdb0b6ce9de7e0dde2424cb64", "guid": "bfdfe7dc352907fc980b868725387e988e9e5ecdc78ce3e8834f8126cfd085b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f67ccf0f9fdc79aba839e12ea33a059", "guid": "bfdfe7dc352907fc980b868725387e98c6caa619ae2a4b800eb4c52a44f5b2cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987267059a1dd69414c2577b3d25086635", "guid": "bfdfe7dc352907fc980b868725387e98990d46f31ae2f9822b0f65b44a15f376"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465c8515235b81d8b9ce39ef51f56f53", "guid": "bfdfe7dc352907fc980b868725387e98438129d3752be1aa8bc0d3f093e470fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d16d1f51bb36e64b34a0d4d05c3370", "guid": "bfdfe7dc352907fc980b868725387e98f30ee21cf016fbd2ad8770a739391e4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4dfe00da2be7c96265bb9858c529e79", "guid": "bfdfe7dc352907fc980b868725387e986c185fa70684a16df904fb1e4f184bf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f30b0df483acd3c279f7a89f5f5e68", "guid": "bfdfe7dc352907fc980b868725387e988005a129c17179716860f46d52b2239e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe924eac7606279a7a47c8cccfedd79c", "guid": "bfdfe7dc352907fc980b868725387e987a230c04766c46ebd78a44f50b2df672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9a76769f8835e046d8f538a52846bf4", "guid": "bfdfe7dc352907fc980b868725387e98c8530d405dad127a67a2a3d4b2efdb6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03dea42234a64701967dd446f7cf173", "guid": "bfdfe7dc352907fc980b868725387e98f4b941fe778de436b66221cf288af8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988baed7398f8068fa6f243dd4199155ae", "guid": "bfdfe7dc352907fc980b868725387e98f329a675049e817493b06ae6551be8a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f352005ed1fbedaf847511cc735cf494", "guid": "bfdfe7dc352907fc980b868725387e9872dc4b7c6fdd9c84dc51b77bc26d9e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988db4cd1a32e27a6bd5d0701f47f29f13", "guid": "bfdfe7dc352907fc980b868725387e9888f76a446720e2aea6c1d721de1fc21b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98451bbfb51bb6f9e9d49d07f9d9af58cd", "guid": "bfdfe7dc352907fc980b868725387e98b18882a0768ea894ed8d6c05a3670108"}], "guid": "bfdfe7dc352907fc980b868725387e98acd56467041527d4c694a42e8909deba", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982beb0c3f155d558a21909f3fa7324af3", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "libwebview_flutter_wkwebview.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
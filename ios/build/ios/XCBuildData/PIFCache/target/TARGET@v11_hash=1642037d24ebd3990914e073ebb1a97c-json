{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985e2e05ecd55741e953d6d3f633c52e19", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e989df572eebdc23f142514c7f569d053d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eb366b9c54cd60af0432cb5185746eda", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/StripeApplePay/StripeApplePay.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e983fb28edb6e8210e427b54b8e08dcfd99", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eb366b9c54cd60af0432cb5185746eda", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/StripeApplePay/StripeApplePay.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e982d388da81c6e053dc69d003d876c44db", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412d71ea091a492eebb6af4c32d0df", "guid": "bfdfe7dc352907fc980b868725387e98df138987c4f1800757c1dbf7cfdca979"}], "guid": "bfdfe7dc352907fc980b868725387e9868fa8f11054ad02d18e59e8245f1e8bb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dfb65788125cd4e23925f153f2f820d1", "guid": "bfdfe7dc352907fc980b868725387e989d8248c07f0311f9896bc530a05b07b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b29a92269798a9e7c5c3370aae8b37", "guid": "bfdfe7dc352907fc980b868725387e989b7a0af60e04169d79772206378c580f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c7c9719feb02d0be0f84add89348a4", "guid": "bfdfe7dc352907fc980b868725387e98d6d0e883c6f46fc1580ca234679a9a88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f82960f91913ed1ed03b495d1ce33f9", "guid": "bfdfe7dc352907fc980b868725387e98261a5d3910c7ec24c4b0befe0c0e46c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be004dee994b439b075949bdb3cd551e", "guid": "bfdfe7dc352907fc980b868725387e988c91e05d73e5527fa83dace0ab90252e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b34d314ae3e8d0131ce68c59d188cd1", "guid": "bfdfe7dc352907fc980b868725387e98b85a73bed18edf939e8880bd2e08f72d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd9200b8959722e9d172f3dc64a2b301", "guid": "bfdfe7dc352907fc980b868725387e98951dac2da710c627585eb3904330ea81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039881e84b6d5f396c7a3d3055677928", "guid": "bfdfe7dc352907fc980b868725387e982917fb24c6a7472db4ff25ee4541fc83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf3335cbddf70b8345026a0fe8af15ac", "guid": "bfdfe7dc352907fc980b868725387e98fd2c05596f9fded305c30b86a0fce172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dacf4cc721b7a386cc2a298baadc13dd", "guid": "bfdfe7dc352907fc980b868725387e98cc8aabb532042c9a81c342883116509a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986716b3576accf19b562453376f38514a", "guid": "bfdfe7dc352907fc980b868725387e98bf1d4fa2462b9eb0b041466068b94739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae8ac1f5022775667da99aa06365578b", "guid": "bfdfe7dc352907fc980b868725387e989d9aae4406f8d188c4a52d5d5d8d9f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5114175d042d9dad102e04bbd29db2f", "guid": "bfdfe7dc352907fc980b868725387e9833ff5204b96aae1e0373cdb3a87f01b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a680a2c2a509825e2e2729e505c35d9", "guid": "bfdfe7dc352907fc980b868725387e98a0b65ab8d557eba3f62a45b6f9600566"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a339570805cd6dd6613036c22e8d8a", "guid": "bfdfe7dc352907fc980b868725387e98db9ef38e251a43d6dbde5c7dd0db3ff8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a529ea20729bc113a1a9ad2d4e9242d0", "guid": "bfdfe7dc352907fc980b868725387e98057b5d05c6163b3d8d108d6ff875b1bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b5122fd3719dc1fe5684f953d44b7a3", "guid": "bfdfe7dc352907fc980b868725387e98b4894cc6d9eb3c5d97323132e6834175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c2c83c2e9041ee7e670e73074f9385a", "guid": "bfdfe7dc352907fc980b868725387e985a89b609a6f81e844df0994d0937c3a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eeb8c2e1ae0ac70e23e3636d4b397a0", "guid": "bfdfe7dc352907fc980b868725387e980ca11320cd6a6c7507d917299ecf7ab1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825668061a40206c9aa7cf99fd9ccca81", "guid": "bfdfe7dc352907fc980b868725387e9814e6a7544e1168908b0976c4fc9d1694"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844d1fd4db27a7ae3fdd75fd0f792778d", "guid": "bfdfe7dc352907fc980b868725387e986157a390d79f40c21d68b8d18c7d15f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce21c6d5c8e8bcba7dbbbdb0a5e7204d", "guid": "bfdfe7dc352907fc980b868725387e98a88daabe32a78b0ffeebaf39f26bba94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4517004395e9bc3741f9e1b9e19785", "guid": "bfdfe7dc352907fc980b868725387e98bcac92ba92f2c287587d053587ce4012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988491c7851188b1d27559b1b0a27a08f6", "guid": "bfdfe7dc352907fc980b868725387e98f9661fa8addf770edba841707d7b58c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3f34bcd8ad5bb9ccdb9684662b5d69", "guid": "bfdfe7dc352907fc980b868725387e9800ab8e2f897c515eee9a4c2556a5c570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a5735e113606af4100be045b313e60", "guid": "bfdfe7dc352907fc980b868725387e98c866c242b09a02170307ce81674fc8e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987891e1973a47682821cdf5b37bb28c85", "guid": "bfdfe7dc352907fc980b868725387e9805cb200885478a3b7e0d358c6cadf233"}], "guid": "bfdfe7dc352907fc980b868725387e985986406c812387f7851c2f20998c4ab7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985742853e27b0c82cb27c31316713e91f", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e989e0826942d527e2b449d2332c5c9cb9c", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/StripeApplePay/StripeApplePay.modulemap", "${PODS_ROOT}/Headers/Public/StripeApplePay/StripeApplePay-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "4BB5775541E563F7149B34575C74A19F", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/StripeApplePay-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/StripeApplePay/StripeApplePay.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/StripeApplePay/StripeApplePay-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "libStripeApplePay.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
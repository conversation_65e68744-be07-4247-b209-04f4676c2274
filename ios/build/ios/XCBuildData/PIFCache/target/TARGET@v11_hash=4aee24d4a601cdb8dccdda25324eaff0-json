{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98566a94f2a5b8de90a11769d15f745d20", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e9863743f0d63512bd2f3a64f3a1534d281", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819435696a19dc9361bb52a39167f337d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseCore/FirebaseCore.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98ea788f5575430c859d522f7fb6604abe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819435696a19dc9361bb52a39167f337d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "MODULEMAP_FILE": "Headers/Public/FirebaseCore/FirebaseCore.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98c14db6211219003f11fd4ab6d1cd4e01", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb8fedd7bea4ff0d35608c12b1771a0e", "guid": "bfdfe7dc352907fc980b868725387e98f12dc4ff07e7c88cd01d6648604dd0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd125d59231e055a53fef42a8aca308", "guid": "bfdfe7dc352907fc980b868725387e9838fb0125d591163bddcc32590fdd4367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872302f61e398714a774dec43e26f118a", "guid": "bfdfe7dc352907fc980b868725387e98508539df622b89e59bc8dee89f2b9724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98747bea2469b3db8bd71de6983b735856", "guid": "bfdfe7dc352907fc980b868725387e98f129efffd290698512dac196d2e207e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f57eb8e1138f74cbba180bdaa0b21a5", "guid": "bfdfe7dc352907fc980b868725387e98e022bcc0c84b11a34a909e5e6863ba96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9cd6c309eb56f68b1ef9349dfa3ab50", "guid": "bfdfe7dc352907fc980b868725387e982a1d94f41a3cfde4e969cac3b88f8be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71d138a55a6e4d013eb24c42ea740fb", "guid": "bfdfe7dc352907fc980b868725387e98aa5317be21aa0c24aa7a9283c35d9026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73d504ec5249165dc0080329b05fc68", "guid": "bfdfe7dc352907fc980b868725387e98283d3ac0080135f2780c17c6a8b79032"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801a906208e16c586d7afe61b1b633567", "guid": "bfdfe7dc352907fc980b868725387e9898320df6d45d0b323d9ba0bdf502ceee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989263c2f4434cd967bdf10d2cd3ffc43d", "guid": "bfdfe7dc352907fc980b868725387e98aaed615f81729d3666e3fc33a8be0e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7779517f110f6174195088b6b012c36", "guid": "bfdfe7dc352907fc980b868725387e98a1802531b2b7d9b62ac45cdf714b65d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e63da078b590f71dcf8e66755f0fca", "guid": "bfdfe7dc352907fc980b868725387e9853695b2b9afb1c0c4fe34558455f2465"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7ea69fea8f85cc7182309f8a5fb93f", "guid": "bfdfe7dc352907fc980b868725387e98a43a693814e4ea588da5ff92225fec94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989156365ce57af6071cf1a9ba7e34b06d", "guid": "bfdfe7dc352907fc980b868725387e987d21b18d1a41c22c97b783a892e47a40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873676caef9900e0776a92376bd32a448", "guid": "bfdfe7dc352907fc980b868725387e9823e4bf9ec0ea0ec29bb7c85c5f81e3a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90358d04b34d9ab0c8bc0f0739cac2c", "guid": "bfdfe7dc352907fc980b868725387e98b0061d86b186717995681270d1467dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d21115904c46e0384b0bc5d9c86fe1", "guid": "bfdfe7dc352907fc980b868725387e98a25d0fbeef14731cba03b0073040bf7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce30e22ae64e7c121e01832c9bf182f5", "guid": "bfdfe7dc352907fc980b868725387e98322dbd11cbb006d9c03acfef3dd5e0ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98815ef4c39a89460e2d5c9d4e5a64b922", "guid": "bfdfe7dc352907fc980b868725387e985ae057ecc6d18cabb164e49fa7c61980"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981beba1912c2480cb8603bf4e8debfad9", "guid": "bfdfe7dc352907fc980b868725387e98c911063c6a715cb052ae214bcb07415a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d29b583308e2064b11d9b89a29238224", "guid": "bfdfe7dc352907fc980b868725387e985f90e4991fbd13e03e0d6cbdeaf77bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a73b6e8f307dc33da0a83f666c78bf7", "guid": "bfdfe7dc352907fc980b868725387e98c50f3bf91d0baebf481e586a2c90b6c1"}], "guid": "bfdfe7dc352907fc980b868725387e981564ab5a9d30946c67b095128b287977", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9826d15103d735fdd118cf6cb090e46069", "guid": "bfdfe7dc352907fc980b868725387e98a14abb9cac822efb874b64f6c3ff4ef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b86722bfc7b0d07f1b5c8c99f5aaa5", "guid": "bfdfe7dc352907fc980b868725387e9861187c021042baaedce4a51e68cfc059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987308bf3653f33266f8c215f8332bc518", "guid": "bfdfe7dc352907fc980b868725387e984aef6803666d566fb1ea92500931dca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989526944db259ea4f4d13d5ec77814ee6", "guid": "bfdfe7dc352907fc980b868725387e982f3ab83123317bf5c24f5662836b866e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f979d73b5934f2d7fb8f5a808ba14b43", "guid": "bfdfe7dc352907fc980b868725387e98f2431b4d82a39854b45e354d863efbac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b10d6893ca155cd0945bb5710f9b87d", "guid": "bfdfe7dc352907fc980b868725387e9884e45ed27bc8b618db4a20438084f14b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a0049bf25890ee7048978794525aa1", "guid": "bfdfe7dc352907fc980b868725387e985d70300e89b491340c2221dfd01c5df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf2eff451b901eda2dc7d7d928f2869", "guid": "bfdfe7dc352907fc980b868725387e98b118b64d982d531e09f2bd877855e2ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ba4cd1057869baabc33c8dbb57e9c65", "guid": "bfdfe7dc352907fc980b868725387e98bfc545a31d287560d10f6ef208aedc1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817436d434d72e5aaa6e761f5bd93463e", "guid": "bfdfe7dc352907fc980b868725387e989c1c71900e85ff9169f889dae079faf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b1abd044d2a859465b12695b40b8aa", "guid": "bfdfe7dc352907fc980b868725387e98d1815fd3ca21f38fc6d1e429ff894930"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe2fc251c31259824ac8b6079b6281c", "guid": "bfdfe7dc352907fc980b868725387e9883276982ff64d5f0eb1bd72423ec66fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d733ca72f3c732a561adcecc97327be7", "guid": "bfdfe7dc352907fc980b868725387e9829425eb4dd99d6b81b9a86e680aebd31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eae4c00e2db7b9f131957706af1d52a", "guid": "bfdfe7dc352907fc980b868725387e98c861dddbe9a9a0f862f12859e4756850"}], "guid": "bfdfe7dc352907fc980b868725387e986490aeaa6375fc8998f129a03a35366d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9852921f7dac95bc6c90ee7d9677cc4a08", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "libFirebaseCore.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
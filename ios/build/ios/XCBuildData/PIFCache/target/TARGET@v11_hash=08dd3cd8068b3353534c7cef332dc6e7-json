{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982276b4a8ba261ccd1e70cd20d6426d65", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "MODULEMAP_FILE": "Headers/Public/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98a05533a8abac7972b46e6f39932e5590", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896b55c47524266bbc30f30a4fdac769a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "MODULEMAP_FILE": "Headers/Public/FirebaseSessions/FirebaseSessions.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9876b00cf144515258b3af69fdc53b8a1b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896b55c47524266bbc30f30a4fdac769a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "MODULEMAP_FILE": "Headers/Public/FirebaseSessions/FirebaseSessions.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98f4ab14ad4ea3c4174a4a1ad44453dc60", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9821d1079bb001beea74a7572de15a55ac", "guid": "bfdfe7dc352907fc980b868725387e98db12379944a115974dd68920af34f544"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fce48314f7a3eeab904db19e681af7a8", "guid": "bfdfe7dc352907fc980b868725387e9833bf565c15bea6396dac1c8999e36af0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801170dbb34a88008f4113149276c90f3", "guid": "bfdfe7dc352907fc980b868725387e983d2367b2274789c92097c716dce0acc0"}], "guid": "bfdfe7dc352907fc980b868725387e98696c9ce901352050e5b92aee7a97bd3e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d6696c7470d5093cd7722838912a8d9", "guid": "bfdfe7dc352907fc980b868725387e9889f929748bfa228a655af1992082c477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed9618255dd4a46daa81e6ff5cd5148", "guid": "bfdfe7dc352907fc980b868725387e9827e8581ae2ab256c37f22becbd39fa8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817ff9b4c336981237febe1efad0b9a01", "guid": "bfdfe7dc352907fc980b868725387e987d81424b4bb0fb3ecc1a1e87efbfc6b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d89d6de330ef88e2d1d979b4e5953e", "guid": "bfdfe7dc352907fc980b868725387e98380039fda328f4d62afc16b817114f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2745423c372b798ebc97c65a3c80d34", "guid": "bfdfe7dc352907fc980b868725387e983f9faa5167343b35eb20ff70bba8b19d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980956b6a4191987d06607fb2fd5bff280", "guid": "bfdfe7dc352907fc980b868725387e98bb2e96448b3a6864afae8ce298144f15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98539f0ca252587711ca0a5b560e9dc018", "guid": "bfdfe7dc352907fc980b868725387e98f46d2ce88eb1a734daeb58c6cbeb40b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc9823d9c9eb36631122cfccdade3041", "guid": "bfdfe7dc352907fc980b868725387e982b7547c1f63afa581a88a23ea6078208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fddca6a3146134bf45fced08eb0eeba8", "guid": "bfdfe7dc352907fc980b868725387e988a83020047aee7d40d1997516d0f9f32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb1669f49342f723927a0e8b8185843b", "guid": "bfdfe7dc352907fc980b868725387e9856dd1769b9ddc43149503d04e6438e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1259667e298e70f6799e6752fb88fae", "guid": "bfdfe7dc352907fc980b868725387e98f275e6cf75d4245809ca2658f9c3f6b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e03621cc02054951b01cc3b3488474b5", "guid": "bfdfe7dc352907fc980b868725387e98b6a71c35c022b707b73b3ee334feaf2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c875ff6a9fed97c474eab4472261239f", "guid": "bfdfe7dc352907fc980b868725387e98cf68c720faca8b5c00e1dcdf9cbc33a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f50766509590adb316466df032d6714", "guid": "bfdfe7dc352907fc980b868725387e98bba64b0e16a003ba16057302b853a212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc0e4be44cfbdcf2a6b4ad04be9008d", "guid": "bfdfe7dc352907fc980b868725387e98028875912446336df24fc733fdd864be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989223ebd0a92e0ca78e2131fb626e37c9", "guid": "bfdfe7dc352907fc980b868725387e9870ce4a8d2d692662b25308524d49f3c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849f24d8e8be430db6369905977ae45e", "guid": "bfdfe7dc352907fc980b868725387e986fbe00e2dbc6084b544006206f2de114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c708ff8eba475b19f6f40860afcdb9ac", "guid": "bfdfe7dc352907fc980b868725387e981c422556c7a812407364f655bc3df8b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b5d3bd8d311761638f182cca43be64", "guid": "bfdfe7dc352907fc980b868725387e98bdcc506e32ee3017048a1b94053d4f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807fedfb20f9329d01c21b81564d3c2b7", "guid": "bfdfe7dc352907fc980b868725387e988accca8eeff60939d44c51377bc26e0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340ec7d015b44d78e229384c7f4f6874", "guid": "bfdfe7dc352907fc980b868725387e987c7ce24147babaa1aa14aa88554e7e9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca4f31e6f9d758a1230b6c77db7bd697", "guid": "bfdfe7dc352907fc980b868725387e98a1a78a0dbfb168346da7d09e980a94fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865573cb27add709a946b429da375829c", "guid": "bfdfe7dc352907fc980b868725387e98302ad8cf29254d948c48626b7a511208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df813855279c78b4ef379d4d96639993", "guid": "bfdfe7dc352907fc980b868725387e98782571874efc20d6718531e9ac7aba62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843922fff571180346b4a46acebf6bccd", "guid": "bfdfe7dc352907fc980b868725387e98a14a2aee54e369a7795d20c6d9655483"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e44aacc343f1d10a13bbaaa92ece9b6", "guid": "bfdfe7dc352907fc980b868725387e987bea5fd02f1c9d369e877214c324d505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099c46485618f7c1cdf7afe62fd7548b", "guid": "bfdfe7dc352907fc980b868725387e988c525957db13dcc71b73d5f33655b45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19012dad48385012b89a03943a5ebe5", "guid": "bfdfe7dc352907fc980b868725387e987063cbac1903816cc43414a90d90b7a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f18f03aa9c2e520a57119b12165e499", "guid": "bfdfe7dc352907fc980b868725387e9846e1ea89c9ac869217cb5343f46d62f0"}], "guid": "bfdfe7dc352907fc980b868725387e98c451835dd9191d466f74618210d271ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98dc7e336925b476f70243450ae1fe5d3b", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e982c919e8972e4af415d6bb7563e407df7", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/FirebaseSessions/FirebaseSessions.modulemap", "${PODS_ROOT}/Headers/Public/FirebaseSessions/FirebaseSessions-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "9EF5F813AF526227AE46F584341D433D", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/FirebaseSessions-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/FirebaseSessions/FirebaseSessions.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/FirebaseSessions/FirebaseSessions-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "libFirebaseSessions.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
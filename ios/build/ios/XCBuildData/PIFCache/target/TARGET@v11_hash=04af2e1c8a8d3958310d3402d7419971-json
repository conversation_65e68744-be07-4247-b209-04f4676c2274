{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984970fb539de807307657615a257a4ed2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e983a12606e2ab3436484cd8fa4f25b9b7b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec94bd2b5a1a8dbf49688866b01d984a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98e12bd6f73b63181240baa3d263dcbbed", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec94bd2b5a1a8dbf49688866b01d984a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98010fe937499eb6c0746a0354129c5402", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c421f6605eb714b62b02a61b0f5bb0d7", "guid": "bfdfe7dc352907fc980b868725387e983eb92f7bea716357aff92a371ad4ef14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce4abf5966d2bbb30ecad3502144b57", "guid": "bfdfe7dc352907fc980b868725387e98c5a7e512be4d47f283223b70b8399a7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ddb9d6ebf3602a2c6fc5d5fb493895b", "guid": "bfdfe7dc352907fc980b868725387e9834683fcf39d445c2d099d156bd789472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e348b08da03bbe97ea3903474279ff9", "guid": "bfdfe7dc352907fc980b868725387e98b52a17020065a27434983894a6f88ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa306c57baace3e69aaaf85d875d80e9", "guid": "bfdfe7dc352907fc980b868725387e98da74bbc8d24ff6bfe1301155605a4bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ffefcc2e87685e92795efd63e8539cb", "guid": "bfdfe7dc352907fc980b868725387e9847e2e8a6c78c9210c162a76c68c5a154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d66adc066a28b5e6d8a57ee013c5e5ab", "guid": "bfdfe7dc352907fc980b868725387e98d913cebdfca88c67b3be2abcfa409f4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998bdb16092eccd096eb885db8a10e4e", "guid": "bfdfe7dc352907fc980b868725387e981ac47055b3b37f1905f6789e7cbdc397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984289a94d866e1e5421b1e759de24c7a0", "guid": "bfdfe7dc352907fc980b868725387e98a5f10088d6a3d3cfd73427d1d8bb6941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960b42069f59e81cc0c565f0e3dcee11", "guid": "bfdfe7dc352907fc980b868725387e9839e19580d6a8d0b2a51f9d0eb738d9fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d55e94624f57309886bffc2849a09c1", "guid": "bfdfe7dc352907fc980b868725387e98b29fe27c415c3759929401b0639e69e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986654f9f77aea89b02b3ea16e0a54246e", "guid": "bfdfe7dc352907fc980b868725387e986438116df7f14cdf4eb40a8f22524cec"}], "guid": "bfdfe7dc352907fc980b868725387e98632239dd4bd291142058664f95a3d224", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd77ac5e3e9378b83a5d3b29abfccf37", "guid": "bfdfe7dc352907fc980b868725387e98f188987574893ea44dba5bc6f122f96f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a13cb51a4df39cfeea7c151db43b28", "guid": "bfdfe7dc352907fc980b868725387e98ca7c0219e2479f568aaafc5e53ecfb9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84eda5b67a666c6ed2c17de6b0459e9", "guid": "bfdfe7dc352907fc980b868725387e98b9d0c1df4f8db5919f0c2011bd7467da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38fa3e91c208dd7cb94b6645a671336", "guid": "bfdfe7dc352907fc980b868725387e983832ff2e6b1b20f418fa554d5d2e68cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29f7343e803a0505d043e5e4649f69f", "guid": "bfdfe7dc352907fc980b868725387e98e52fbf33cca47e3ca7717bd772286b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846c6189f6801cf393cb8204982a3bc4f", "guid": "bfdfe7dc352907fc980b868725387e983e478b5d942a9d058983584eef803cb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982121838f04b2d54a9c05ce1e79ee18d1", "guid": "bfdfe7dc352907fc980b868725387e985c1603696ff742bfc654ab044add024f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98251827dea6a39f8d07dc927c31fbb41f", "guid": "bfdfe7dc352907fc980b868725387e98c33ec57ed1a82613499bc19f78827a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c829ce723635d1b0b335695edb5f1c6", "guid": "bfdfe7dc352907fc980b868725387e989500f042cb47b41cc407aa9334415a3e"}], "guid": "bfdfe7dc352907fc980b868725387e985b5ee4c65aa1b5f8697092906de96707", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d7299aeaf0b3319cc4df57ec7e66204c", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "libsqflite.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98055db8619aaced5b30bf1140c9f525e0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/Promises/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "PromisesSwift", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98ff7e207cad7e03ba268df7f0d3ac91d3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e46fde7f4a3bf82d9eae6408e9a7795", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/Promises/PromisesSwift.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "PromisesSwift", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e985978fd958ece0b65a746cb13a056925d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e46fde7f4a3bf82d9eae6408e9a7795", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "MODULEMAP_FILE": "Headers/Public/Promises/PromisesSwift.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "PromisesSwift", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98261297499658cdcf5ba303138f5892f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f39aa11a098f3c55cc5f7f4d64ae05f", "guid": "bfdfe7dc352907fc980b868725387e98c7484cfd8b0f6be3e384d0268f7f146a"}], "guid": "bfdfe7dc352907fc980b868725387e986666373ee39c2af2f78c1d1eda1886da", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f084237bde67a17e42ed59325d1df18", "guid": "bfdfe7dc352907fc980b868725387e9896448dc7c6ebc57c9c70faf92365a7d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846468b47fb7520386cf34a7633ec015d", "guid": "bfdfe7dc352907fc980b868725387e985882b100ebf235323d5dc8ebd21419fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2c02d3bab4fca67efc825aece369bc", "guid": "bfdfe7dc352907fc980b868725387e98652085982013cfd304da4c6df9a0c43b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2e992f0b95550edae6634f8cce76d5", "guid": "bfdfe7dc352907fc980b868725387e98eb360d11e70218e6ba479a53a6031375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c02187dfc08ec99c45b19c56cb9d3488", "guid": "bfdfe7dc352907fc980b868725387e98858661f4c5e5d77241e9c7f6d4eb571f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e9f8d804d6eddbab270a246c6b8fe1", "guid": "bfdfe7dc352907fc980b868725387e9807ab9caaab4dfb01349d480fd6db8030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd028a766c6245dcaa4fd5169426b45", "guid": "bfdfe7dc352907fc980b868725387e9826cc23c79273160faa43b0fbbc4bf593"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98479d0c0fe4ba6fef74e29657edb853ef", "guid": "bfdfe7dc352907fc980b868725387e982e2b631d8d6f61b9aa967f6a541f70a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8aaa440c1f4a60f50c743a8c8694602", "guid": "bfdfe7dc352907fc980b868725387e98c5a5b0fc524ea7a8468b4ceb5e9e5ec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262d0a196bfa975f32f465ddbe5ba72b", "guid": "bfdfe7dc352907fc980b868725387e98f5b2f8fcdd9d2f827ef4b4692c97d96d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982472288ef2feeb583dc77cdecc727250", "guid": "bfdfe7dc352907fc980b868725387e98dabbd6a7ebd6732e06e03c5617a09e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9ee0484be214e1a215cb7bd847d3d3", "guid": "bfdfe7dc352907fc980b868725387e98cab114a7d862af757ba1251dbb9e4c82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98facb6f8ebf6fb04d23ae823c2e8b98bf", "guid": "bfdfe7dc352907fc980b868725387e9822012aa45028c548d62743a02b7477ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8725fe3d37177c51de29bcc0268d63", "guid": "bfdfe7dc352907fc980b868725387e986c93d956a15af1a4140d8ce6794fa177"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5dfd99beb6cca62e97c4f114b21a25e", "guid": "bfdfe7dc352907fc980b868725387e98738109a2654826c927ccd0a2f5ffb3a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eba2a0e1c8eaf3f072386ec24dce9d1e", "guid": "bfdfe7dc352907fc980b868725387e983d8e2dd2c0eb94fb8a25185e8ec4659c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a4664d704d30b62ecd3086fcb1e6085", "guid": "bfdfe7dc352907fc980b868725387e989dd0f2313bb84d146a91dff16c9863f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d22a8eb39f35eafb10e953637ce4414", "guid": "bfdfe7dc352907fc980b868725387e983613f2c5215ac31ab778598df1951f4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c0e471a9d6d422fa4940bad058b5793", "guid": "bfdfe7dc352907fc980b868725387e98d533742d1f2e0fb63c84d5b54d121192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811664c8091e0b17108f5e455aaa011ed", "guid": "bfdfe7dc352907fc980b868725387e989b8e03ed40560359aca69f66b6802dee"}], "guid": "bfdfe7dc352907fc980b868725387e9860173f48dfae5f0d8fcc310cbc52664f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9855298a0e847052988fbcaeca73f1f073", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98c812715cacfefa9a37d57e8189be3c0f", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/Promises/PromisesSwift.modulemap", "${PODS_ROOT}/Headers/Public/Promises/PromisesSwift-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "45403D1ADF13C9DFC1123C234A708593", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/PromisesSwift-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/Promises/PromisesSwift.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/Promises/PromisesSwift-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "libPromisesSwift.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98325ecc055ac0a4ebb5358a8a5b9a16d3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PusherSwift/PusherSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/PusherSwift/PusherSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "PusherSwift", "PRODUCT_NAME": "PusherSwift", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98559693706c2719dc86e6f0e99bd82d94", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9856ef6cfd00c08c9be5f72a339dfcbc6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PusherSwift/PusherSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/PusherSwift/PusherSwift.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "PusherSwift", "PRODUCT_NAME": "PusherSwift", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98a5391206346b1a9cd33d5fe44c4ee34f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9856ef6cfd00c08c9be5f72a339dfcbc6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PusherSwift/PusherSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/PusherSwift/PusherSwift.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "PusherSwift", "PRODUCT_NAME": "PusherSwift", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9857619cbf4b80f8f80e1fcc86a9f7e197", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be1f8c5aec9f428372bd3ec7dd856961", "guid": "bfdfe7dc352907fc980b868725387e981eccf8c8824dfd6a006ed3aeeda3cae9"}], "guid": "bfdfe7dc352907fc980b868725387e986c49282e708d2f1ca50401a449ab6dbb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9810e4020a7d5ca8b7b96a5a00d0396f71", "guid": "bfdfe7dc352907fc980b868725387e9823cc6cb5d8991d6f266ee19cf28750ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed88467625cacbebe3c01a410d82d66e", "guid": "bfdfe7dc352907fc980b868725387e9827501e1f57da8e6d078a681caa8a7432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98527f60fc371612f9273e419f0f226e50", "guid": "bfdfe7dc352907fc980b868725387e985b2beaa4350e116e1f46e3e784175a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c11a76d375a62344ca6e86d5d7b8b1", "guid": "bfdfe7dc352907fc980b868725387e98243cd522bd93fbd38ca1f7cdbcec5fac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1dc96470cc33165ea9832209cffb2d8", "guid": "bfdfe7dc352907fc980b868725387e983d16c37ccd6bd64dbb097d71cb145f55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a524588fda2a9e164ec8e300ef01a5", "guid": "bfdfe7dc352907fc980b868725387e98caf53cdfe1bf3507deabd254a087f0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d811c570544752251683ee57e3d4be07", "guid": "bfdfe7dc352907fc980b868725387e983c008635caa828a9cdd145e903875266"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e22e791a48b899562de4c1f240eb969", "guid": "bfdfe7dc352907fc980b868725387e98ac1007aaa57769cd932ed46e2ad0d10a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2f23d2da074bf4934f12b68a6822365", "guid": "bfdfe7dc352907fc980b868725387e981b288b7f896b14cc5ba82335187f3768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a9a8b703c982ed83c9d2567d5f0d5b", "guid": "bfdfe7dc352907fc980b868725387e9861257f23346bce97a378dc400db3e1b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd0e840df472687bb3878be2781fa68", "guid": "bfdfe7dc352907fc980b868725387e987e1312d127458c00feb40dcfcc52e8a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98072af8ac0338c1582818fcf032757050", "guid": "bfdfe7dc352907fc980b868725387e986ff1722b2ef836a489ab92fe0c22cca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c5894a28538edb401c0886e22d2e354", "guid": "bfdfe7dc352907fc980b868725387e98391d478ac06c8b01a0d848170df852f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cb985a3c8cdf378b2aafd02cdcc136d", "guid": "bfdfe7dc352907fc980b868725387e987c2ff5390dfc1a285d17a64bf4f6d1a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809bd53872cfa895ef9bef50467d58269", "guid": "bfdfe7dc352907fc980b868725387e9852308f239e0bb048d48a584fc1911598"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897fd91c39aa10509b71fedf6301a4c53", "guid": "bfdfe7dc352907fc980b868725387e98d9655c317f82afe86908d453cc68e6ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842572200a9d6e4981e7f5049f28606d0", "guid": "bfdfe7dc352907fc980b868725387e98294baaa519b798907563b75c3c3ea7f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987105e55b24d3542be775482bdd1f0141", "guid": "bfdfe7dc352907fc980b868725387e9893341d7d6fa16633f589046f8f861fbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98372af70d998943dbe972b2d8fe8ecb17", "guid": "bfdfe7dc352907fc980b868725387e98c7cc0233334a28d15565a722c0054fca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af91284f96a5677de68d142e3084b82c", "guid": "bfdfe7dc352907fc980b868725387e987f81f8a7a69b75e8e443f70e8ac6075d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1119b9f87ed5d7bc651e25a4f59bfbd", "guid": "bfdfe7dc352907fc980b868725387e98e320e10b016a99ae77f8a33eb658e0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b6855f2e426e27870fbeb4878aaf75", "guid": "bfdfe7dc352907fc980b868725387e98716011b25e950d7e0dbc4cc2585bff46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983141864f52731077b04f186ac52d8d84", "guid": "bfdfe7dc352907fc980b868725387e9807fc249541396b0b5366fa464ada97b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98410d9393dd5b674ad67aab290e6f9cac", "guid": "bfdfe7dc352907fc980b868725387e98c76ccd7def519a035293486016d28489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858e330b443603625a861310c2909ce78", "guid": "bfdfe7dc352907fc980b868725387e982aeda3c1763e5a72ec6632f08d563fb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b328ff69e0804bb52cc1dc50ec53149", "guid": "bfdfe7dc352907fc980b868725387e987effa8dec754fa184ea96c6dec1e3201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac6a6947996efa088b29c10da56eecf", "guid": "bfdfe7dc352907fc980b868725387e98f3c1404d3de0541d8c2a3e08fb3ae91e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841709e0516d791bf415a6b5824ad1370", "guid": "bfdfe7dc352907fc980b868725387e98ce1f97a242cd428a191efafc82fde1a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866dc967f8f2f178f0910d0e6ba165324", "guid": "bfdfe7dc352907fc980b868725387e9867ba8ecde6249d58d98d58c365bec358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d90bb4b94381d458e01d6b9d92e1eca", "guid": "bfdfe7dc352907fc980b868725387e9884af1cec7cbca3f3ddf8a1c07ec5d83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebc416b85556d8cb4408d3969da7d9f2", "guid": "bfdfe7dc352907fc980b868725387e982cc5b702e122fb1d70745fb6086d9c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984059a0ff36d783017965ba6d3e3f9433", "guid": "bfdfe7dc352907fc980b868725387e98949e55123f4c7c1b9d3e35a1ce437dae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86cd7ab8f2a62f55a20ed20dd51593a", "guid": "bfdfe7dc352907fc980b868725387e9855f18155c0b2a555f27325aca279a8e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865740a0e4b7973690568c66e40ac12df", "guid": "bfdfe7dc352907fc980b868725387e98282558a1b8b4c66d8ca7a715f117e2a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987980a267ed0402df0886beaa860cdcd3", "guid": "bfdfe7dc352907fc980b868725387e981a367f901f78a701f0e786112bb68652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c1806cd0d1f2cb4698b4fa07318687", "guid": "bfdfe7dc352907fc980b868725387e987477972e38f7f1ec9840a894f77312b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9ffcdadbb0287af9c0486c7155c2984", "guid": "bfdfe7dc352907fc980b868725387e98a4518fb9e19a04b83477a0f09379bb4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df7d896adb9c0573fd945a5b675e42a7", "guid": "bfdfe7dc352907fc980b868725387e982706f80e2d787c30d4a94d534b761d54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856dc39e65a0c537022c770899c576c5a", "guid": "bfdfe7dc352907fc980b868725387e98b38d1a2e12453758c77ba68b62827e3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b5a84e55426da25ff0f285085293e2", "guid": "bfdfe7dc352907fc980b868725387e98d4a18fdcdfd1b2f33e8e597536ac7159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eceb2a37cda4c69c52fa6ce3f300d7f", "guid": "bfdfe7dc352907fc980b868725387e983a09312cb70ef06f3e8fccab4c7cd6db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed162053894e98f3f2c8ffaccf90d366", "guid": "bfdfe7dc352907fc980b868725387e988bed40b3538ef383c3d2669f9e2cc449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf8eb65af8ffb7541c679b25897a6679", "guid": "bfdfe7dc352907fc980b868725387e984d4b99451698556b3c60872598ac01f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c050fb2ae753d26351d44d0425de61ac", "guid": "bfdfe7dc352907fc980b868725387e98ead6e20164c8df827321ef0ee6be030f"}], "guid": "bfdfe7dc352907fc980b868725387e9858be59314e0ba6f21758bcba41112a23", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98918bcf3f9c0e45987629c93de0f95b4e", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98dadcec1f050d480b21e129a558f5b448", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/PusherSwift/PusherSwift.modulemap", "${PODS_ROOT}/Headers/Public/PusherSwift/PusherSwift-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "C2763FB5C354C238ED642E78A0E0261F", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/PusherSwift-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/PusherSwift/PusherSwift.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/PusherSwift/PusherSwift-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98190a113842c85c603af8671f062d30b0", "name": "NWWebSocket"}, {"guid": "bfdfe7dc352907fc980b868725387e9842f7ac8b091bda0ff8b1dcbcdc5277a8", "name": "TweetNacl"}], "guid": "bfdfe7dc352907fc980b868725387e983ecc2fb8d7fda5d375e2f7543a8c6484", "name": "PusherSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f5139a27d98cff9b28c3c4506e72b4ba", "name": "libPusherSwift.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
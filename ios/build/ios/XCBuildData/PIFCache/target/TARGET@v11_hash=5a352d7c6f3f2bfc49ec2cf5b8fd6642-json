{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0f2f4bdcf512cfc5e4d9ddc66d34807", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeCore/StripeCore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/StripeCore/StripeCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "StripeCore", "PRODUCT_NAME": "StripeCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e982f6ac5a6d96c4191fc190df898d7df70", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dd0e24ed13c872e0ca886964334b89b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeCore/StripeCore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/StripeCore/StripeCore.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "StripeCore", "PRODUCT_NAME": "StripeCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e989ffac009fc44feeb932404dd39d87f1e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dd0e24ed13c872e0ca886964334b89b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeCore/StripeCore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/StripeCore/StripeCore.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "StripeCore", "PRODUCT_NAME": "StripeCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e988d55a9552ad977afc32fb495f3b48984", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b46d8bb9eae8e799359ca3b5e75a1aaf", "guid": "bfdfe7dc352907fc980b868725387e98d2d29a26eed23dd3445ef6d9842ce14a"}], "guid": "bfdfe7dc352907fc980b868725387e981658b3cbbe0a188cd3855584833583f3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98998733b1d5f191bc5a2e250867a6741f", "guid": "bfdfe7dc352907fc980b868725387e98b26958313e11faf9a01bbb511fd4192a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822acc6536bd329d68a8268a9771986a5", "guid": "bfdfe7dc352907fc980b868725387e98a3812f90bfe29b4840f1527b61f078eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595ccd0f5b0f3802fd851597736a76d5", "guid": "bfdfe7dc352907fc980b868725387e986a089cccfd3f35a5db9c167c1edef809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed21977e0bddcdd401091c901fa1f4f6", "guid": "bfdfe7dc352907fc980b868725387e98ecee72e0fca0c6e92e5345285724110d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865104921ee5c21f2d0db8ee9a019794b", "guid": "bfdfe7dc352907fc980b868725387e981f76d40b2e271b5f631acc9ed7d908f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f801e63d0ddff18f232d735e10152afb", "guid": "bfdfe7dc352907fc980b868725387e98fbe6ffa2d07e2e7f8b5c8b3e15b97cd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcdc9e3146a69a617b1f7762fb647d06", "guid": "bfdfe7dc352907fc980b868725387e980cdda1314a6e490ebaa098818e2b45d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852fec648b03c091ed7b4222c1891a6a", "guid": "bfdfe7dc352907fc980b868725387e98bc442ed9e0f6609f5b857f143d068d38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a43cf819adada63830d30daff9358253", "guid": "bfdfe7dc352907fc980b868725387e985b4714604d9515a71675e638778ed312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40f0da1f1c7ae63f83be07af643443a", "guid": "bfdfe7dc352907fc980b868725387e987063b2204ea627ee4ce6eeaadc16a249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94751beae0a2af84aa2785f93c86869", "guid": "bfdfe7dc352907fc980b868725387e9807c60984e12c5b3cabbd42100e36361a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ddc3696ea8d337c34730fbc845910e", "guid": "bfdfe7dc352907fc980b868725387e98646c0bee2fb540a2cc2fd7172a47ec2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828cef6f880de58dcf615b2ddd88ea8d7", "guid": "bfdfe7dc352907fc980b868725387e980516db87f63037e1a60cac27e6f483d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876d3e816ca6c0dda9584c91607e8510e", "guid": "bfdfe7dc352907fc980b868725387e98ab80a61d89c967f8a9d8404477082b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d052385d03fa3aae3ec4a108619c7ee", "guid": "bfdfe7dc352907fc980b868725387e98a61831b1c3ff1068976cc9c97e8f866d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ea37b6a78e88e4237a2ea39bfc26d7", "guid": "bfdfe7dc352907fc980b868725387e985541cc351175bbf8080cbfea5cab61c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85854e47aaf6eeab3b59c31e23a82d9", "guid": "bfdfe7dc352907fc980b868725387e98ffa79cff3371b14e3b55245879182330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d82d377b6c4f37e4406da62662f4e9", "guid": "bfdfe7dc352907fc980b868725387e988dc6ed1cd50f76221a39ada7684a7f3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbd328738846876c13d8f141828c6742", "guid": "bfdfe7dc352907fc980b868725387e982f318b5d9eb33502307e6cacb349d535"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895598c401e889438d5024d466edf5bdd", "guid": "bfdfe7dc352907fc980b868725387e98532c2eaf43df2057d622c7296af59c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d916b67e40e43eb6cb3396968a3a094", "guid": "bfdfe7dc352907fc980b868725387e98369b976ebaecee9f487b6e5fb0e85d3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c15f028d5e3a4c72d8b7c3605c49a39", "guid": "bfdfe7dc352907fc980b868725387e98f6f8995a1362e6863450b0a2d5f449e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880855f0159724d96bbb41343b1df62e0", "guid": "bfdfe7dc352907fc980b868725387e985c4b85389c463e2aff82e4ff21b9296c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834744a55a95846067a3b4d47c1a88c12", "guid": "bfdfe7dc352907fc980b868725387e98f28d40112c236093e6d3a08a1aed6767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983029ca1234352a001de4bad5eede7b71", "guid": "bfdfe7dc352907fc980b868725387e98b9762f58c745544f1a24fcc3542e723b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c13ad7bb8a289e357f55b79123ea46b", "guid": "bfdfe7dc352907fc980b868725387e987390ef1ebec0cad11c41cf46bb0cf37a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848fd87e7cbd1d6c8415c288272fa4c10", "guid": "bfdfe7dc352907fc980b868725387e9856ddea562e958725bbf6f8a577ff05f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c74556dd94b4171367671cda417877", "guid": "bfdfe7dc352907fc980b868725387e98bdc15a59b36a33426e7d543e118e479f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98084f232505d33a8e6a354f073d5a12f2", "guid": "bfdfe7dc352907fc980b868725387e98cd2ceb59b6d9dff5f0e4b463cdd0f9fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10b9cc671a01fa48ae8d82fd4eb01f5", "guid": "bfdfe7dc352907fc980b868725387e98114e6617d530778a1f83464d29f2472d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5202afacb6c3ac79bee7e30e34d7d69", "guid": "bfdfe7dc352907fc980b868725387e989c39a7a22d3d6c9e82ce067dc94d4e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d403aa72f6334d5a148d968f754e47", "guid": "bfdfe7dc352907fc980b868725387e981fc8c71af43da53d5b65487805d0c19c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2e808f4b150be023b076cbc33ddd46", "guid": "bfdfe7dc352907fc980b868725387e98c7841856dc7b8d47a59d98e56dba09b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8e23b043339e3a6aea156d70508249", "guid": "bfdfe7dc352907fc980b868725387e98deb32bb25eea578b7999ae51cc341800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d90e6dd44bfbad72e35b78b35f9485d", "guid": "bfdfe7dc352907fc980b868725387e98201f56849a6ae94d40bb6fbb020a8620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c5af3b99a8795146d35d20bed86ab0", "guid": "bfdfe7dc352907fc980b868725387e987b618ac3a44f7d621d72cffb795e17b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bf05567bcf801382dae79fc7124fd89", "guid": "bfdfe7dc352907fc980b868725387e98f9a327a46bc18a44dc2c0d6f74b2e03d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad5d4b62a21215f3e238ff683a9fd2c", "guid": "bfdfe7dc352907fc980b868725387e984dda7fe68deab7db48fa28f406416cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98594c375796c2dedb1142611c21db1a18", "guid": "bfdfe7dc352907fc980b868725387e98e5722bc2becd8592b8e72b9ffb3ac0f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832717fb8558cf6f834389f19ed7af950", "guid": "bfdfe7dc352907fc980b868725387e98c138cc6221b17b2057ee310c7dfd4aa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871dd96675ee8fcceeb16ca9ed87007e4", "guid": "bfdfe7dc352907fc980b868725387e98600f921cfa47c325a1d4b6209dd30101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982735e39eb23f0a8bd87a216bb695e08d", "guid": "bfdfe7dc352907fc980b868725387e9894cee408c6a0b7679f361ce9abedaba8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460701aff7905fd7e985b7fdad2732fd", "guid": "bfdfe7dc352907fc980b868725387e98cdbee414eea65accd8481ffdd1b2906a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98863fc7bf7d9d13c5b90f5f1ee6b621a3", "guid": "bfdfe7dc352907fc980b868725387e988e9be53e41d37ad6f6711a275558f5a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980133d2c58e961b9a420bd21b90c51d52", "guid": "bfdfe7dc352907fc980b868725387e98b2a4d3e5e10090ffce2bba769498bf99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dc89d84e270ee8d5e4a25348011e88b", "guid": "bfdfe7dc352907fc980b868725387e987ab6beab55525e7116611e9747ffdfc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a434370ef066f89041c7a14b26b8b14a", "guid": "bfdfe7dc352907fc980b868725387e985eac35399e124d559e94deadfe53e585"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b52d7907a8fc14a126afa5b847b024", "guid": "bfdfe7dc352907fc980b868725387e985826cf028935bf21ce3b8df6097a4868"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865fcd69aac74c04e8d9e4b2a370709c0", "guid": "bfdfe7dc352907fc980b868725387e988d7f077820e6afd8baa6ad582209a4d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ada0313153b75436d3b7f671376ef5", "guid": "bfdfe7dc352907fc980b868725387e98b8d1fc5ac216c8252cfba14a1b01515a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce5c06688701d6406d32802f7b291c60", "guid": "bfdfe7dc352907fc980b868725387e98afef8ccb02573be963ce8bfffb591cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981780de22591f2d79c4cc737e577ea5b4", "guid": "bfdfe7dc352907fc980b868725387e9839bedd4907fcb594570b629b9514d836"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98795ce1fd017422514310958bba38a8c0", "guid": "bfdfe7dc352907fc980b868725387e98fa483adbad8f816ea6d799067e0bdc19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90648503da7bc14a55dfcd1ac56e5aa", "guid": "bfdfe7dc352907fc980b868725387e9835f740f6c3b2682f556ef99a94f0224c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ab29cb61aaa56fa6688f4867eeca3b", "guid": "bfdfe7dc352907fc980b868725387e98332672ed6eb562c39d935d8dc9e0a630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808fb40f32a16a7446b3a63c2ea56c4d0", "guid": "bfdfe7dc352907fc980b868725387e981668bc778b39f4f7abad1e19ee4dc263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e2fcbdcf8f9518f8fde9887130f9f6", "guid": "bfdfe7dc352907fc980b868725387e989976593c33fb557e95280a9a6b467c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b0812dbdd48c6a8aec35c465da6d1cd", "guid": "bfdfe7dc352907fc980b868725387e98203614ce9da91b35419bc602462a83fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b3bbf0aad28bdfd44e079351d38ea07", "guid": "bfdfe7dc352907fc980b868725387e98d8162298f852914315317f5237c66a52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987586b739d25649bdc88ea11c1f74546f", "guid": "bfdfe7dc352907fc980b868725387e98020a18feeedeab29d43bc5a3add6fff5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec447d3ebb4ca43677d18001a6dd8c0", "guid": "bfdfe7dc352907fc980b868725387e9885640430271a21e7273f10b27b8d322a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc55ce136003ae3efb8fc2c1e77785b3", "guid": "bfdfe7dc352907fc980b868725387e989725a82533ed627ae4bc0f1a08f010f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a262dbcfcb0ca461bf093d45aa4c98", "guid": "bfdfe7dc352907fc980b868725387e98225c1bedd3fcd4df8bdcb953e0b67a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e71957d11431819ff55d93b4175a7eb", "guid": "bfdfe7dc352907fc980b868725387e983b28c09b5803f8213aa1b2297ea390eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e07aa71d17b68228f3ffaa25495280e2", "guid": "bfdfe7dc352907fc980b868725387e98a7396aa247092a1933e82400c7569a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a80950c32704d51919076a9238436729", "guid": "bfdfe7dc352907fc980b868725387e980706c24ffd50651f6357cdcfa42e0c1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e05cf3344bdee23b57a536cee695025", "guid": "bfdfe7dc352907fc980b868725387e9852dbc0a3cc3a91f7f046f19e04d96c5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a34e4357196e90ab107c26621d26d19a", "guid": "bfdfe7dc352907fc980b868725387e98b5542273b42f975f71bd970cc49c959a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865b121d64a0ccc2952aa982f0d9f943f", "guid": "bfdfe7dc352907fc980b868725387e98eb13d1ce07391f73dde4b867c80fddd3"}], "guid": "bfdfe7dc352907fc980b868725387e98a1ca8c34d21eaab502ea2ed3b7e3eb7e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982196b8b103d44a3302568a5f69fc7bfb", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98bd547417afc3d39d75c7892761cc7d43", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/StripeCore/StripeCore.modulemap", "${PODS_ROOT}/Headers/Public/StripeCore/StripeCore-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "2A53B5E3D20A20E978E58FF988285D7E", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/StripeCore-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/StripeCore/StripeCore.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/StripeCore/StripeCore-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98187aa9b7cb03205c0a20870023770d0e", "name": "StripeCore-StripeCoreBundle"}], "guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3a5e4922eb6ec9b57e8bc25d09273d6", "name": "libStripeCore.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
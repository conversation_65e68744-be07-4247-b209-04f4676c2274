{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e02356f4e5beeafc541e9565146e7ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e984f996bd50c3d28aeb27d87669d0a959c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d887c0a8d039c5c85baf19ea1de0663", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e9813f29b5af12bffe2c8fba94816c70598", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d887c0a8d039c5c85baf19ea1de0663", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98680890655cd220930d8a94e6c72d27db", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98da9d8b730711b81f29fa442837f97c86", "guid": "bfdfe7dc352907fc980b868725387e983788f3e409e3cf66bea36ef45cd10057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f79e0681464fcf93c528f7d58214453", "guid": "bfdfe7dc352907fc980b868725387e98d97b3398b903dbb2b56750f29714a944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d971265e24d31b081d7498c347e24f28", "guid": "bfdfe7dc352907fc980b868725387e98db0f53f6cf9a525bdc64cc605d579bb1"}], "guid": "bfdfe7dc352907fc980b868725387e98738083e0404dd6faa6cca8eb9d5ccd47", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9889cf1a2291cf8280518663ef418f575e", "guid": "bfdfe7dc352907fc980b868725387e989a7679fd4f9c15d060c379a759eaaad8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a9c70ccb3ba7a37f517d30a44d09019", "guid": "bfdfe7dc352907fc980b868725387e98d2e4a56da350d2ec391cd3d8736562a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d441d82c895187f845f928e1d959eb", "guid": "bfdfe7dc352907fc980b868725387e98ee4bbbfaa382f1b2db8cf59e5d6a0168"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e3d308bdeaa2ec56292664330b71cfc", "guid": "bfdfe7dc352907fc980b868725387e987b833cde96e1e7856df5bd07423486bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce6d70f77818835436f2252860c36b4", "guid": "bfdfe7dc352907fc980b868725387e985052f4ce44f84a1657116b6c9b51daa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ee2dfc53abb39d662fcb9830659721", "guid": "bfdfe7dc352907fc980b868725387e983f56c57816c4e05fd85895150529fbc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98863818d6e9e405a86add40626825124d", "guid": "bfdfe7dc352907fc980b868725387e9875a6ef2f911e534ce11fc64ad0ac19b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc97cc83d00e4c5c70d04148cea7fd58", "guid": "bfdfe7dc352907fc980b868725387e98bf78a7810efd168b4838e265f7ecc210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98743cfe5aba10367688d3b68520c9b32e", "guid": "bfdfe7dc352907fc980b868725387e98c1babd18e04fcd8351a3b72a3fa5309d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981182d974d2857e79a0e351bad8428db1", "guid": "bfdfe7dc352907fc980b868725387e98ab7cbb9ac662dd9db6a7c95488e676cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9daac04c3c6fe00f9becc007a673bcd", "guid": "bfdfe7dc352907fc980b868725387e9842fa5f94952790c8fb42221d6c80cff2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a97aaddd542cf25c02ff8d25001eeb", "guid": "bfdfe7dc352907fc980b868725387e9860304d444034251805b5b155659d981a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec0d9e46553db58bb5f02cabe1a49da", "guid": "bfdfe7dc352907fc980b868725387e989cdd7531cf1bb47cf05316d3d9c839f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d0e77bd64d46686561b95b8f7a78e59", "guid": "bfdfe7dc352907fc980b868725387e9874efd41d9711d72ec145745ed0606bff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3f29fae2bc63b6ee9a445ea62c7d24", "guid": "bfdfe7dc352907fc980b868725387e9864339b8d0b962de9736bfa08f2b7a622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a3a1f7c9729a5ea31008d6a3d66621", "guid": "bfdfe7dc352907fc980b868725387e98b1744211cdb2aeab8886900656c34bbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985762b32c4bc86e091f4dca70b7220028", "guid": "bfdfe7dc352907fc980b868725387e9871df879b9a3106a9c0ad72eb469d7102"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc547b114ebeb3e2af0fc4b0f9f1381", "guid": "bfdfe7dc352907fc980b868725387e98a0bff78e4eac296a030db59fba816f9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2819cfcd973b0b58a289db430a16bf", "guid": "bfdfe7dc352907fc980b868725387e98b6f0ef0e22222063829cca0cf103161e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982355c32582117e54a8c2e0f6698aebdb", "guid": "bfdfe7dc352907fc980b868725387e98a39fa7733cf7bac1da4733469003bb55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983439109a00e892eeac591792a6621a16", "guid": "bfdfe7dc352907fc980b868725387e9886794549e864c22ad1e23d2b3f690f0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e179b5279112fb339fca063e59ba650", "guid": "bfdfe7dc352907fc980b868725387e98d03a797ecdc150f07e3b9ce91da130a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e74270525e6e63055e68421cbc21b8", "guid": "bfdfe7dc352907fc980b868725387e98e726777984c092fdaf41f0fd494e016e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f3f44432e04b6d5972ce6b3f02905e", "guid": "bfdfe7dc352907fc980b868725387e98bbf4bfa13c768d6310002de76af3c534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98777fbb4989e8fcf1bd27b55d9b1bc820", "guid": "bfdfe7dc352907fc980b868725387e98cc0533feb5bd12741e87da51c1657848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0e526d2daf2fc70f4f09665dd6d936", "guid": "bfdfe7dc352907fc980b868725387e9800b38618dd14be8c7697c457d935169b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830924da6f44d8378213347aed9b798c", "guid": "bfdfe7dc352907fc980b868725387e9813a5bc254b7d8313871bb03098facabd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986981bbd2644d986c2baabed7808568f0", "guid": "bfdfe7dc352907fc980b868725387e9849820c44bc351e1a8120aa4388b2fca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982316a1a0d4156a54bcdccdd1c89f6b40", "guid": "bfdfe7dc352907fc980b868725387e98ba7d52609aca9d489a363b82b272c6b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aabf76358e44784012dcc35d8bd4c3f", "guid": "bfdfe7dc352907fc980b868725387e989543585a68abb84c11e60a61076fce0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f270ec03ed3f16d841f3650c99de84a", "guid": "bfdfe7dc352907fc980b868725387e98e15b149ab6ae6e749bd91befbbf1cf28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22b3d9961520c428db67dea8800113c", "guid": "bfdfe7dc352907fc980b868725387e980e824c79f368c0c0f5dcecbc2c075409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fce2e8af7a5353dd649ed1e275e5a8de", "guid": "bfdfe7dc352907fc980b868725387e986b0ed77de58bf8bd9dba57c1d11bc4bf"}], "guid": "bfdfe7dc352907fc980b868725387e98567edb691c3f9975ba775a84f606bf29", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e26f3ac02324e8337c8610940875d9f5", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98e8154cec9390dbd98a6cdf991ae66a72", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/stripe_ios/stripe_ios.modulemap", "${PODS_ROOT}/Headers/Public/stripe_ios/stripe_ios-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "A67E80BAF2D984782DC4DE6D6279254F", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/stripe_ios-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/stripe_ios/stripe_ios.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/stripe_ios/stripe_ios-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "libstripe_ios.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec5f3bd16c814879c800ed0bb7ea7844", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98227aba8a4c977971f0927dbde6cefc2d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f34770258b2140722181769d0493f646", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98c55e08f7e4103f474e5c31da6fe48259", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f34770258b2140722181769d0493f646", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Headers/Public/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98f51eade4a54a475cd20999c7998d855a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9816845e37d1fb3398690f5f316b4d0081", "guid": "bfdfe7dc352907fc980b868725387e98eaeb141716ef42a77148786a3b1e6ddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a978417e968530552541f9f42d079b", "guid": "bfdfe7dc352907fc980b868725387e98ae71af15ee84eeaecfe2dafefaba5707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f92afb48f668cd5dacb295c992228823", "guid": "bfdfe7dc352907fc980b868725387e98ebd7b204d7d8ef72dfdf492a89ec9020"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987be5a13e626f6963359f88ad03a9ab5c", "guid": "bfdfe7dc352907fc980b868725387e987fe78bee7dfd228d4f97529fce326db2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982029f5a37d0a50b02da7573b59b5832d", "guid": "bfdfe7dc352907fc980b868725387e9839255a6525d097f4c6c10bc2dfbd4ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d38bed88b64b966d19140deaec9d2098", "guid": "bfdfe7dc352907fc980b868725387e983f3cab70b15a5cc110cf0c03aa2c77fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878d5970e72071a74fabfbd75e0e9ab57", "guid": "bfdfe7dc352907fc980b868725387e98c3f491f34d935ff3062e162bb871e90e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810c9aafdc98ebc3b1fe7e53b327377ab", "guid": "bfdfe7dc352907fc980b868725387e98c44105603e191519d24a958ddcd12c3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875dfc16c4f6e838596ad863447096827", "guid": "bfdfe7dc352907fc980b868725387e98d8f998c103598781aaaa64967928f6ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2884c305a2cd2ce9aa73450a28901ba", "guid": "bfdfe7dc352907fc980b868725387e988ed7ee5612824ca2a08f314444ce0f16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be255d97d636df1affec8a669716cc4", "guid": "bfdfe7dc352907fc980b868725387e98e507037349ccfc451c1e2ceb66e692c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864446933cf197fb54363a169fe03848b", "guid": "bfdfe7dc352907fc980b868725387e982f5d9f83fd0793f3bca203af876bd15f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb8b6af539b9e53b2e2e79d77759921", "guid": "bfdfe7dc352907fc980b868725387e98e34e8e70e3a1fd3240ddc8d97d79f2ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d89c362ccfeadcefa783f27796ee2d", "guid": "bfdfe7dc352907fc980b868725387e98bcead7a265768137ef105a4fbe851f96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc248fd192f46a4a917c486a7bbb5240", "guid": "bfdfe7dc352907fc980b868725387e9865df0a5797d731f56a030591d661a356"}], "guid": "bfdfe7dc352907fc980b868725387e9859716bc8a7d5876def85600926ab0261", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a84568c7447ecf83f7aece0abe77351", "guid": "bfdfe7dc352907fc980b868725387e98bb0c8f67297ed7e0aa0e5d8154ea68b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824f64cbd2549778db543f2fe689a0e39", "guid": "bfdfe7dc352907fc980b868725387e98cf7c522f1db85c51184c89b7eb437c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98409b4d0b3ed03ee3334c049f0219dc3b", "guid": "bfdfe7dc352907fc980b868725387e98dbfb61e2ef66f03373682f8043a5f23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44c6f03aa73a52e33c46e9bb2920c6a", "guid": "bfdfe7dc352907fc980b868725387e983d69e468956ca5a8148f95264c5597e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889afd0f2ca44d912d984322d8664cb79", "guid": "bfdfe7dc352907fc980b868725387e98da910379e6c2ba452e30828d9e6fd91c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806de4788a3f54c5a5f71b51a10501eb8", "guid": "bfdfe7dc352907fc980b868725387e988f7511d49e7dff00a41ae3b7accda653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d218ca7753adf7d3fc1b8a838289d3e3", "guid": "bfdfe7dc352907fc980b868725387e980c4ffd16902beda26fe0cdbefb8fc66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5116294e86ecf7b0c69893ebfa5b501", "guid": "bfdfe7dc352907fc980b868725387e987b5c358f3b9321c47a707762eac5ee3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f496aadb12bf3a49f1aabcce096246b2", "guid": "bfdfe7dc352907fc980b868725387e988d71b8cc96291e86267bfc58305016e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882deb140cf59961abce1a7e2e29837f6", "guid": "bfdfe7dc352907fc980b868725387e981278b0bade9c108eb41c4f569a96a53e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac43e0b19ff0988f22dad745bc1bfa7e", "guid": "bfdfe7dc352907fc980b868725387e98fafa6465c87791d9906609576fdb285e"}], "guid": "bfdfe7dc352907fc980b868725387e9825e670cd1a2091a4cd1e9d58270130ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98bfef89246aa7113cf48c32d48515dfe4", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "libvideo_player_avfoundation.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
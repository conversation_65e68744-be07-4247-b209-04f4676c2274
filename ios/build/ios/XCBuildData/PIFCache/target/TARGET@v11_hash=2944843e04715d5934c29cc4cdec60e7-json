{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98278a549e60fc7394608161cf01b14502", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e9824bc3d72a99330d9d9efd2476aa11a0b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee9cef43c0199aa93a0c3482fb805c29", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98122893db730f91f0bd020df9e45f6787", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee9cef43c0199aa93a0c3482fb805c29", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "6.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e981c4c2b831acf1d4b73f9e6a57606d86c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6f106818397d52b9a64082a3f86c4e9", "guid": "bfdfe7dc352907fc980b868725387e9879c646ade35586536a011db1099f0142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a21148d040019f1ae63cdc8272d714", "guid": "bfdfe7dc352907fc980b868725387e984e2348aa671056bf9d59808fd5155179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988960b3d74f4c24c3848c4631381e7ef1", "guid": "bfdfe7dc352907fc980b868725387e98a2d7b6b27be2c2346eb8e4b2d030124a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857fddfc588e68a7b9dfbcedf75fae30a", "guid": "bfdfe7dc352907fc980b868725387e9827db5cbf72c0f4d496cae59c60ff0f78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983538ae7b43236f1181d005c18540367b", "guid": "bfdfe7dc352907fc980b868725387e986881af40ce04377e06f247f13e26bddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ef2b3460df1796940b7b2d419dfc41", "guid": "bfdfe7dc352907fc980b868725387e98c96bb88244796ae8ed0e41d5abf72797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98884fb35f6424a147ecc5e37b9f159f67", "guid": "bfdfe7dc352907fc980b868725387e98c2dbc4f2efd030c5b11c3a860c268825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f24d9ef923511c89dc447549954b26", "guid": "bfdfe7dc352907fc980b868725387e987855a1454274c0a18ca181d752e15f41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8142ecb53ed431087b722ef923d7f59", "guid": "bfdfe7dc352907fc980b868725387e98fdda11a638852f3997176d8dd510c6cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac0e889e7230d709fa3e53320682b0c", "guid": "bfdfe7dc352907fc980b868725387e980e5f3c3d0e0decd26166964f5a6f1fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d26fa804576e566ff179b621a0c38cd", "guid": "bfdfe7dc352907fc980b868725387e9822a1e2c5d450cc503206ca5220ed336f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b5b0e43ba092915416f3eb8bd8de90", "guid": "bfdfe7dc352907fc980b868725387e982aa1437969d01445563582eaa6af072f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dc7276eabc5162d3b836df153e9cc0b", "guid": "bfdfe7dc352907fc980b868725387e989f8c6cc3297b1c384fa755ca47625c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ef9193c74ea39b96d1a204816825f8", "guid": "bfdfe7dc352907fc980b868725387e985e223febe999b7182c9fdda05a9a8b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b93df0872a979d2ca7575ed0168f8964", "guid": "bfdfe7dc352907fc980b868725387e98efe757c155f0ce614190f474783351a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f763dc36d30944cc7033b3ccc093aab", "guid": "bfdfe7dc352907fc980b868725387e983f3309b5e3506b39ce208622262d5fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b35fcdd88ee994bcbd64d545d8a99b", "guid": "bfdfe7dc352907fc980b868725387e98fc73a90a9bfa6eefd14af735583caa6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841149aa05902a1f13b874c4917c0f30b", "guid": "bfdfe7dc352907fc980b868725387e9836a12f34bc2747b8b62773d2b4ef83da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e887c8e58906611947302521178fd3a", "guid": "bfdfe7dc352907fc980b868725387e986a1c913773e8d3b37b43e1e97bededa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc9a554944582b291ec4f7834fbf807", "guid": "bfdfe7dc352907fc980b868725387e9821817e79817e806c30975f5875ecaf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986705c73788495cfbca3f4f4250a72091", "guid": "bfdfe7dc352907fc980b868725387e98a735c25299b733bbe8141dd3fbd78a11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd361fac9151963f97d869f68959587e", "guid": "bfdfe7dc352907fc980b868725387e989d606acf07f34d67cacafd640f878bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98230b171ea038104e1d5ab5ba1da77bee", "guid": "bfdfe7dc352907fc980b868725387e98edbad5fb88d37e2eca857fd70f2b97c0"}], "guid": "bfdfe7dc352907fc980b868725387e98407dd6d4cb2baa9fcb7c4f70cb64706d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ee38b360dbe805220e612feb926cb159", "guid": "bfdfe7dc352907fc980b868725387e987878667facb2cdf0ce7f38b6e0fe9b1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e374a73055a2d05b5232f25b5fcd8a", "guid": "bfdfe7dc352907fc980b868725387e98e08bb4f22c39dccd69d5ddddda589768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ab7e733045a4821c459907400464dd", "guid": "bfdfe7dc352907fc980b868725387e98e85f191448a398f81a2005749c7de04d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864f554f907beb143bb9476a9f6511bf5", "guid": "bfdfe7dc352907fc980b868725387e9882d12ebb6f896c3f6f734128aeec8313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c17e95e99717d6ecaf592f104288ff57", "guid": "bfdfe7dc352907fc980b868725387e98c7c0894ace85cebd81de698d6f053f58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfaff842f53820f0d385164f929e3c0c", "guid": "bfdfe7dc352907fc980b868725387e9841bc455163254d5f2d4da6f9044ddfcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987017a17c6f905559839cbb3f858ccd0c", "guid": "bfdfe7dc352907fc980b868725387e9866695b3a2a0d1d7d76b39a17b6b7b436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857febb13ac71892559ae272bba12ca62", "guid": "bfdfe7dc352907fc980b868725387e98a51369df0e9228de4b43164851485cc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a9eb8d7d7649e7a67b26ee124aeb17", "guid": "bfdfe7dc352907fc980b868725387e98fd823ac00ab9361060528f9a90fb2cc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f14afe90b48844c98d37267265591d", "guid": "bfdfe7dc352907fc980b868725387e982b5a081fcfa16ac525f1ae0628f678b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d846ba6e3142d86f18f58fe23ba0c9", "guid": "bfdfe7dc352907fc980b868725387e98d53b9659980c8d3f6ff92d3998d27578"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834fe1b1e2b7755d4c859a8aeff4d7040", "guid": "bfdfe7dc352907fc980b868725387e981fb2da63718c5b94396efda36d224457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ddadcdc788b5238b9bc2f2c02adfec7", "guid": "bfdfe7dc352907fc980b868725387e98d21b9cdca8e0e57f0a34627b91c45ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a43ec3dfa37113a3d6593c67304cd210", "guid": "bfdfe7dc352907fc980b868725387e98f8dcee32238ad5c7f887b9481e892bc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986446dc3da30361752399e4d0990c4546", "guid": "bfdfe7dc352907fc980b868725387e98add1e2940b9cd7d86d649b5731533905"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d213b00f3e23e0e3af3a634fa054518", "guid": "bfdfe7dc352907fc980b868725387e989a931853f2baf4db1612dbbf2faedb95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98846a9eb9edbad3611ec0373a6e3c8298", "guid": "bfdfe7dc352907fc980b868725387e986ba21f169a495667ca3eac5bb5c373ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0bcfd6c39957546a2e7c40d72f57773", "guid": "bfdfe7dc352907fc980b868725387e98db62d3c863c8aeee5a93e9bff743e919"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da2187daadf1c6e62898b47d9b579e61", "guid": "bfdfe7dc352907fc980b868725387e98af92828a52f6f0846cb9331dbd300034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8bf8a5377fa55e38afef586285290a", "guid": "bfdfe7dc352907fc980b868725387e983184211da98e82975c43de2a4289ba64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982776e826c8fb895343f70beeb9446c1e", "guid": "bfdfe7dc352907fc980b868725387e9841e377c481a42d269ff639d124f23af8"}], "guid": "bfdfe7dc352907fc980b868725387e9800e9a27248b934827170bd366a5e42de", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98514f996dd408d5ead3e39ec2dc211364", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "libpermission_handler_apple.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
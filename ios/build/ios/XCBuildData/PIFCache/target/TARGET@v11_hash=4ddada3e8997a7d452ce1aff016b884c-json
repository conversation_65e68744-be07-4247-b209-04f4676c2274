{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5cb7384438246866358b5eb9adc9efa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e983edd1dc08996b30e802f2b1cf0437f62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984243e425127452483e75c64eeda9f89b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98e34367d615b2402effa37da4728445ed", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984243e425127452483e75c64eeda9f89b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_EVENTS=0 PERMISSION_REMINDERS=0 PERMISSION_CONTACTS=0 PERMISSION_MICROPHONE=0 PERMISSION_SPEECH_RECOGNIZER=0 PERMISSION_MEDIA_LIBRARY=0 PERMISSION_SENSORS=0 PERMISSION_BLUETOOTH=0 PERMISSION_APP_TRACKING_TRANSPARENCY=0 PERMISSION_CRITICAL_ALERTS=0", "GENERATE_INFOPLIST_FILE": "NO", "MODULEMAP_FILE": "Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e9871fa975f31eeec97324faa770391ea41", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9b25856f30c7ed8ac22544bda703994", "guid": "bfdfe7dc352907fc980b868725387e986164931630c90267e53ba694b2c3e2eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f468e7a73ebdcba0a391496d86c70b3", "guid": "bfdfe7dc352907fc980b868725387e9870555d96ccf0ad9a1bbdb45833bad0e4"}], "guid": "bfdfe7dc352907fc980b868725387e983e662390f7e1da9842c53398231140c4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860b9c4e2a8e8573e0b39d16fbe1e8897", "guid": "bfdfe7dc352907fc980b868725387e9829a8a385c8c774dbd2197296d3aa1d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859df3e1d8c16cf56deb04023f1273f97", "guid": "bfdfe7dc352907fc980b868725387e98a3902369a15c85b024fa0ae2d522e707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba028b71303af7efac8a61fb2a679bb3", "guid": "bfdfe7dc352907fc980b868725387e98a0b6e96d443f69d149bdc1f2fd00a44c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98604244d5bf7796b331b258fb1ce0f1d5", "guid": "bfdfe7dc352907fc980b868725387e9846cf2c69b813fc55a993bc1f6690588f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d17475b3e663e16a21eaafc67faa4d", "guid": "bfdfe7dc352907fc980b868725387e98100423ed45c72f1c5cdbc9fdbcaced86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884673d74fee18a2827989358f4aca467", "guid": "bfdfe7dc352907fc980b868725387e9837f5689091cc56e061cf7f02523b302d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6f0aac55d8ca5edcde4ba5f74235dea", "guid": "bfdfe7dc352907fc980b868725387e98241ff7ac03a2ad08ed2257f09090eebf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982374782c1fbeb0feaa06fb9fe06f6640", "guid": "bfdfe7dc352907fc980b868725387e984ddf326fb7f9a8200c3219e7b6f264c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e148c65b17ae06e88d3647575ecd5f3", "guid": "bfdfe7dc352907fc980b868725387e98004c2138bd8192db7e554f45f0af5c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df833b72787bb80521afa980ae3de478", "guid": "bfdfe7dc352907fc980b868725387e98eaa4e10b3feb846dd45165d74d0eff6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6cfa898cd1a433279183e17322d10b", "guid": "bfdfe7dc352907fc980b868725387e98671296e88dc4b52dfbfdbb27c9dd387d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28cea5c5a424937bb514f3108b67e80", "guid": "bfdfe7dc352907fc980b868725387e9824668b81d3905ca859e6fd759591a230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811efcb5f0e53d21794d5e975d3ec8d1a", "guid": "bfdfe7dc352907fc980b868725387e9894ee352a8162e909fc11fac911cc3715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7c53b99c2baae30e34cdaebc15c255c", "guid": "bfdfe7dc352907fc980b868725387e9833ef383b05291a60958bf0c482b7e97f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc6d947b1ed7f9ed175d24678e74a90b", "guid": "bfdfe7dc352907fc980b868725387e9888ca94d9a1606b7e82bf46aedf3da0f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a352872b3f59e3ddc2c5f3819c499d2", "guid": "bfdfe7dc352907fc980b868725387e9841bcd64b4dfde29c458109af50ffe56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984405d8e2293a8c245b7df86c17c561f2", "guid": "bfdfe7dc352907fc980b868725387e98d158084822dfdc67b296d89590b3aa70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984545c892d163122909496f0af741aca7", "guid": "bfdfe7dc352907fc980b868725387e98465f60a4367c832eef4d0601bd3f4f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98339117f0bf744eefe307882970e538b4", "guid": "bfdfe7dc352907fc980b868725387e984ba5284e0ff1efef09b49980b1ec729d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983253870a8a3cb3968c738806ac99f9cf", "guid": "bfdfe7dc352907fc980b868725387e9828ac0edba210ac0fc1a46e80affaacc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893fc0a20ca015b48ebab670162c2c133", "guid": "bfdfe7dc352907fc980b868725387e98a82991e43fe3c65c75f15854f6b523cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2cc4f9e0e063aea1c616eeec2cbdf11", "guid": "bfdfe7dc352907fc980b868725387e987d6b1e6c74c17a5d483add42be92d63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f514f254b1fe2db4b8e81614ff5f139", "guid": "bfdfe7dc352907fc980b868725387e98c4c19bb0600ddb1415198ad3c19a75b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d879f60ccb2ee7fd7355a3f74db25f5c", "guid": "bfdfe7dc352907fc980b868725387e985effc35313f2fb2012d9978cbefb9f21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983752bba148e93df8a12d4e956a9b8eb8", "guid": "bfdfe7dc352907fc980b868725387e98a66e93eac40e3895badae252fad9119d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef800691cb2f95c4dbb6bfa69bbb5a61", "guid": "bfdfe7dc352907fc980b868725387e98c2e329f5e74d29d261a17e1886482836"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b17acf8d30b6b4c492ca114491baaed5", "guid": "bfdfe7dc352907fc980b868725387e985c5057bb7548d2428c03fd57e3f7d067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd0f746d48c539a58556a1821d4aff6", "guid": "bfdfe7dc352907fc980b868725387e9847de607ad5879d670f10297cb85b2ffb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27bda962c5cbd44b4ad3738f151e191", "guid": "bfdfe7dc352907fc980b868725387e98c3faa53dc9bb9e86269d1ce88cae4d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849bd75b513a4e0723e1041f912097f8f", "guid": "bfdfe7dc352907fc980b868725387e986a3e43c9183c73a599cba1e9101e7111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62269493c0dcaa0069d023f42cbf821", "guid": "bfdfe7dc352907fc980b868725387e9833d7b6dd743dc7a0cd867d39e0483823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9d883456857bc2e2da681bbf1dece5e", "guid": "bfdfe7dc352907fc980b868725387e98af1b73ecab378dd9016776e6e0b39ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833af94f36ff407dbadb14c707ea956d4", "guid": "bfdfe7dc352907fc980b868725387e98aa19c7c7486c6fba334181ddf9e866db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d7b3141ce93853ffbbf5cd632a4e1ca", "guid": "bfdfe7dc352907fc980b868725387e987f067d7da33e6dffa5fae5e3941e1480"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d33289dab4f0d6df90554d5dd965df", "guid": "bfdfe7dc352907fc980b868725387e9809a7a42a84ebeae9313e66b2133f780c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb637e1d2b83d0488929680aec6337d8", "guid": "bfdfe7dc352907fc980b868725387e98140ea842f57b97a44f78de05f445c715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982781a4b7e9dc0674491c92e63fb6bf60", "guid": "bfdfe7dc352907fc980b868725387e98a09246075422ba3a8fa64a883413e0c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf5f04db51d2d1326c43b9064b463ee6", "guid": "bfdfe7dc352907fc980b868725387e983c9331702dc67131113f3ff466425d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983904c5e4bdd3e3467ae5b121bc1d1b13", "guid": "bfdfe7dc352907fc980b868725387e9845df5ff9190eb43c0ab1565ed80c5ec2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817e14f917603489d1b4f43b7745f045f", "guid": "bfdfe7dc352907fc980b868725387e98f5ab5a49e84b990a6373f52b3a285e2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ac2470319938bd75a81deaa7cf3f3a", "guid": "bfdfe7dc352907fc980b868725387e98b125d4457114a3c4a09e961e7bd76283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879e46381f5bd344edaf7ac6a8b217546", "guid": "bfdfe7dc352907fc980b868725387e986ee3f7e23319f95b100c528a9bc8a335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5c2cde01b1a39de2c9a8f0e255ebfa", "guid": "bfdfe7dc352907fc980b868725387e9805f658eaf9b7fec8d99f16de976e4427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e01f8cb51c1be7ea29333e41cb3a4c", "guid": "bfdfe7dc352907fc980b868725387e9844975f365a52ed2d960cae883f4de0ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984db5b14cac62fc623f49aa45b9e4de32", "guid": "bfdfe7dc352907fc980b868725387e987583528f8f6077cebf8bdc3b015cf327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839544eb62fdf4f9cd76add0f28e171a2", "guid": "bfdfe7dc352907fc980b868725387e98db8695afabfaff8571042c98fde3c68a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9b1d392a2ff33d67f95759a1f96601", "guid": "bfdfe7dc352907fc980b868725387e98e79784e433809643af9f89d760a16cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844415f6e5937e26399be2331108fc534", "guid": "bfdfe7dc352907fc980b868725387e98bf8c2758691a2fc347b3600005cb8bf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a422ee2e78dff6c16b5fdeaad6af4489", "guid": "bfdfe7dc352907fc980b868725387e983341846eea2c4246f3eeb2747145db4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866ee7028a90e618ef603c1190a3f0e7f", "guid": "bfdfe7dc352907fc980b868725387e9896e3761267c192c756afec36be2c6c53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98251da45fffcf4d25852ece74130cb1ec", "guid": "bfdfe7dc352907fc980b868725387e9815abc12f211ceab07445d648e47e8251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843c26faeb7c19ebc7259d0601b7e3333", "guid": "bfdfe7dc352907fc980b868725387e98858da80df950139b9d75ef2c65abf88b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837cc669f5d00c28c25d8492524980d0b", "guid": "bfdfe7dc352907fc980b868725387e9847b2fe501af72c8109702eb55dc2d40b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbf9dc64dff2c4aa1f6bb8456d9b40b", "guid": "bfdfe7dc352907fc980b868725387e9824afa5ba5afd14e5b83e581c62310976"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca9492122daf065ac40a0a68f9e198b", "guid": "bfdfe7dc352907fc980b868725387e98d74a1a33d6846b613553b0950d920926"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d83eb4a270c1131953fa15de67f5bc9", "guid": "bfdfe7dc352907fc980b868725387e980ceffab275fde9d5dcb6dd1a4499247f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca863c88547be5ab80105489e418d3e", "guid": "bfdfe7dc352907fc980b868725387e98811dd654e8b7d8e43dc4eae7356e1e91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892c9137a9b4f1f8791e8422cc4a9d676", "guid": "bfdfe7dc352907fc980b868725387e986d7569bdbf2b688ae5eedfe6fb8a0931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987408ab31e202efc555f27f16bad7c0c4", "guid": "bfdfe7dc352907fc980b868725387e98350d3ca585aec915a01635c5b4ff6603"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e4f3f8c3cf8cc988b1233d60e19622c", "guid": "bfdfe7dc352907fc980b868725387e986a94b02b8e876c20647eb14778d266f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f31f5dd0426885dc2c54edbfec5ade", "guid": "bfdfe7dc352907fc980b868725387e9869c52b654aedb516bd1c185a0c259da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d07ca73d7ea986fe0af7d04ee8805c", "guid": "bfdfe7dc352907fc980b868725387e980add5bcece97c30743a6d66a7593afce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a8e283fcfefe7ffd1ed2746ff22630e", "guid": "bfdfe7dc352907fc980b868725387e98b2bfcac8ca6b1a967d486b629d2fecab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932e5796365f1255d8b75f91882f7f77", "guid": "bfdfe7dc352907fc980b868725387e98e8e3f61bf3e6ef43f35f2c6596faea9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746c3886a406cd0e821e7b25d0671c99", "guid": "bfdfe7dc352907fc980b868725387e98f2739e4e0faf882d3289952cc064109e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff178f11a5641c1880a3b9b6d33ca992", "guid": "bfdfe7dc352907fc980b868725387e98832c30a8360397e0b1b3063c5dcbd77f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ddffe53019b8bde109b73546551b83b", "guid": "bfdfe7dc352907fc980b868725387e98eac45b3a714112f432eb47ed6dbc8b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca18bcd28c6100360bed446404fe3103", "guid": "bfdfe7dc352907fc980b868725387e98a5f52e602dc260052a22417dfd31a696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c5ef60455c674f848707afb4153c9ff", "guid": "bfdfe7dc352907fc980b868725387e98ebd9e356abea0a73f5311ea29eab1c7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bc49c22f9276ac2b61f225365f36aa3", "guid": "bfdfe7dc352907fc980b868725387e98cdde21d515d3eaba6f30fae7b73d87a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f4c954fba26daf43ae5b537fc595fcd", "guid": "bfdfe7dc352907fc980b868725387e98de64a2a7f1c2385b31258de3560af0a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f70dac92a36554d6450d9f6573c8c66", "guid": "bfdfe7dc352907fc980b868725387e98567dd3ec3119c9f2f113702617ff241f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98781b2a306add621fa0814f86180620da", "guid": "bfdfe7dc352907fc980b868725387e98d95d9265013991efc752a1da1ab7e669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e77280d8d869712d47d3376ac79417", "guid": "bfdfe7dc352907fc980b868725387e980acf7c77447a28cf2d1708043fb92fe7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6aa3d91665c6570c1b70eed719a455", "guid": "bfdfe7dc352907fc980b868725387e98c4a153fe51f97bc3a2ed9d726d8f1c24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e367315831f26c36d949629d02d9f3b", "guid": "bfdfe7dc352907fc980b868725387e98e9afc2272185633da382218297f21dfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851579afcb00eba2d38c09ace8179c06e", "guid": "bfdfe7dc352907fc980b868725387e9878589e9565279016750421e543a47b39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98958648bb6785c657a458258418b0e45a", "guid": "bfdfe7dc352907fc980b868725387e9883bb9b893e526edac1ab124db305ae26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ece39d345caaa341de1dea0bd2c79b0", "guid": "bfdfe7dc352907fc980b868725387e989d222a020c4a4757f257e5a3984d7aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8b89058781772d7077f29987ff829a", "guid": "bfdfe7dc352907fc980b868725387e9888bdc0623c63a17e47b7b23a1eebf8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d443597976e52e53b0df3df78a6a13a", "guid": "bfdfe7dc352907fc980b868725387e98e8cb928ef369872caeb11625527679db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1df7ed3608d02e32d1895b5aaf9f879", "guid": "bfdfe7dc352907fc980b868725387e982051a3fe8cdfb8a6be4be9942d679475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980119a45ed26cb68cfb31ed6df59d9e12", "guid": "bfdfe7dc352907fc980b868725387e981bb8f5c2b0810d39947bdf97e11c7ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98831dfd6b49d0debdcf2dddffdeb729b6", "guid": "bfdfe7dc352907fc980b868725387e9821ecda470e1a9f3d8644065278f86999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98260e87aaee6a3e3ca89f4eac61ed3b4c", "guid": "bfdfe7dc352907fc980b868725387e98156e2b059f13bcf85a1b08bedf87c4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4278828f9e709c15fd85ae32a6479f8", "guid": "bfdfe7dc352907fc980b868725387e9822abdc51340b26bccc108b7559685c04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7400c0739d82d14ca51ddc27139e556", "guid": "bfdfe7dc352907fc980b868725387e9819fe14b89fe13e14ff80985d3ce1d694"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fc314653ca4631037d1a853cec07866", "guid": "bfdfe7dc352907fc980b868725387e981706d04e06a647e5f8ebf0f4d8d2b112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824dd28fd0703cac12ffee9649543055e", "guid": "bfdfe7dc352907fc980b868725387e98f1140e7ed227926b874238feb2d9640e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980147dc146811eb37959b68abf7924500", "guid": "bfdfe7dc352907fc980b868725387e9839fd3d89ac93e7f4f981280bc08f8a80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aea159bcab31c9030b46837a160cad63", "guid": "bfdfe7dc352907fc980b868725387e98f7265c6d1506317bcc71cba239938554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987969642860b00a9a385b7e19924231b9", "guid": "bfdfe7dc352907fc980b868725387e98336c0fc8389b6126c07b2d2d488db590"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98368010812f750ac92339f6d7fcaf6992", "guid": "bfdfe7dc352907fc980b868725387e983d808e6cd9b3354559c3de6a076225c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98637b183d21525253bd79bc31cc84a76e", "guid": "bfdfe7dc352907fc980b868725387e989fdcc31489ad0ea1e6455a44ebe6ab93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989559729b4b27f61dcb472e8f701ca86e", "guid": "bfdfe7dc352907fc980b868725387e98188a97c78924694b50b13840ab6221c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d16847372ca6489e680070e08e3f290", "guid": "bfdfe7dc352907fc980b868725387e98850568c856050ff8d8dab2c602d2dc0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a54300c5fa8957d7e7764768a1d54a", "guid": "bfdfe7dc352907fc980b868725387e981a4295a953be887c3482dd46939c7fff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813dc2e895fa1106446fbc3a22b70c582", "guid": "bfdfe7dc352907fc980b868725387e98858fecdb3f4db15804aefef865a2f8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44dd99101c3b13a6522a828cfad31b4", "guid": "bfdfe7dc352907fc980b868725387e98d4a03e8e41038e937842d4f3b20ab765"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642cb37b6102f2610024424c90f943a5", "guid": "bfdfe7dc352907fc980b868725387e98bca9191a2b8b2eb29742668b60f99d7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05047071e7d7c67e6816feab2cdfc45", "guid": "bfdfe7dc352907fc980b868725387e987d10cc78e43271dd880c22d4154bb055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f0ea0b0eec9215f99211be836ddb699", "guid": "bfdfe7dc352907fc980b868725387e9813a9b0350f7ba02fe8c06d0c7b50d7b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871dc30a3adf8a3e54b35e6a51d5fe000", "guid": "bfdfe7dc352907fc980b868725387e98f76e479771f2d2deeedda7ef3cfe61d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f79f3e954147d8822ff711f73671e9", "guid": "bfdfe7dc352907fc980b868725387e98823aa31ce3a4229ce086a297467ef548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863b3173c0b94b627e154c52376e3ed1a", "guid": "bfdfe7dc352907fc980b868725387e986e3977fb7a06c50cae825db6446f4d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d337ad91c1c9a7717df9a17ff45bf9f8", "guid": "bfdfe7dc352907fc980b868725387e9802ef49fb20efd878feccc47d0270e873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca86c8397fa8c9f6d1e607812766d3d9", "guid": "bfdfe7dc352907fc980b868725387e983ccefeeef56ef09a991ba5a2eb7b594c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df12763511000caf3d46af60ae429271", "guid": "bfdfe7dc352907fc980b868725387e981001baa20f15884bb61497898327a4ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be5fe0da6d66d166c99f3bcc043b8ed", "guid": "bfdfe7dc352907fc980b868725387e98d5e69494f39a7d43abe8f0dfc42d1dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464babdca6b76498cede04a82c14c27d", "guid": "bfdfe7dc352907fc980b868725387e98530adb108bc9889d895c370ad6838d6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f533cd70191269ff8b37718fcba4de", "guid": "bfdfe7dc352907fc980b868725387e98bce7cbdbd268fa7f3f12f54030246441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d144989f8443f4defe939599bdaa951", "guid": "bfdfe7dc352907fc980b868725387e98f54fce87b8b547133949b3fd87458a31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9fce20cc80cbc44b1efc293efab0b9", "guid": "bfdfe7dc352907fc980b868725387e98ded24aca2dbeffbfecd316ab7d5acedc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892dd02beed00224e14d65b6170207a81", "guid": "bfdfe7dc352907fc980b868725387e98312d598c63f7615419114d94835bcedf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2fc5b0ae33e868d684121240836780", "guid": "bfdfe7dc352907fc980b868725387e98444ea7afa38faf183ef1d5bc669f6e4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a7dabf64a161f6ee7eb427369af116", "guid": "bfdfe7dc352907fc980b868725387e9800fc56635c655a26cc5b6375910978cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e683e0d345ac14053a81c11d30e688ae", "guid": "bfdfe7dc352907fc980b868725387e98bdd2ad695f82c9d312f1e71299a22444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c12018266122d912782601dfdca05dac", "guid": "bfdfe7dc352907fc980b868725387e98c1a70da61b7f575e22a3c1d7a984b96a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a93b93b34c2d973641add7723a34486a", "guid": "bfdfe7dc352907fc980b868725387e98cb45a6e413659f545e921ac076139119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac39fec04ddc50270dfb93e5abffbc9d", "guid": "bfdfe7dc352907fc980b868725387e9821d6128fd516d4b88de3c014c97b3ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980256057e87ec4148abb15cd13825a23e", "guid": "bfdfe7dc352907fc980b868725387e98be1cbd5833da06ca2b319f46ea4f295f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7735fc7e155c52ae5d3603536de3fc6", "guid": "bfdfe7dc352907fc980b868725387e9897f712b753e3b9ac6c249162fd8068f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af4c93b91de5f848a6d34e468ed7c69", "guid": "bfdfe7dc352907fc980b868725387e98c69ab554e8608c1ac03989edf4c6770a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5efe2d8f464fccca67b073238aecfaf", "guid": "bfdfe7dc352907fc980b868725387e98c8e80ee9c287d361dd23ea33c4478859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986935b8c47722d5872d9dd8e6fdcab866", "guid": "bfdfe7dc352907fc980b868725387e98926bccacf510936a60ae8d7e1e6e0814"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1175d07ce0e380e4c52a7aace2fcc7d", "guid": "bfdfe7dc352907fc980b868725387e9810540d3a530fe22ff2dcc8b1edc8367a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf67a875c6b9054a2282bea77a45aa0", "guid": "bfdfe7dc352907fc980b868725387e98ab151c0d98367ac52158356f013ed943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac9e4dc0cfa6ab3365514e86f4c7e42", "guid": "bfdfe7dc352907fc980b868725387e986369809c1c69e51aebe1a5016ddaf226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2f9b473e2324c4f23bdda8816ef60a", "guid": "bfdfe7dc352907fc980b868725387e981e246d85a0ee07cb88cc2e4161554eaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e16d1d2931e987253bd25f80ca1120e", "guid": "bfdfe7dc352907fc980b868725387e9843d18e25ff45e72c90e6d9a4a89f4882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985057621f5cf8364fccbf265d56a996b1", "guid": "bfdfe7dc352907fc980b868725387e98e7dab84e22fec6400f0db152e9365a3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98017620cd7802b8d1769d8065e823646a", "guid": "bfdfe7dc352907fc980b868725387e98993f4d1fb49be8f5edfaab4ceea9bd45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad8cad71086f72b6a4e8dbe75c43c83", "guid": "bfdfe7dc352907fc980b868725387e981b7d08792d29940cba8f9115cd7d8dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98160022243417a7ab73b400878cbdc932", "guid": "bfdfe7dc352907fc980b868725387e9837a1587a6a88a674a13eeadaee7b303b"}], "guid": "bfdfe7dc352907fc980b868725387e98b6501e2912ab4b78745dde0842749708", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98753e2eab8ede6436d2c2fc440333bb83", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e987ad868c3e7117c5ed36e2c427edd40b8", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "${PODS_ROOT}/Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "AA6CAB48F0C97112B7959B49CAAF3D6B", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/flutter_inappwebview_ios-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/flutter_inappwebview_ios/flutter_inappwebview_ios-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "libflutter_inappwebview_ios.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
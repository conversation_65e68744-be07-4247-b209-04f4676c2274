<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>DV Ads Manger</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>NSAppTransportSecurity</key>
        <dict>
            <key>NSAllowsArbitraryLoads</key>
            <true/>
        </dict>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>NSAppleMerchantIdentifier</key>
        <string>merchant.com.dv.ads.manger</string>
        <key>PKMerchantCapabilities</key>
        <array>
            <string>3DS</string>
        </array>

        <key>PKPaymentNetworks</key>
        <array>
            <string>visa</string>
            <string>masterCard</string>
            <string>amex</string>
            <string>discover</string>
        </array>

		<key>CFBundleName</key>
		<string>DV Ads Manger</string>
		<key>NSAppTransportSecurity</key>
        <dict>
          <key>NSAllowsArbitraryLoads</key>
          <true/>
        </dict>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0.54</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb715941350234044</string>
					<string>com.googleusercontent.apps.923196999770-o5ajf93iq235fgj0eluk6gk8r66fo0m5</string>
				</array>
			</dict>
		</array>
		<key>LSApplicationQueriesSchemes</key>
        <array>
            <string>http</string>
            <string>https</string>
            <string>tiktok</string>
            <string>tiktok-auth</string>
        </array>
		<key>CFBundleVersion</key>
		<string>34</string>
		<key>FacebookAppID</key>
		<string>715941350234044</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>DV Ads Manger</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>sms</string>
			<string>tel</string>
			<string>whatsapp</string>
			<string>mailto</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSCameraUsageDescription</key>
		<string>DV Ads Manger needs access to Camera to send or update, current or expected picture.
			For example update profile picture image or send consulting to DV Optimize.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>DV Ads Manger needs location access to help you find your target audience as fast as
			can</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>DV Ads Manger needs location access to help you find your target audience as fast as
			can</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>DV Ads Manger needs location access to help you find your target audience as fast as
			can</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>DV Ads Manger needs access to Photo Library to send or update, current or expected
			picture. For example update profile picture image or send consulting to DV Optimize.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
	</dict>
</plist>
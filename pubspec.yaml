name: ads_dv
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.58+63

environment:
  sdk: '>=3.0.5 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  flutter_stripe: ^10.0.0
  country_code_picker: ^3.3.0
  cupertino_icons: ^1.0.2
  flutter_bloc: ^8.1.3
  logger:
  auto_size_text: ^3.0.0
  hive_flutter: ^1.1.0
  hive: ^2.2.3
  dio: ^5.2.1
  dartz: ^0.10.1
  get_it: ^7.2.0
  internet_connection_checker: ^1.0.0+1
  equatable: ^2.0.5
  flutter_localizations:
    sdk: flutter
  awesome_dialog: ^3.1.0
  flutter_svg: ^2.0.7
  cached_network_image: ^3.2.3
  flutter_snake_navigationbar: ^0.6.1
  carousel_slider: ^4.2.1
  flutter_staggered_grid_view: ^0.6.2
  device_preview: ^1.1.0
  pinput: ^3.0.0
  intl: ^0.18.0
  url_launcher: ^6.2.5
  lottie: ^2.6.0
  flutter_screenutil:
  expansion_tile_group: 1.2.4
  percent_indicator: ^4.2.3
  toggle_switch: ^2.3.0
  control_style:
#  mobkit_dashed_border: any
  flutter_animate: ^4.5.0
  oktoast:
  pin_input_text_field: ^4.5.1
  firebase_auth: ^4.7.1
  firebase_core: ^2.15.0
  firebase_messaging: ^14.6.5
  firebase_crashlytics: ^3.3.5
  flutter_facebook_auth: 6.0.3
  floating_bottom_bar: ^0.0.2
  file_picker: ^5.3.2
  image_picker: ^1.0.4
  awesome_snackbar_content: ^0.1.3
  chewie: 1.8.5
  google_maps_flutter: ^2.2.6
  get:
  geolocator: ^10.0.0
  image: ^4.1.7
  syncfusion_flutter_charts: ^25.2.3+1
  gradient_borders: ^1.0.0
  flutter_local_notifications: ^15.1.0+1
  youtube_player_flutter: 9.0.1
  video_player: 2.9.2
  showcaseview: any
  sign_in_with_apple: ^6.1.3
  photo_view: ^0.14.0
  crypto: ^3.0.3
  permission_handler: ^11.3.1
  pusher_channels_flutter: 2.2.1
  flutter_expandable_fab: ^2.3.0
  animate_do: ^3.0.2
  webview_flutter: 4.7.0
#  video_compress: any
  location: ^6.0.2
  geocoding: ^2.2.0
#  flutter_web_auth: ^0.5.0
#  pusher_channels_flutter: ^1.0.3
#  pusher_client: ^2.0.0
#  upgrader: ^10.3.0
dev_dependencies:
  flutter_test:
    sdk: flutter

dependency_overrides:
  intl: ^0.19.0
  wakelock_plus: 1.2.8

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true # generates flutter localizations

  assets:
    - assets/images/
    - assets/lottie/
    - assets/icons/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Karma
      fonts:
        - asset: assets/fonts/karma/Karma-Light.ttf
          weight: 300
        - asset: assets/fonts/karma/Karma-Regular.ttf
          weight: 400
        - asset: assets/fonts/karma/Karma-Medium.ttf
          weight: 500
        - asset: assets/fonts/karma/Karma-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/karma/Karma-Bold.ttf
          weight: 700

    - family: Cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/cairo/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/cairo/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
          weight: 600


  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages